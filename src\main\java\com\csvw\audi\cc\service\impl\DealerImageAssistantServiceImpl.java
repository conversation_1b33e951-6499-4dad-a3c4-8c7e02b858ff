package com.csvw.audi.cc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.csvw.audi.cc.common.ObsService;
import com.csvw.audi.cc.config.OssConfig;
import com.csvw.audi.cc.entity.dto.DealerDto;
import com.csvw.audi.cc.entity.po.SvcdChannelOrganizationFile;
import com.csvw.audi.cc.entity.vo.DealerVo;
import com.csvw.audi.cc.mapper.SvcdChannelOrganizationFileMapper;
import com.csvw.audi.cc.mapper.SvcdChannelOrganizationMapper;
import com.csvw.audi.cc.service.IDealerImageAssistant;
import com.csvw.audi.common.config.OssServer;
import com.csvw.audi.common.uti.DateUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/16 14:08
 * @description 线下体验店代理商图片
 */
@Slf4j
@Service
public class DealerImageAssistantServiceImpl implements IDealerImageAssistant {

    @Value("${svcd.dealer-image-url}")
    private String svcdDealerImageUrl;

    @Autowired
    private OssConfig ossConfig;
    @Autowired
    private OssServer ossServer;

    @Autowired
    private SvcdChannelOrganizationMapper svcdChannelOrganizationMapper;
    @Autowired
    private SvcdChannelOrganizationFileMapper svcdChannelOrganizationFileMapper;

    @Autowired
    private RestTemplate restTemplate;
//    @Autowired
//    private ObsService obsService;

    /**
     * <AUTHOR>
     * @date 2022/10/31 12:02
     * @description 异步执行经销商图片上传
     */
    @Override
    public void asyncFindAndStorageDealerImage(List<SvcdChannelOrganizationFile> fileList) {
        if(CollectionUtils.isEmpty(fileList)){
            log.info("fileList 数据为空");
            return;
        }
        long startMillis = System.currentTimeMillis();
        log.info(startMillis + "开始经销商线下图片更新");
        CompletableFuture.runAsync(() -> {
            Set<String> dealerCodeCollect = fileList.stream().map(SvcdChannelOrganizationFile::getDealerCode).collect(Collectors.toSet());
            for (String dealerCode : dealerCodeCollect) {
                if(StringUtils.isEmpty(dealerCode)){
                    continue;
                }
                DealerDto dealerDto = new DealerDto();
                dealerDto.setDealerCode(dealerCode);
                try {
                    this.findAndStorageDealerImage(dealerDto);
                } catch (Exception e) {
                    log.info("网发推送来的消息文件上传oss异常");
                    e.printStackTrace();
                }
            }
        }).whenComplete((v,e)->{
            if(e == null){
                long endMillis = System.currentTimeMillis();
                log.info(endMillis + "经销商线下图片更新完成, 耗时 " + (endMillis - startMillis) + " 毫秒");
            }
        });
    }

    @Override
    public void findAndStorageDealerImage(DealerDto dealerDto) {
        try {

            QueryWrapper<SvcdChannelOrganizationFile> fileQuery = new QueryWrapper<>();
            // 7:图片简介
            fileQuery.eq("file_type", "7");
            if (dealerDto != null) {
                List<DealerVo> list = svcdChannelOrganizationMapper.getAgentList(dealerDto);
                Set<String> dealerCodes = list.stream().map(i -> i.getDealerCode()).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(dealerCodes)) {
                    fileQuery.in("dealer_code", new ArrayList<>(dealerCodes));
                }
            }
            List<SvcdChannelOrganizationFile> fileList = svcdChannelOrganizationFileMapper.selectList(fileQuery);
            fileList.stream().filter(file -> StringUtils.isNotEmpty(file.getUrl())).forEach(file -> {
                // 保存原有 URL
                file.setOldUrl(file.getUrl());
                // 下载图片进行oss转存
                String url = publicUploadV2(file.getUrl(), file.getDealerCode());
                // 将本地的 URL 进行保存
                file.setUrl(url);
                file.updateById();
            });
        } catch (Exception e) {
            log.info("经销商图片上传oss异常- {}", dealerDto == null ? null : dealerDto.getDealerCode());
            log.error("", e);
        }
    }

    public String publicUploadV2(String imageUrl, String dealerCode) {
        String dealerImageUrl = svcdDealerImageUrl + imageUrl;
        String fileName = imageUrl;
        String ossImageUrl = null;
        try {
            log.info("进行文件下载地址：{}",dealerImageUrl);
            InputStream inputStream = findResultInputStream(dealerImageUrl);
            String filePath = ossConfig.getDealerImagePath() + "/" + dealerCode;
            if ( imageUrl.indexOf("?")>0 ){
                fileName = UUID.randomUUID()+fileName.substring(fileName.indexOf("."), fileName.indexOf("?"));
            }else {
                fileName = UUID.randomUUID()+fileName.substring(fileName.indexOf("."), fileName.length()-1);
            }
            ossImageUrl = ossServer.publicUpload(inputStream, fileName, ossConfig.getBucketName(), ossConfig.getSegmentationUrl(), filePath);
            log.info("上传oss地址:{}", ossImageUrl);
        } catch (Exception e) {
            log.error("获取图片上传oss异常-{}", imageUrl, e);
        }
        return fileName;
    }
//    public String publicUpload(String imageUrl, String dealerCode) {
//        String ossImageUrl = null;
//        InputStream inputStream = null;
//        try {
//            inputStream = obsService.obsGetObjectInputStream(imageUrl);
//            String filePath = ossConfig.getDealerImagePath() + "/" + dealerCode;
//            ossImageUrl = ossServer.publicUpload(inputStream, imageUrl, ossConfig.getBucketName(), ossConfig.getSegmentationUrl(), filePath);
//            log.info("上传oss地址:{}", ossImageUrl);
//        } catch (Exception e) {
//            log.error("获取图片上传oss异常-{}-{}", dealerCode, imageUrl, e);
//        } finally {
//            if (inputStream != null) {
//                try {
//                    inputStream.close();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//        return ossImageUrl;
//    }

    public String publicUpload(String imageUrl, String dealerCode) {
        String dealerImageUrl = svcdDealerImageUrl + imageUrl;
        String ossImageUrl = null;
        try {
            InputStream inputStream = findResultInputStream(dealerImageUrl);
            String filePath = ossConfig.getDealerImagePath() + "/" + dealerCode;
            String imagePath = imageUrl;
            if ( imageUrl.indexOf("?")>0 ){
                imagePath = imageUrl.substring(0, imageUrl.indexOf("?"));
            }
            imagePath = URLDecoder.decode(imagePath, "UTF-8");
            ossImageUrl = ossServer.publicUpload(inputStream, imagePath, ossConfig.getBucketName(), ossConfig.getSegmentationUrl(), filePath);
            log.info("上传oss地址:{}", ossImageUrl);
        } catch (Exception e) {
            log.error("获取图片上传oss异常-{}", imageUrl, e);
        }
        return ossImageUrl;
    }

    private InputStream findResultInputStream(String dealerImageUrl) throws IOException {
        OkHttpClient client = new OkHttpClient();
        Response response = client.newCall(new Request.Builder()
                .addHeader("referer", "https://audi-embedded-wap.saic-audi.mobi/")
                .url(dealerImageUrl).build()).execute();
        if (response.code() != 200) {
            log.error("网发数据同步异常，response编码: {}， responseContent: {}", response.code(), response.body().string());
            throw new IOException(String.valueOf(response.code()));
        }
        InputStream inputStream1 = response.body().byteStream();
        System.out.println(inputStream1.available());
        InputStream inputStream = new ByteArrayInputStream(response.body().bytes());
        return inputStream;
    }

}
