package com.csvw.audi.cc.entity.enumeration;

public enum OptionCategoryEnum {
    OUTCOLOR("COLOR_EXTERIEUR"), INCOLOR("COLOR_INTERIEUR"),
    WHEEL("RAD"), SEET("VOS"), SIB("SIB"),
    PACKET("PACKET"), EIH("EIH"), IN_SIB("F_SIB_COLOR_INTERIEUR");
    private final String value;
    OptionCategoryEnum(String value){
        this.value = value;
    }
    public String getValue(){
        return this.value;
    }
}
