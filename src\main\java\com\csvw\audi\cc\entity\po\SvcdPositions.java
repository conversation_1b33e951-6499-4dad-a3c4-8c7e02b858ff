package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 员工岗位列表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SvcdPositions对象", description="员工岗位列表")
public class SvcdPositions extends Model<SvcdPositions> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "数据id")
      @TableId(value = "positions_id", type = IdType.AUTO)
    private Long positionsId;

    @ApiModelProperty(value = "渠道商人员信息id")
    private Long userId;

    @ApiModelProperty(value = "岗位编号")
    private String positionCode;

    @ApiModelProperty(value = "主岗/副岗（1:主岗 2:副岗）")
    private Long jobType;

    @ApiModelProperty(value = "是否为正式岗位 1：是 0：否")
    private Integer checkStatus;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @Override
    protected Serializable pkVal() {
        return this.positionsId;
    }

}
