package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 车型
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarModel对象", description="车型")
public class CarModel extends Model<CarModel> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "车型id")
    private String modelId;

    @ApiModelProperty(value = "车型名称")
    private String modelName;

    @ApiModelProperty(value = "参数类型")
    private String modelCode;

    @ApiModelProperty(value = "参数类型名称")
    private String seriesId;

    @ApiModelProperty(value = "车型年款")
    private String modelYear;

    @ApiModelProperty(value = "车系图片")
    private String imageUrl;

    private String channel;

    @ApiModelProperty(value = "权重")
    private Integer weight;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;

    private Integer fromOmd;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
