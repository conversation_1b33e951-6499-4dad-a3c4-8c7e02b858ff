package com.csvw.audi.cc.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.csvw.audi.cc.common.utils.accb.ACCBResponse;
import com.csvw.audi.cc.common.utils.accb.Client;
import com.csvw.audi.cc.common.utils.accb.Request;
import com.csvw.audi.cc.config.AppConfig;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class AccbRequestUtil {
    @Autowired
    private AppConfig appConfig;

    public <T> ACCBResponse<T> get(String path){
        Request request = new Request();
        try {
            request.setKey(appConfig.getAccb().getKey());
            request.setSecret(appConfig.getAccb().getSecret());
            request.setMethod("GET");
            request.setUrl(appConfig.getAccb().getUrl() + path);
            request.addHeader("HOST", appConfig.getAccb().getHost());
        } catch (Exception e) {
            log.error("accb 参数异常", e);
        }
        try {
            okhttp3.Request signedRequest = Client.signOkhttp(request);
            OkHttpClient client = new OkHttpClient.Builder().build();
            Response response = client.newCall(signedRequest).execute();
            ResponseBody resEntity = response.body();
            String entityString = resEntity.string();
            ACCBResponse<T> accbResponse = JSON.parseObject(entityString,new TypeReference<ACCBResponse<T>>(){});
            if (StringUtils.isNotBlank(accbResponse.getErrorCode())&&
                    accbResponse.getStatus() != null &&
                    accbResponse.getStatus() != 200
            ){
                log.error("accb 接口访问业务异常，异常结果：" + accbResponse.toString());
                return null;
            }
            return accbResponse;
        } catch (Exception e) {
            log.error("accb 请求异常", e);
        }
        return null;
    }

    public <T> ACCBResponse<T> post(String path, String body){
        Request request = new Request();
        try {
            request.setKey(appConfig.getAccb().getKey());
            request.setSecret(appConfig.getAccb().getSecret());
            request.setMethod("POST");
            request.setUrl(appConfig.getAccb().getUrl() + path); // "/models/lang/zh/default.json"
            request.addHeader("Content-Type", "application/json");
            request.setBody(body);
            request.addHeader("HOST", appConfig.getAccb().getHost());
        } catch (Exception e) {
            log.error("accb 参数异常", e);
        }
        okhttp3.Request signedRequest = null;
        try {
            signedRequest = Client.signOkhttp(request);
            OkHttpClient client = new OkHttpClient.Builder().readTimeout(1, TimeUnit.MINUTES).build();
            Response response = client.newCall(signedRequest).execute();
            ResponseBody resEntity = response.body();
            String entityString = resEntity.string();
            ACCBResponse<T> accbResponse = JSON.parseObject(entityString,new TypeReference<ACCBResponse<T>>(){});
            if (StringUtils.isNotBlank(accbResponse.getErrorCode())&&
                    accbResponse.getStatus() != null &&
                    accbResponse.getStatus() != 200){
                log.error("accb 接口访问业务异常，异常结果：" + accbResponse.toString());
                return null;
            }
            return accbResponse;
        } catch (Exception e) {
            log.error("accb 请求异常, request: "+signedRequest.toString(), e);
        }
        return null;
    }

}
