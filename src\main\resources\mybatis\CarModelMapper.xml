<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarModelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarModel">
        <id column="id" property="id" />
        <result column="model_id" property="modelId" />
        <result column="model_name" property="modelName" />
        <result column="model_code" property="modelCode" />
        <result column="series_id" property="seriesId" />
        <result column="model_year" property="modelYear" />
        <result column="image_url" property="imageUrl" />
        <result column="channel" property="channel" />
        <result column="weight" property="weight" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, model_id, model_name, model_code, series_id, model_year, image_url, channel, weight, create_time, update_time, del_flag
    </sql>

</mapper>
