package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.common.utils.ChannelDataUtils;
import com.csvw.audi.cc.entity.dto.CustomSeriesDto;
import com.csvw.audi.cc.entity.dto.CustomSeriesParam;
import com.csvw.audi.cc.entity.dto.SeriesParamDto;
import com.csvw.audi.cc.entity.po.CarCustomSeries;
import com.csvw.audi.cc.entity.vo.CustomSeriesVo;
import com.csvw.audi.cc.entity.vo.SeriesOptionVo;
import com.csvw.audi.cc.mapper.CarCustomSeriesMapper;
import com.csvw.audi.cc.mapper.CarOptionMapper;
import com.csvw.audi.cc.service.ICarCustomSeriesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csvw.audi.cc.service.ICarOptionService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 自定义车系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Service
public class CarCustomSeriesServiceImpl extends ServiceImpl<CarCustomSeriesMapper, CarCustomSeries> implements ICarCustomSeriesService {

    @Autowired
    private CarCustomSeriesMapper seriesMapper;

    @Autowired
    private ICarOptionService carOptionService;

    @Override
    public List<CarCustomSeries> listCustomSeries(String channel, CustomSeriesParam seriesParam) throws Exception {
        CarCustomSeries carCustomSeries = new CarCustomSeries();
        if (seriesParam != null){
            BeanUtils.copyProperties(seriesParam, carCustomSeries);
        }
        carCustomSeries.setChannel(channel);
        carCustomSeries.setDelFlag(0);
        List<CarCustomSeries> series = seriesMapper.listCustomSeries(carCustomSeries);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            series = ChannelDataUtils.channelData(series, CarCustomSeries.class, channel, "customSeriesId", true);
            return series;
        }
        return series;
    }

    @Override
    public CustomSeriesDto getCustomSeriesDto(String channel, String customSeriesId) {
        return seriesMapper.getCustomSeriesDto(channel, customSeriesId);
    }

    @Override
    public List<CustomSeriesVo> listCustomSeriesVo(SeriesParamDto paramDto) throws Exception {
        List<CustomSeriesVo> series = seriesMapper.listCustomSeriesVo(paramDto);
        if (!Constant.MASTER_CHANNEL.equals(paramDto.getChannel())) {
            series = ChannelDataUtils.channelData(series, CustomSeriesVo.class, paramDto.getChannel(), "customSeriesId", true);
            return series;
        }
        return series;
    }

    @Override
    public List<SeriesOptionVo> seriesOptions(String channel, String customSeriesId) {
        return carOptionService.seriesOptions(channel, customSeriesId);
    }
}
