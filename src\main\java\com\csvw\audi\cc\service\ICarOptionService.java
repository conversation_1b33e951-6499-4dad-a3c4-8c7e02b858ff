package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.po.CarOption;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.entity.vo.SeriesOptionVo;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 车系配置项 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface ICarOptionService extends IService<CarOption> {

    List<ModelLineOptionVo> optionQueryByOptionCodes(String channel, String customSeriesId, String modelLineId, List<String> optionCodes) throws NoSuchFieldException, IllegalAccessException;

    List<CarOption> listPacketSpecialItem(Set<String> optionIds, String modelLineId);

    List<SeriesOptionVo> seriesOptions(String channel, String customSeriesId);
}
