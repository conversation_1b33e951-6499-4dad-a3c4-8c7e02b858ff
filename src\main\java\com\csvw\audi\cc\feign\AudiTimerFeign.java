package com.csvw.audi.cc.feign;

import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@FeignClient(name = "audi-timer")
public interface AudiTimerFeign {

    @PostMapping("/private/quartzJob/addSyncOmdUpdatePrice")
    JSONObject addSyncOmdUpdatePrice(@RequestBody Map<String, String> map);

    @PostMapping("/private/quartzJob/removeSyncOmdUpdatePrice")
    JSONObject removeSyncOmdUpdatePrice(@RequestBody Map<String, String> map);

    @PostMapping("/private/quartzJob/addSyncOmdRecommendCar")
    JSONObject addSyncOmdUpdateRecommendCar(@RequestBody Map<String, String> map);

    @PostMapping("/private/quartzJob/removeSyncOmdRecommendCar")
    JSONObject removeSyncOmdUpdateRecommendCar(@RequestBody Map<String, String> map);

    @PostMapping("/private/quartzJob/addSyncOmdRecommendCarStock")
    JSONObject addSyncOmdUpdateRecommendCarStock(@RequestBody Map<String, String> map);

    @PostMapping("/private/quartzJob/removeSyncOmdRecommendCarStock")
    JSONObject removeSyncOmdUpdateRecommendCarStock(@RequestBody Map<String, String> map);

    @PostMapping("/private/quartzJob/addSyncOmdMeasureMadeStock")
    JSONObject addSyncOmdMeasureMadeStock(@RequestBody Map<String, String> map);

    @PostMapping("/private/quartzJob/removeSyncOmdMeasureMadeStock")
    JSONObject removeSyncOmdMeasureMadeStock(@RequestBody Map<String, String> map);

    @PostMapping("/private/quartzJob/addHistorySnapshot")
    JSONObject addHistorySnapshot(@RequestBody Map<String, String> map);

    @PostMapping("/private/quartzJob/removeHistorySnapshot")
    JSONObject removeHistorySnapshot(@RequestBody Map<String, String> map);
}
