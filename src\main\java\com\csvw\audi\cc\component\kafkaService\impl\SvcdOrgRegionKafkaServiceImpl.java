package com.csvw.audi.cc.component.kafkaService.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.csvw.audi.cc.component.kafkaService.SvcdKafkaService;
import com.csvw.audi.cc.entity.po.SvcdOrgRegion;
import com.csvw.audi.cc.mapper.SvcdOrgRegionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service("svcdOrgRegionKafkaService")
public class SvcdOrgRegionKafkaServiceImpl implements SvcdKafkaService {

    @Autowired
    private SvcdOrgRegionMapper svcdOrgRegionMapper;

    @Override
    public void insertData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdOrgRegion svcdOrgRegion = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdOrgRegion.class);
        svcdOrgRegion.setCreatedAt(nowDate);
        svcdOrgRegion.setUpdatedAt(nowDate);
        svcdOrgRegion.insert();
    }

    @Override
    public void updateData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdOrgRegion svcdOrgRegion = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdOrgRegion.class);
        String regionCode = svcdOrgRegion.getRegionCode();

        QueryWrapper<SvcdOrgRegion> regionQusery = new QueryWrapper<>();
        regionQusery.eq("region_code",regionCode);
        List<SvcdOrgRegion> list = svcdOrgRegion.selectList(regionQusery);
        if(list == null || list.size() == 0) {
            svcdOrgRegion.setCreatedAt(nowDate);
            svcdOrgRegion.setUpdatedAt(nowDate);
            svcdOrgRegion.insert();
        } else {
            svcdOrgRegion.setUpdatedAt(nowDate);

            UpdateWrapper<SvcdOrgRegion> regionUpdateWrapper = new UpdateWrapper<>();
            regionUpdateWrapper.eq("region_code",regionCode);
            svcdOrgRegion.setRegionCode(null);//主键置空，防止数据库更新报错
            svcdOrgRegionMapper.update(svcdOrgRegion,regionUpdateWrapper);
        }
    }
}
