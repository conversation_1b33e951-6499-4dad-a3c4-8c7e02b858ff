package com.csvw.audi.cc.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

// 价格查询 品牌，accb配置线，车型代码，车型年，车型版本，价格类型，optionCode
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PriceCondition {
    private String brand="A";
    private String accbTypeCode;
    private String modelCode;
    private String modelYear;
    private String modelVersion;
    private String priceType="130";
    private String optionCode;

    public PriceCondition(String accbTypeCode, String modelCode, String modelYear, String modelVersion){
        this.accbTypeCode = accbTypeCode;
        this.modelCode = modelCode;
        this.modelYear = modelYear;
        this.modelVersion = modelVersion;
    }

    @Override
    public PriceCondition clone(){
        return new PriceCondition(this.brand, this.accbTypeCode, this.modelCode,
                this.modelYear, this.modelVersion, this.priceType, this.optionCode);
    }
}
