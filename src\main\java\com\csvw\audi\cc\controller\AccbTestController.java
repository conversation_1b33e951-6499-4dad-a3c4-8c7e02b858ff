package com.csvw.audi.cc.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.common.utils.AccbRequestUtil;
import com.csvw.audi.cc.common.utils.accb.ACCBResponse;
import com.csvw.audi.cc.entity.dto.AudiCodeParam;
import com.csvw.audi.cc.entity.dto.accb.*;
import com.csvw.audi.cc.entity.enumeration.OptionTypeEnum;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.service.ICarModelLineTypeService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/test/accb")
@Api(tags = "accb")
@Slf4j
public class AccbTestController extends BaseController {

    @Autowired
    AccbRequestUtil requestUtil;

    @Autowired
    RedissonClient redissonClient;

    @Autowired
    ICarModelLineTypeService modelLineTypeService;

    @GetMapping("/models")
    public AjaxMessage<Object> models(String language, String product){
        String models = "/v1/models/lang/{0}/{1}.json";
        String modelsApi = MessageFormat.format(models, language, product);
        ACCBResponse<JSONObject> response = requestUtil.get(modelsApi);
        log.info(response.toString());
        List<ACCBCarModels> data = response.getData().getJSONArray("data").toJavaList(ACCBCarModels.class);
        data.forEach(i->System.out.println(i.toString()));
        return new AjaxMessage<>("00", "成功", response);
    }

    @GetMapping("/types")
    public AjaxMessage<Object> types(String language, String product, String modelId, String modelYear, String version){
        String models = "/v1/type/model/{0}/lang/{1}/{2}.json";
        String modelsApi = MessageFormat.format(models, modelId, language, product);
        ACCBResponse<JSONArray> response = requestUtil.get(modelsApi);
//        log.info(JSONObject.toJSONString(response));
        List<TypeDto.Type> types = new ArrayList<>();
        List<String> typeStr = new ArrayList<>();
        List<TypeDto> data = response.getData().toJavaList(TypeDto.class);
        data.forEach(i->{
            System.out.println(i.toString());
            Optional.ofNullable(i.getTypes()).ifPresent(t-> types.addAll(
                t.stream().filter(type -> {
                    if (modelYear != null && version!= null){
                        return type.getModelyear().equals(modelYear) && type.getVersion().equals(version);
                    }
                    if (modelYear != null){
                        return type.getModelyear().equals(modelYear);
                    }
                    if (version!= null){
                        return type.getVersion().equals(version);
                    }
                    return true;
                }).sorted(Comparator.comparing(TypeDto.Type::getCode)).sorted(Comparator.comparing(TypeDto.Type::getModelyear)).collect(Collectors.toList())
            ));
        });
        typeStr = types.stream().map(t->t.getCode() + "\t" + t.getId() + "\t" + t.getModelyear() + "\t" + t.getVersion() + "\t" + t.getHeadline() + "\n").collect(Collectors.toList());
        return new AjaxMessage<>("00", "成功", typeStr);
    }

    @GetMapping("/options")
    public AjaxMessage<ACCBResponse> options(String language, String product, String typeId){
        String options = "/v1/model-option/type/{0}/lang/{1}/{2}.json";
        String optionsApi = MessageFormat.format(options, typeId, language, product);
        ACCBResponse<JSONArray> response = requestUtil.get(optionsApi);
//        log.info(JSONObject.toJSONString(response));
        List<OptionDto> data = response.getData().toJavaList(OptionDto.class);
        data.forEach(i->{
            System.out.println(i.toString());
        });
        return new AjaxMessage<>("00", "成功", response);
    }

    @GetMapping("/internal-options")
    public AjaxMessage<ACCBResponse> internalOptions(String language, String product, String modelId, String typeId){
        String inOptions = "/v1/interior-option/model/{0}/type/{1}/lang/{2}/{3}.json";
        String inOptionsApi = MessageFormat.format(inOptions, modelId, typeId, language, product);
        ACCBResponse<JSONArray> response = requestUtil.get(inOptionsApi);
//        log.info(JSONObject.toJSONString(response));
        List<InteriorOptionDto> data = response.getData().toJavaList(InteriorOptionDto.class);
        data.forEach(i->{
            System.out.println(i.toString());
        });
        return new AjaxMessage<>("00", "成功", response);
    }

    @GetMapping("/preview")
    public AjaxMessage<ACCBResponse> preview(String typeCode, String modelYear, String exteriorCode, String interiorCode){
        String inOptions = "/v1/render/typeCode/{0}/modelYear/{1}/modelVersion/3/exterior/{2}/interior/{3}.json";
        String inOptionsApi = MessageFormat.format(inOptions, typeCode, modelYear, exteriorCode, interiorCode);
        ACCBResponse<JSONArray> response = requestUtil.get(inOptionsApi);
        log.info(JSONObject.toJSONString(response));
        List<ConfigPreviewDto> data = response.getData().toJavaList(ConfigPreviewDto.class);
        data.forEach(i->{
            System.out.println(i.toString());
        });
        return new AjaxMessage<>("00", "成功", response);
    }

    @PostMapping("/equipments")
    public AjaxMessage<ACCBResponse> equipments(String language, String product, @RequestBody EquipmentParam equipmentParam){
        String inOptions = "/v1/order/lang/{0}/{1}.json";
        String inOptionsApi = MessageFormat.format(inOptions, language, product);
        ACCBResponse<JSONObject> response = requestUtil.post(inOptionsApi, JSONObject.toJSONString(equipmentParam));
        log.info(JSONObject.toJSONString(response));
        List<EquipmentDto> data = response.getData().getJSONArray("standard_equipment").toJavaList(EquipmentDto.class);
        data.forEach(i->{
            System.out.println(i.toString());
        });
        return new AjaxMessage<>("00", "成功", response);
    }

    @PostMapping("/refreshOption")
    public AjaxMessage<ACCBResponse> refreshOption(String language, String product, @RequestBody RefreshOptionParam optionParam){
        String inOptions = "/v1/refresh-option/lang/{0}/{1}.json";
        String inOptionsApi = MessageFormat.format(inOptions, language, product);
        ACCBResponse<JSONArray> response = requestUtil.post(inOptionsApi, JSONObject.toJSONString(optionParam));
        log.info(JSONObject.toJSONString(response));
        List<OptionDto> data = response.getData().toJavaList(OptionDto.class);
        data.forEach(i->{
            System.out.println(i.toString());
        });
        return new AjaxMessage<>("00", "成功", response);
    }

    @PostMapping("/genAudiCode")
    public AjaxMessage<ACCBResponse> gen(@RequestBody AudiConfigDto configDto){
//        String genApi = "/v1/audi-code/generate";
        String genApi = "/v1/audi-code/generate/lang/en/CHINA_SAIC.json";
        ACCBResponse<JSONObject> response = requestUtil.post(genApi, JSONObject.toJSONString(configDto));
        log.info(JSONObject.toJSONString(response));

        return new AjaxMessage<>("00", "成功", response);
    }

    @GetMapping("/audiConfig")
    public AjaxMessage<ACCBResponse> audiConfig(String audiCode){
//        String genApi = "/v1/audi-code/";
        String genApi = "/v1/audi-code/{0}/lang/en/CHINA_SAIC.json";
        genApi = MessageFormat.format(genApi, audiCode);
        ACCBResponse<JSONObject> response = requestUtil.get(genApi);
        log.info(JSONObject.toJSONString(response));
        return new AjaxMessage<>("00", "成功", response);
    }

    @PostMapping("genAudiCodeFromCCPRO")
    public AjaxMessage<JSONObject> genAudiCodeFromCCPRO(@RequestBody AudiCodeParam codeParam){
        String genApi = "/v1/audi-code/generate/lang/en/CHINA_SAIC.json";
        AudiConfigDto configDto = new AudiConfigDto();
        List<OptionBriefDto> options = new ArrayList<>();
        configDto.setOptions(options);
        LambdaQueryWrapper<CarModelLine> mQ = new LambdaQueryWrapper<>();
        mQ.eq(CarModelLine::getModelLineCode, codeParam.getModelLineCode())
                .eq(CarModelLine::getVersion, codeParam.getVersion()).eq(CarModelLine::getChannel, Constant.MASTER_CHANNEL);
        CarModelLine modelLine = new CarModelLine();
        modelLine = modelLine.selectOne(mQ);

        configDto.setTypeCode(modelLine.getAccbTypeCode());
        configDto.setTypeId(modelLine.getAccbTypeId());
        configDto.setTypeDesc(modelLine.getModelLineName());
        configDto.setHeadline(modelLine.getModelLineName());
        configDto.setModelVersion(modelLine.getVersion());
        LambdaQueryWrapper<CarModel> modelQ = new LambdaQueryWrapper<>();
        modelQ.eq(CarModel::getModelId, modelLine.getModelId()).eq(CarModel::getChannel, Constant.MASTER_CHANNEL);
        CarModel model= new CarModel();
        model = model.selectOne(modelQ);
        configDto.setModelYear(model.getModelYear());

        LambdaQueryWrapper<CarCustomSeries> ccsQ = new LambdaQueryWrapper<>();
        ccsQ.eq(CarCustomSeries::getChannel, Constant.MASTER_CHANNEL)
                .eq(CarCustomSeries::getCustomSeriesId, modelLine.getCustomSeriesId());
        CarCustomSeries customSeries = new CarCustomSeries();
        customSeries = customSeries.selectOne(ccsQ);

        LambdaQueryWrapper<CarSeries> sQ = new LambdaQueryWrapper<>();
        sQ.eq(CarSeries::getSeriesId, customSeries.getSeriesId()).eq(CarSeries::getChannel, Constant.MASTER_CHANNEL);
        CarSeries cs = new CarSeries();
        cs = cs.selectOne(sQ);

        configDto.setModelCode(cs.getAccbModelCode());
        configDto.setModelId(cs.getAccbModelId());


        LambdaQueryWrapper<CarOption> oQ = new LambdaQueryWrapper<>();
        oQ.eq(CarOption::getOptionCode, codeParam.getExCode()).eq(CarOption::getChannel, Constant.MASTER_CHANNEL)
                .eq(CarOption::getCustomSeriesId, modelLine.getCustomSeriesId()).ne(CarOption::getOptionType, OptionTypeEnum.PACKETITEM.getValue());
        CarOption carOption = new CarOption();
        List<CarOption> carOptions = new ArrayList<>();
        carOptions = carOption.selectList(oQ);
        if (carOptions.size() == 1){
            OptionBriefDto briefDto = new OptionBriefDto();
            CarOption o = carOptions.get(0);
            briefDto.setCategory(o.getCategory());
            briefDto.setCode(o.getCategory() + ":" + o.getOptionCode());
            briefDto.setDescription(o.getOptionName());
            options.add(briefDto);
        }else {
            for (CarOption o : carOptions){
                LambdaQueryWrapper<CarModelLineOption> mloQ = new LambdaQueryWrapper<>();
                mloQ.eq(CarModelLineOption::getOptionId, o.getOptionId())
                        .eq(CarModelLineOption::getModelLineId, modelLine.getModelLineId())
                        .eq(CarModelLineOption::getStatus, 2);
                CarModelLineOption mlo = new CarModelLineOption();
                if(CollectionUtils.isNotEmpty(mlo.selectList(mloQ))){
                    OptionBriefDto briefDto = new OptionBriefDto();
                    briefDto.setCategory(o.getCategory());
                    briefDto.setCode(o.getCategory() + ":" + o.getOptionCode());
                    briefDto.setDescription(o.getOptionName());
                    options.add(briefDto);
                    break;
                }
            }
        }

        oQ = new LambdaQueryWrapper<>();
        oQ.eq(CarOption::getOptionCode, codeParam.getInCode()).eq(CarOption::getChannel, Constant.MASTER_CHANNEL).eq(CarOption::getCustomSeriesId, modelLine.getCustomSeriesId());
        carOptions = carOption.selectList(oQ);
        if (carOptions.size() == 1){
            OptionBriefDto briefDto = new OptionBriefDto();
            CarOption o = carOptions.get(0);
            briefDto.setCategory(o.getCategory());
            briefDto.setCode(o.getCategory() + ":" + o.getOptionCode());
            briefDto.setDescription(o.getOptionName());
            options.add(briefDto);
        }else {
            for (CarOption o : carOptions){
                LambdaQueryWrapper<CarModelLineOption> mloQ = new LambdaQueryWrapper<>();
                mloQ.eq(CarModelLineOption::getOptionId, o.getOptionId())
                        .eq(CarModelLineOption::getModelLineId, modelLine.getModelLineId())
                        .eq(CarModelLineOption::getStatus, 2);
                CarModelLineOption mlo = new CarModelLineOption();
                if(CollectionUtils.isNotEmpty(mlo.selectList(mloQ))){
                    OptionBriefDto briefDto = new OptionBriefDto();
                    briefDto.setCategory(o.getCategory());
                    briefDto.setCode(o.getCategory() + ":" + o.getOptionCode());
                    briefDto.setDescription(o.getOptionName());
                    options.add(briefDto);
                    break;
                }
            }
        }

        oQ = new LambdaQueryWrapper<>();
        oQ.eq(CarOption::getOptionCode, codeParam.getRadCode()).eq(CarOption::getChannel, Constant.MASTER_CHANNEL).eq(CarOption::getCustomSeriesId, modelLine.getCustomSeriesId())
                .ne(CarOption::getOptionType, OptionTypeEnum.PACKETITEM.getValue());
        carOptions = carOption.selectList(oQ);
        if (carOptions.size() == 1){
            OptionBriefDto briefDto = new OptionBriefDto();
            CarOption o = carOptions.get(0);
            briefDto.setCategory(o.getCategory());
            briefDto.setCode(o.getCategory() + ":" + o.getOptionCode());
            briefDto.setDescription(o.getOptionName());
            options.add(briefDto);
        }else {

            for (CarOption o : carOptions){
                LambdaQueryWrapper<CarModelLineOption> mloQ = new LambdaQueryWrapper<>();
                mloQ.eq(CarModelLineOption::getOptionId, o.getOptionId())
                        .eq(CarModelLineOption::getModelLineId, modelLine.getModelLineId())
                        .eq(CarModelLineOption::getStatus, 2);
                CarModelLineOption mlo = new CarModelLineOption();
                if(CollectionUtils.isNotEmpty(mlo.selectList(mloQ))){
                    OptionBriefDto briefDto = new OptionBriefDto();
                    briefDto.setCategory(o.getCategory());
                    briefDto.setCode(o.getCategory() + ":" + o.getOptionCode());
                    briefDto.setDescription(o.getOptionName());
                    options.add(briefDto);
                    break;
                }
            }
        }

        oQ = new LambdaQueryWrapper<>();
        oQ.eq(CarOption::getOptionCode, codeParam.getSibCode()).eq(CarOption::getChannel, Constant.MASTER_CHANNEL).eq(CarOption::getCustomSeriesId, modelLine.getCustomSeriesId())
                .ne(CarOption::getOptionType, OptionTypeEnum.PACKETITEM.getValue());
        carOptions = carOption.selectList(oQ);
        if (carOptions.size() == 1){
            OptionBriefDto briefDto = new OptionBriefDto();
            CarOption o = carOptions.get(0);
            briefDto.setCategory(o.getCategory());
            briefDto.setCode(o.getCategory() + ":" + o.getOptionCode());
            briefDto.setDescription(o.getOptionName());
            options.add(briefDto);
        }else {

            for (CarOption o : carOptions){
                LambdaQueryWrapper<CarModelLineOption> mloQ = new LambdaQueryWrapper<>();
                mloQ.eq(CarModelLineOption::getOptionId, o.getOptionId())
                        .eq(CarModelLineOption::getModelLineId, modelLine.getModelLineId())
                        .eq(CarModelLineOption::getStatus, 2);
                CarModelLineOption mlo = new CarModelLineOption();
                if(CollectionUtils.isNotEmpty(mlo.selectList(mloQ))){
                    OptionBriefDto briefDto = new OptionBriefDto();
                    briefDto.setCategory(o.getCategory());
                    briefDto.setCode(o.getCategory() + ":" + o.getOptionCode());
                    briefDto.setDescription(o.getOptionName());
                    options.add(briefDto);
                    break;
                }
            }
        }

        oQ = new LambdaQueryWrapper<>();
        oQ.eq(CarOption::getOptionCode, codeParam.getVosCode()).eq(CarOption::getChannel, Constant.MASTER_CHANNEL).eq(CarOption::getCustomSeriesId, modelLine.getCustomSeriesId())
                .ne(CarOption::getOptionType, OptionTypeEnum.PACKETITEM.getValue());
        carOptions = carOption.selectList(oQ);
        if (carOptions.size() == 1){
            OptionBriefDto briefDto = new OptionBriefDto();
            CarOption o = carOptions.get(0);
            briefDto.setCategory(o.getCategory());
            briefDto.setCode(o.getCategory() + ":" + o.getOptionCode());
            briefDto.setDescription(o.getOptionName());
            options.add(briefDto);
        }else {

            for (CarOption o : carOptions){
                LambdaQueryWrapper<CarModelLineOption> mloQ = new LambdaQueryWrapper<>();
                mloQ.eq(CarModelLineOption::getOptionId, o.getOptionId())
                        .eq(CarModelLineOption::getModelLineId, modelLine.getModelLineId())
                        .eq(CarModelLineOption::getStatus, 2);
                CarModelLineOption mlo = new CarModelLineOption();
                if(CollectionUtils.isNotEmpty(mlo.selectList(mloQ))){
                    OptionBriefDto briefDto = new OptionBriefDto();
                    briefDto.setCategory(o.getCategory());
                    briefDto.setCode(o.getCategory() + ":" + o.getOptionCode());
                    briefDto.setDescription(o.getOptionName());
                    options.add(briefDto);
                    break;
                }
            }
        }

        oQ = new LambdaQueryWrapper<>();
        oQ.eq(CarOption::getOptionCode, codeParam.getEihCode()).eq(CarOption::getChannel, Constant.MASTER_CHANNEL).eq(CarOption::getCustomSeriesId, modelLine.getCustomSeriesId())
                .ne(CarOption::getOptionType, OptionTypeEnum.PACKETITEM.getValue());
        carOptions = carOption.selectList(oQ);
        if (carOptions.size() == 1){
            OptionBriefDto briefDto = new OptionBriefDto();
            CarOption o = carOptions.get(0);
            briefDto.setCategory(o.getCategory());
            briefDto.setCode(o.getCategory() + ":" + o.getOptionCode());
            briefDto.setDescription(o.getOptionName());
            options.add(briefDto);
        }else {

            for (CarOption o : carOptions){
                LambdaQueryWrapper<CarModelLineOption> mloQ = new LambdaQueryWrapper<>();
                mloQ.eq(CarModelLineOption::getOptionId, o.getOptionId())
                        .eq(CarModelLineOption::getModelLineId, modelLine.getModelLineId())
                        .eq(CarModelLineOption::getStatus, 2);
                CarModelLineOption mlo = new CarModelLineOption();
                if(CollectionUtils.isNotEmpty(mlo.selectList(mloQ))){
                    OptionBriefDto briefDto = new OptionBriefDto();
                    briefDto.setCategory(o.getCategory());
                    briefDto.setCode(o.getCategory() + ":" + o.getOptionCode());
                    briefDto.setDescription(o.getOptionName());
                    options.add(briefDto);
                    break;
                }
            }
        }


        ACCBResponse<JSONObject> response = requestUtil.post(genApi, JSONObject.toJSONString(configDto));
        log.info(JSONObject.toJSONString(response));
        JSONObject res = new JSONObject();
        res.put("code", response.getData());
        res.put("modelLineName", modelLine.getModelLineName());
        res.put("modelLineCode", modelLine.getModelLineCode());
        res.put("modelLineId", modelLine.getModelLineId());
        return new AjaxMessage<>("00", "成功", res);
    }

}

