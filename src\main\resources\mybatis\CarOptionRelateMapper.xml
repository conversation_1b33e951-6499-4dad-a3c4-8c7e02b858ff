<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarOptionRelateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarOptionRelate">
        <id column="id" property="id" />
        <result column="custom_series_id" property="customSeriesId"/>
        <result column="model_line_id" property="modelLineId" />
        <result column="option_id" property="optionId" />
        <result column="option_code" property="optionCode" />
        <result column="option_category" property="optionCategory" />
        <result column="option_relate_id" property="optionRelateId" />
        <result column="option_relate_code" property="optionRelateCode" />
        <result column="option_relate_category" property="optionRelateCategory" />
        <result column="relate_type" property="relateType" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, model_line_id, option_id, option_code, option_category, option_relate_id, option_relate_code, option_relate_category, relate_type, del_flag
    </sql>

    <select id="listOptionRelDto" parameterType="com.csvw.audi.cc.entity.dto.OptionRelateParam" resultType="com.csvw.audi.cc.entity.dto.OptionRelDto">
        select option_id, option_code, option_category, option_relate_id, option_relate_code, option_relate_category, relate_type, default_depend,
            case relate_type
            when 'depend' then
                ifnull(relate_type_group, option_relate_category)
            else relate_type_group
            end relate_type_group
        from car_option_relate
        <where>
            <if test="customSeriesId != null and customSeriesId != ''">
                custom_series_id = #{customSeriesId}
            </if>
            <if test="modelLineId != null and modelLineId != ''">
                model_line_id = #{modelLineId}
            </if>
            <if test="optionId != null and optionId != ''">
                and option_id = #{optionId}
            </if>
            <if test="optionRelateCodes != null">
                and option_relate_code in
                <foreach collection="optionRelateCodes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="relateTypes != null">
                and relate_type in
                <foreach collection="relateTypes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="notInRelateCategory != null">
                and option_relate_category not in
                <foreach collection="notInRelateCategory" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="listCombines" resultType="com.csvw.audi.cc.entity.dto.OptionRelDto">
        select distinct option_relate_code, option_relate_category
        from car_option o left join car_option_relate rel on o.option_id = rel.option_id
        where o.option_code = #{optionCode} and o.`channel`='master' and rel.relate_type="combine" and rel.del_flag=0
        and o.custom_series_id = (
        select distinct(custom_series_id)
        from car_model_line
        where model_line_id = #{modelLineId} and channel='master')
    </select>

    <select id="listConflictOptionRelate" resultType="com.csvw.audi.cc.entity.po.CarOptionRelate">
        SELECT * FROM `car_option_relate` WHERE `relate_type` = 'conflict' and `model_line_id` = #{modelLineId} and  `option_id` in
        <foreach collection="optionIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and `option_relate_id` in
        <foreach collection="optionIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="listOptionConflict" resultType="com.csvw.audi.cc.entity.po.CarOptionRelate">
        SELECT * FROM `car_option_relate` WHERE `relate_type` = 'conflict' and `model_line_id` = #{modelLineId} and `option_id` = #{optionId}
        and `option_relate_id` in
        <foreach collection="optionRelateIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="listOptionDepend" resultType="com.csvw.audi.cc.entity.po.CarOptionRelate">
        SELECT * FROM `car_option_relate` WHERE `relate_type` = 'depend' and `model_line_id` = #{modelLineId} and `option_id` = #{optionId}
        and `option_relate_id` not in
        <foreach collection="optionRelateIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="listDependCategory" resultType="String">
        SELECT DISTINCT( `option_relate_category`)  FROM `car_option_relate` WHERE (`option_relate_category` is null or `option_relate_category` != 'F_SIB_COLOR_INTERIEUR')
        and `option_id` = #{optionId}
        and `model_line_id` = #{modelLineId}
        and `relate_type` = 'depend'
        and `relate_type_group` is null
    </select>

    <select id="listDependGroup" resultType="String">
        SELECT DISTINCT( `relate_type_group`)  FROM `car_option_relate` WHERE (`option_relate_category` is null or `option_relate_category` != 'F_SIB_COLOR_INTERIEUR')
        and `option_id` = #{optionId}
        and `model_line_id` = #{modelLineId}
        and `relate_type` = 'depend'
        and `relate_type_group` is not null
    </select>

    <select id="listDependsForValid" resultType="com.csvw.audi.cc.entity.po.CarOptionRelate">
        SELECT * FROM `car_option_relate` WHERE `model_line_id` = #{modelLineId}
        and `option_id` = #{optionId}
        and `option_relate_id` in
        <foreach collection="optionIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="category == null or category == ''">
            and (`option_relate_category` is null or `option_relate_category` = '')
        </if>
        <if test="category != null and category != ''">
            and `option_relate_category` = #{category}
        </if>
        and `relate_type` = 'depend'
    </select>

    <select id="listDependsForGroupValid" resultType="com.csvw.audi.cc.entity.po.CarOptionRelate">
        SELECT * FROM `car_option_relate` WHERE `model_line_id` = #{modelLineId}
        and `option_id` = #{optionId}
        and `option_relate_id` in
        <foreach collection="optionIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and `relate_type_group` = #{group}
        and `relate_type` = 'depend'
    </select>

    <select id="listDependedByOptionId" resultType="com.csvw.audi.cc.entity.po.CarOptionRelate">
        SELECT * FROM `car_option_relate` WHERE `model_line_id` = #{modelLineId}
        and `option_relate_id` = #{optionId}
        and `option_id` in
        <foreach collection="optionIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and `relate_type` = 'depend'
    </select>

    <select id="listSibInterieurIdDependedByOptionId" resultType="com.csvw.audi.cc.entity.po.CarOptionRelate">
        SELECT * FROM `car_option_relate` WHERE `model_line_id` = #{modelLineId}
        and `option_id` = #{sibInterieurId}
        and `option_relate_id` in
        <foreach collection="optionIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and `relate_type` = 'depend'
    </select>

</mapper>
