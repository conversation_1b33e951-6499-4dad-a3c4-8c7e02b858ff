package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.entity.vo.DealerVo;
import com.csvw.audi.cc.service.ICityWideDealerSortRuleService;
import com.csvw.audi.common.model.activity.Activity4IntraCity;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.keyvalue.DefaultMapEntry;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/3/2 17:20
 * @description 同城经销商列表排序
 */
@Service
public class CityWideDealerSortRuleServiceImpl implements ICityWideDealerSortRuleService {

    /**
     * @description 综合排序方法
     */
    public List<DealerVo> sortByCityWideRule(List<DealerVo> dealerList, List<String> hopOrAudiCityDealerList,
        Map<String, List<Activity4IntraCity>> activity4IntraCityMap) {

        Collections.sort(dealerList, new Comparator<DealerVo>() {
            @Override
            public int compare(DealerVo o1, DealerVo o2) {
                // 1. 先判断 HOP or AudiCity
                int hopResult = compareByHOPOrAudiCity(hopOrAudiCityDealerList, o1, o2);
                if (hopResult != 0) {
                    return hopResult;
                }
                // 2. 如果同是 HOP or AudiCity,则进行活动的比较
                if (MapUtils.isNotEmpty(activity4IntraCityMap)) {
                    Map.Entry<String, List<Activity4IntraCity>> o1Entry =
                        new DefaultMapEntry(o1.getDealerCode(), activity4IntraCityMap.get(o1.getDealerCode()));
                    Map.Entry<String, List<Activity4IntraCity>> o2Entry =
                        new DefaultMapEntry(o2.getDealerCode(), activity4IntraCityMap.get(o2.getDealerCode()));
                    int activityResult = compareByActivity(o1Entry, o2Entry);
                    if (activityResult != 0) {
                        return activityResult;
                    }
                }
                // 3. 如果活动相同,则比较地理位置
                int distanceResult = compareByDistance(o1, o2);
                return distanceResult;
            }
        });
        return dealerList;
    }

    /**
     * @description 比较是否是HOP or AudiCity
     */
    private int compareByHOPOrAudiCity(List<String> hopOrAudiCityDealerList, DealerVo o1, DealerVo o2) {
        // HOP or AudiCity
        if (hopOrAudiCityDealerList.contains(o1.getDealerCode())
            && !hopOrAudiCityDealerList.contains(o2.getDealerCode())) {
            return -1;
        }
        if (!hopOrAudiCityDealerList.contains(o1.getDealerCode())
            && hopOrAudiCityDealerList.contains(o2.getDealerCode())) {
            return 1;
        }
        return 0;
    }

    /**
     * @description 根据活动进行排序
     */
    private int compareByActivity(Map.Entry<String, List<Activity4IntraCity>> o1,
        Map.Entry<String, List<Activity4IntraCity>> o2) {
        boolean o1Null = Objects.isNull(o1) || Objects.isNull(o1.getValue());
        boolean o2Null = Objects.isNull(o2) || Objects.isNull(o2.getValue());
        if (o1Null && o2Null) {
            return 0;
        }
        if (o1Null && !o2Null) {
            return 1;
        }
        if (!o1Null && o2Null) {
            return -1;
        }
        // 活动数量比较
        int sizeCompareResult = o2.getValue().size() - o1.getValue().size();
        if (sizeCompareResult != 0) {
            return sizeCompareResult;
        }
        // 数量相同,比较活动时间最晚的
        Activity4IntraCity o2Max = o2.getValue().stream().max((a1, a2) -> {
            int a2S = 0;
            int a1S = 0;
            if (Objects.nonNull(a2.getScheduleStart())) {
                Long localDateTimeLong2 = Timestamp.valueOf(a2.getScheduleStart()).getTime();
                a2S = localDateTimeLong2.intValue();
            }
            if (Objects.nonNull(a1.getScheduleStart())) {
                Long localDateTimeLong1 = Timestamp.valueOf(a1.getScheduleStart()).getTime();
                a1S = localDateTimeLong1.intValue();
            }
            return a1S - a2S;
        }).get();

        Activity4IntraCity o1Max = o1.getValue().stream().max((a1, a2) -> {
            int a2S = 0;
            int a1S = 0;
            if (Objects.nonNull(a2.getScheduleStart())) {
                Long localDateTimeLong2 = Timestamp.valueOf(a2.getScheduleStart()).getTime();
                a2S = localDateTimeLong2.intValue();
            }
            if (Objects.nonNull(a1.getScheduleStart())) {
                Long localDateTimeLong1 = Timestamp.valueOf(a1.getScheduleStart()).getTime();
                a1S = localDateTimeLong1.intValue();
            }
            return a1S - a2S;
        }).get();

        Long localDateTimeLongMax2 = Timestamp.valueOf(o2Max.getScheduleStart()).getTime();
        Long localDateTimeLongMax1 = Timestamp.valueOf(o1Max.getScheduleStart()).getTime();
        Long timeCompareResult = localDateTimeLongMax2 - localDateTimeLongMax1;
        return timeCompareResult.intValue();
    }

    /**
     * @description 根据距离排序
     */
    private int compareByDistance(DealerVo o1, DealerVo o2) {
        boolean o1Null = Objects.isNull(o1) || Objects.isNull(o1.getDistance());
        boolean o2Null = Objects.isNull(o2) || Objects.isNull(o2.getDistance());
        if (o1Null && o2Null) {
            return 0;
        }
        if (o1Null && !o2Null) {
            return 1;
        }
        if (!o1Null && o2Null) {
            return -1;
        }
        // 地理位置比较,近的在前
        return o1.getDistance().intValue() - o2.getDistance().intValue();
    }
}
