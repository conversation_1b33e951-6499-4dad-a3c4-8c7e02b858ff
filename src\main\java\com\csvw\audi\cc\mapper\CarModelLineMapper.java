package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.dto.ModelParamDto;
import com.csvw.audi.cc.entity.dto.OmdModelParam;
import com.csvw.audi.cc.entity.dto.PriceCondition;
import com.csvw.audi.cc.entity.dto.TypePriceParam;
import com.csvw.audi.cc.entity.po.CarModelLine;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csvw.audi.cc.entity.vo.AdminModel;
import com.csvw.audi.cc.entity.vo.ModelLineBriefVo;
import com.csvw.audi.cc.entity.vo.ModelLineVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 配置线 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface CarModelLineMapper extends BaseMapper<CarModelLine> {

    List<ModelLineVo> listModelLine(ModelParamDto modelParamDto);

    PriceCondition listPriceCondition(@Param("modelLineId") String modelLineId);

    PriceCondition listAccbPriceCondition(TypePriceParam typePriceParam);

    List<ModelLineBriefVo> listModelLineAstro(ModelParamDto paramDto);

    List<ModelLineVo> listModelLineByOmdData(OmdModelParam omdModelParam);

    List<AdminModel> listAdminModels(@Param("customSeriesId") String customSeriesId);

    List<String> listBlips(@Param("modelLineId") String modelLineId);
}
