<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarOptionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarOption">
        <id column="id" property="id" />
        <result column="option_id" property="optionId" />
        <result column="option_name" property="optionName" />
        <result column="option_code" property="optionCode" />
        <result column="category" property="category" />
        <result column="option_type" property="optionType" />
        <result column="option_weight" property="optionWeight" />
        <result column="custom_series_id" property="customSeriesId" />
        <result column="image_url" property="imageUrl" />
        <result column="image_url_detail" property="imageUrlDetail" />
        <result column="image_url_list" property="imageUrlList" />
        <result column="description" property="description" />
        <result column="remark" property="remark" />
        <result column="default_config" property="defaultConfig" />
        <result column="channel" property="channel" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, option_id, option_name, option_code, category, option_type, option_weight, custom_series_id, image_url, image_url_detail, image_url_list, description, remark, default_config, channel, create_time, update_time, del_flag
    </sql>

    <select id="carOptionQuery" resultType="com.csvw.audi.cc.entity.vo.ModelLineOptionVo" parameterType="com.csvw.audi.cc.entity.dto.OptionParamDto">
        select p.option_id, p.option_name, p.option_code, p.category, p.option_type, t.type_name as option_type_name, p.image_url, p.image_url_detail, p.image_url_list,
        p.description, p.remark, p.channel
        from car_option p
        left join car_type t on t.type = p.option_type and t.custom_series_id = p.custom_series_id
        <where>
            <if test="optionId != null and optionId != ''">
                p.option_id = #{optionId}
            </if>
            <if test="customSeriesId != null and customSeriesId != ''">
                and p.custom_series_id = #{customSeriesId}
            </if>
            <if test="optionIds != null">
                and p.option_id in
                <foreach collection="optionIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="optionCodes != null">
                and p.option_code in
                <foreach collection="optionCodes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="notOptionTypes != null">
                and p.option_type not in
                <foreach collection="notOptionTypes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="channel == null or channel == '' or channel == 'master'">
                and p.channel = 'master'
            </if>
            <if test="channel != null and channel != '' and channel != 'master'">
                and p.channel in (#{channel}, 'master')
            </if>
            <if test="delFlag != null">
                and p.del_flag = #{delFlag}
            </if>
        </where>
        order by p.option_weight;
    </select>

    <select id="listPacketSpecialItem" resultType="com.csvw.audi.cc.entity.po.CarOption" >
        SELECT item.* FROM `car_option` packet LEFT JOIN `car_package_item` packet_item on packet.`option_id`  = packet_item.`package_id`
        LEFT JOIN `car_option` item on packet_item.`package_item_id` = item.`option_id`
        left join `car_model_line_option` mlo on mlo.option_id = item.option_id
        WHERE packet.`category` = 'PACKET' and packet.`channel` = 'master' and item.`channel` = 'master'
        AND item.`category` in ('RAD', 'SIB', 'VOS')
        and mlo.status != 0
        and packet.`option_id` in
        <foreach collection="optionIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="modelLineId != null and modelLineId != ''">
            and model_line_id = #{modelLineId}
        </if>
    </select>

    <select id="seriesOptions" resultType="com.csvw.audi.cc.entity.vo.SeriesOptionVo">
        select option_id, option_code, option_name from car_option where channel = 'master' and custom_series_id = #{customSeriesId}
    </select>

</mapper>
