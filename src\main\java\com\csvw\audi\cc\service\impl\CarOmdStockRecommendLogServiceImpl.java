package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.entity.po.CarOmdStockRecommendLog;
import com.csvw.audi.cc.mapper.CarOmdStockRecommendLogMapper;
import com.csvw.audi.cc.mapper.CarOmdStockRecommendMapper;
import com.csvw.audi.cc.service.ICarOmdStockRecommendLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * OMD经销商库存车日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
@Service
public class CarOmdStockRecommendLogServiceImpl extends ServiceImpl<CarOmdStockRecommendLogMapper, CarOmdStockRecommendLog> implements ICarOmdStockRecommendLogService {

    @Autowired
    private CarOmdStockRecommendLogMapper mapper;

    @Override
    public List<Long> listUnhandleRecommend() {
        return mapper.listUnhandleRecommend();
    }
}
