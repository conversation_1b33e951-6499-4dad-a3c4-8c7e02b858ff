<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarMeasureMadeConfigOptionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarMeasureMadeConfigOption">
        <id column="measure_option_id" property="measureOptionId" />
        <result column="measure_id" property="measureId" />
        <result column="option_id" property="optionId" />
        <result column="option_code" property="optionCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        measure_option_id, measure_id, option_id, option_code
    </sql>

</mapper>
