package com.csvw.audi.cc.entity.vo;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 车配标签
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Data
public class CarTagVo implements Serializable {

    @ApiModelProperty(value = "标签文案")
    private String tagName;

    @ApiModelProperty(value = "标签编码")
    private String tagCode;

    @ApiModelProperty(value = "描述")
    private String tagDesc;

    @ApiModelProperty(value = "图片地址")
    private String imageUrl;

    @ApiModelProperty(value = "标签个性内容")
    private String tagValue;

}
