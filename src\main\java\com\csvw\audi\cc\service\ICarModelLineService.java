package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.dto.*;
import com.csvw.audi.cc.entity.dto.omd.EstimateDeliveryRes;
import com.csvw.audi.cc.entity.dto.omd.ModelQueryRes;
import com.csvw.audi.cc.entity.po.CarModelLine;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.vo.*;
import com.csvw.audi.cc.exception.ServiceException;
import org.springframework.cache.annotation.Cacheable;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 配置线 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface ICarModelLineService extends IService<CarModelLine> {

    List<ModelLineVo> listModelLine(ModelParamDto modelParamDto) throws Exception;

    ModelLineConfigVo modelLineConfig(String channel, String modelLineId, Integer status) throws Exception;

    List<ModelLineOptionVo> modelLineTagOption(ModelLineOptionTagParam tagParam) throws Exception;

    List<ModelLineOptionVo> modelLineOption(String channel, String modelLineId, String category) throws Exception;

    @Cacheable(cacheNames = {"cc_cache"}, keyGenerator = "springCacheKeyGenerator")
    List<ModelLineOptionVo> modelLineOptionWithOutPriceFilter(String channel, String modelLineId, String category) throws Exception;

    List<ModelLineOptionVo> modelLineOptionalEquipment(String channel, String modelLineId);

    @Cacheable(cacheNames = {"cc_cache"}, keyGenerator = "springCacheKeyGenerator")
    List<ModelLineOptionVo> modelLinePersonalOptionDefault(String modelLineId) throws Exception;

    List<ModelLineOptionVo> modelLinePersonalOption(String channel, String modelLineId, List<String> notInCategory) throws Exception;

    PriceCondition listPriceCondition(String modelLineId);

    PriceCondition listAccbPriceCondition(TypePriceParam modelLineId);

    List<ModelLineOptionVo> modelLinePacketItem(OptionParamDto optionParamDto) throws NoSuchFieldException, IllegalAccessException, Exception;

    Object computePrice(PriceComputeParam priceComputeParam) throws ServiceException;

    BigDecimal computeDiscount(PriceComputeParam priceComputeParam) throws ServiceException;

    List<ModelLineOptionVo> modelLineOptionQuery(String channel, String modelLineId, Collection<String> optionIds) throws Exception;

    List<ModelLineOptionVo> modelLineOptionQuery(OptionParamDto param) throws Exception;

    List<ModelLineOptionVo> modelLineOptionQueryByOptionCodes(String channel, String customSeriesId, String modelLineId, Collection<String> optionCodes) throws Exception;

    ModelLineConfigCompareVo modelLineConfigCompare(String modelLineId);

    List<ModelLineBriefVo> listModelLineAstro(ModelParamDto paramDto) throws NoSuchFieldException, IllegalAccessException;

    ModelLineVo getModelLineByOmdData(OmdModelParam omdModelParam) throws Exception;

    ModelLineConfigVo modelLineConfigToDrm(String channel, String modelLineId, Integer status) throws NoSuchFieldException, IllegalAccessException;

    List<ModelLineOptionVo> optionQueryByOptionIds(String channel, String customSeriesId, String modelLineId, List<String> optionIds) throws Exception;

    SeriesConfigsVo seriesConfigs(String channel, String customSeriesId) throws Exception;

    List<ModelLineOptionVo> optionQueryByOptionId(String channel, String customSeriesId, String modelLineId, String optionId, boolean priceHandle) throws Exception;

    EstimateDeliveryRes estimateQuery(EstimateDeliveryParam estimateDeliveryParam) throws Exception;

    List<ModelLineOptionVo> modelLineOptionQueryWithOutPriceFilter(OptionParamDto param) throws NoSuchFieldException, IllegalAccessException;

    Object measurePriceCompute(PriceComputeParam priceComputeParam) throws ServiceException;

    FrontOptions baseFrontOptions(ConfigValidationParam param, String channel) throws Exception;

    FrontOptions listFrontOptions(ConfigValidationParam param, String channel) throws Exception;

    EstimateDeliveryRes similarityModel(String ccid, String dealerNetCode, String channel) throws Exception;

    EstimateDeliveryRes estimateQueryByCc(String ccid, String dealerNetCode) throws Exception;

    List<ModelLineOptionVo> handleVirtualRad(ModelLineVo line, String seats, List<ModelLineOptionVo> optionVos) throws Exception;

    @Cacheable(cacheNames = {"cc_cache"}, keyGenerator = "springCacheKeyGenerator")
    BigDecimal getModelEarnestMoney(String customSeriesCode, String accbTypeCode, int depositType);

    List<AmsQueryVo> convertModelQuery(List<ModelQueryRes> models, String channel, String accbTypeCode);

    boolean measureFilter(String channel, String accbTypeCode, String modelLineId);

    List<AdminModel> listAdminModels(String customSeriesId);

    List<String> listBlips(String modelLineId);

    CcEstimateVo ccEstimate(CcEstimateDeliveryParam estimateDeliveryParam) throws ServiceException;

}
