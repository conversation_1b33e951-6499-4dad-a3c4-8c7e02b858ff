package com.csvw.audi.cc.controller.svcd;

import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.dto.DealerDto;
import com.csvw.audi.cc.entity.po.SvcdChannelOrganization;
import com.csvw.audi.cc.entity.vo.DealerVo;
import com.csvw.audi.cc.service.ISvcdChannelOrganizationService;
import com.csvw.sx.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "代理商-feign")
@RestController
@RequestMapping("/private/api/v1/dealer")
public class PrivateDealerController extends BaseController {

    @Autowired
    private ISvcdChannelOrganizationService svcdChannelOrganizationService;

    @ApiOperation("feign-获取代理商(766)列表")
    @PostMapping("/getDealerList")
    public AjaxMessage<List<DealerVo>> getAgentList(@RequestBody DealerDto dealerDto) {
        //默认返回总部
        if(dealerDto.getDefaultHeadquarters() == null) {
            dealerDto.setDefaultHeadquarters(1);
        }
        List<DealerVo> list =  svcdChannelOrganizationService.getAgentList(dealerDto);
        return new AjaxMessage<>("00", "成功", list);
    }

    @ApiOperation("feign-获取代理商(766)详情")
    @GetMapping("/getDealerDetails")
    public AjaxMessage<DealerVo> getAgentDetails(@RequestParam String dealerCode) {
        DealerDto dealerDto = new DealerDto();
        dealerDto.setDealerCode(dealerCode);
        dealerDto.setDefaultHeadquarters(1);
        List<DealerVo> list =  svcdChannelOrganizationService.getAgentList(dealerDto);
        return new AjaxMessage<>("00", "成功", list.size()==0?null:list.get(0));
    }

    @ApiOperation("feign-获取渠道商详情")
    @PostMapping("/getOrgDetails")
    public AjaxMessage<DealerVo> getOrgDetails(@RequestBody DealerDto dealerDto) {
        //默认查询全部状态
        if(StringUtil.isEmpty(dealerDto.getBusinessStatus())) {
            dealerDto.setBusinessStatus("0");
        }
        //默认查询全部门店
        if(dealerDto.getSearchType() == null) {
            dealerDto.setSearchType(0);
        }
        //不取门店图片
        if(dealerDto.getHaveImage() == null) {
            dealerDto.setHaveImage(0);
        }
        List<DealerVo> list =  svcdChannelOrganizationService.getOrgList(dealerDto);
        return new AjaxMessage<>("00", "成功", list.size()==0?null:list.get(0));
    }

    @ApiOperation("feign-获取渠道商列表（代理商+服务商）")
    @PostMapping("/getOrgList")
    public AjaxMessage<List<DealerVo>> getOrgList(@RequestBody DealerDto dealerDto) {
        return new AjaxMessage<>("00", "成功", svcdChannelOrganizationService.getOrgList(dealerDto));
    }

    @ApiOperation("feign-根据区域获取经销商")
    @GetMapping("/selectSvcdChannelOrganizationByPCode")
    public List<SvcdChannelOrganization> selectSvcdChannelOrganizationByPCode(@RequestParam("pCode") String pCode) {
        return svcdChannelOrganizationService.selectSvcdChannelOrganizationByPCode(pCode);
    }
}
