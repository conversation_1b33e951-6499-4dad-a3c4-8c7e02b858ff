package com.csvw.audi.cc.entity.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class OmdCompareExportDto {
    @Excel(name = "OneApp订单编号")
    private String orderId;
    @Excel(name = "配置线代码")
    private String modelLineCode;
    @Excel(name = "车型年")
    private String modelYear;
    @Excel(name = "车型版本")
    private String modelVersion;
    @Excel(name = "内饰")
    private String insideCode;
    @Excel(name = "颜色代码")
    private String outsideCode;
    @Excel(name = "选装列表")
    private String prCodes;
    @Excel(name = "cc配置线代码")
    private String ccModelLineCode;
    @Excel(name = "cc车型年")
    private String ccModelYear;
    @Excel(name = "cc车型版本")
    private String ccModelVersion;
    @Excel(name = "cc内饰")
    private String ccInsideCode;
    @Excel(name = "cc颜色代码")
    private String ccOutsideCode;
    @Excel(name = "cc选装列表")
    private String ccPrCodes;
    @Excel(name = "校验结果")
    private String res;
    @Excel(name = "校验结果,描述")
    private String resDesc;
    @Excel(name = "业务附加编码")
    private String omdAttachCode;
}
