<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.SvcdProvinceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.SvcdProvince">
        <id column="province_id" property="provinceId" />
        <result column="name" property="name" />
        <result column="province_code" property="provinceCode" />
        <result column="simple_name" property="simpleName" />
        <result column="quick_code" property="quickCode" />
        <result column="province_en" property="provinceEn" />
        <result column="status" property="status" />
        <result column="zm_province_code" property="zmProvinceCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        province_id, name, province_code, simple_name, quick_code, province_en, status, zm_province_code
    </sql>

</mapper>
