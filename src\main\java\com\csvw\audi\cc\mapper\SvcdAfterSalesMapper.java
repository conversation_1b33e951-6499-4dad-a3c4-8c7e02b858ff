package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.dto.SvcdAfterSalesDto;
import com.csvw.audi.cc.entity.po.SvcdAfterSales;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csvw.audi.cc.entity.vo.SvcdAfterSalesVo;

import java.util.List;

/**
 * <p>
 * 售后服务商 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
public interface SvcdAfterSalesMapper extends BaseMapper<SvcdAfterSales> {

    public List<SvcdAfterSalesVo> getAfterSalesList(SvcdAfterSalesDto afterSalesDto);

    public List<SvcdAfterSalesVo> getAgentBindingAfterSalesList();

}
