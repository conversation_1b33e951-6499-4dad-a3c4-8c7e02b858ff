package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.po.SvcdUser;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/21 14:17
 * @description svcd用户头像处理辅助类
 */
public interface ISvcdUserImageAssistant {

    void findAndStorageSvcdUserImage(List<String> userUIDs);

    void asyncFindAndStorageSvcdUserImage(List<SvcdUser> userUIDs);

    /**
     * @description 获取用户图片在oss上的路径
     */
    String getSvcdUserPicOssFullPathURI(SvcdUser svcdUser);
}
