package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.dto.OptionParamDto;
import com.csvw.audi.cc.entity.dto.OptionPriceQuery;
import com.csvw.audi.cc.entity.po.CarModelLineOption;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.vo.ModelLineOptionCompareVo;
import com.csvw.audi.cc.entity.vo.ModelLineOptionTagParam;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 配置线配置项 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface ICarModelLineOptionService extends IService<CarModelLineOption> {

    List<ModelLineOptionVo> listModelLineOption(ModelLineOptionVo modelLineOptionVo);

    List<ModelLineOptionVo> listModelLinePacketItem(String channel, String modelLineId, Integer delFlag);

    List<ModelLineOptionVo> listModelLinePersonalOption(String modelLineId, String channel, Integer delFlag, List<String> notInCategory, String type);

    List<ModelLineOptionVo> listModelLinePacketItem(OptionParamDto optionParamDto);

    List<ModelLineOptionVo> modelLineOptionQuery(OptionParamDto param);

    List<ModelLineOptionVo> modelLineOptionQueryStrict(OptionParamDto param);

    List<ModelLineOptionVo> listModelLineOptionWithoutRel(ModelLineOptionVo param);

    List<ModelLineOptionVo> listOptionPacketItemByDrm(OptionPriceQuery param);

    List<ModelLineOptionCompareVo> listModelLineOptionCompare(ModelLineOptionVo param);

    List<ModelLineOptionVo> optionQuery(OptionParamDto param);

    List<ModelLineOptionVo> listModelLinePacketItemByCode(String modelLineId, String optionCode, Set<String> optionIds);

    List<ModelLineOptionVo> listModelLineOptionByTag(ModelLineOptionTagParam param);
}
