package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.common.utils.ChannelDataUtils;
import com.csvw.audi.cc.entity.po.CarOptionDetail;
import com.csvw.audi.cc.entity.vo.CarOptionDetailVo;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.mapper.CarOptionDetailMapper;
import com.csvw.audi.cc.service.ICarOptionDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 车系配置项细节 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-16
 */
@Service
public class CarOptionDetailServiceImpl extends ServiceImpl<CarOptionDetailMapper, CarOptionDetail> implements ICarOptionDetailService {

    @Autowired
    private CarOptionDetailMapper mapper;

    @Override
    public List<CarOptionDetailVo> listOptionDetailVo(CarOptionDetailVo carOptionDetailVo) throws Exception {
        List<CarOptionDetailVo> detailVos = mapper.listOptionDetailVo(carOptionDetailVo);
        if (!Constant.MASTER_CHANNEL.equals(carOptionDetailVo.getChannel())) {
            detailVos = ChannelDataUtils.channelData(detailVos, CarOptionDetailVo.class, carOptionDetailVo.getChannel(), "optionDetailId", false);
        }
        return detailVos;
    }
}
