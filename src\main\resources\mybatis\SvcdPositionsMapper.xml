<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.SvcdPositionsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.SvcdPositions">
        <id column="positions_id" property="positionsId" />
        <result column="position_code" property="positionCode" />
        <result column="user_id" property="userId" />
        <result column="job_type" property="jobType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        positions_id, position_code, user_id, job_type
    </sql>

</mapper>
