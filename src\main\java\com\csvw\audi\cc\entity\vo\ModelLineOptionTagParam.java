package com.csvw.audi.cc.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ModelLineOptionTagParam implements Serializable {

    @ApiModelProperty(value = "配置项id")
    private String optionId;

    @ApiModelProperty(value = "配置项名称")
    private String optionName;

    @ApiModelProperty(value = "配置编码")
    private String optionCode;

    @ApiModelProperty(value = "配置种类编码")
    private String category;

    @ApiModelProperty(value = "配置项类型")
    private String optionType;

    @ApiModelProperty(value = "配置项类型")
    private String optionTypeName;

    @ApiModelProperty(value = "默认配置项")
    private Integer defaultConfig;

    @ApiModelProperty(value = "配置线id")
    private String modelLineId;

    @ApiModelProperty(value = "配置线配置项状态（0:无，1:标准装备，2:可选装备）")
    private Integer status;

    private String channel;

    private String mloChannel;

    private Integer delFlag;

    private String packageId;

    private String hasCode;

    private String tagCode;

}
