package com.csvw.audi.cc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.common.utils.CcUtils;
import com.csvw.audi.cc.common.utils.JsonString;
import com.csvw.audi.cc.elasticsearch.ContractIndex;
import com.csvw.audi.cc.elasticsearch.CustomVoIndex;
import com.csvw.audi.cc.elasticsearch.DetailIndex;
import com.csvw.audi.cc.elasticsearch.OmdDetailIndex;
import com.csvw.audi.cc.entity.dto.SnapshotUpdateDto;
import com.csvw.audi.cc.entity.dto.vwcc.CarCustomDetail;
import com.csvw.audi.cc.entity.po.CarCustom;
import com.csvw.audi.cc.entity.po.CarCustomOption;
import com.csvw.audi.cc.entity.po.CarCustomOptionSnapshot;
import com.csvw.audi.cc.entity.po.CarCustomSnapshot;
import com.csvw.audi.cc.entity.vo.CarCustomVo;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.mapper.CarCustomSnapshotMapper;
import com.csvw.audi.cc.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.client.RestHighLevelClient;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-14
 */
@Service
@Slf4j
public class CarCustomSnapshotServiceImpl extends ServiceImpl<CarCustomSnapshotMapper, CarCustomSnapshot> implements ICarCustomSnapshotService {

    @Autowired
    private ICarCustomService carCustomService;

    @Autowired
    private ICarCustomOptionService customOptionService;

    @Autowired
    private ICarCustomOptionSnapshotService optionSnapshotService;

    @Autowired
    private ICarCustomSnapshotService customSnapshotService;

    @Autowired
    private CarRecommendCustomServiceImpl recommendCustomService;

    @Autowired
    private ICarOmdPriceTypeService priceTypeService;

    @Autowired
    private RestHighLevelClient client;

    @Autowired
    private RedissonClient redissonClient;

    private static final String snapshotRowsPrefix = "saic_audi:audi_car_config:history_snapshot:rows";

    private static final String snapshotFlagPrefix = "saic_audi:audi_car_config:history_snapshot:flag";

    @Override
    @Transactional
    public Long snapshotCarCustom(CarCustom carCustom, LocalDateTime dataTime) throws Exception {
        CarCustomDetail customDetail;
        carCustom = carCustomService.getById(carCustom.getCcid());
        String modelLineId = carCustom.getModelLineId();

        CarCustomSnapshot customSnapshot = new CarCustomSnapshot();
        carCustom.setSnapshotId(null);
        BeanUtils.copyProperties(carCustom, customSnapshot);
        customSnapshot.setCreateTime(dataTime);
        customSnapshot.insert();

        List<CarCustomOption> customOptionList = customOptionService.lambdaQuery()
                .eq(CarCustomOption::getCcid, carCustom.getCcid())
                .list();
        customOptionList.forEach(o->{
            CarCustomOptionSnapshot optionSnapshot = new CarCustomOptionSnapshot();
            optionSnapshot.setSnapshotId(customSnapshot.getSnapshotId());
            BeanUtils.copyProperties(o, optionSnapshot);
            BigDecimal optionPrice = null;
            try {
                optionPrice = priceTypeService.optionPrice(modelLineId, o.getCode(), o.getCategory());
            } catch (ServiceException e) {
               log.error("选装快照价格错误", e);
            }
            optionSnapshot.setPrice(optionPrice);
            optionSnapshot.insert();
        });
        // detail
        for(String channel : new String[]{Constant.MASTER_CHANNEL, Constant.ONEAPP_CHANNEL}) {
            if (carCustom.getBestRecommendId() != null) {
                customDetail = recommendCustomService.getCarConfigDetail(channel, carCustom);
            } else {
                customDetail = carCustomService.getCarConfigDetail(channel, carCustom);
            }
            if (Constant.MASTER_CHANNEL.equals(channel)) {
                customSnapshot.setDetailSnapshot(JSONObject.toJSONString(customDetail));
                if (customDetail.getConfigDetail().getCarModel().getModelPrice() instanceof BigDecimal) {
                    customSnapshot.setModelPrice((BigDecimal) customDetail.getConfigDetail().getCarModel().getModelPrice());
                }
                if (customDetail.getConfigDetail().getTotalPrice() instanceof BigDecimal) {
                    customSnapshot.setTotalPrice((BigDecimal) customDetail.getConfigDetail().getTotalPrice());
                }
            }
            DetailIndex detailIndex = new DetailIndex(client, channel);
            detailIndex.addIndex("doc", customSnapshot.getSnapshotId().toString(), customDetail, dataTime);
        }
        // omd
        if (carCustom.getBestRecommendId() != null){
            customDetail = recommendCustomService.getCarConfigDetailOmd(carCustom);
        }else {
            customDetail = carCustomService.getCarConfigDetailOmd(carCustom);
        }
        if (customDetail != null) {
            customDetail.setMeasureId(JsonString.valueOf(carCustom.getMeasureId()));
        }
        customSnapshot.setOmdSnapshot(JSONObject.toJSONString(customDetail));
        customSnapshot.setConfigCode(CcUtils.getCcUniqueCode(customDetail.getConfigDetail().getCarModel().getModelCode(),
                customDetail.getConfigDetail().getCarModel().getModelYear(),
                customDetail.getConfigDetail().getCarModel().getOmdModelVersion(),
                customDetail.getConfigDetail().getOutsideColor().getColorCode(),
                customDetail.getConfigDetail().getInsideColor().getColorCode(),
                customDetail.getConfigDetail().getOptionList().stream().map(i->i.getOptionCode()).collect(Collectors.joining(","))));
        OmdDetailIndex omdDetailIndex= new OmdDetailIndex(client);
        omdDetailIndex.addIndex("doc", customSnapshot.getSnapshotId().toString(), customDetail, dataTime);

        //contract
        if (carCustom.getBestRecommendId() != null){
            customDetail = recommendCustomService.getCarConfigDetailContract(carCustom);
        }else {
            customDetail = carCustomService.getCarConfigDetailContract(carCustom);
        }
        if (customDetail != null) {
            customDetail.setMeasureId(JsonString.valueOf(carCustom.getMeasureId()));
        }
        customSnapshot.setContractSnapshot(JSONObject.toJSONString(customDetail));
        ContractIndex contractIndex= new ContractIndex(client);
        contractIndex.addIndex("doc", customSnapshot.getSnapshotId().toString(), customDetail, dataTime);

        //customVo
        CarCustomVo customVo = carCustomService.getCarCustom(carCustom);
        CustomVoIndex customVoIndex= new CustomVoIndex(client);
        customVoIndex.addIndex("doc", customSnapshot.getSnapshotId().toString(), customVo, dataTime);


        customSnapshot.updateById();
        carCustomService.lambdaUpdate().set(CarCustom::getSnapshotId, customSnapshot.getSnapshotId())
                .eq(CarCustom::getCcid, carCustom.getCcid()).update();
        return customSnapshot.getSnapshotId();
    }

    @Override
    @Transactional
    public Long updateSnapshotCarCustom(Long ccid, SnapshotUpdateDto updateDto, LocalDateTime dataTime) throws Exception {
        CarCustom carCustom = carCustomService.getById(ccid);
        LambdaUpdateWrapper<CarCustom> cU = new LambdaUpdateWrapper<>();
        cU.eq(CarCustom::getCcid, carCustom.getCcid())
                .set(CarCustom::getClassify, updateDto.getClassify())
                .set(CarCustom::getOmdVehicleTypeId, updateDto.getOmdVehicleTypeId())
                .set(CarCustom::getClassifyVersion, updateDto.getClassifyVersion())
                .set(CarCustom::getEstimateDelivery, updateDto.getEstimateDelivery())
                .set(CarCustom::getDepositType, updateDto.getDepositType());
        carCustomService.update(cU);
        if (carCustom.getSnapshotId() == null){
            throw new ServiceException("400401", "can not update snapshot, has no snapshot");
        }
        CarCustomSnapshot customSnapshot = customSnapshotService.getById(carCustom.getSnapshotId());
        customSnapshot.setSnapshotId(null);
        customSnapshot.setCreateTime(dataTime);
        customSnapshot.setClassify(updateDto.getClassify());
        customSnapshot.setClassifyVersion(updateDto.getClassifyVersion());
        customSnapshot.setOmdVehicleTypeId(updateDto.getOmdVehicleTypeId());
        customSnapshot.setEstimateDelivery(updateDto.getEstimateDelivery());
        customSnapshot.setDepositType(updateDto.getDepositType());
        customSnapshot.insert();
        List<CarCustomOptionSnapshot> customOptionList = optionSnapshotService.lambdaQuery()
                .eq(CarCustomOptionSnapshot::getSnapshotId, carCustom.getSnapshotId())
                .list();
        customOptionList.forEach(o->{
            o.setCustomOptionId(null);
            o.setSnapshotId(customSnapshot.getSnapshotId());
            o.insert();
        });

        // detail
        for (String channel : new String[]{Constant.MASTER_CHANNEL, Constant.ONEAPP_CHANNEL}) {
            DetailIndex detailIndex = new DetailIndex(client, channel);
            CarCustomDetail customDetail = detailIndex.getDetailBySnapshotId(carCustom.getSnapshotId());
            customDetail.setClassify(updateDto.getClassify());
            customDetail.setDepositType(updateDto.getDepositType());
            customDetail.setEstimateDelivery(updateDto.getEstimateDelivery());
            if (Constant.MASTER_CHANNEL.equals(channel)) {
                customSnapshot.setDetailSnapshot(JSONObject.toJSONString(customDetail));
            }
            detailIndex.addIndex("doc", customSnapshot.getSnapshotId().toString(), customDetail, dataTime);
        }

        // omd
        OmdDetailIndex omdDetailIndex= new OmdDetailIndex(client);
        CarCustomDetail omdDetail = omdDetailIndex.getDetailBySnapshotId(carCustom.getSnapshotId());
        omdDetail.setClassify(updateDto.getClassify());
        omdDetail.setDepositType(updateDto.getDepositType());
        omdDetail.setEstimateDelivery(updateDto.getEstimateDelivery());
        customSnapshot.setOmdSnapshot(JSONObject.toJSONString(omdDetail));
        omdDetailIndex.addIndex("doc", customSnapshot.getSnapshotId().toString(), omdDetail, dataTime);

        //contract
        ContractIndex contractIndex= new ContractIndex(client);
        CarCustomDetail contractDetail = contractIndex.getDetailBySnapshotId(carCustom.getSnapshotId());
        contractDetail.setClassify(updateDto.getClassify());
        contractDetail.setDepositType(updateDto.getDepositType());
        contractDetail.setEstimateDelivery(updateDto.getEstimateDelivery());
        customSnapshot.setContractSnapshot(JSONObject.toJSONString(contractDetail));
        contractIndex.addIndex("doc", customSnapshot.getSnapshotId().toString(), contractDetail, dataTime);

        //customVo
        CustomVoIndex customVoIndex= new CustomVoIndex(client);
        CarCustomVo customVo = customVoIndex.getDetailBySnapshotId(carCustom.getSnapshotId());
        customVo.setClassify(updateDto.getClassify());
        customVo.setEstimateDelivery(updateDto.getEstimateDelivery());
        customVoIndex.addIndex("doc", customSnapshot.getSnapshotId().toString(), customVo, dataTime);

        customSnapshot.updateById();
        carCustomService.lambdaUpdate().set(CarCustom::getSnapshotId, customSnapshot.getSnapshotId())
                .eq(CarCustom::getCcid, carCustom.getCcid()).update();
        return customSnapshot.getSnapshotId();
    }

    @Override
    @Transactional
    public Long bindUserSnapshotCarCustom(Long ccid, String userId, String userMobile, LocalDateTime dataTime) throws Exception {
        CarCustom carCustom = carCustomService.getById(ccid);
        LambdaUpdateWrapper<CarCustom> customUpdate = new LambdaUpdateWrapper<>();
        customUpdate.set(CarCustom::getUserId, userId).set(CarCustom::getUserMobile, userMobile).eq(CarCustom::getCcid, ccid);
        carCustomService.update(customUpdate);
        if (carCustom.getSnapshotId() == null){
            throw new ServiceException("400401", "can not update snapshot, has no snapshot");
        }
        CarCustomSnapshot customSnapshot = customSnapshotService.getById(carCustom.getSnapshotId());
        customSnapshot.setSnapshotId(null);
        customSnapshot.setCreateTime(dataTime);
        customSnapshot.setUserId(userId);
        customSnapshot.setUserMobile(userMobile);
        customSnapshot.insert();
        List<CarCustomOptionSnapshot> customOptionList = optionSnapshotService.lambdaQuery()
                .eq(CarCustomOptionSnapshot::getSnapshotId, carCustom.getSnapshotId())
                .list();
        customOptionList.forEach(o->{
            o.setCustomOptionId(null);
            o.setSnapshotId(customSnapshot.getSnapshotId());
            o.insert();
        });

        // detail
        for (String channel : new String[]{Constant.MASTER_CHANNEL, Constant.ONEAPP_CHANNEL}) {
            DetailIndex detailIndex = new DetailIndex(client, channel);
            CarCustomDetail customDetail = detailIndex.getDetailBySnapshotId(carCustom.getSnapshotId());
            customDetail.setAccountId(userId);
            if (Constant.MASTER_CHANNEL.equals(channel)) {
                customSnapshot.setDetailSnapshot(JSONObject.toJSONString(customDetail));
            }
            detailIndex.addIndex("doc", customSnapshot.getSnapshotId().toString(), customDetail, dataTime);
        }

        // omd
        OmdDetailIndex omdDetailIndex= new OmdDetailIndex(client);
        CarCustomDetail omdDetail = omdDetailIndex.getDetailBySnapshotId(carCustom.getSnapshotId());
        omdDetail.setAccountId(userId);
        customSnapshot.setOmdSnapshot(JSONObject.toJSONString(omdDetail));
        omdDetailIndex.addIndex("doc", customSnapshot.getSnapshotId().toString(), omdDetail, dataTime);

        //contract
        ContractIndex contractIndex= new ContractIndex(client);
        CarCustomDetail contractDetail = contractIndex.getDetailBySnapshotId(carCustom.getSnapshotId());
        contractDetail.setAccountId(userId);
        customSnapshot.setContractSnapshot(JSONObject.toJSONString(contractDetail));
        contractIndex.addIndex("doc", customSnapshot.getSnapshotId().toString(), contractDetail, dataTime);

        //customVo
        CustomVoIndex customVoIndex= new CustomVoIndex(client);
        CarCustomVo customVo = customVoIndex.getDetailBySnapshotId(carCustom.getSnapshotId());
        customVoIndex.addIndex("doc", customSnapshot.getSnapshotId().toString(), customVo, dataTime);

        customSnapshot.updateById();
        carCustomService.lambdaUpdate().set(CarCustom::getSnapshotId, customSnapshot.getSnapshotId())
                .eq(CarCustom::getCcid, carCustom.getCcid()).update();
        return customSnapshot.getSnapshotId();
    }

    @Override
    public Long snapshotCarCustom(CarCustom carCustom) throws Exception {
        return this.snapshotCarCustom(carCustom, carCustom.getCreateTime());
    }

    @Override
    @Transactional
    public CarCustomDetail snapshotFrontDetail(String channel, CarCustom carCustom) throws Exception {
        carCustom = carCustomService.getById(carCustom.getCcid());
        CarCustomDetail customDetail;
        if (carCustom.getBestRecommendId() != null){
            customDetail = recommendCustomService.getCarConfigDetail(Constant.MASTER_CHANNEL, carCustom);
        }else {
            customDetail = carCustomService.getCarConfigDetail(Constant.MASTER_CHANNEL, carCustom);
        }
        DetailIndex detailIndex = new DetailIndex(client, channel);
        detailIndex.addIndex("doc", carCustom.getSnapshotId().toString(), customDetail, carCustom.getCreateTime());
        return customDetail;
    }

    ExecutorService executorService = Executors.newFixedThreadPool(20);

    @Override
    public void historySnapshot() throws InterruptedException {
        List<CarCustom> carCustoms = carCustomService.lambdaQuery().eq(CarCustom::getModelLineId, "cd5d8d4b-9158-46fb-9f7a-81f7f5860657")
                .isNull(CarCustom::getSnapshotId)
                .orderByAsc(CarCustom::getCreateTime)
                .list();

        for (CarCustom carCustom : carCustoms){
            Thread.sleep(2l);
            executorService.execute(()->{
                Long currentCcid = carCustom.getCcid();
                try {
                    this.snapshotCarCustom(carCustom);
                }catch (Exception e){
                    log.error("历史快照处理错误 , ccid: " + currentCcid, e);
                }
            });
        }
    }

    @Override
    public Long snapshotFromOld(Long oldCcid, Long newCcid) throws Exception {
        LocalDateTime dataTime = LocalDateTime.now();
        CarCustom carCustom = carCustomService.getById(oldCcid);
        CarCustomSnapshot customSnapshot = customSnapshotService.getById(carCustom.getSnapshotId());
        customSnapshot.setSnapshotId(null);
        customSnapshot.setCcid(newCcid);
        customSnapshot.setCreateTime(dataTime);
        customSnapshot.insert();
        List<CarCustomOptionSnapshot> customOptionList = optionSnapshotService.lambdaQuery()
                .eq(CarCustomOptionSnapshot::getSnapshotId, carCustom.getSnapshotId())
                .list();
        customOptionList.forEach(o->{
            o.setCustomOptionId(null);
            o.setSnapshotId(customSnapshot.getSnapshotId());
            o.insert();
        });

        // detail
        for (String channel : new String[]{Constant.MASTER_CHANNEL, Constant.ONEAPP_CHANNEL}) {
            DetailIndex detailIndex = new DetailIndex(client, channel);
            CarCustomDetail customDetail = detailIndex.getDetailBySnapshotId(carCustom.getSnapshotId());
            if (Constant.MASTER_CHANNEL.equals(channel)) {
                customSnapshot.setDetailSnapshot(JSONObject.toJSONString(customDetail));
            }
            detailIndex.addIndex("doc", customSnapshot.getSnapshotId().toString(), customDetail, dataTime);
        }

        // omd
        OmdDetailIndex omdDetailIndex= new OmdDetailIndex(client);
        CarCustomDetail omdDetail = omdDetailIndex.getDetailBySnapshotId(carCustom.getSnapshotId());
        customSnapshot.setOmdSnapshot(JSONObject.toJSONString(omdDetail));
        omdDetailIndex.addIndex("doc", customSnapshot.getSnapshotId().toString(), omdDetail, dataTime);

        //contract
        ContractIndex contractIndex= new ContractIndex(client);
        CarCustomDetail contractDetail = contractIndex.getDetailBySnapshotId(carCustom.getSnapshotId());
        customSnapshot.setContractSnapshot(JSONObject.toJSONString(contractDetail));
        contractIndex.addIndex("doc", customSnapshot.getSnapshotId().toString(), contractDetail, dataTime);

        //customVo
        CustomVoIndex customVoIndex= new CustomVoIndex(client);
        CarCustomVo customVo = customVoIndex.getDetailBySnapshotId(carCustom.getSnapshotId());
        customVoIndex.addIndex("doc", customSnapshot.getSnapshotId().toString(), customVo, dataTime);

        customSnapshot.updateById();
        carCustomService.lambdaUpdate().set(CarCustom::getSnapshotId, customSnapshot.getSnapshotId())
                .eq(CarCustom::getCcid, carCustom.getCcid()).update();
        return customSnapshot.getSnapshotId();
    }
}
