package com.csvw.audi.cc.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 数据导入后返回的响应
 */
@Data
public class ImportRes {

    @ApiModelProperty(value = "导入的总数量")
    private Integer total;

    @ApiModelProperty(value = "导入成功的数量")
    private Integer successNum;

    @ApiModelProperty(value = "导入失败的数量")
    private Integer failureNum;

    @ApiModelProperty(value = "导入失败的信息")
    private List<String> failedInfos;
}
