package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.po.CarStyle;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.vo.*;

import java.util.List;

/**
 * <p>
 * 款式 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
public interface ICarStyleService extends IService<CarStyle> {

    List<StyleVo> listStyle(String channel, String customSeriesId, Integer type) throws Exception;

    List<StyleModelLineVo> listModelLine(String channel, CarStyle style) throws Exception;

    List<StyleVo> recommendStyleList(String channel, String customSeriesId) throws NoSuchFieldException, IllegalAccessException;

    List<RecommendCarSphereVo> recommendCarByStyle(String channel, CarStyle style);

    List<RecommendFixVo> recommendStyleFix(String channel, String customSeriesId) throws NoSuchFieldException, IllegalAccessException;

    List<EnergyStyleVo> listEnergyStyle(String channel, String customSeriesId, String modelYear, Integer type) throws Exception;
}
