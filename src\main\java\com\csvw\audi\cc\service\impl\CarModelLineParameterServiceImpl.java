package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.entity.po.CarModelLineParameter;
import com.csvw.audi.cc.entity.vo.ModelLineParameterVo;
import com.csvw.audi.cc.mapper.CarModelLineParameterMapper;
import com.csvw.audi.cc.service.ICarModelLineParameterService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 配置线参数 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Service
public class CarModelLineParameterServiceImpl extends ServiceImpl<CarModelLineParameterMapper, CarModelLineParameter> implements ICarModelLineParameterService {

    @Autowired
    private CarModelLineParameterMapper modelLineParameterMapper;

    @Override
    public List<ModelLineParameterVo> listModelLineParameter(String modelLineId) {
        List<ModelLineParameterVo> vos = modelLineParameterMapper.listModelLineParameter(modelLineId);
        return vos;
    }
}
