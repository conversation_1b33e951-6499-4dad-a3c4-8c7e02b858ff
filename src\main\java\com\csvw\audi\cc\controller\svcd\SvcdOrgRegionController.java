package com.csvw.audi.cc.controller.svcd;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.dto.SvcdCityDto;
import com.csvw.audi.cc.entity.po.SvcdOrgRegion;
import com.csvw.audi.cc.entity.vo.SvcdCityVo;
import com.csvw.audi.cc.service.ISvcdCityService;
import com.csvw.audi.cc.service.ISvcdOrgRegionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "组织区划信息")
@RestController
@RequestMapping("/api/v1/orgRegion")
public class SvcdOrgRegionController extends BaseController {

    @Autowired
    private ISvcdOrgRegionService svcdOrgRegionService;

    @ApiOperation("获取组织区划大区列表")
    @GetMapping("/getRegionList")
    public AjaxMessage<List<SvcdOrgRegion>> getRegionList() {
        QueryWrapper<SvcdOrgRegion> queryWrapper = new QueryWrapper();
        queryWrapper.eq("region_type","region").orderByAsc("region_code");
        List<SvcdOrgRegion> list =  svcdOrgRegionService.list(queryWrapper);
        return new AjaxMessage<>("00", "成功", list);
    }

}
