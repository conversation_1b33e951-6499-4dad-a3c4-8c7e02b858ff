<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.SvcdCityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.SvcdCity">
        <id column="city_id" property="svcdCityId" />
        <result column="name" property="name" />
        <result column="city_code" property="cityCode" />
        <result column="province_code" property="provinceCode" />
        <result column="simple_name" property="simpleName" />
        <result column="quick_code" property="quickCode" />
        <result column="city_en" property="cityEn" />
        <result column="status" property="status" />
        <result column="zm_city_code" property="zmCityCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        city_id, name, city_code, province_code, simple_name, quick_code, city_en, status, zm_city_code
    </sql>

</mapper>
