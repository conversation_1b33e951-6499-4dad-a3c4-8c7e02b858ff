package com.csvw.audi.cc.entity.vo.svcd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("代理商人员查询返回模型")
public class SvcdUserListRO {
    @ApiModelProperty("工作状态 1：在岗，0：无岗，2：离职")
    private Integer workStatus;
    @ApiModelProperty("人员状态 0: 停用 1：启用")
    private Integer status;
    @ApiModelProperty("所属渠道商 网络代码")
    private String dealerCode;
    @ApiModelProperty("电话")
    private String mobile;
    @ApiModelProperty("人员姓名")
    private String userName;
    @ApiModelProperty("员工照片")
    private String pic;
    @ApiModelProperty("员工简介")
    private String profile;
    @ApiModelProperty("userId")
    private String userId;

}
