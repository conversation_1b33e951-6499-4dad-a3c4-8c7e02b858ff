package com.csvw.audi.cc.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.config.AppConfig;
import com.csvw.audi.cc.entity.dto.CustomSeriesParam;
import com.csvw.audi.cc.entity.dto.ModelParamDto;
import com.csvw.audi.cc.entity.dto.accb.ACCBCarModels;
import com.csvw.audi.cc.entity.dto.accb.TypeDto;
import com.csvw.audi.cc.entity.po.CarCustomSeries;
import com.csvw.audi.cc.entity.po.SvcdOrgRegion;
import com.csvw.audi.cc.entity.vo.CustomSeriesVo;
import com.csvw.audi.cc.entity.vo.ModelLineVo;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.service.IACCBService;
import com.csvw.audi.cc.service.ICarCustomSeriesService;
import com.csvw.audi.cc.service.ICarModelLineService;
import com.csvw.audi.cc.service.ISvcdOrgRegionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/admin/api/v1")
@Api(tags = "运营侧接口")
public class AdminApiController extends BaseController {

    @Autowired
    private ICarCustomSeriesService customSeriesService;

    @Autowired
    private ICarModelLineService modelLineService;

    @Autowired
    private ISvcdOrgRegionService svcdOrgRegionService;

    @ApiOperation("车系接口")
    @GetMapping("/model/list")
    public AjaxMessage<List<CustomSeriesVo>> customSeries() throws Exception {
        List<CarCustomSeries> data = customSeriesService.listCustomSeries(Constant.MASTER_CHANNEL, null);
        List<CustomSeriesVo> vos = new ArrayList<>();
        if (data != null){
            vos = data.stream().map(i->{
                CustomSeriesVo vo = new CustomSeriesVo();
                BeanUtils.copyProperties(i, vo);
                return vo;
            }).collect(Collectors.toList());
        }
        return successMessage(vos);
    }

    @ApiOperation("车型接口")
    @GetMapping("/type/list")
    public AjaxMessage<List<ModelLineVo>> modelLine(@RequestParam String customSeriesId) throws Exception {
        String channel = Constant.MASTER_CHANNEL;
        CustomSeriesParam seriesParam = new CustomSeriesParam();
        seriesParam.setCustomSeriesId(customSeriesId);
        List<CarCustomSeries> carCustomSeries = customSeriesService.listCustomSeries(channel, seriesParam);
        if (CollectionUtils.isEmpty(carCustomSeries)){
            throw new ServiceException("参数异常：customSeriesId", "400401", "");
        }
        ModelParamDto paramDto = new ModelParamDto();
        paramDto.setCustomSeriesId(customSeriesId);
        paramDto.setDelFlag(0);
        paramDto.setChannel(channel);
        List<ModelLineVo> data = modelLineService.listModelLine(paramDto);
        return successMessage(data);
    }

    @ApiOperation("获取组织区划大区列表")
    @GetMapping("/getRegionList")
    public AjaxMessage<List<SvcdOrgRegion>> getCityList() {
        QueryWrapper<SvcdOrgRegion> queryWrapper = new QueryWrapper();
        queryWrapper.eq("region_type","region").orderByAsc("region_code");
        List<SvcdOrgRegion> list =  svcdOrgRegionService.list(queryWrapper);
        return new AjaxMessage<>("00", "成功", list);
    }

}

