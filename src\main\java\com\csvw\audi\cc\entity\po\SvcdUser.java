package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 渠道商人员信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SvcdUser对象", description="渠道商人员信息")
public class SvcdUser extends Model<SvcdUser> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "数据id")
      @TableId(value = "user_id", type = IdType.AUTO)
    private Long userId;

    @ApiModelProperty(value = "用户 uid,人员注册成功后的 id,UUID 形式")
    private String userUid;

    @ApiModelProperty(value = "工号")
    private String workNo;

    @ApiModelProperty(value = "人员姓名")
    private String userName;

    @ApiModelProperty(value = "所属渠道商网络代码")
    private String dealerCode;

    @ApiModelProperty(value = "所属渠道商名称")
    private String dealerName;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "电话")
    private String mobile;

    @ApiModelProperty(value = "电子邮件")
    private String email;

    @ApiModelProperty(value = "出生年月,时间戳")
    private Long birthday;

    @ApiModelProperty(value = "人员状态（0: 停用 1：启用）")
    private Long status;

    @ApiModelProperty(value = "邮政编码")
    private String postCode;

    @ApiModelProperty(value = "年龄")
    private Long age;

    @ApiModelProperty(value = "性别（1:男 2:女）")
    private Long gender;

    @ApiModelProperty(value = "证件类型（1：身份证 2：其他证件类型）")
    private Long identityType;

    @ApiModelProperty(value = "证件号码")
    private String identityCode;

    @ApiModelProperty(value = "民族")
    private String nation;

    @ApiModelProperty(value = "政治面貌")
    private String politicalStatus;

    @ApiModelProperty(value = "学历（1:博士研究生 2:硕士研究生 3:本科 4:大专 5:高中 6:中专、技校 7:中学 8:小学 9:没有受过教育）")
    private Long education;

    @ApiModelProperty(value = "计算机能力")
    private String computerGrade;

    @ApiModelProperty(value = "是否有驾照（1：是，0：否）")
    private Long isDrivingLicense;

    @ApiModelProperty(value = "是否有二手车评估证件（1：是 0：否）")
    private Long isUsedCarLicense;

    @ApiModelProperty(value = "工作状态（1：在岗 0：无岗 2：离职）")
    private Long workStatus;

    @ApiModelProperty(value = "技术职称")
    private String techTitle;

    @ApiModelProperty(value = "入职时间,时间戳")
    private Long hireDate;

    @ApiModelProperty(value = "离职时间,时间戳")
    private Long leaveDate;

    @ApiModelProperty(value = "银行卡类型（0：借记卡，1：储蓄卡，2：蒙古族，3：信用卡）")
    private String bankCardType;

    @ApiModelProperty(value = "银行卡号")
    private String bankCardNum;

    @ApiModelProperty(value = "开户行")
    private String bankOfDeposit;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "p 码")
    private String pCode;

    @ApiModelProperty(value = "员工照片")
    private String pic;

    @ApiModelProperty(value = "员工简介")
    private String profile;

    @ApiModelProperty(value = "预留字段")
    private String spare1;

    @ApiModelProperty(value = "预留字段")
    private String spare2;

    @ApiModelProperty(value = "预留字段")
    private String spare3;

    @ApiModelProperty(value = "售后代码")
    private String serviceCode;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;

    @TableField(exist = false)
    private List<SvcdPositions> positions;//员工岗位列表

    @TableField(exist = false)
    private List<SvcdRoles> roles;//用户角色列表


    @Override
    protected Serializable pkVal() {
        return this.userId;
    }

}
