package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 省份信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SvcdProvince对象", description="省份信息")
public class SvcdProvince extends Model<SvcdProvince> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "数据id")
      @TableId(value = "province_id", type = IdType.AUTO)
    private Long provinceId;

    @ApiModelProperty(value = "省份名称")
    private String name;

    @ApiModelProperty(value = "省份代码")
    private Long provinceCode;

    @ApiModelProperty(value = "简称")
    private String simpleName;

    @ApiModelProperty(value = "速查码")
    private String quickCode;

    @ApiModelProperty(value = "省份拼音")
    private String provinceEn;

    @ApiModelProperty(value = "状态")
    private Long status;

    @ApiModelProperty(value = "招募省份代码")
    private Long zmProvinceCode;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;


    @Override
    protected Serializable pkVal() {
        return this.provinceId;
    }

}
