package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.config.AppConfig;
import com.csvw.audi.cc.entity.dto.BestRecommendQueryParam;
import com.csvw.audi.cc.entity.enumeration.StockTypeEnum;
import com.csvw.audi.cc.entity.po.CarBestRecommendStock;
import com.csvw.audi.cc.entity.vo.BestRecommendCarVo;
import com.csvw.audi.cc.mapper.CarBestRecommendStockMapper;
import com.csvw.audi.cc.service.ICarBestRecommendStockService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * OMD推荐车库存表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
@Service
public class CarBestRecommendStockServiceImpl extends ServiceImpl<CarBestRecommendStockMapper, CarBestRecommendStock> implements ICarBestRecommendStockService {

    @Autowired
    CarBestRecommendStockMapper mapper;

    @Autowired
    AppConfig appConfig;

    @Override
    public void plusStockNum(Long id, Integer stockNum) {
        mapper.plusStockNum(id, stockNum);
    }

    @Override
    public boolean validRecommendFixStock(BestRecommendCarVo bestRecommendCar, String dealerCode) {
        String bestRecommendId = bestRecommendCar.getBestRecommendId();
        BestRecommendQueryParam queryParam = new BestRecommendQueryParam();
        if (dealerCode != null && !dealerCode.equals(appConfig.getHqDealerCode())){
            queryParam.setDealerCode(dealerCode);
            queryParam.setType(StockTypeEnum.DEALER.getValue());
        }else {
            queryParam.setType(StockTypeEnum.HQ.getValue());
            queryParam.setDealerCode("");
        }
        queryParam.setBestRecommendId(bestRecommendId);
        Integer stockNum = mapper.getRecommendStock(queryParam).intValue();
        bestRecommendCar.setStockNum(stockNum);
        return stockNum > 0;
    }

    @Override
    public boolean validRecommendStock(BestRecommendCarVo bestRecommendCar, String dealerCode) {
        String bestRecommendId = bestRecommendCar.getBestRecommendId();
        BestRecommendQueryParam queryParam = new BestRecommendQueryParam();
        if (dealerCode != null && !dealerCode.equals(appConfig.getHqDealerCode())){
            queryParam.setDealerCode(dealerCode);
            queryParam.setType(StockTypeEnum.DEALER.getValue());
        }else {
            queryParam.setType(StockTypeEnum.HQ.getValue());
        }
        queryParam.setBestRecommendId(bestRecommendId);
        Integer stockNum = mapper.getRecommendStock(queryParam).intValue();
        bestRecommendCar.setStockNum(stockNum);
        return stockNum > 0;
    }
}
