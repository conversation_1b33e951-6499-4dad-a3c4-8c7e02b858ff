package com.csvw.audi.cc.controller;

import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.config.AppConfig;
import com.csvw.audi.cc.entity.dto.accb.*;
import com.csvw.audi.cc.service.IACCBService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/accb")
@Api(tags = "ACCB车辆数据API")
public class AccbApiController extends BaseController {

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private IACCBService iaccbService;

    @ApiOperation("车系接口")
    @GetMapping("/model/list")
    public AjaxMessage<List<ACCBCarModels>> model(){
        List<ACCBCarModels> accbCarModels = iaccbService.allModels(appConfig.getAccb().getLang(), appConfig.getAccb().getProduct());
        return new AjaxMessage<>("00", "成功", accbCarModels);
    }

    @ApiOperation("车型接口")
    @GetMapping("/type/list")
    public AjaxMessage<List<TypeDto>> series(String modelId){
        List<TypeDto> typeDtos = iaccbService.modelTypes(appConfig.getAccb().getLang(), appConfig.getAccb().getProduct(), modelId);
        return new AjaxMessage<>("00", "成功", typeDtos);
    }

    @ApiOperation("配置项接口")
    @GetMapping("/option/list")
    public AjaxMessage<List<OptionDto>> option(String typeId){
        List<OptionDto> optionDtos = iaccbService.typeOptions(appConfig.getAccb().getLang(), appConfig.getAccb().getProduct(), typeId);
        return new AjaxMessage<>("00", "成功", optionDtos);
    }

    @ApiOperation("配置项视图资源接口")
    @PostMapping("/preview/list")
    public AjaxMessage<List<ConfigPreviewDto>> preview(String typeCode, String modelYear, String exteriorCode, String interiorCode){
        List<ConfigPreviewDto> previews = iaccbService.preview(typeCode, modelYear, exteriorCode, interiorCode);
        return new AjaxMessage<>("00", "成功", previews);
    }

    @ApiOperation("配置项接口")
    @PostMapping("/refreshOption/list")
    public AjaxMessage<List<OptionDto>> refreshOption(String language, String product, @RequestBody RefreshOptionParam optionParam){
        List<OptionDto> optionDtos = iaccbService.refreshOption(language, product, optionParam);
        return new AjaxMessage<>("00", "成功", optionDtos);
    }

    @ApiOperation("标准装备接口")
    @GetMapping("/equipment/list")
    public AjaxMessage<List<EquipmentDto>> equipment(String language, String product, @RequestBody EquipmentParam equipmentParam){
        List<EquipmentDto> equipmentDtos = iaccbService.standardEquipment(language, product, equipmentParam);
        return new AjaxMessage<>("00", "成功", equipmentDtos);
    }

}

