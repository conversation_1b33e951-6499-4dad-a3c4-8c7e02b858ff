<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.SvcdAfterSalesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.SvcdAfterSales">
        <id column="after_sales_id" property="afterSalesId" />
        <result column="dealer_code" property="dealerCode" />
        <result column="faw_audi_build_level" property="fawAudiBuildLevel" />
        <result column="faw_audi_start_time" property="fawAudiStartTime" />
        <result column="faw_audi_authorization_type" property="fawAudiAuthorizationType" />
        <result column="faw_audi_authorization_deadline" property="fawAudiAuthorizationDeadline" />
        <result column="faw_audi_new_energy_import_vehicle_qualification" property="fawAudiNewEnergyImportVehicleQualification" />
        <result column="faw_audi_new_energy_domestic_vehicle_qualification" property="fawAudiNewEnergyDomesticVehicleQualification" />
        <result column="faw_audi_battery_maintenance_center" property="fawAudiBatteryMaintenanceCenter" />
        <result column="faw_audi_rescue_service_vehicle_num" property="fawAudiRescueServiceVehicleNum" />
        <result column="faw_audi_car_replace_num" property="fawAudiCarReplaceNum" />
        <result column="hard_accept_date" property="hardAcceptDate" />
        <result column="it_sys_accept_date" property="itSysAcceptDate" />
        <result column="first_attachments_accept_date" property="firstAttachmentsAcceptDate" />
        <result column="saic_audi_authorization_deadline" property="saicAudiAuthorizationDeadline" />
        <result column="saic_audi_maintenance_level" property="saicAudiMaintenanceLevel" />
        <result column="saic_audi_rescue_level" property="saicAudiRescueLevel" />
        <result column="provide_saic_audi_car_replace_service" property="provideSaicAudiCarReplaceService" />
        <result column="provide_saic_audi_pick_up_service" property="provideSaicAudiPickUpService" />
        <result column="receive_saic_audi_asr_service_clues" property="receiveSaicAudiAsrServiceClues" />
        <result column="receive_saic_audi_non_asr_service_clues" property="receiveSaicAudiNonAsrServiceClues" />
        <result column="service_phone" property="servicePhone" />
        <result column="rescue" property="rescue" />
        <result column="rescue_phone" property="rescuePhone" />
        <result column="service_receive_workbench" property="serviceReceiveWorkbench" />
        <result column="machine_repair_station" property="machineRepairStation" />
        <result column="bp_station" property="bpStation" />
        <result column="bakeries_num" property="bakeriesNum" />
        <result column="interflow_power" property="interflowPower" />
        <result column="interflow_num" property="interflowNum" />
        <result column="direct_current_power" property="directCurrentPower" />
        <result column="direct_current_num" property="directCurrentNum" />
        <result column="check_apply_report" property="checkApplyReport" />
        <result column="check_complete_time" property="checkCompleteTime" />
        <result column="service_check_date" property="serviceCheckDate" />
        <result column="rescue_contract_code" property="rescueContractCode" />
        <result column="rescue_contract_take_effect_date" property="rescueContractTakeEffectDate" />
        <result column="rescue_contract_invalid_date" property="rescueContractInvalidDate" />
        <result column="take_send_car_contract_code" property="takeSendCarContractCode" />
        <result column="take_send_car_take_effect_date" property="takeSendCarTakeEffectDate" />
        <result column="take_send_car_invalid_date" property="takeSendCarInvalidDate" />
        <result column="info_check_date" property="infoCheckDate" />
        <result column="service_img" property="serviceImg" />
        <result column="check_approval_date" property="checkApprovalDate" />
        <result column="after_sale_service_contacts" property="afterSaleServiceContacts" />
        <result column="after_sale_service_contacts_phone" property="afterSaleServiceContactsPhone" />
        <result column="is_vip_after_sale" property="isVipAfterSale" />
        <result column="east_longitude" property="eastLongitude" />
        <result column="north_latitude" property="northLatitude" />
        <result column="code758" property="code758" />
        <result column="spare1" property="spare1" />
        <result column="spare2" property="spare2" />
        <result column="spare3" property="spare3" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        after_sales_id, dealer_code, faw_audi_build_level, faw_audi_start_time, faw_audi_authorization_type, faw_audi_authorization_deadline, faw_audi_new_energy_import_vehicle_qualification, faw_audi_new_energy_domestic_vehicle_qualification, faw_audi_battery_maintenance_center, faw_audi_rescue_service_vehicle_num, faw_audi_car_replace_num, hard_accept_date, it_sys_accept_date, first_attachments_accept_date, saic_audi_authorization_deadline, saic_audi_maintenance_level, saic_audi_rescue_level, provide_saic_audi_car_replace_service, provide_saic_audi_pick_up_service, receive_saic_audi_asr_service_clues, receive_saic_audi_non_asr_service_clues, service_phone, rescue, rescue_phone, service_receive_workbench, machine_repair_station, bp_station, bakeries_num, interflow_power, interflow_num, direct_current_power, direct_current_num, check_apply_report, check_complete_time, service_check_date, rescue_contract_code, rescue_contract_take_effect_date, rescue_contract_invalid_date, take_send_car_contract_code, take_send_car_take_effect_date, take_send_car_invalid_date, info_check_date, service_img, check_approval_date, after_sale_service_contacts, after_sale_service_contacts_phone, is_vip_after_sale, east_longitude, north_latitude, code758, spare1, spare2, spare3, deleted
    </sql>

<!--    or (org.dealer_type = 4 and policy.control_status = '有' )-->
    <select id="getAfterSalesList" resultType="com.csvw.audi.cc.entity.vo.SvcdAfterSalesVo" parameterType="com.csvw.audi.cc.entity.dto.SvcdAfterSalesDto">
        SELECT DISTINCT
            org.dealer_code,
            org.service_code,
            org.longitude,
            org.latitude,
            org.simple_name AS dealer_name,
            org.operate_address AS dealer_adrress,
            org.contact AS dealer_contacts,
            after_sales.service_phone AS dealer_phone,
            org.province_code,
            org.city_code,
            org.area_code,
            org.business_status,
            org.dealer_type,
            org.created_at,
            province.name AS province_name,
            city.name AS city_name,
            area.name AS area_name,
            after_sales.dealer_after_sale_type,
            after_sales.rescue_phone,
            after_sales.code758,
            after_sales.east_longitude AS after_sales_longitude,
            after_sales.north_latitude AS after_sales_latitude,
            after_sales.provide_saic_audi_pick_up_service,
            after_sales.saic_audi_maintenance_level,
            after_sales.use_system as useSystem
        FROM
            svcd_channel_organization org
        LEFT JOIN
            svcd_city city ON org.city_code = city.city_code
        LEFT JOIN
            svcd_province province ON org.province_code = province.province_code
        LEFT JOIN
            svcd_area area ON org.area_code = area.area_code
        LEFT JOIN
            svcd_after_sales after_sales on org.dealer_code = after_sales.dealer_code
        LEFT JOIN
            svcd_channel_organization_policy policy on policy.dealer_code = org.dealer_code
        WHERE
            1 = 1
            AND org.deleted = 0
            AND ( org.dealer_type = 1 or (org.dealer_type = 0 and policy.control_status = '有'))
            AND org.dealer_code NOT IN (SELECT dealer_code FROM svcd_channel_organization_policy where business_code = 'BC001' and control_status = '1')
        <choose>
            <when test="businessStatus == 0">
            </when>
            <when test="businessStatus != null and businessStatus != ''">
                AND org.business_status = #{businessStatus,jdbcType=VARCHAR}
            </when>
            <otherwise>
                AND org.business_status in ('3','6')
            </otherwise>
        </choose>
        <if test="dealerCode != null and dealerCode != ''">
            AND org.dealer_code =  #{dealerCode,jdbcType=VARCHAR}
        </if>
        <if test="cityName != null and cityName != ''">
            AND city.name LIKE concat(concat("%",#{cityName,jdbcType=VARCHAR}),"%")
        </if>
        <if test="cityCode != null and cityCode != ''">
            AND org.city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="code758 != null and code758 != ''">
            AND after_sales.code758 = #{code758,jdbcType=VARCHAR}
        </if>
        <if test="agentCode != null and agentCode != ''">
            AND org.dealer_code IN (SELECT after_sale_code FROM svcd_sales_agent WHERE dealer_code = #{agentCode,jdbcType=VARCHAR})
        </if>
        <if test="dealerAddress != null and dealerAddress != ''">
            AND org.operate_address LIKE concat(concat("%",#{dealerAddress,jdbcType=VARCHAR}),"%")
        </if>
        <if test="serviceCode != null and serviceCode != ''">
            AND org.service_code =  #{serviceCode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getAgentBindingAfterSalesList" resultType="com.csvw.audi.cc.entity.vo.SvcdAfterSalesVo">
        SELECT
            agent.after_sale_code AS dealer_code,
            org.dealer_code as bind_agent_code,
            org.simple_name as bind_agent_name
        FROM
            svcd_sales_agent agent
        LEFT JOIN
            svcd_channel_organization org ON org.dealer_code = agent.dealer_code
        WHERE
            1 = 1
            AND org.deleted = 0
            AND org.dealer_type in (0,2,4)
            AND org.business_status IN ('3' , '6')
            AND agent.after_sale_code IS NOT NULL
            AND agent.after_sale_code <![CDATA[ <> ]]> ''
    </select>

</mapper>
