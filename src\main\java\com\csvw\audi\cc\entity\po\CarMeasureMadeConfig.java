package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 半订制化车辆配置
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarMeasureMadeConfig对象", description="半订制化车辆配置")
public class CarMeasureMadeConfig extends Model<CarMeasureMadeConfig> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "半订制车辆id")
      @TableId(value = "measure_id", type = IdType.ASSIGN_ID)
    private Long measureId;

    @ApiModelProperty(value = "配置线id")
    private String modelLineId;

    @ApiModelProperty(value = "外饰颜色id")
    private String colorId;

    @ApiModelProperty(value = "外饰颜色code")
    private String colorCode;

    @ApiModelProperty(value = "内饰颜色id")
    private String interiorId;

    @ApiModelProperty(value = "内饰颜色code")
    private String interiorCode;

    @ApiModelProperty(value = "轮毂id")
    private String radId;

    @ApiModelProperty(value = "轮毂code")
    private String radCode;

    @ApiModelProperty(value = "面料id")
    private String sibId;

    @ApiModelProperty(value = "面料code")
    private String sibCode;

    @ApiModelProperty(value = "座椅id")
    private String vosId;

    @ApiModelProperty(value = "座椅code")
    private String vosCode;

    @ApiModelProperty(value = "饰条id")
    private String eihId;

    @ApiModelProperty(value = "饰条code")
    private String eihCode;

    @ApiModelProperty(value = "内饰颜色面料id")
    private String sibInterieurId;

    @ApiModelProperty(value = "私人订制标识，0:无前置选装，1:前置选装包含轮毂，2:前置选装包含座椅，3:前置选装包含面料，4:前置选装包好座椅和面料")
    private Integer prFlag;

    @ApiModelProperty(value = "半订制车辆id")
    private Long measureOriginId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.measureId;
    }

}
