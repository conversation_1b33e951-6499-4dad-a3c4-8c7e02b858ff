package com.csvw.audi.cc.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 渠道商对应的金融机构
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-17
 */
@Data
public class AdminOrgBankDto {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "渠道商编号")
    private String dealerCode;

    @ApiModelProperty(value = "渠道商名称")
    private String dealerName;

    @ApiModelProperty(value = "渠道商全称")
    private String dealerFullName;

    @ApiModelProperty(value = "金融机构代码")
    private String bankCode;

    @ApiModelProperty(value = "金融机构名称")
    private String bankName;

    @ApiModelProperty(value = "id列表")
    private List<String> orgBankIds;

}
