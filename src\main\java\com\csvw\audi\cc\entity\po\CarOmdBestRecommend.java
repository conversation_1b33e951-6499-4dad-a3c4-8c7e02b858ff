package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * OMD畅销推荐车
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarOmdBestRecommend对象", description="OMD畅销推荐车")
public class CarOmdBestRecommend extends Model<CarOmdBestRecommend> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "omd畅销车记录id")
      @TableId(value = "best_sell_recommend_model_id", type = IdType.ASSIGN_ID)
    private Long bestSellRecommendModelId;

    @ApiModelProperty(value = "车型编码")
    private String modelCode;

    @ApiModelProperty(value = "车型版本号")
    private String modelVersion;

    @ApiModelProperty(value = "车型年款")
    private String modelYear;

    @ApiModelProperty(value = "内饰code")
    @TableField("interiorCode")
    private String interiorCode;

    @ApiModelProperty(value = "外饰code")
    @TableField("colorCode")
    private String colorCode;

    @ApiModelProperty(value = "pr列表，逗号分隔")
    @TableField("prList")
    private String prList;

    @ApiModelProperty(value = "产品编码")
    @TableField("classCode")
    private String classCode;

    private Integer type;

    private Integer stockMapRecommend;

    private String ccUniqueCode;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.bestSellRecommendModelId;
    }

}
