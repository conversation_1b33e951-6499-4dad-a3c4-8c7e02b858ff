package com.csvw.audi.cc.controller.svcd;

import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.dto.DealerDto;
import com.csvw.audi.cc.entity.vo.DealerVo;
import com.csvw.audi.cc.service.IDealerImageAssistant;
import com.csvw.audi.cc.service.impl.DealerImageAssistantServiceImpl;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/19 13:25
 * @description
 */
@RestController
@RequestMapping("/private/DealerImageAssistant")
public class DealerImageAssistantController extends BaseController {

    @Autowired
    private IDealerImageAssistant dealerImageAssistant;

    @GetMapping("/findAndStorageDealerImage")
    public AjaxMessage findAndStorageDealerImage(@RequestParam(value = "dealerCodes", required = false) String[] dealerCodes) {
        if (ArrayUtils.isEmpty(dealerCodes)) {
            dealerImageAssistant.findAndStorageDealerImage(null);
        } else {
            Arrays.stream(dealerCodes).forEach(dealerCode -> {
                DealerDto dealerDto = new DealerDto();
                dealerDto.setDealerCode(dealerCode);
                dealerImageAssistant.findAndStorageDealerImage(dealerDto);
            });
        }
        return successMessage("操作完成");
    }

//    @ApiOperation("testimage")
//    @GetMapping("/testImage")
//    public AjaxMessage<Object> testImage(String imageUrl, String dealerCode) {
//        return new AjaxMessage<>("00", "成功", ((DealerImageAssistantServiceImpl)dealerImageAssistant).publicUpload(imageUrl, dealerCode));
//    }
}
