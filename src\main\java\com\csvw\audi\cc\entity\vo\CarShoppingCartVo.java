package com.csvw.audi.cc.entity.vo;

import com.csvw.audi.cc.entity.dto.CarCustomDto;
import com.csvw.audi.cc.entity.dto.vwcc.ColorDetail;
import com.csvw.audi.cc.entity.dto.vwcc.ModelDetail;
import com.csvw.audi.cc.entity.dto.vwcc.Option;
import com.csvw.audi.cc.entity.dto.vwcc.SeriesDetail;
import com.csvw.audi.cc.entity.enumeration.CcInvalidReasonEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CarShoppingCartVo {

    @ApiModelProperty(value = "购物车ID")
    private String shoppingCartId;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "ccid")
    private String ccid;

    @ApiModelProperty(value = "商品id")
    private String skuid;

    @ApiModelProperty(value = "意向金")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "车辆总价格")
    private Object configTotalPrice;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "更新时间")
    private String updateTime;

    @ApiModelProperty(value = "车系")
    private SeriesDetail carSeries;

    @ApiModelProperty(value = "车型")
    private ModelDetail carModel;

    @ApiModelProperty(value = "车辆外饰颜色")
    private ColorDetail outsideColor;

    @ApiModelProperty(value = "车辆内饰颜色")
    private ColorDetail insideColor;

    @ApiModelProperty(value = "购物车id列表，用于批量更新/删除/查询")
    private List<String> shoppingCartIds;

    @ApiModelProperty(value = "车辆装备")
    private List<Option> optionList;

    @ApiModelProperty(value = "自动保存（1:是 0:否）")
    private Integer autoSave;

    @ApiModelProperty(value = "邀请码")
    private String invitationCode;

    @ApiModelProperty(value = "配置单是否有效，0:失效，1:有效")
    private Integer valid;

    private String invalidReason;
    @ApiModelProperty(value = "配置单更新标识，0:无更新，1:有更新")
    private Integer updateFlag;

    @ApiModelProperty(value = "配置单更新内容")
    private String updateContent;

    private CarCustomDto carCustomDto;

    private String dealerCode;
}
