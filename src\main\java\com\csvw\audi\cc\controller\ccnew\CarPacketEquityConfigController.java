package com.csvw.audi.cc.controller.ccnew;


import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.entity.dto.CustomSeriesParam;
import com.csvw.audi.cc.entity.dto.ModelParamDto;
import com.csvw.audi.cc.entity.dto.PriceComputeParam;
import com.csvw.audi.cc.entity.enumeration.OptionCategoryEnum;
import com.csvw.audi.cc.entity.enumeration.OptionTypeEnum;
import com.csvw.audi.cc.entity.po.CarCustomSeries;
import com.csvw.audi.cc.entity.po.CarStyle;
import com.csvw.audi.cc.entity.vo.*;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.service.ICarCustomSeriesService;
import com.csvw.audi.cc.service.ICarModelLineService;
import com.csvw.audi.cc.service.ICarStyleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1/cc")
@Slf4j
@Api(tags = "车辆选装包权益")
public class CarPacketEquityConfigController extends BaseController {

    @Autowired
    private ICarCustomSeriesService customSeriesService;

    @Autowired
    private ICarModelLineService modelLineService;

    @Autowired
    private ICarStyleService styleService;

    @GetMapping({"/modelLine/configs/packetEquity", "/public/modelLine/configs/packetEquity"})
    @ApiOperation("配置器权益列表")
    public AjaxMessage<List<ModelLineOptionVo>> packetEquity(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, @RequestParam String modelLineId) throws Exception {
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        String category = OptionCategoryEnum.PACKET.getValue();
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, modelLineId, category).stream().filter(i-> OptionTypeEnum.PACKET_EQUITY.getValue().equals(i.getOptionType()) && i.getStatus() != 0).collect(Collectors.toList());
        return successMessage(optionVos);
    }

    @GetMapping({"/modelLine/configs/styleList", "/public/modelLine/configs/styleList"})
    @ApiOperation("配置器车辆款式列表")
    public AjaxMessage<List<StyleVo>> styleList(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, @RequestParam String customSeriesId, Integer type) throws Exception {
        CustomSeriesParam seriesParam = new CustomSeriesParam();
        seriesParam.setCustomSeriesId(customSeriesId);
        List<CarCustomSeries> carCustomSeries = customSeriesService.listCustomSeries(channel, seriesParam);
        if (CollectionUtils.isEmpty(carCustomSeries)){
            throw new ServiceException("参数异常：customSeriesId", "400401", "");
        }
        List<StyleVo> styleVos = styleService.listStyle(channel, customSeriesId, type);
        return successMessage(styleVos);
    }

    @GetMapping({"/modelLine/configs/energyStyleList", "/public/modelLine/configs/energyStyleList"})
    @ApiOperation("配置器车辆款式列表")
    public AjaxMessage<List<EnergyStyleVo>> energyStyleList(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                      @RequestParam String customSeriesId, String modelYear, Integer type) throws Exception {
        CustomSeriesParam seriesParam = new CustomSeriesParam();
        seriesParam.setCustomSeriesId(customSeriesId);
        List<CarCustomSeries> carCustomSeries = customSeriesService.listCustomSeries(channel, seriesParam);
        if (CollectionUtils.isEmpty(carCustomSeries)){
            throw new ServiceException("参数异常：customSeriesId", "400401", "");
        }
        List<EnergyStyleVo> styleVos = styleService.listEnergyStyle(channel, customSeriesId, modelYear, type);
        styleVos.forEach(e->e.getStyleVos().forEach(s->{
            Iterator<ModelLineVo> mIt = s.getModelLineList().iterator();
            while (mIt.hasNext()){
                ModelLineVo i = mIt.next();
                boolean exist = false;
                switch (type){
                    case 1:
                        exist = i.getPersonal() != null && i.getPersonal().intValue() == 1;
                        break;
                    case 2:
                        exist = i.getMeasure() != null && i.getMeasure().intValue() == 1 && modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId());
                        break;
                    case 3:
                        exist = (i.getMeasure() != null && i.getMeasure().intValue() == 1 && modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId())) ||
                                (i.getPersonal() != null && i.getPersonal().intValue()  == 1);
                        break;
                }
                if (!exist){
                    mIt.remove();
                }
            }
        }));
        styleVos.forEach(e->{
            Iterator<StyleVo> sIt = e.getStyleVos().iterator();
            while (sIt.hasNext()){
                StyleVo s = sIt.next();
                if (CollectionUtils.isEmpty(s.getModelLineList())){
                    sIt.remove();
                }
            }
        });
        Iterator<EnergyStyleVo> eIt = styleVos.iterator();
        while (eIt.hasNext()){
            EnergyStyleVo e = eIt.next();
            if (CollectionUtils.isEmpty(e.getStyleVos())){
                eIt.remove();
            }
        }
        return successMessage(styleVos);
    }


    @GetMapping({"/modelLine/byStyle", "/public/modelLine/byStyle"})
    @ApiOperation("配置器款式车辆列表")
    public AjaxMessage<List<StyleModelLineVo>> modelLineByStyle(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, @RequestParam String styleId, @RequestParam( defaultValue = "1") int type) throws Exception {
        CarStyle style = styleService.lambdaQuery().eq(CarStyle::getChannel, Constant.MASTER_CHANNEL).eq(CarStyle::getStyleId, styleId).eq(CarStyle::getDelFlag, 0).one();
        if (style == null){
            throw new ServiceException("参数异常：styleId", "400401", "");
        }
        List<StyleModelLineVo> data = styleService.listModelLine(channel, style);
        data = data.stream()
                /*.sorted((a,b)->{
                        BigDecimal aPrice=null, bPrice=null;
                        if (a.getPrice() != null && a.getPrice() instanceof BigDecimal){
                            aPrice = (BigDecimal) a.getPrice();
                        }
                        if (b.getPrice() != null && b.getPrice() instanceof BigDecimal){
                            bPrice = (BigDecimal) b.getPrice();
                        }
                        if (aPrice==null && bPrice== null){
                            return 0;
                        }
                        if (aPrice == null){
                            return 1;
                        }
                        if (bPrice == null){
                            return -1;
                        }
                        return bPrice.compareTo(aPrice);
                    })*/
                .filter(i-> {
                    if(i.getOmdModelStatus() == 0 && (i.getMeasure() == null || i.getMeasure() != 1) && Constant.NEED_TYPE_FLAG.contains(channel)){
                        if (StringUtils.isBlank(i.getTypeFlag())){
                            return false;
                        }
                    }
                    List<ModelLineOptionVo> optionVos;
                    try {
                        optionVos = modelLineService.modelLinePersonalOptionDefault(i.getModelLineId());
                        if(i.getPriceAddColor() != null && i.getPriceAddColor().intValue() == 1) {
                            String category = "COLOR_EXTERIEUR";
                            List<ModelLineOptionVo> exterieurs = modelLineService.modelLineOption(channel, i.getModelLineId(), category).stream().filter(e -> e.getDefaultConfig() == 1).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(exterieurs)) {
                                optionVos.add(exterieurs.get(0));
                            }
                        }
                    } catch (Exception e) {
                        log.error("查询配置线默认私人定制异常", e);
                        return false;
                    }
                    if (CollectionUtils.isNotEmpty(optionVos)){
                        List<String> optionIds = optionVos.stream().map(ModelLineOptionVo::getOptionId).collect(Collectors.toList());
                        PriceComputeParam computeParam = new PriceComputeParam();
                        computeParam.setOptionIds(optionIds);
                        computeParam.setModelLineId(i.getModelLineId());
                        try {
                            i.setPrice(modelLineService.computePrice(computeParam));
                        } catch (ServiceException e) {
                            log.error("查询配置线价格计算异常", e);
                            return false;
                        }
                    }
                    if (i.getFrontStatus() == null || i.getFrontStatus().intValue() == 0) {
                        return false;
                    }
                    switch (type){
                        case 1:
                            return i.getPersonal() != null && i.getPersonal().intValue() == 1;
                        case 2:
                            return i.getMeasure() != null && i.getMeasure().intValue() == 1 && modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId());
                        case 3:
                            return (i.getMeasure() != null && i.getMeasure().intValue() == 1 && modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId())) ||
                                    (i.getPersonal() != null && i.getPersonal().intValue()  == 1);
                    }
                    return false;
                }).collect(Collectors.toList());
        return successMessage(data);
    }

    @GetMapping({"/modelLine/configs/recommendStyleFix", "/public/modelLine/configs/recommendStyleFix"})
    @ApiOperation("爆款推荐列表混合接口")
    public AjaxMessage<List<RecommendFixVo>> recommendStyleFix(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, @RequestParam String customSeriesId) throws Exception {
        CustomSeriesParam seriesParam = new CustomSeriesParam();
        seriesParam.setCustomSeriesId(customSeriesId);
        List<CarCustomSeries> carCustomSeries = customSeriesService.listCustomSeries(channel, seriesParam);
        if (CollectionUtils.isEmpty(carCustomSeries)){
            throw new ServiceException("参数异常：customSeriesId", "400401", "");
        }
        List<RecommendFixVo> styleVos = styleService.recommendStyleFix(channel, customSeriesId);
        return successMessage(styleVos);
    }


    @GetMapping({"/modelLine/configs/recommendStyleList", "/public/modelLine/configs/recommendStyleList"})
    @ApiOperation("爆款推荐款式列表")
    public AjaxMessage<List<StyleVo>> recommendStyleList(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, @RequestParam String customSeriesId) throws Exception {
        CustomSeriesParam seriesParam = new CustomSeriesParam();
        seriesParam.setCustomSeriesId(customSeriesId);
        List<CarCustomSeries> carCustomSeries = customSeriesService.listCustomSeries(channel, seriesParam);
        if (CollectionUtils.isEmpty(carCustomSeries)){
            throw new ServiceException("参数异常：customSeriesId", "400401", "");
        }
        List<StyleVo> styleVos = styleService.recommendStyleList(channel, customSeriesId);
        return successMessage(styleVos);
    }


    @GetMapping({"/modelLine/recommendCarByStyle", "/public/modelLine/recommendCarByStyle"})
    @ApiOperation("爆款推荐款式车辆列表")
    public AjaxMessage<List<RecommendCarSphereVo>> recommendCarByStyle(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, @RequestParam String styleId) throws Exception {
        CarStyle style = styleService.lambdaQuery().eq(CarStyle::getChannel, Constant.MASTER_CHANNEL).eq(CarStyle::getStyleId, styleId).eq(CarStyle::getDelFlag, 0).one();
        if (style == null){
            throw new ServiceException("参数异常：styleId", "400401", "");
        }
        List<RecommendCarSphereVo> data = styleService.recommendCarByStyle(channel, style);

        return successMessage(data);
    }
}

