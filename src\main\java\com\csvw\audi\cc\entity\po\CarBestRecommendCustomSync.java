package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 库存车配置单订单状态同步
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarBestRecommendCustomSync对象", description="库存车配置单订单状态同步")
public class CarBestRecommendCustomSync extends Model<CarBestRecommendCustomSync> {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    private Long bestRecommendId;

    @ApiModelProperty(value = "配置ID")
    private Long ccid;

    @ApiModelProperty(value = "omd推荐车id")
    private Long recommendModelId;

    @ApiModelProperty(value = "类型（总部推荐车:hq, 经销商推荐车:dealer)")
    private String type;

    @ApiModelProperty(value = "同步状态(0:未同步，1:同步完成，2:同步中)")
    private Integer status;

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    @ApiModelProperty(value = "订单同步OMD状态")
    private String orderOmdStatus;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
