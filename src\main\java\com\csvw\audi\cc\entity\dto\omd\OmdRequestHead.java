package com.csvw.audi.cc.entity.dto.omd;

import lombok.Data;

import java.util.UUID;

@Data
public class OmdRequestHead {
    private String requestId;
    private String sourceApp;
    private String targetApp ;
    private Long transTime;

    public static OmdRequestHead getOmdRequestHead(){
        OmdRequestHead omdRequestHead = new OmdRequestHead();
        omdRequestHead.setRequestId(UUID.randomUUID().toString());
        omdRequestHead.setSourceApp("OneApp");
        omdRequestHead.setTargetApp("OMD");
        omdRequestHead.setTransTime(System.currentTimeMillis());
        return omdRequestHead;
    }
}
