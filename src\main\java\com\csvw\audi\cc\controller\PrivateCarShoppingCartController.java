package com.csvw.audi.cc.controller;


import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.dto.CarShoppingCartDto;
import com.csvw.audi.cc.entity.vo.CarShoppingCartVo;
import com.csvw.audi.cc.service.ICarShoppingCartService;
import com.csvw.sx.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * 购物车 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Api(tags = "购物车-feign")
@RestController
@RequestMapping("/private/api/v1/carShoppingCart")
public class PrivateCarShoppingCartController extends BaseController {

    @Autowired
    private ICarShoppingCartService carShoppingCartService;

    @ApiOperation("删除购物车")
    @PostMapping("/delCarShoppingCart")
    public AjaxMessage<String> delCarShoppingCart(@RequestHeader(value = "X-User-Id", required = false) String userId,
                                                  @RequestBody CarShoppingCartVo carShoppingCartVo) {
        if((carShoppingCartVo.getShoppingCartId() == null || "".equals(carShoppingCartVo.getShoppingCartId())) &&
                StringUtils.isBlank(carShoppingCartVo.getCcid())) {
            return new AjaxMessage<>("01", "失败", "数据ID为空");
        }
        carShoppingCartVo.setUserId(userId);
        List<String> ids = new ArrayList<>();
        ids.add(carShoppingCartVo.getShoppingCartId());
        carShoppingCartVo.setShoppingCartIds(ids);
        carShoppingCartService.delCarShoppingCartByIds(carShoppingCartVo);
        return new AjaxMessage<>("00", "成功", "");
    }

    @ApiOperation("发送邀请积分")
    @PostMapping("/finishInvitationIntegral")
    public AjaxMessage<String> finishInvitationIntegral(@RequestHeader(value = "X-User-Id") String userId,@RequestBody CarShoppingCartDto dto) {
        if(StringUtil.isEmpty(dto.getCcid())) {
            return new AjaxMessage<>("01", "失败", "ccid为空");
        }
        dto.setUserId(userId);
        carShoppingCartService.finishInvitationIntegral(dto);
        return new AjaxMessage<>("00", "成功", "");
    }
}

