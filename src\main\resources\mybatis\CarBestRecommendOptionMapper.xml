<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarBestRecommendOptionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarBestRecommendOption">
        <id column="id" property="id" />
        <result column="best_recommend_id" property="bestRecommendId" />
        <result column="option_id" property="optionId" />
        <result column="option_code" property="optionCode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, best_recommend_id, option_id, option_code, create_time, update_time, del_flag
    </sql>

    <select id="listRecommendOptionIds" resultType="string">
        select option_id from car_best_recommend_Option where best_recommend_id = #{bestRecommendId}
    </select>

</mapper>
