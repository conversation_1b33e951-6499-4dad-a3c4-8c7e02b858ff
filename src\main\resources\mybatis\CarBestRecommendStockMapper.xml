<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarBestRecommendStockMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarBestRecommendStock">
        <id column="id" property="id" />
        <result column="best_recommend_id" property="bestRecommendId" />
        <result column="recommend_model_id" property="recommendModelId" />
        <result column="dealer_net_code" property="dealerNetCode" />
        <result column="sync_stock_num" property="syncStockNum" />
        <result column="stock_num" property="stockNum" />
        <result column="type" property="type" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, best_recommend_id, recommend_model_id, dealer_net_code, sync_stock_num, stock_num, type, create_time, update_time, del_flag
    </sql>

    <update id="plusStockNum">
        update car_best_recommend_stock set stock_num = stock_num + #{stockNum} where id = #{id}
    </update>

    <select id="getRecommendFixStock" parameterType="com.csvw.audi.cc.entity.dto.BestRecommendQueryParam" resultType="int">
        SELECT ifnull(sum(rs.stock_num), 0) FROM `car_best_recommend` r LEFT JOIN `car_best_recommend_stock` rs on r.`best_recommend_id` = rs.`best_recommend_id`
        WHERE rs.`del_flag` = 0 and ((rs.`type` = 'dealer' and rs.`dealer_net_code` = #{dealerCode}) or (rs.`type` = 'hq'))
        and r.best_recommend_id = #{bestRecommendId}
    </select>

    <select id="getRecommendStock" parameterType="com.csvw.audi.cc.entity.dto.BestRecommendQueryParam" resultType="int">
        SELECT ifnull(sum(rs.stock_num), 0) stock_num FROM `car_best_recommend` r LEFT JOIN `car_best_recommend_stock` rs on r.`best_recommend_id` = rs.`best_recommend_id`
        WHERE r.best_recommend_id = #{bestRecommendId} and rs.`del_flag` = 0
        <if test = "dealerCode != null and dealerCode != ''">
            and rs.`dealer_net_code` = #{dealerCode}
        </if>
        <if test = "type != null and type != ''">
            and rs.`type` = #{type}
        </if>
    </select>

</mapper>
