<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarCustomSourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarCustomSource">
        <id column="source_id" property="sourceId" />
        <result column="name" property="name" />
        <result column="type" property="type" />
        <result column="dealer_code" property="dealerCode" />
        <result column="dealer_name" property="dealerName" />
        <result column="app" property="app" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        source_id, name, type, dealer_code, dealer_name, app, create_time
    </sql>

</mapper>
