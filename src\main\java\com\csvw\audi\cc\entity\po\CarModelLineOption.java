package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 配置线配置项
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarModelLineOption对象", description="配置线配置项")
public class CarModelLineOption extends Model<CarModelLineOption> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "配置项id")
    private String optionId;

    @ApiModelProperty(value = "配置项名称")
    private String optionName;

    @ApiModelProperty(value = "配置项权重")
    private Integer optionWeight;

    @ApiModelProperty(value = "配置线id")
    private String modelLineId;

    @ApiModelProperty(value = "默认配置项")
    private Integer defaultConfig;

    @ApiModelProperty(value = "配置线配置项状态（0:无，1:标准装备，2:可选装备）")
    @TableField("`status`")
    private Integer status;

    @ApiModelProperty(value = "配置线配置项标准状态（0:无，1:标准装备，2:可选装备）")
    @TableField("`condition`")
    private Integer condition;

    @ApiModelProperty(value = "装备图片")
    private String imageUrl;

    @ApiModelProperty(value = "装备详情图片")
    private String imageUrlDetail;

    @ApiModelProperty(value = "装备列表图片")
    private String imageUrlList;

    @ApiModelProperty(value = "数据渠道")
    private String channel;

    private String equipmentRights;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
