<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarBestRecommendCustomSyncMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarBestRecommendCustomSync">
        <id column="id" property="id" />
        <result column="best_recommend_id" property="bestRecommendId" />
        <result column="ccid" property="ccid" />
        <result column="recommend_model_id" property="recommendModelId" />
        <result column="type" property="type" />
        <result column="status" property="status" />
        <result column="order_id" property="orderId" />
        <result column="order_status" property="orderStatus" />
        <result column="order_omd_status" property="orderOmdStatus" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, best_recommend_id, ccid, recommend_model_id, type, status, order_id, order_status, order_omd_status, create_time, update_time, del_flag
    </sql>

</mapper>
