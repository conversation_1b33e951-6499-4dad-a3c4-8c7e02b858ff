<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarCustomEntryPointMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarCustomEntryPoint">
        <id column="id" property="id" />
        <result column="channel" property="channel" />
        <result column="description" property="description" />
        <result column="entry_point" property="entryPoint" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, channel, description, entry_point
    </sql>

</mapper>
