package com.csvw.audi.cc.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/3 13:58
 * @description 省市列表结构
 */
@Data
public class SvcdProvinceCityListDto {

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "省份代码")
    private String provinceCode;

    @ApiModelProperty(value = "省内城市")
    private List<SvcdInnerCity> children;

    @Data
    public static class SvcdInnerCity {
        @ApiModelProperty(value = "城市名称")
        private String cityName;

        @ApiModelProperty(value = "城市代码")
        private String cityCode;

    }
}
