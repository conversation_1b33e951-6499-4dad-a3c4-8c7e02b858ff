package com.csvw.audi.cc.entity.dto.okapi;

import lombok.Data;

import java.util.List;

@Data
public class LabelInterieurResDto {
    private String brandCode;
    private String subBrandCode;
    private String modelUnicode;
    private String modelUnicodeShort;
    private List<InterieurResDto> children;

    @Data
    public static class InterieurResDto {
        private String interiorCode;
        private String labelCode;
        private String labelNameZh;
        private String familyCode;
        private String featureCode;
        private String externalFeatureNameZh;
        private String externalFeatureNameEn;
    }

}
