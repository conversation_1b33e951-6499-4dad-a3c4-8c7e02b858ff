package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 推荐车
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarRecommend对象", description="推荐车")
public class CarRecommend extends Model<CarRecommend> {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    private String customSeriesId;

    private String modelLineId;

    @ApiModelProperty(value = "accb audicode")
    private String audiCode;

    @ApiModelProperty(value = "accb audicode")
    private String audiCodeContent;

    @ApiModelProperty(value = "备注")
    private String remark;

    private String channel;

    @ApiModelProperty(value = "权重")
    private Integer weight;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
