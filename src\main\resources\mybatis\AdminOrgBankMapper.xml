<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.AdminOrgBankMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.AdminOrgBank">
        <id column="org_bank_id" property="orgBankId" />
        <result column="dealer_code" property="dealerCode" />
        <result column="dealer_name" property="dealerName" />
        <result column="dealer_full_name" property="dealerFullName" />
        <result column="bank_code" property="bankCode" />
        <result column="bank_name" property="bankName" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        org_bank_id, dealer_code, dealer_name, dealer_full_name, bank_code, bank_name, created_time, updated_time
    </sql>

    <select id="getOrgBankList" resultType="com.csvw.audi.cc.entity.vo.AdminOrgBankVo" parameterType="com.csvw.audi.cc.entity.dto.AdminOrgBankDto">
        SELECT
            org_bank.org_bank_id,
            org_bank.dealer_code,
            org_bank.dealer_name,
            org_bank.dealer_full_name,
            org_bank.bank_code,
            org_bank.bank_name,
            org_bank.del_flag,
            org_bank.confirm_text,
            org_bank.created_time,
            org_bank.updated_time
        FROM
            admin_org_bank org_bank left join admin_org_bank_order org_bank_order on org_bank.bank_code = org_bank_order.bank_code
        WHERE
        1 = 1
            AND org_bank.del_flag = '0'
        <if test="dealerCode != null and dealerCode != ''">
            AND org_bank.dealer_code =  #{dealerCode,jdbcType=VARCHAR}
        </if>
        <if test="dealerName != null and dealerName != ''">
            AND org_bank.dealer_name LIKE concat(concat("%",#{dealerName,jdbcType=VARCHAR}),"%")
        </if>
        <if test="bankCode != null and bankCode != ''">
            AND org_bank.bank_code =  #{bankCode,jdbcType=VARCHAR}
        </if>
        <if test="bankName != null and bankName != ''">
            AND org_bank.bank_name LIKE concat(concat("%",#{bankName,jdbcType=VARCHAR}),"%")
        </if>
        order by isnull(org_bank_order.bank_order),org_bank_order.bank_order
    </select>

</mapper>
