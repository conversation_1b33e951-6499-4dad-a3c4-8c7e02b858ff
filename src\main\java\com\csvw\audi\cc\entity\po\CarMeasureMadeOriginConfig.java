package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 半订制化车辆原始配置
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarMeasureMadeOriginConfig对象", description="半订制化车辆原始配置")
public class CarMeasureMadeOriginConfig extends Model<CarMeasureMadeOriginConfig> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "半订制车辆id")
      @TableId(value = "measure_origin_id", type = IdType.ASSIGN_ID)
    private Long measureOriginId;

    @ApiModelProperty(value = "omd记录ID")
    private Long semiCustomizationModelId;

    @ApiModelProperty(value = "车型编码")
    private String modelCode;

    @ApiModelProperty(value = "配置线编码")
    private String accbTypeCode;

    @ApiModelProperty(value = "车型版本号")
    private String modelVersion;

    @ApiModelProperty(value = "车型年款")
    private String modelYear;

    @ApiModelProperty(value = "内饰code")
    private String interiorCode;

    @ApiModelProperty(value = "外饰code")
    private String colorCode;

    @ApiModelProperty(value = "pr列表，逗号分隔")
    private String prList;

    @ApiModelProperty(value = "产品编码")
    private String classCode;

    @ApiModelProperty(value = "库存数量")
    private Integer stockNum;

    @ApiModelProperty(value = "唯一码")
    private String uniqueCode;

    @ApiModelProperty(value = "cc唯一码,高定转半定比较")
    private String ccUniqueCode;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.measureOriginId;
    }

}
