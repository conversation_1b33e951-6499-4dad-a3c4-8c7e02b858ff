package com.csvw.audi.cc.entity.dto;

import lombok.Data;

import java.util.List;

@Data
public class CopOrderDto {

    private Long orderId;

    private String orderType;

    private List<Product> productList;

    private BaseInfo baseInfo;

    private DealerInfo dealerInfo;

    private PayInfo payInfo;

    private String carCustomId;

    @Data
    public static class Product {
        private Long orderItemId;
        private Integer quantity;
        private String productName;
    }

    @Data
    public static class BaseInfo {

        private String buyType;
        private String buyerMobile;
        private String buyerType;
        private String buyerUid;
        private String carBuyerMobile;
        private String carBuyerName;

        private String carOwnerCertificateNumber;
        private String carOwnerCertificateType;
        private String carOwnerMobile;
        private String carOwnerName;
        private String orderStatus;
        private String enterpriseName;
        private String moreContact;
    }

    @Data
    public static class DealerInfo {
        private String address;
        private String carLicenseCityCode;
        private String carLicenseCityName;
        private String contact;
        private String dealerCityCode;
        private String dealerCityName;
        private String dealerId;
        private String dealerName;
        private String salesPhone;
    }

    @Data
    public static class PayInfo{
         private String activityId;//:null,
         private String activityName;//:null,
         private String credit;//:0,
         private String deliveryFee;//:0,
         private String discountAmount;//:0,
         private String outPaySn;//
         private String payAccount;//:null,
         private String payAmount;//:2,
         private String paySn;//
         private String payStatusName;//:;//支付成功;//,
         private String payTime;//:;//2021-12-07 11:11:12;//,
         private String payType;//:;
         private String payTypeName;//
         private String totalAmount;//:2,
         private String totalCreditAmount;//:
        
    }

}
