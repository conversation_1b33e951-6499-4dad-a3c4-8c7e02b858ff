package com.csvw.audi.cc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.entity.po.CarConfigImage;
import com.csvw.audi.cc.mapper.CarConfigImageMapper;
import com.csvw.audi.cc.service.ICarConfigImageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 配置线角度图 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
@Service
public class CarConfigImageServiceImpl extends ServiceImpl<CarConfigImageMapper, CarConfigImage> implements ICarConfigImageService {

    @Override
    public String getCcHeadImageUrl(String channel, String modelLineId, String exterieurCode) {
        LambdaQueryWrapper<CarConfigImage> imageQ = new LambdaQueryWrapper<>();
        imageQ.eq(CarConfigImage::getModelLineId, modelLineId)
                .eq(CarConfigImage::getChannel, Constant.MASTER_CHANNEL)
                .eq(CarConfigImage::getExterieurCode, exterieurCode)
                .eq(CarConfigImage::getType, "front")
                .eq(CarConfigImage::getDelFlag, 0);
        CarConfigImage carConfigImage = this.getOne(imageQ);
        return carConfigImage==null?"":carConfigImage.getImageUrl();
    }

    @Override
    public String getCcLeftImageUrl(String channel, String modelLineId, String exterieurCode, String radCode) {
        LambdaQueryWrapper<CarConfigImage> imageQ = new LambdaQueryWrapper<>();
        imageQ.eq(CarConfigImage::getModelLineId, modelLineId)
                .eq(CarConfigImage::getChannel, Constant.MASTER_CHANNEL)
                .eq(CarConfigImage::getExterieurCode, exterieurCode)
//                .eq(CarConfigImage::getRadCode, radCode) // 当前版本忽略轮毂，待定
                .eq(CarConfigImage::getType, "left")
                .eq(CarConfigImage::getDelFlag, 0);
        CarConfigImage carConfigImage = this.getOne(imageQ);
        return carConfigImage==null?"":carConfigImage.getImageUrl();
    }

    @Override
    public List<String> getDiscoveryViewExterieur(String channel, String modelLineId, String exterieurCode) {
        LambdaQueryWrapper<CarConfigImage> imageQ = new LambdaQueryWrapper<>();
        imageQ.eq(CarConfigImage::getModelLineId, modelLineId)
                .eq(CarConfigImage::getChannel, Constant.MASTER_CHANNEL)
                .eq(CarConfigImage::getExterieurCode, exterieurCode)
//                .eq(CarConfigImage::getRadCode, radCode) // 当前版本忽略轮毂，待定
                .eq(CarConfigImage::getType, "discovery-view")
                .eq(CarConfigImage::getDelFlag, 0);
        List<CarConfigImage> carConfigImage = this.list(imageQ);
        return carConfigImage.stream().map(i->i.getImageUrl()).collect(Collectors.toList());
    }

    @Override
    public List<String> getDiscoveryViewInterieur(String channel, String modelLineId, String interieurCode) {
        LambdaQueryWrapper<CarConfigImage> imageQ = new LambdaQueryWrapper<>();
        imageQ.eq(CarConfigImage::getModelLineId, modelLineId)
                .eq(CarConfigImage::getChannel, Constant.MASTER_CHANNEL)
                // todo 添加内饰编码字段
                .eq(CarConfigImage::getExterieurCode, interieurCode)
                .eq(CarConfigImage::getType, "discovery-view")
                .eq(CarConfigImage::getDelFlag, 0);
        List<CarConfigImage> carConfigImage = this.list(imageQ);
        return carConfigImage.stream().map(i->i.getImageUrl()).collect(Collectors.toList());
    }
}
