package com.csvw.audi.cc.component.kafkaService.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csvw.audi.cc.component.kafkaService.SvcdKafkaService;
import com.csvw.audi.cc.entity.po.SvcdCompany;
import com.csvw.audi.cc.mapper.SvcdCompanyMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service("svcdCompanyKafkaService")
public class SvcdCompanyKafkaServiceImpl implements SvcdKafkaService {

    @Autowired
    private SvcdCompanyMapper svcdCompanyMapper;

    @Override
    public void insertData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdCompany company = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdCompany.class);
        company.setCreatedAt(nowDate);
        company.setUpdatedAt(nowDate);
        company.insert();
    }

    @Override
    public void updateData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdCompany company = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdCompany.class);

        QueryWrapper<SvcdCompany> companyQusery = new QueryWrapper<>();
        companyQusery.eq("company_code",company.getCompanyCode());
        List<SvcdCompany> list = company.selectList(companyQusery);
        if(list == null || list.size() == 0) {
            company.setCreatedAt(nowDate);
            company.setUpdatedAt(nowDate);
            company.insert();
        } else {
            company.setCompanyId(list.get(0).getCompanyId());
            company.setUpdatedAt(nowDate);
            company.updateById();
        }
    }
}
