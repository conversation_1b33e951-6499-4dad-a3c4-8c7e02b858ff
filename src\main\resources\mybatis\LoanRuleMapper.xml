<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.LoanRuleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.LoanRule">
        <id column="loan_rule_id" property="loanRuleId" />
        <result column="loan_agency" property="loanAgency" />
        <result column="min_payment_ratio" property="minPaymentRatio" />
        <result column="interest_rates" property="interestRates" />
        <result column="weight" property="weight" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        loan_rule_id, loan_agency, min_payment_ratio, interest_rates, weight, create_time, update_time
    </sql>

    <select id="queryModelLineLoanRule" resultType="com.csvw.audi.cc.entity.po.LoanRule">
        SELECT r.* FROM `loan_rule` r LEFT JOIN `model_line_loan_rule` mlr on r.`loan_rule_id` = mlr.`loan_rule_id`
        <where>
            r.del_flag = 0
            <if test="modelLineId != null and modelLineId != ''">
                and mlr.`model_line_id` = #{modelLineId}
            </if>
            <if test="loanAgencyCodes != null">
                and r.`loan_agency_code` in
                <foreach collection="loanAgencyCodes" item="item" open="(" close=")" separator=",">
                #{item}
                </foreach>
            </if>
            <if test="type != null">
                and r.`type` = #{type}
            </if>
        </where>
        order by r.weight
    </select>

</mapper>
