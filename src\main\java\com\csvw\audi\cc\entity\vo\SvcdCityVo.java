package com.csvw.audi.cc.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 城市信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Data
public class SvcdCityVo {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "城市名称")
    private String name;

    @ApiModelProperty(value = "城市代码")
    private String cityCode;

    @ApiModelProperty(value = "省份代码")
    private String provinceCode;

    @ApiModelProperty(value = "简称")
    private String simpleName;

    @ApiModelProperty(value = "速查码")
    private String quickCode;

    @ApiModelProperty(value = "城市拼音")
    private String cityEn;

    @ApiModelProperty(value = "城市首字母-大写")
    private String capInitials;

}
