package com.csvw.audi.cc.entity.dto.omd;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class AmsModelQueryRecordParam {

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime queryTime;
    private String orgCode;
    private String userUid;
    private String seriesCode;
    private String seriesName;
    private String modelCode;
    private String modelName;
    private String colorCode;
    private String colorName;
    private String colorInsideCode;
    private String colorInsideName;
    private List<Idzs> idzs;

    @Data
    public static class Idzs {
        private String idzCode;
        private String idzName;
    }

}
