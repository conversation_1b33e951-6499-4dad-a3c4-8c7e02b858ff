package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 配置线拓展标识
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarModelLineExt对象", description="配置线拓展标识")
public class CarModelLineExt extends Model<CarModelLineExt> {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "配置线id")
    private String modelLineId;

    @ApiModelProperty(value = "处理价格标识，默认处理（1:不处理）")
    private Integer priceUnhandle;

    @ApiModelProperty(value = "渠道")
    private String channel;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
