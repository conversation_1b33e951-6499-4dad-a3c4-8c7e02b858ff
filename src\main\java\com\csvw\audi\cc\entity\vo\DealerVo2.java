package com.csvw.audi.cc.entity.vo;

import com.csvw.audi.cc.entity.po.SvcdChannelOrganizationPolicy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DealerVo2 {

    @ApiModelProperty(value = "门店编号")
    private String dealerCode;

    @ApiModelProperty(value = "代理商推荐码")
    private String remCode;

    @ApiModelProperty(value = "门店名称-简称")
    private String dealerName;

    @ApiModelProperty(value = "门店名称-全称")
    private String dealerFullName;

    @ApiModelProperty(value = "渠道商英文名称")
    private String dealerEnName;

    @ApiModelProperty(value = "门店地址")
    private String dealerAdrress;

    @ApiModelProperty(value = "门店图片地址(封面图)")
    private String imageUrl;

    @ApiModelProperty(value = "门店图片地址(略缩图)")
    private String thumbnailUrl;

    @ApiModelProperty(value = "门店联系人")
    private String dealerContacts;

    @ApiModelProperty(value = "门店联系电话/销售热线")
    private String dealerPhone;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "距离（单位：米）")
    private Double distance;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "省份代码")
    private String provinceCode;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "城市代码")
    private String cityCode;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "区域代码")
    private String areaCode;

    @ApiModelProperty(value = "是否默认返回总部（1:是 0:否）")
    private Integer defaultHeadquarters;

    @ApiModelProperty(value = "售后救援服务联系电话")
    private String rescuePhone;

    @ApiModelProperty(value = "售后服务联系电话")
    private String servicePhone;

    @ApiModelProperty(value = "渠道商类型（1:服务商 2:代理商）  新增1,2 表示两个")
    private String dealerType;

    @ApiModelProperty(value = "业务状态（1:意向 2:筹备 3:开业 4:出网 5:PopUP 6:试运营 7:意向终止 8:预营业）")
    private String businessStatus;

    @ApiModelProperty(value = "经度(临时值,不做为有效返回结果)")
    private String afterSalesLongitude;

    @ApiModelProperty(value = "纬度(临时值,不做为有效返回结果)")
    private String afterSalesLatitude;

    @ApiModelProperty(value = "门店地址")
    private String afterSalesAdrress;

    @ApiModelProperty(value = "展厅形态（1:都市店 2:奥迪之城 3:奥迪进取汇/品牌体验中心）4:用户中心 5:轻量版都市店")
    private String exhibitionHallForm;

    @ApiModelProperty(value = "上汽奥迪维修级别/是否新能源车售后(B：否，A：是)")
    private String saicAudiMaintenanceLevel;

    @ApiModelProperty(value = "行政区划代码")
    private String regionCode;

    @ApiModelProperty(value = "一汽高压电池维修(1:是 0:否)")
    private String fawAudiBatteryMaintenanceCenter;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "工作日营业时间")
    private String workingDay;

    @ApiModelProperty(value = "非工作日营业时间")
    private String nonWorkingDay;

    @ApiModelProperty(value = "服务营业时间备注")
    private String serviceWorkingDayRemarks;

    @ApiModelProperty(value = "工作日服务营业时间")
    private String serviceWorkingDay;

    @ApiModelProperty(value = "非工作日服务营业时间")
    private String nonServiceWorkingDay;

    @ApiModelProperty(value = "评价分数")
    private String evaluateScore;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "24小时热线")
    private String hotPhone24;

    private String controlStatus;

    private String dealerAfterSaleType;

    private String serviceCode;

    private List<SvcdChannelOrganizationPolicy> policyList;
}
