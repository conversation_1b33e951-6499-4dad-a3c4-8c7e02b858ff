package com.csvw.audi.cc.entity.vo;

import com.csvw.audi.cc.entity.dto.OptionRelDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MeasureOptionVo implements Serializable {

    @ApiModelProperty(value = "配置项id")
    private String optionId;

    @ApiModelProperty(value = "配置项名称")
    private String optionName;

    @ApiModelProperty(value = "配置编码")
    private String optionCode;

    @ApiModelProperty(value = "配置种类编码")
    private String category;

    @ApiModelProperty(value = "配置项类型")
    private String optionType;

    @ApiModelProperty(value = "配置项类型")
    private String optionTypeName;

    @ApiModelProperty(value = "装备图片")
    private String imageUrl;

    @ApiModelProperty(value = "装备详情图片")
    private String imageUrlDetail;

    @ApiModelProperty(value = "装备列表图片")
    private String imageUrlList;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "默认配置项")
    private Integer defaultConfig;

    @ApiModelProperty(value = "配置线id")
    @JsonIgnore
    private String modelLineId;

    @JsonIgnore
    private String channel;

    @JsonIgnore
    private Integer delFlag;

    @JsonIgnore
    private String packageId;

    @ApiModelProperty(value = "价格")
    private Object price;

    @ApiModelProperty(value = "配置项详情")
    private List<CarOptionDetailVo> optionDetailList;

    @ApiModelProperty(value = "选装包PR列表")
    private List<MeasureOptionVo> packetItems;

}
