package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.dto.SnapshotUpdateDto;
import com.csvw.audi.cc.entity.dto.vwcc.CarCustomDetail;
import com.csvw.audi.cc.entity.po.CarCustom;
import com.csvw.audi.cc.entity.po.CarCustomSnapshot;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-14
 */
public interface ICarCustomSnapshotService extends IService<CarCustomSnapshot> {

    @Transactional
    Long updateSnapshotCarCustom(Long ccid, SnapshotUpdateDto updateDto, LocalDateTime dataTime) throws Exception;

    @Transactional
    Long bindUserSnapshotCarCustom(Long ccid, String userId, String userMobile, LocalDateTime dataTime) throws Exception;

    Long snapshotCarCustom(CarCustom carCustom) throws Exception;

    Long snapshotCarCustom(CarCustom carCustom, LocalDateTime dataTime) throws Exception;

    CarCustomDetail snapshotFrontDetail(String channel, CarCustom carCustom) throws Exception;

    void historySnapshot() throws InterruptedException;

    Long snapshotFromOld(Long oldCcid, Long newCcid) throws Exception;
}
