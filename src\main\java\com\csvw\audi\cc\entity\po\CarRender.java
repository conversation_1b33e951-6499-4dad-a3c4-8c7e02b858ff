package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * SC车辆渲染图
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarRender对象", description="SC车辆渲染图")
public class CarRender extends Model<CarRender> {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "详图")
    private String preview;

    @ApiModelProperty(value = "配置")
    private String config;

    @ApiModelProperty(value = "车系")
    private String modelCode;

    @ApiModelProperty(value = "车型")
    private String typeCode;

    @ApiModelProperty(value = "外饰")
    private String exteriorCode;

    @ApiModelProperty(value = "配置")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
