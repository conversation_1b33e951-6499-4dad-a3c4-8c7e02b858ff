package com.csvw.audi.cc.entity.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
public class StyleVo implements Serializable {

    @ApiModelProperty(value = "款式id")
    private String styleId;

    @ApiModelProperty(value = "款式名称")
    private String styleName;

    @ApiModelProperty(value = "优惠")
    private String preferential;

    @ApiModelProperty(value = "展示图片")
    private String imageUrl;

    @ApiModelProperty(value = "特别描述")
    private String description;

    @ApiModelProperty(value = "爆款标识")
    private Integer recommendStatus;

    private Object price;

    private List<ModelLineVo> modelLineList;

    @ApiModelProperty("销售亮点")
    private List<String> sellBlips;

    @JsonIgnore
    private Set<String> engines;

    private Integer personal=0;

    private Integer measure=0;

    private Integer version;

    private String modelYear;

    private Integer stockout;

}
