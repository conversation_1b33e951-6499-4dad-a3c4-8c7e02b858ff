package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.po.CarBestRecommendOption;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 畅销推荐车配置项 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-08
 */
public interface CarBestRecommendOptionMapper extends BaseMapper<CarBestRecommendOption> {

    List<String> listRecommendOptionIds(@Param("bestRecommendId") Long bestRecommendId);
}
