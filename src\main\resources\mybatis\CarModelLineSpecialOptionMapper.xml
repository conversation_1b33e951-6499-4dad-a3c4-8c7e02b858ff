<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarModelLineSpecialOptionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarModelLineSpecialOption">
        <id column="id" property="id" />
        <result column="model_line_id" property="modelLineId" />
        <result column="option_id" property="optionId" />
        <result column="option_code" property="optionCode" />
        <result column="category" property="category" />
        <result column="weight" property="weight" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, model_line_id, option_id, option_code, category, weight, create_time, update_time, del_flag
    </sql>

</mapper>
