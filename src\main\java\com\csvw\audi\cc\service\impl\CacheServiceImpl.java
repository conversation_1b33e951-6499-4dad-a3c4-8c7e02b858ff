package com.csvw.audi.cc.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CacheServiceImpl {
    @CacheEvict(cacheNames = {"cc_cache", "cc_cache_level2_best_recommend"}, allEntries = true)
    public void cacheEvictCc(){
        log.info("清除缓存，cc_cache");
        return ;
    }

    @CacheEvict(cacheNames = {"cc_cache_level2_best_recommend"}, allEntries = true)
    public void cacheBestRecommendEvictCc() {
        log.info("清除缓存，cc_cache_level2_best_recommend");
        return ;
    }
}
