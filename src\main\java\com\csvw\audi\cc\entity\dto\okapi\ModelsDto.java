package com.csvw.audi.cc.entity.dto.okapi;

import lombok.Data;

import java.util.List;

@Data
public class ModelsDto {
    private String brandCode;
    private String subBrandCode;
    private String seriesCode;
    private String externalModelNameZh;
    private String externalModelYear;
    private String externalModelYearDesc;
    private String externalSeriesName;
    private String externalFullModelName;
    private Integer modelWeight;
    private List<ModelInfoDto> children;
    @Data
    public static class ModelInfoDto {
        private String subModelRuleId;
        private String modelUnicode;
        private String modelUnicodeShort;
        private String modelCode;
        private String modelYear;
        private String modelVersion;
        private List<ParamInfo> paramChildren;
        private List<ModelLabelDto> labelChildren;
    }

    @Data
    public static class ModelLabelDto{
        private String salesLabelCode;
        private String salesLabelZh;
        private String effectiveDate;
        private String expireDate;
    }

    @Data
    public static class ParamInfo {
        private String prodCode;
        private String prodValue;
        private String prodNameZh;
        private String prodNameEn;
        private String paramSubCategory;
    }
}
