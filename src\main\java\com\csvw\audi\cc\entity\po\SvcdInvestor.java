package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 投资人信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SvcdInvestor对象", description="投资人信息")
public class SvcdInvestor extends Model<SvcdInvestor> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "数据id")
      @TableId(value = "investor_id", type = IdType.AUTO)
    private Long investorId;

    @ApiModelProperty(value = "投资人代码")
    private String code;

    @ApiModelProperty(value = "投资人全称")
    private String investorName;

    @ApiModelProperty(value = "投资人企业类型(国有企业 /上市公司/股份制/有限责任公司/合资公司/民营企业/个体工商业)")
    private Long enterpriseType;

    @ApiModelProperty(value = "注册资本")
    private String registCapital;

    @ApiModelProperty(value = "公司地址")
    private String companyAddress;

    @ApiModelProperty(value = "董事长姓名")
    private String chairmanName;

    @ApiModelProperty(value = "董事长电话")
    private String chairmanPhone;

    @ApiModelProperty(value = "法人代表姓名")
    private String legalRepresentativeName;

    @ApiModelProperty(value = "法人代表电话")
    private String legalRepresentativePhone;

    @ApiModelProperty(value = "现有 Audi 数量")
    private Long audiNum;

    @ApiModelProperty(value = "授权代理商筹建联系人")
    private String dealerUserName;

    @ApiModelProperty(value = "授权代理商筹建联系人电话")
    private String dealerUserPhone;

    @ApiModelProperty(value = "授权代理商筹建联系人邮件")
    private String dealerUserEmail;

    @ApiModelProperty(value = "是否删除（0:未删除 1:已删）")
    private Long deleted;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;


    @Override
    protected Serializable pkVal() {
        return this.investorId;
    }

}
