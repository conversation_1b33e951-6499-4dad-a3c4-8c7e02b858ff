<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.ParameterMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.Parameter">
        <id column="parameter_id" property="parameterId" />
        <result column="parameter_name" property="parameterName" />
        <result column="parameter_value" property="parameterValue" />
        <result column="parameter_type" property="parameter_type" />
        <result column="type_code" property="typeCode" />
        <result column="model_year" property="modelYear" />
        <result column="weight" property="weight" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        parameter_id, parameter_name, parameter_value, parameter_type, type_code, model_year, weight, remark
    </sql>

</mapper>
