<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.SvcdSalesAgentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.SvcdSalesAgent">
        <id column="sales_agent_id" property="salesAgentId" />
        <result column="dealer_code" property="dealerCode" />
        <result column="recruitment_manager" property="recruitmentManager" />
        <result column="email" property="email" />
        <result column="exhibition_hall_form" property="exhibitionHallForm" />
        <result column="exhibition_hall_level" property="exhibitionHallLevel" />
        <result column="lease_area" property="leaseArea" />
        <result column="used_area" property="usedArea" />
        <result column="exhibition_hall_market" property="exhibitionHallMarket" />
        <result column="exhibition_hall_address" property="exhibitionHallAddress" />
        <result column="deliver_space_market" property="deliverSpaceMarket" />
        <result column="deliver_space_address" property="deliverSpaceAddress" />
        <result column="parking_space_num" property="parkingSpaceNum" />
        <result column="dealer_general_manager" property="dealerGeneralManager" />
        <result column="dealer_general_manager_phone" property="dealerGeneralManagerPhone" />
        <result column="dealer_general_manager_email" property="dealerGeneralManagerEmail" />
        <result column="reply_confirm_date" property="replyConfirmDate" />
        <result column="loi_sign_date" property="loiSignDate" />
        <result column="project_confirm_date" property="projectConfirmDate" />
        <result column="scene_disclose_date" property="sceneDiscloseDate" />
        <result column="scene_start_date" property="sceneStartDate" />
        <result column="scene_check_date" property="sceneCheckDate" />
        <result column="hardware_check_confirm_date" property="hardwareCheckConfirmDate" />
        <result column="organization_check_date" property="organizationCheckDate" />
        <result column="digital_equipment_check_date" property="digitalEquipmentCheckDate" />
        <result column="charge_check_date" property="chargeCheckDate" />
        <result column="contract_take_effect_date" property="contractTakeEffectDate" />
        <result column="contract_effect_date" property="contractEffectDate" />
        <result column="intention_cancel_date" property="intentionCancelDate" />
        <result column="out_accept_date" property="outAcceptDate" />
        <result column="out_date" property="outDate" />
        <result column="sales_phone" property="salesPhone" />
        <result column="after_sale_code" property="afterSaleCode" />
        <result column="spare1" property="spare1" />
        <result column="spare2" property="spare2" />
        <result column="spare3" property="spare3" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        sales_agent_id, dealer_code, recruitment_manager, email, exhibition_hall_form, exhibition_hall_level, lease_area, used_area, exhibition_hall_market, exhibition_hall_address, deliver_space_market, deliver_space_address, parking_space_num, dealer_general_manager, dealer_general_manager_phone, dealer_general_manager_email, reply_confirm_date, loi_sign_date, project_confirm_date, scene_disclose_date, scene_start_date, scene_check_date, hardware_check_confirm_date, organization_check_date, digital_equipment_check_date, charge_check_date, contract_take_effect_date, contract_effect_date, intention_cancel_date, out_accept_date, out_date, sales_phone, after_sale_code, spare1, spare2, spare3, deleted
    </sql>

</mapper>
