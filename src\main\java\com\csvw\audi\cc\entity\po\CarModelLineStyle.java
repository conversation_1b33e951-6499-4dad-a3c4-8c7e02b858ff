package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;


@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarModelLineStyle对象", description="配置线款式关联")
public class CarModelLineStyle extends Model<CarModelLineStyle> {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "配置线id")
    private String modelLineId;

    private String styleId;

    @ApiModelProperty(value = "1 款式默认配置")
    private Integer defaultConfig;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
