<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarRecommendMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarRecommend">
        <id column="id" property="id" />
        <result column="custom_series_id" property="customSeriesId" />
        <result column="model_line_id" property="modelLineId" />
        <result column="audi_code" property="audiCode" />
        <result column="remark" property="remark" />
        <result column="weight" property="weight" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, custom_series_id, audi_code, remark, weight, del_flag
    </sql>

</mapper>
