<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarParameterMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarParameter">
        <id column="id" property="id" />
        <result column="parameter_id" property="parameterId" />
        <result column="parameter_name" property="parameterName" />
        <result column="parameter_type" property="parameterType" />
        <result column="parameter_type_name" property="parameterTypeName" />
        <result column="weight" property="weight" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parameter_id, parameter_name, parameter_type, parameter_type_name, weight, create_time, update_time, del_flag
    </sql>

</mapper>
