spring:
  application:
    name: ${APPLICATION_NAME:audi-car-config} #服务编码，（需要修改）
  profiles:
    active: ${PROFILES_ACTIVE:local} #环境选择，默认选择本地开发环境，部署时微服务管理平台会默认改成default
  cloud:
    config:
      uri: ${CONFIG_URI:********************************}    
      fail-fast: false #连不上配置中心，快速失败，程序停止（不可修改）
      discovery:
        enabled: false #通过注册中心寻找配置中心，注册中心实现高可用（不可修改）
        service-id: kmsp-kit-config #注册中心服务编码（不可修改）

eureka:
  instance:
    preferIpAddress: true #IP优先（不建议修改）
    hostname: ${spring.cloud.client.ip-address} #（不建议修改）
    instance-id: ${spring.application.name}:${spring.cloud.client.ip-address}:${server.port:8080} #（不建议修改）
    metadataMap:
      user.name: ${spring.security.user.name}
      user.password: ${spring.security.user.password}
  client:
    fetchRegistry: true  #如果不通过注册中心调用其他服务，可以设置为false
    serviceUrl:
      defaultZone: ${DISCOVERY_URI:********************************/eureka/}

logging:
  path: logs #日志路径（不建议修改）
  file: info.log
  level:
    com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver: WARN

---
spring:
  profiles: local
  cloud:
    config:
      enabled: false # 如果使用配置中心的配置，可以改为true
      discovery.enabled: ${spring.cloud.config.enabled}

eureka:
  client:
    enabled: false #如果需要访问集群里的集群，可以改为true
    register-with-eureka: true #本地开发，自己的实例一般不要注册到开发环境里