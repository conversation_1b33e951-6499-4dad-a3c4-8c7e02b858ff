package com.csvw.audi.cc.controller.svcd;

import java.util.List;
import com.csvw.audi.cc.service.ISvcdUserImageAssistant;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;

/**
 * <AUTHOR>
 * @date 2023/03/21 13:25
 * @description svcd图片转存
 */
@RestController
@RequestMapping("/private/SvcdUserImageAssistant")
public class PrivateSvcdUserImageAssistantController extends BaseController {

    @Autowired
    private ISvcdUserImageAssistant svcdUserImageAssistant;

    @GetMapping("/findAndStorageSvcdUserImage")
    public AjaxMessage
        findAndStorageSvcdUserImage(@RequestParam(value = "svcdUserUIDs", required = false) List<String> svcdUserUIDs) {
        if (CollectionUtils.isEmpty(svcdUserUIDs)) {
            svcdUserImageAssistant.findAndStorageSvcdUserImage(null);
        } else {
            svcdUserImageAssistant.findAndStorageSvcdUserImage(svcdUserUIDs);
        }
        return successMessage("操作完成");
    }
}
