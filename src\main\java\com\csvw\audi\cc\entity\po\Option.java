package com.csvw.audi.cc.entity.po;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="Option对象", description="")
@TableName("`option`")
public class Option extends Model<Option> {

    private static final long serialVersionUID=1L;

      @TableId(value = "option_id", type = IdType.ASSIGN_ID)
    private Long optionId;

    @ApiModelProperty(value = "accb option id")
    private String accbOptionId;

    @ApiModelProperty(value = "详图")
    private String preview;

    @ApiModelProperty(value = "略图")
    private String thumbnail;

    @ApiModelProperty(value = "code")
    private String code;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "类型")
    private String category;

    @ApiModelProperty(value = "装备类别,OPTIONAL_EQUIPMENT: 选装，STANDARD_EQUIPMENT: 标准装备")
    private String classification;


    @Override
    protected Serializable pkVal() {
        return this.optionId;
    }

}
