package com.csvw.audi.cc.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DealerDto {

    @ApiModelProperty(value = "门店编号")
    private String dealerCode;

    @ApiModelProperty(value = "门店名称-简称")
    private String dealerName;

    @ApiModelProperty(value = "门店名称-全称")
    private String dealerFullName;

    @ApiModelProperty(value = "门店联系电话")
    private String dealerPhone;

    @ApiModelProperty(value = "经度")
    private Double longitude;

    @ApiModelProperty(value = "纬度")
    private Double latitude;

    @ApiModelProperty(value = "省份代码")
    private String provinceCode;

    @ApiModelProperty(value = "城市代码")
    private String cityCode;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "是否默认返回总部（1:是 0:否）")
    private Integer defaultHeadquarters;

    @ApiModelProperty(value = "合并检索(电话/门店简称/门店全称/公司名称)")
    private String phoneOrName;

    @ApiModelProperty(value = "检索类型（0:全部 1:服务商 2:代理商）")
    private Integer searchType;

    @ApiModelProperty(value = "服务商类型（1:销售+售后绑定 2:独立授权服务商）")
    private String dealerAfterSaleType;

//    @ApiModelProperty(value = "24小时救援-服务商（1:是 0:否）")
//    private String is24Rescue;

    @ApiModelProperty(value = "新能源车售后（1:是 0:否）")
    private Integer isNewEnergy;

    @ApiModelProperty(value = "展厅形态（1:都市店 2:奥迪之城 3:奥迪进取汇/品牌体验中心）4.用户中心")
    private Integer exhibitionHallForm;
    // @ApiModelProperty(value = "展厅形态(支持多选)")
    private List<Integer> exhibitionHallFormList;

    @ApiModelProperty(value = "业务状态（0:全部 1:意向 2:筹备 3:开业 4:出网 5:PopUP 6:试运营 7:意向终止 8:预营业）")
    private String businessStatus;

    @ApiModelProperty(value = "前端检索枚举（1:代理商-全部 1-2:奥迪进取汇 1-3:奥迪之城 1-4:奥迪都市店 2:服务商-全部 2-1:销售+售后绑定 2-2:独立授权服务商 2-4:新能源车售后 2-5:高压电池维修）")
    private String searchEnum;

    @ApiModelProperty(value = "是否需要门店图片（1:是 0:否）")
    private Integer haveImage;

    @ApiModelProperty(value = "行政区划代码")
    private String regionCode;

    @ApiModelProperty(value = "城市检索为空时是否返回城市所在大区门店（1:是 0:否）")
    private Integer ifRegionCodeByCity;

    @ApiModelProperty(value = "一汽高压电池维修(1:是 0:否)")
    private String fawAudiBatteryMaintenanceCenter;

    @ApiModelProperty(value = "代理商推荐码")
    private String remCode;


}
