package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.po.SvcdUser;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.vo.svcd.SvcdUserListRO;
import com.csvw.audi.cc.entity.vo.svcd.SvcdUserListVO;

import java.util.List;

/**
 * <p>
 * 渠道商人员信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-06
 */
public interface ISvcdUserService extends IService<SvcdUser> {

    List<SvcdUserListRO> listNoPage(SvcdUserListVO vo);

    String audiStewardLink(Long userId, String userMobile);

    /**
     * 检查用户是否有效，无效异常
     * @param userId
     * @return
     */
    SvcdUser checkUserStatus(Long userId);

    /**
     * @description 异步更新渠道商人员邀请好友状态
     */
    void asyncUpdateNoInvite(SvcdUser user);
}
