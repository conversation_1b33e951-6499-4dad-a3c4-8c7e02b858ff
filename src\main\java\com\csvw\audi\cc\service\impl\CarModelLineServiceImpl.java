package com.csvw.audi.cc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.common.utils.ChannelDataUtils;
import com.csvw.audi.cc.config.AppConfig;
import com.csvw.audi.cc.entity.dto.*;
import com.csvw.audi.cc.entity.dto.omd.*;
import com.csvw.audi.cc.entity.dto.vwcc.CarCustomDetail;
import com.csvw.audi.cc.entity.dto.vwcc.Option;
import com.csvw.audi.cc.entity.enumeration.*;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.entity.vo.*;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.feign.CopProdQueryFeign;
import com.csvw.audi.cc.feign.OmdFeign;
import com.csvw.audi.cc.mapper.CarModelLineHighlightMapper;
import com.csvw.audi.cc.mapper.CarModelLineMapper;
import com.csvw.audi.cc.mapper.CarModelLineOptionMapper;
import com.csvw.audi.cc.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 配置线 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Service
@Slf4j
public class CarModelLineServiceImpl extends ServiceImpl<CarModelLineMapper, CarModelLine> implements ICarModelLineService {

    @Autowired
    private ICarModelLineParameterService parameterService;

    @Autowired
    private ICarModelLineOptionService modelLineOptionService;

    @Autowired
    private CarModelLineMapper modelLineMapper;

    @Autowired
    private ICarOmdPriceTypeService priceTypeService;

    @Autowired
    private ICarOptionService carOptionService;

    @Autowired
    private ICarOptionRelateService optionRelateService;

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private ICarOptionDetailService optionDetailService;

    @Autowired
    private ICarModelLineExtService lineExtService;

    @Autowired
    private ICarPriceExtService priceExtService;

    @Autowired
    private ICarCustomSeriesService customSeriesService;

    @Autowired
    private CarModelLineOptionMapper modelLineOptionMapper;

    @Autowired
    private ICarModelLineSpecialOptionService specialOptionService;

    @Autowired
    private OmdFeign omdFeign;

    @Autowired
    private ICarModelLineSibInterieurService sibInterieurService;

    @Autowired
    private ICarCustomService carCustomService;

    @Autowired
    private ICarRecommendService recommendService;

    @Autowired
    private CarRecommendCustomServiceImpl recommendCustomService;

    @Autowired
    private OmdServiceImpl omdService;

    @Autowired
    private ICarMeasureMadeOriginConfigService measureMadeOriginConfigService;

    @Autowired
    private ICarMeasureMadeConfigService measureMadeConfigService;

    @Autowired
    private ICarTagRelateService tagRelateService;

    @Autowired
    private CopProdQueryFeign copProdQueryFeign;

    @Autowired
    private ICarModelLineService modelLineService;

    @Autowired
    private ICarModelLineTypeService modelLineTypeService;

    @Autowired
    private CarModelLineHighlightMapper highlightMapper;


    private final List<String> baseCategory = Arrays.asList(OptionCategoryEnum.OUTCOLOR.getValue(),
            OptionCategoryEnum.INCOLOR.getValue(), OptionCategoryEnum.SIB.getValue(), OptionCategoryEnum.WHEEL.getValue(),
            OptionCategoryEnum.EIH.getValue());

    @Override
    @Cacheable(cacheNames = {"cc_cache"}, keyGenerator = "springCacheKeyGenerator")
    public List<ModelLineVo> listModelLine(ModelParamDto modelParamDto) throws Exception {

        List<ModelLineVo> vos = modelLineMapper.listModelLine(modelParamDto);
        String channel = modelParamDto.getChannel();
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            vos = ChannelDataUtils.channelData(vos, ModelLineVo.class, channel, "modelLineId", true);
        }
        vos = vos.stream().map( i->{
            try {
                String category = OptionCategoryEnum.PACKET.getValue();
                List<ModelLineOptionVo> optionVos = this.modelLineOption(channel, i.getModelLineId(), category).stream().filter(o-> OptionTypeEnum.PACKET_EQUITY.getValue().equals(o.getOptionType()) && o.getStatus() != 0).collect(Collectors.toList());
                i.setPacketEquity(optionVos);
                if ( handlePriceControl(i.getModelLineId(), channel)){
                    LambdaQueryWrapper<CarPriceExt> lineExtQuery = new LambdaQueryWrapper<>();
                    lineExtQuery.eq(CarPriceExt::getModelLineId, i.getModelLineId()).
                            eq(CarPriceExt::getType, 1).
                            eq(CarPriceExt::getDelFlag, 0).
                            orderByDesc(CarPriceExt::getCreateTime);
                    List<CarPriceExt> priceExts = priceExtService.list(lineExtQuery);
                    if(CollectionUtils.isNotEmpty(priceExts)){
                        i.setPrice(priceExts.get(0).getPriceDisplay());
                    }else {
                        i.setPrice(Constant.PRICE_NOT_HANDLE);
                    }
                }else {
                    BigDecimal typePrice = priceTypeService.typePrice(i.getModelLineId());
                    // 特定配置线加上选装价格
                    if (typePrice != null && i.getSpecialLine().intValue() == 1){
                        BigDecimal specialOptionPrice = specialOptionService.totalOptionPrice(i.getModelLineId());
                        typePrice = typePrice.add(specialOptionPrice);
                    }
                    i.setPrice(typePrice);
                }

            } catch (ServiceException e) {
                i.setPrice(null);
            } catch (Exception e) {
                log.error("配置线处理异常", e);
            }
            if (i.getCustomSeriesCode().equals("49")){
                i.setDeliveryTime(appConfig.getCc().getDelivery());
            }else if (i.getCustomSeriesCode().equals("G4")){
                i.setDeliveryTime(appConfig.getCc().getQ5eDelivery());
            }
            // 标签封装
            i.setTags(tagRelateService.modelLineTag(i.getModelLineId()));
            i.setSellBlips(modelLineMapper.listBlips(i.getModelLineId()));
            return i;
        }).filter(i-> i.getPrice() != null).collect(Collectors.toList());
        return vos;
    }

    @Override
    public ModelLineConfigVo modelLineConfig(String channel, String modelLineId, Integer status) throws Exception {
        ModelLineConfigVo configVo = new ModelLineConfigVo();
        List<ModelLineParameterVo> parameterVos = parameterService.listModelLineParameter(modelLineId);
        ModelLineOptionVo param = new ModelLineOptionVo();
        param.setModelLineId(modelLineId);
        param.setChannel(channel);
        param.setDelFlag(0);
        param.setStatus(status);
        List<ModelLineOptionVo> optionVos = modelLineOptionService.listModelLineOptionWithoutRel(param);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            optionVos = ChannelDataUtils.channelData(optionVos, ModelLineOptionVo.class, channel, "optionId", false);
        }
        OptionRelateParam optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLineId);
        optionRelateParam.setRelateTypes(Arrays.asList("conflict", "depend"));
        List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        Map<String, List<OptionRelDto>> oMap = new HashMap<>();
        relDtos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel == null) {
                oRel = new ArrayList<>();
                oMap.put(i.getOptionId(), oRel);
            }
            oRel.add(i);
        });
        ModelLineOptionTagParam tagParam = new ModelLineOptionTagParam();
        tagParam.setModelLineId(modelLineId);
        tagParam.setChannel(Constant.MASTER_CHANNEL);
        tagParam.setTagCode("PUBLIC_PRODUCT_SELECTED");
        List<ModelLineOptionVo> productSelected = this.modelLineTagOption(tagParam);
        Map<String, ModelLineOptionVo> productSelectMap = productSelected.stream().collect(Collectors.toMap(ModelLineOptionVo::getOptionId, i->i, (o, n)->n));
        optionVos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel != null){
                i.setOptionRelates(oRel);
            }
            if (productSelectMap != null && productSelectMap.get(i.getOptionId()) != null){
                i.setDetailPageHidden(1);
            }
        });
        List<ModelLineOptionVo> packetItems = modelLineOptionService.listModelLinePacketItem(channel, modelLineId, 0);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            packetItems = ChannelDataUtils.channelData(packetItems, ModelLineOptionVo.class, channel, "optionId", false);
        }
        HashMap<String , List<ModelLineOptionVo>> packageItemMap = new HashMap<>();
        packetItems.forEach(i->{
            List<ModelLineOptionVo> items = packageItemMap.get(i.getPackageId());
            if (items == null){
                items = new ArrayList<>();
                packageItemMap.put(i.getPackageId(), items);
            }
            items.add(i);
        });
        // 价格筛选
        optionVos = optionVos.stream().map(i->{
            if (i.getOptionType() != null && i.getOptionType().equals("packet")){
                i.setPacketItems(packageItemMap.get(i.getOptionId()));
            }
            if (i.getStatus() != null && i.getStatus().intValue() != 1) {
                try {
                    if (handlePriceControl(i.getModelLineId(), channel)){
                        i.setPrice(Constant.PRICE_NOT_HANDLE);
                    }else {
                        i.setPrice(priceTypeService.optionPrice(modelLineId, i.getOptionCode(), i.getCategory()));
                    }
                } catch (ServiceException e) {
                    i.setPrice(null);
                }
            }
            CarOptionDetailVo carOptionDetailVo = new CarOptionDetailVo();
            carOptionDetailVo.setOptionId(i.getOptionId());
            carOptionDetailVo.setDelFlag(0);
            carOptionDetailVo.setChannel(channel);
            try {
                i.setOptionDetailList(optionDetailService.listOptionDetailVo(carOptionDetailVo));
            } catch (Exception e) {
               log.error("配置项详情查询异常", e);
            }
            return i;
        }).filter(i->this.priceFilter(i)).collect(Collectors.toList());
        configVo.setParameter(parameterVos);
        configVo.setModelLineOption(optionVos);
        List<CarModelLineHighlight> highlights = highlightMapper.selectList(Wrappers.<CarModelLineHighlight>lambdaQuery()
                .eq(CarModelLineHighlight::getModelLineId, modelLineId)
                .eq(CarModelLineHighlight::getDelFlag, 0)
                .orderByAsc(CarModelLineHighlight::getOptionWeight));
        if (CollectionUtils.isNotEmpty(highlights)){
            highlights.forEach(h->{
                ModelLineOptionVo highlightVo = new ModelLineOptionVo();
                BeanUtils.copyProperties(h, highlightVo);
                highlightVo.setPrice(h.getPrice());
                highlightVo.setStatus(1);
                configVo.getModelLineOption().add(highlightVo);
            });

        }
        return configVo;
    }

    @Override
    @Cacheable(cacheNames = {"cc_cache"}, keyGenerator = "springCacheKeyGenerator")
    public List<ModelLineOptionVo> modelLineTagOption(ModelLineOptionTagParam tagParam) throws Exception {
        String modelLineId = tagParam.getModelLineId();
        String channel = tagParam.getChannel();
        List<ModelLineOptionVo> optionVos = modelLineOptionService.listModelLineOptionByTag(tagParam);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            optionVos = ChannelDataUtils.channelData(optionVos, ModelLineOptionVo.class, channel, "optionId", false);
        }
       /* OptionRelateParam optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLineId);
        optionRelateParam.setRelateTypes(Arrays.asList("conflict", "depend"));
        List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        Map<String, List<OptionRelDto>> oMap = new HashMap<>();
        relDtos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel == null) {
                oRel = new ArrayList<>();
                oMap.put(i.getOptionId(), oRel);
            }
            oRel.add(i);
        });
        optionVos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel != null){
                i.setOptionRelates(oRel);
            }
        });*/
        List<ModelLineOptionVo> packetItems = modelLineOptionService.listModelLinePacketItem(channel, modelLineId, 0);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            packetItems = ChannelDataUtils.channelData(packetItems, ModelLineOptionVo.class, channel, "optionId", false);
        }
        HashMap<String , List<ModelLineOptionVo>> packageItemMap = new HashMap<>();
        packetItems.forEach(i->{
            List<ModelLineOptionVo> items = packageItemMap.get(i.getPackageId());
            if (items == null){
                items = new ArrayList<>();
                packageItemMap.put(i.getPackageId(), items);
            }
            items.add(i);
        });
        // 价格筛选
        optionVos = optionVos.stream().map(i->{
            if (i.getOptionType() != null && i.getOptionType().equals("packet")){
                i.setPacketItems(packageItemMap.get(i.getOptionId()));
            }
            if (i.getStatus() != null && i.getStatus().intValue() != 1) {
                try {
                    if (handlePriceControl(i.getModelLineId(), channel)){
                        i.setPrice(Constant.PRICE_NOT_HANDLE);
                    }else {
                        i.setPrice(priceTypeService.optionPrice(modelLineId, i.getOptionCode(), i.getCategory()));
                    }
                } catch (ServiceException e) {
                    i.setPrice(null);
                }
            }
            CarOptionDetailVo carOptionDetailVo = new CarOptionDetailVo();
            carOptionDetailVo.setOptionId(i.getOptionId());
            carOptionDetailVo.setDelFlag(0);
            carOptionDetailVo.setChannel(channel);
            try {
                i.setOptionDetailList(optionDetailService.listOptionDetailVo(carOptionDetailVo));
            } catch (Exception e) {
                log.error("配置项详情查询异常", e);
            }
            return i;
        }).filter(i->this.priceFilter(i)).collect(Collectors.toList());
        return optionVos;
    }

    @Override
    @Cacheable(cacheNames = {"cc_cache"}, keyGenerator = "springCacheKeyGenerator")
    public List<ModelLineOptionVo> modelLineOption(String channel, String modelLineId, String category) throws Exception {
        ModelLineOptionVo param = new ModelLineOptionVo();
        param.setModelLineId(modelLineId);
        param.setCategory(category);
        param.setChannel(channel);
        List<ModelLineOptionVo> optionVos = modelLineOptionService.listModelLineOption(param);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            Map<String, Boolean> options = new HashMap<>();
            optionVos.forEach(i->{
                if (channel.equals(i.getMloChannel())){
                    options.put(i.getOptionId(), true);
                }
            });
            optionVos = optionVos.stream().filter(i->{
                if (options.get(i.getOptionId()) == null || !options.get(i.getOptionId())){
                    return true;
                }else {
                    if (i.getChannel().equals(i.getMloChannel())){
                        return true;
                    }else{
                        return false;
                    }
                }
            }).collect(Collectors.toList());
            optionVos = ChannelDataUtils.channelData(optionVos, ModelLineOptionVo.class, channel, "optionId", false);
        }
        Stream<ModelLineOptionVo> optionVoStream = optionVos.stream().map(i->{
            if (i.getStatus() != null && i.getStatus().intValue() != 1) {
                try {
                    if (handlePriceControl(i.getModelLineId(), channel)){
                        i.setPrice(Constant.PRICE_NOT_HANDLE);
                    }else {
                        i.setPrice(priceTypeService.optionPrice(modelLineId, i.getOptionCode(), i.getCategory()));
                    }
                } catch (ServiceException e) {
                    i.setPrice(null);
                }
            }
            CarOptionDetailVo carOptionDetailVo = new CarOptionDetailVo();
            carOptionDetailVo.setOptionId(i.getOptionId());
            carOptionDetailVo.setDelFlag(0);
            carOptionDetailVo.setChannel(channel);
            try {
                i.setTags(tagRelateService.modelLineOptionTag(i.getModelLineId(), i.getOptionId()));
                i.setOptionDetailList(optionDetailService.listOptionDetailVo(carOptionDetailVo));
            } catch (Exception e) {
                log.error("配置项详情查询异常", e);
            }
            return i;
        });
        if (category != null && !category.equals(OptionCategoryEnum.INCOLOR.getValue())) {
            optionVos = optionVoStream.filter(i->this.priceFilter(i)).collect(Collectors.toList());
        }
        return optionVos;
    }

    @Override
    @Cacheable(cacheNames = {"cc_cache"}, keyGenerator = "springCacheKeyGenerator")
    public List<ModelLineOptionVo> modelLineOptionWithOutPriceFilter(String channel, String modelLineId, String category) throws Exception {
        ModelLineOptionVo param = new ModelLineOptionVo();
        param.setModelLineId(modelLineId);
        param.setCategory(category);
        param.setChannel(channel);
        List<ModelLineOptionVo> optionVos = modelLineOptionService.listModelLineOption(param);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            Map<String, Boolean> options = new HashMap<>();
            optionVos.forEach(i->{
                if (channel.equals(i.getMloChannel())){
                    options.put(i.getOptionId(), true);
                }
            });
            optionVos = optionVos.stream().filter(i->{
                if (options.get(i.getOptionId()) == null || !options.get(i.getOptionId())){
                    return true;
                }else {
                    if (i.getChannel().equals(i.getMloChannel())){
                        return true;
                    }else{
                        return false;
                    }
                }
            }).collect(Collectors.toList());
            optionVos = ChannelDataUtils.channelData(optionVos, ModelLineOptionVo.class, channel, "optionId", false);
        }
        Stream<ModelLineOptionVo> optionVoStream = optionVos.stream().map(i->{
            if (i.getStatus() != null && i.getStatus().intValue() != 1) {
                try {
                    if (handlePriceControl(i.getModelLineId(), channel)){
                        i.setPrice(Constant.PRICE_NOT_HANDLE);
                    }else {
                        i.setPrice(priceTypeService.optionPrice(modelLineId, i.getOptionCode(), i.getCategory()));
                    }
                } catch (ServiceException e) {
                    i.setPrice(null);
                }
            }
            CarOptionDetailVo carOptionDetailVo = new CarOptionDetailVo();
            carOptionDetailVo.setOptionId(i.getOptionId());
            carOptionDetailVo.setDelFlag(0);
            carOptionDetailVo.setChannel(channel);
            try {
                i.setOptionDetailList(optionDetailService.listOptionDetailVo(carOptionDetailVo));
            } catch (Exception e) {
                log.error("配置项详情查询异常", e);
            }
            return i;
        });

        return optionVoStream.collect(Collectors.toList());
    }

    @Override
    public List<ModelLineOptionVo> modelLineOptionalEquipment(String channel, String modelLineId) {
        ModelLineOptionVo param = new ModelLineOptionVo();
        param.setModelLineId(modelLineId);
        param.setStatus(OptionStatusEnum.OPTIONAL_EQUIPMENT.getValue());
        param.setChannel(channel);
        List<ModelLineOptionVo> optionVos = modelLineOptionService.listModelLineOption(param);
        List<ModelLineOptionVo> packetItems = modelLineOptionService.listModelLinePacketItem(channel, modelLineId, 0);
        HashMap<String , List<ModelLineOptionVo>> packageItemMap = new HashMap<>();
        packetItems.forEach(i->{
            List<ModelLineOptionVo> items = packageItemMap.get(i.getPackageId());
            if (items == null){
                items = new ArrayList<>();
                packageItemMap.put(i.getPackageId(), items);
            }
            items.add(i);
        });
        optionVos = optionVos.stream().map(i->{
            if (i.getOptionType() != null && i.getOptionType().equals("packet")){
                i.setPacketItems(packageItemMap.get(i.getOptionId()));
            }
            if (i.getStatus() != null && i.getStatus().intValue() != 1) {
                try {
                    if (handlePriceControl(i.getModelLineId(), channel)){
                        i.setPrice(Constant.PRICE_NOT_HANDLE);
                    }else {
                        i.setPrice(priceTypeService.optionPrice(modelLineId, i.getOptionCode(), i.getCategory()));
                    }
                } catch (ServiceException e) {
                    i.setPrice(null);
                }
            }
            CarOptionDetailVo carOptionDetailVo = new CarOptionDetailVo();
            carOptionDetailVo.setOptionId(i.getOptionId());
            carOptionDetailVo.setDelFlag(0);
            carOptionDetailVo.setChannel(channel);
            try {
                i.setOptionDetailList(optionDetailService.listOptionDetailVo(carOptionDetailVo));
            } catch (Exception e) {
                log.error("配置项详情查询异常", e);
            }
            return i;
        }).filter(i->this.personalOptionFilter(i)).collect(Collectors.toList());
        return optionVos;
    }

    @Override
    @Cacheable(cacheNames = {"cc_cache"}, keyGenerator = "springCacheKeyGenerator")
    public List<ModelLineOptionVo> modelLinePersonalOptionDefault(String modelLineId) throws Exception {
        // 默认权益
        String category = OptionCategoryEnum.PACKET.getValue();
        List<ModelLineOptionVo> optionVos = this.modelLineOption(Constant.MASTER_CHANNEL, modelLineId, category).stream()
                .filter(i-> OptionTypeEnum.PACKET_EQUITY.getValue().equals(i.getOptionType()) &&
                       i.getDefaultConfig() != null && i.getDefaultConfig().intValue() == 1 && i.getStatus() != 0).collect(Collectors.toList());
        return optionVos;
    }

    @Override
    @Cacheable(cacheNames = {"cc_cache"}, keyGenerator = "springCacheKeyGenerator")
    public List<ModelLineOptionVo> modelLinePersonalOption(String channel, String modelLineId, List<String> notInCategory) throws Exception {
        List<String> modelLineIds = Arrays.asList("3af45079-71f0-4d3f-95ec-4cf9e2faa881","5dd76400-eef9-4c37-a7a2-df6e4432448a", "cd5d8d4b-9158-46fb-9f7a-81f7f5860657",
                "d9150277-7dbe-4840-aac3-2b7e010ead7f", "5cd07835-f1c3-4be0-902a-13ba1d14360c");
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        String type = "personal";
        if (modelLineIds.contains(modelLineId) || lines.get(0).getOmdModelStatus() != 0){
            type = "all";
        }
        if (Constant.DRM_CHANNEL.equals(channel)){
            // type = "personalWithEquity";
        }
        List<ModelLineOptionVo> optionVos = modelLineOptionService.listModelLinePersonalOption(modelLineId, channel, 0, notInCategory, type);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            optionVos = ChannelDataUtils.channelData(optionVos, ModelLineOptionVo.class, channel, "optionId", false);
        }
        List<ModelLineOptionVo> packetItems = modelLineOptionService.listModelLinePacketItem(channel, modelLineId, 0);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            packetItems = ChannelDataUtils.channelData(packetItems, ModelLineOptionVo.class, channel, "optionId", false);
        }
        HashMap<String , List<ModelLineOptionVo>> packageItemMap = new HashMap<>();
        packetItems.forEach(i->{
            List<ModelLineOptionVo> items = packageItemMap.get(i.getPackageId());
            if (items == null){
                items = new ArrayList<>();
                packageItemMap.put(i.getPackageId(), items);
            }
            items.add(i);
        });
        optionVos = optionVos.stream().map(i->{
            if (i.getStatus() != null && i.getStatus().intValue() != 1) {
                try {
                    if (handlePriceControl(modelLineId, channel)){
                        i.setPrice(Constant.PRICE_NOT_HANDLE);
                    }else {
                        i.setPrice(priceTypeService.optionPrice(modelLineId, i.getOptionCode(), i.getCategory()));
                    }
                } catch (ServiceException e) {
                    i.setPrice(null);
                }
            }
            if (i.getOptionType() != null && i.getOptionType().equals("packet")){
                i.setPacketItems(packageItemMap.get(i.getOptionId()));
            }
            CarOptionDetailVo carOptionDetailVo = new CarOptionDetailVo();
            carOptionDetailVo.setOptionId(i.getOptionId());
            carOptionDetailVo.setDelFlag(0);
            carOptionDetailVo.setChannel(channel);
            try {
                i.setOptionDetailList(optionDetailService.listOptionDetailVo(carOptionDetailVo));
            } catch (Exception e) {
                log.error("配置项详情查询异常", e);
            }
            return i;
        }).filter(i->this.personalOptionFilter(i)).sorted((a,b)->{
            if (a.getPrice() == null || b.getPrice() == null){
                return 0;
            }
            if (a.getPrice() instanceof BigDecimal && b.getPrice() instanceof BigDecimal){
                return ((BigDecimal)b.getPrice()).compareTo((BigDecimal)a.getPrice());
            }else {
                return 0;
            }
        }).collect(Collectors.toList());
        List<ModelLineOptionVo> res = new ArrayList<>();
        List<ModelLineOptionVo> pEquityList = new ArrayList<>();
        List<ModelLineOptionVo> pList = new ArrayList<>();
        List<ModelLineOptionVo> npList = new ArrayList<>();
        Iterator<ModelLineOptionVo> iterator = optionVos.iterator();
        while (iterator.hasNext()) {
            ModelLineOptionVo i = iterator.next();
            if (i.getCategory() != null && i.getOptionType().equals(OptionTypeEnum.PACKET_EQUITY.getValue())) {
                pEquityList.add(i);
            } else if (i.getCategory() != null && i.getCategory().equals(OptionCategoryEnum.PACKET.getValue())) {
                pList.add(i);
            } else {
                npList.add(i);
            }
            iterator.remove();
        }
        res.addAll(pEquityList);
        res.addAll(pList);
        res.addAll(npList);
        return res;
    }

    @Override
    public PriceCondition listPriceCondition(String modelLineId) {
        PriceCondition priceCondition = modelLineMapper.listPriceCondition(modelLineId);
        if (priceCondition != null && priceCondition.getAccbTypeCode() != null && priceCondition.getAccbTypeCode().startsWith("TYPE:")){
            priceCondition.setAccbTypeCode(priceCondition.getAccbTypeCode().replaceFirst("TYPE:", ""));
        }
        return priceCondition;
    }

    @Override
    public PriceCondition listAccbPriceCondition(TypePriceParam typePriceParam) {
        PriceCondition priceCondition = modelLineMapper.listAccbPriceCondition(typePriceParam);
        if (priceCondition != null && priceCondition.getAccbTypeCode() != null && priceCondition.getAccbTypeCode().startsWith("TYPE:")){
            priceCondition.setAccbTypeCode(priceCondition.getAccbTypeCode().replaceFirst("TYPE:", ""));
        }
        return priceCondition;
    }

    @Override
    public List<ModelLineOptionVo> modelLinePacketItem(OptionParamDto optionParamDto) throws Exception {
        String channel = optionParamDto.getChannel();
        List<ModelLineOptionVo> optionVos = modelLineOptionService.listModelLinePacketItem(optionParamDto);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            optionVos = ChannelDataUtils.channelData(optionVos, ModelLineOptionVo.class, channel, "optionId", false);
        }
        return optionVos;
    }

    @Override
    public Object computePrice(PriceComputeParam priceComputeParam) throws ServiceException {
        if (handlePriceControl(priceComputeParam.getModelLineId(), Constant.MASTER_CHANNEL)){
            LambdaQueryWrapper<CarPriceExt> lineExtQuery = new LambdaQueryWrapper<>();
            lineExtQuery.eq(CarPriceExt::getModelLineId, priceComputeParam.getModelLineId()).
                    eq(CarPriceExt::getType, 1).
                    eq(CarPriceExt::getDelFlag, 0).
                    orderByDesc(CarPriceExt::getCreateTime);
            List<CarPriceExt> priceExts = priceExtService.list(lineExtQuery);
            if(CollectionUtils.isNotEmpty(priceExts)){
                return priceExts.get(0).getPriceDisplay();
            }else {
                return Constant.PRICE_NOT_HANDLE;
            }
//            return Constant.PRICE_NOT_HANDLE;
        }
        BigDecimal totalPrice = priceTypeService.typePrice(priceComputeParam.getModelLineId());
        // 特定配置线加上选装价格
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setModelLineId(priceComputeParam.getModelLineId());
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        modelParamDto.setDelFlag(0);
        List<ModelLineVo> vos = modelLineMapper.listModelLine(modelParamDto);
        ModelLineVo modelLine = vos.get(0);
        if (totalPrice != null && modelLine.getSpecialLine().intValue() == 1){
            BigDecimal specialOptionPrice = specialOptionService.totalOptionPrice(modelLine.getModelLineId());
            totalPrice = totalPrice.add(specialOptionPrice);
        }
        if (totalPrice == null){
            throw new ServiceException("50001", "价格异常");
        }
        List<String> options = priceComputeParam.getOptionIds();
        Map<String, ModelLineOptionVo> packageItemMap = new HashMap<>();
        List<String> duplicateCodes = new ArrayList<>();
        String sibCode = null;
        if (!CollectionUtils.isEmpty(options)){
            OptionParamDto optionParamDto = new OptionParamDto();
            optionParamDto.setModelLineId(priceComputeParam.getModelLineId());
            optionParamDto.setCustomSeriesId(priceComputeParam.getCustomSeriesId());
            optionParamDto.setOptionIds(options);
            optionParamDto.setChannel(Constant.MASTER_CHANNEL);
            optionParamDto.setDelFlag(0);
            List<ModelLineOptionVo> optionTotalVos = modelLineOptionService.modelLineOptionQuery(optionParamDto);
            Map<String, ModelLineOptionVo> vosTemp = new HashMap<>();
            Set<String> optionCodes = optionTotalVos.stream().map(ModelLineOptionVo::getOptionCode).collect(Collectors.toSet());
            log.debug("价格计算调试，params: {} , modelLine: {},  optionCodes: {}", priceComputeParam, modelLine, optionCodes);
            optionTotalVos.forEach(i->{
//                if (vosTemp.get(i.getOptionId()) == null){
//                    vosTemp.put(i.getOptionId(), new ArrayList<>());
//                }
                vosTemp.put(i.getOptionId(), i);
            });
            for(String optionId: options){
                ModelLineOptionVo option = vosTemp.get(optionId);
                if (option == null){
                    throw new ServiceException("400401", "参数异常：optionIds", "optionId: "+ optionId );
                }
                // 选装包的件不算价格
                if (option.getOptionType().equals("packet-item")){
                    continue;
                }
                if (option.getCategory() != null && option.getCategory().equals("SIB")){
                    sibCode = option.getOptionCode();
                }
                if (option.getCategory() != null && option.getCategory().equals("PACKET")) {
                    OptionParamDto paramDto = new OptionParamDto();
                    paramDto.setOptionId(optionId);
                    paramDto.setModelLineId(priceComputeParam.getModelLineId());
                    paramDto.setChannel(Constant.MASTER_CHANNEL);
                    paramDto.setDelFlag(0);
                    List<ModelLineOptionVo> packageItems = modelLineOptionService.listModelLinePacketItem(paramDto);
                    if (packageItems != null){
                        packageItems.forEach(i->{
                            if (packageItemMap.get(i.getOptionCode()) != null){
                                duplicateCodes.add(i.getOptionCode());
                            }else {
                                packageItemMap.put(i.getOptionCode(), i);
                            }
                        });
                    }
                }
                BigDecimal optionPrice = priceTypeService.optionPrice(priceComputeParam.getModelLineId(), option.getOptionCode(), option.getCategory());
                if (optionPrice != null) {
                    // 选装包中有的配件，不计算价格
                    List<ModelLineOptionVo> items = modelLineOptionMapper.findPaketItemFromCodes(modelLine.getCustomSeriesId(), modelLine.getModelLineId(), option.getOptionCode(), optionCodes);
                    if (CollectionUtils.isNotEmpty(items)) {
                        continue;
                    }
                    totalPrice = totalPrice.add(optionPrice);
                }else if (option.getCategory() == null || !option.getCategory().equals(OptionCategoryEnum.INCOLOR.getValue())){ // 内饰颜色不算价格
                    // 选装需要有价格，排除选装包的件，以及标装
                    if (option.getStatus() == null || option.getStatus().intValue() != 1){
                        List<ModelLineOptionVo> items = modelLineOptionMapper.findPaketItemFromCodes(modelLine.getCustomSeriesId(), modelLine.getModelLineId(), option.getOptionCode(), optionCodes);
                        if (CollectionUtils.isEmpty(items)) {
                            throw new ServiceException("50000", "价格异常", JSONObject.toJSONString(option));
                        }
                    }
                }
            }

            // remove duplicate package item price
            for (String i : duplicateCodes){
                BigDecimal optionPrice = priceTypeService.optionPrice(priceComputeParam.getModelLineId(), i, "");
                if (optionPrice != null)
                    totalPrice = totalPrice.subtract(optionPrice);
            }
        }
        return totalPrice;
    }

    @Override
    public BigDecimal computeDiscount(PriceComputeParam priceComputeParam) throws ServiceException {
        BigDecimal discount = BigDecimal.ZERO;
        if (StringUtils.isEmpty(priceComputeParam.getCouponNo())){
            return discount;
        }

        BigDecimal totalPrice = priceTypeService.typePrice(priceComputeParam.getModelLineId());
        // 特定配置线加上选装价格
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setModelLineId(priceComputeParam.getModelLineId());
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        modelParamDto.setDelFlag(0);
        List<ModelLineVo> vos = modelLineMapper.listModelLine(modelParamDto);
        ModelLineVo modelLine = vos.get(0);
        if (totalPrice != null && modelLine.getSpecialLine().intValue() == 1){
            BigDecimal specialOptionPrice = specialOptionService.totalOptionPrice(modelLine.getModelLineId());
            totalPrice = totalPrice.add(specialOptionPrice);
        }
        if (totalPrice == null){
            throw new ServiceException("50001", "价格异常");
        }
        List<String> options = priceComputeParam.getOptionIds();
        Map<String, ModelLineOptionVo> packageItemMap = new HashMap<>();
        List<String> duplicateCodes = new ArrayList<>();
        if (!CollectionUtils.isEmpty(options)){
            OptionParamDto optionParamDto = new OptionParamDto();
            optionParamDto.setModelLineId(priceComputeParam.getModelLineId());
            optionParamDto.setCustomSeriesId(priceComputeParam.getCustomSeriesId());
            optionParamDto.setOptionIds(options);
            optionParamDto.setChannel(Constant.MASTER_CHANNEL);
            optionParamDto.setDelFlag(0);
            List<ModelLineOptionVo> optionTotalVos = modelLineOptionService.modelLineOptionQuery(optionParamDto);
            Map<String, ModelLineOptionVo> vosTemp = new HashMap<>();
            Set<String> optionCodes = optionTotalVos.stream().map(ModelLineOptionVo::getOptionCode).collect(Collectors.toSet());
            log.debug("价格计算调试，params: {} , modelLine: {},  optionCodes: {}", priceComputeParam, modelLine, optionCodes);
            optionTotalVos.forEach(i-> vosTemp.put(i.getOptionId(), i));
            for(String optionId: options){
                ModelLineOptionVo option = vosTemp.get(optionId);
                if (option == null){
                    throw new ServiceException("400401", "参数异常：optionIds", "optionId: "+ optionId );
                }
                // 选装包的件不算价格
                if (option.getOptionType().equals("packet-item")){
                    continue;
                }
                if (option.getCategory() != null && option.getCategory().equals("PACKET")) {
                    OptionParamDto paramDto = new OptionParamDto();
                    paramDto.setOptionId(optionId);
                    paramDto.setModelLineId(priceComputeParam.getModelLineId());
                    paramDto.setChannel(Constant.MASTER_CHANNEL);
                    paramDto.setDelFlag(0);
                    List<ModelLineOptionVo> packageItems = modelLineOptionService.listModelLinePacketItem(paramDto);
                    if (packageItems != null){
                        packageItems.forEach(i->{
                            if (packageItemMap.get(i.getOptionCode()) != null){
                                duplicateCodes.add(i.getOptionCode());
                            }else {
                                packageItemMap.put(i.getOptionCode(), i);
                            }
                        });
                    }
                }
                BigDecimal optionPrice = priceTypeService.optionPrice(priceComputeParam.getModelLineId(), option.getOptionCode(), option.getCategory());
                if (optionPrice != null) {
                    // 选装包中有的配件，不计算价格
                    List<ModelLineOptionVo> items = modelLineOptionMapper.findPaketItemFromCodes(modelLine.getCustomSeriesId(), modelLine.getModelLineId(), option.getOptionCode(), optionCodes);
                    if (CollectionUtils.isNotEmpty(items)) {
                        continue;
                    }
                    totalPrice = totalPrice.add(optionPrice);
                    if ("1".equals(option.getEquipmentRights())){
                        discount = discount.add(optionPrice);
                    }
                }else if (option.getCategory() == null || !option.getCategory().equals(OptionCategoryEnum.INCOLOR.getValue())){ // 内饰颜色不算价格
                    // 选装需要有价格，排除选装包的件，以及标装
                    if (option.getStatus() == null || option.getStatus().intValue() != 1){
                        List<ModelLineOptionVo> items = modelLineOptionMapper.findPaketItemFromCodes(modelLine.getCustomSeriesId(), modelLine.getModelLineId(), option.getOptionCode(), optionCodes);
                        if (CollectionUtils.isEmpty(items)) {
                            throw new ServiceException("50000", "价格异常", JSONObject.toJSONString(option));
                        }
                    }
                }
            }

            // remove duplicate package item price
            for (String i : duplicateCodes){
                BigDecimal optionPrice = priceTypeService.optionPrice(priceComputeParam.getModelLineId(), i, "");
                if (optionPrice != null)
                    totalPrice = totalPrice.subtract(optionPrice);
            }
        }
        return discount;
    }

    @Override
    public List<ModelLineOptionVo> modelLineOptionQuery(String channel, String modelLineId, Collection<String> optionIds) throws Exception {
        OptionParamDto param = new OptionParamDto();
        param.setModelLineId(modelLineId);
        param.setChannel(channel);
        param.setNotOptionTypes(Arrays.asList("packet-item"));
        param.setOptionIds(optionIds);
        List<ModelLineOptionVo> optionVos = modelLineOptionService.modelLineOptionQuery(param);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            optionVos = ChannelDataUtils.channelData(optionVos, ModelLineOptionVo.class, channel, "optionId", false);
        }
        optionVos = optionVos.stream().map(i->{
            if (i.getStatus() != null && i.getStatus().intValue() != 1) {
                try {
                    if (handlePriceControl(i.getModelLineId(), channel)){
                        i.setPrice(Constant.PRICE_NOT_HANDLE);
                    }else {
                        i.setPrice(priceTypeService.optionPrice(modelLineId, i.getOptionCode(), i.getCategory()));
                    }
                } catch (ServiceException e) {
                    i.setPrice(null);
                }
            }
            CarOptionDetailVo carOptionDetailVo = new CarOptionDetailVo();
            carOptionDetailVo.setOptionId(i.getOptionId());
            carOptionDetailVo.setDelFlag(0);
            carOptionDetailVo.setChannel(channel);
            try {
                i.setOptionDetailList(optionDetailService.listOptionDetailVo(carOptionDetailVo));
            } catch (Exception e) {
                log.error("配置项详情查询异常", e);
            }
            return i;
        }).filter(i->this.priceFilter(i)).collect(Collectors.toList());
        return optionVos;
    }

    @Override
    public List<ModelLineOptionVo> modelLineOptionQuery(OptionParamDto param) throws Exception {
        String channel = param.getChannel();
        String modelLineId = param.getModelLineId();
        List<ModelLineOptionVo> optionVos = modelLineOptionService.modelLineOptionQuery(param);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            optionVos = ChannelDataUtils.channelData(optionVos, ModelLineOptionVo.class, channel, "optionId", false);
        }
        optionVos = optionVos.stream().map(i->{
            if (i.getStatus() != null && i.getStatus().intValue() != 1) {
                try {
                    if (handlePriceControl(i.getModelLineId(), channel)){
                        i.setPrice(Constant.PRICE_NOT_HANDLE);
                    }else {
                        i.setPrice(priceTypeService.optionPrice(modelLineId, i.getOptionCode(), i.getCategory()));
                    }
                } catch (ServiceException e) {
                    i.setPrice(null);
                }
            }
            CarOptionDetailVo carOptionDetailVo = new CarOptionDetailVo();
            carOptionDetailVo.setOptionId(i.getOptionId());
            carOptionDetailVo.setDelFlag(0);
            carOptionDetailVo.setChannel(channel);
            try {
                i.setTags(tagRelateService.modelLineOptionTag(i.getModelLineId(), i.getOptionId()));
                i.setOptionDetailList(optionDetailService.listOptionDetailVo(carOptionDetailVo));
            } catch (Exception e) {
                log.error("配置项详情查询异常", e);
            }
            return i;
        }).filter(i->this.priceFilter(i)).collect(Collectors.toList());
        return optionVos;
    }

    @Override
    public List<ModelLineOptionVo> modelLineOptionQueryByOptionCodes(String channel, String customSeriesId, String modelLineId, Collection<String> optionCodes) throws Exception {
        OptionParamDto param = new OptionParamDto();
        param.setModelLineId(modelLineId);
        param.setChannel(channel);
        param.setNotOptionTypes(Arrays.asList("packet-item"));
        param.setOptionCodes(optionCodes);
        param.setCustomSeriesId(customSeriesId);
        List<ModelLineOptionVo> optionVos = modelLineOptionService.optionQuery(param);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            optionVos = ChannelDataUtils.channelData(optionVos, ModelLineOptionVo.class, channel, "optionId", false);
        }
        optionVos = optionVos.stream().map(i->{
            // 畅销车code转换，允许status为null
            if (i.getStatus() == null || i.getStatus().intValue() != 1) {
                try {
                    if (handlePriceControl(i.getModelLineId(), channel)){
                        i.setPrice(Constant.PRICE_NOT_HANDLE);
                    }else {
                        i.setPrice(priceTypeService.optionPrice(modelLineId, i.getOptionCode(), i.getCategory()));
                    }
                } catch (ServiceException e) {
                    i.setPrice(null);
                }
            }
            CarOptionDetailVo carOptionDetailVo = new CarOptionDetailVo();
            carOptionDetailVo.setOptionId(i.getOptionId());
            carOptionDetailVo.setDelFlag(0);
            carOptionDetailVo.setChannel(channel);
            try {
                i.setOptionDetailList(optionDetailService.listOptionDetailVo(carOptionDetailVo));
            } catch (Exception e) {
                log.error("配置项详情查询异常", e);
            }
            return i;
        }).filter(i->this.priceFilter(i)).collect(Collectors.toList());
        return optionVos;
    }

    @Override
    public ModelLineConfigCompareVo modelLineConfigCompare(String modelLineId) {
        String channel = Constant.MASTER_CHANNEL;
        ModelLineConfigCompareVo configVo = new ModelLineConfigCompareVo();
        List<ModelLineParameterVo> parameterVos = parameterService.listModelLineParameter(modelLineId);
        ModelLineOptionVo param = new ModelLineOptionVo();
        param.setModelLineId(modelLineId);
        param.setChannel(channel);
        param.setDelFlag(0);
        List<ModelLineOptionCompareVo> optionVos = modelLineOptionService.listModelLineOptionCompare(param);
        List<ModelLineOptionVo> packetItems = modelLineOptionService.listModelLinePacketItem(channel, modelLineId, 0);
        HashMap<String , List<ModelLineOptionVo>> packageItemMap = new HashMap<>();
        packetItems.forEach(i->{
            List<ModelLineOptionVo> items = packageItemMap.get(i.getPackageId());
            if (items == null){
                items = new ArrayList<>();
                packageItemMap.put(i.getPackageId(), items);
            }
            items.add(i);
        });
        configVo.setParameter(parameterVos);
        configVo.setModelLineOption(optionVos);
        return configVo;
    }

    @Override
    public List<ModelLineBriefVo> listModelLineAstro(ModelParamDto paramDto) throws NoSuchFieldException, IllegalAccessException {
        List<ModelLineBriefVo> vos = modelLineMapper.listModelLineAstro(paramDto);
        String channel = paramDto.getChannel();
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            vos = ChannelDataUtils.channelData(vos, ModelLineBriefVo.class, channel, "modelLineId", true);
        }
        return vos;
    }

    @Override
    public ModelLineVo getModelLineByOmdData(OmdModelParam omdModelParam) throws Exception {
        List<ModelLineVo> vos = modelLineMapper.listModelLineByOmdData(omdModelParam);
        String channel = omdModelParam.getChannel();
        if (vos != null && vos.size()  > 0){
            Set<String> modelLineIds = vos.stream().map(ModelLineVo::getModelLineId).collect(Collectors.toSet());
            vos = new ArrayList<>();
            for (String modelLineId : modelLineIds) {
                ModelParamDto paramDto = new ModelParamDto();
                paramDto.setModelLineId(modelLineId);
                paramDto.setChannel(channel);
                paramDto.setDelFlag(0);
                vos.addAll(this.listModelLine(paramDto));
            }
        }
        vos = vos.stream().map( i->{
            try {
                if ( handlePriceControl(i.getModelLineId(), channel)){
                    LambdaQueryWrapper<CarPriceExt> lineExtQuery = new LambdaQueryWrapper<>();
                    lineExtQuery.eq(CarPriceExt::getModelLineId, i.getModelLineId()).
                            eq(CarPriceExt::getType, 1).
                            eq(CarPriceExt::getDelFlag, 0).
                            orderByDesc(CarPriceExt::getCreateTime);
                    List<CarPriceExt> priceExts = priceExtService.list(lineExtQuery);
                    if(CollectionUtils.isNotEmpty(priceExts)){
                        i.setPrice(priceExts.get(0).getPriceDisplay());
                    }else {
                        i.setPrice(Constant.PRICE_NOT_HANDLE);
                    }
                }else {
                    i.setPrice(priceTypeService.typePrice(i.getModelLineId()));
                }

            } catch (ServiceException e) {
                i.setPrice(null);
            }
            if (i.getCustomSeriesCode().equals("49")){
                i.setDeliveryTime(appConfig.getCc().getDelivery());
            }else if (i.getCustomSeriesCode().equals("G4")){
                i.setDeliveryTime(appConfig.getCc().getQ5eDelivery());
            }
            return i;
        }).filter(i-> i.getPrice() != null &&
                // 只匹配基础配置线
                (i.getSpecialLine() == null || i.getSpecialLine().intValue() != 1)
        ).collect(Collectors.toList());
        if (vos == null || vos.size() == 0){
            return null;
        }
        if (vos.size() > 1){
            if ("TYPE:498BZY-GWAEWAE".equals(vos.get(0).getAccbTypeCode())){
                // 处理白法师/黑武士配置线
                for (ModelLineVo vo : vos){
                    OptionParamDto optionParamDto = new OptionParamDto();
                    optionParamDto.setModelLineId(vo.getModelLineId());
                    optionParamDto.setOptionCode(omdModelParam.getColorCode());
                    optionParamDto.setCategory(OptionCategoryEnum.OUTCOLOR.getValue());
                    optionParamDto.setChannel(Constant.MASTER_CHANNEL);
                    optionParamDto.setStatus(2);
                    optionParamDto.setDelFlag(0);
                    List<ModelLineOptionVo> optionVos = modelLineOptionService.modelLineOptionQueryStrict(optionParamDto);
                    if (CollectionUtils.isNotEmpty(optionVos)){
                        return vo;
                    }
                }
            }
            List<String> q6SpecialLine = Arrays.asList("TYPE:G6ICAY-MREIH14-MHUDKS1-GPCEPCE-GPS1PS1-GYECYEC-MEIH5MD-MRAD53E-MLSE9VS",
                    "TYPE:G6ICAY-MREIH14-MHUDKS1-GPCEPCE-GPS8PS8-MCHRQJ3-GWTYWTY-GYEBYEB-MEIH5MD-MRAD53E-MLSE9VS",
                    // q5e 黑/武/影
                    "TYPE:G4ICC3-MSIBN4X-GYEAYEA-MSIH4A4-MEIH5TD-MRAD56F-MLSE9VS",
                    "TYPE:G4ICF3-MSIBN4X-MLCPQQ9-GYEBYEB-MRAD56F",
                    // a7l 45 55影黑
                    "TYPE:498BZY-GPGCPGC-GWAEWAE-GYEHYEH",
                    "TYPE:498B2Y-MHUDKS1-GPCYPCY-GPGCPGC-GWAEWAE-GYEKYEK-MLSE9VS",
                    "TYPE:498BZY-MREII56-GPAHPAH-GPS6PS6-GYEAYEA-MLRA2PF-MRAD53D",
                    "TYPE:498B2Y-MESTGZ2-MREII56-GPAHPAH-GPCYPCY-GPDWPDW-GPS6PS6-GWA6WA6-GYECYEC-MEIH5MK-MRAD53D-MLSE9VS",
                    // Q6 行云影黑
                    "TYPE:G6ICBY-MREIH14-MHUDKS1-GPS2PS2-GYEDYED-MEIH5MD-MRAD53E-MLSE9VS",

                    // Q6 黑影2024
                    "TYPE:G6ICAY-MREIH14-MHUDKS1-GPCEPCE-GPC2PC2-GPS8PS8-GWTYWTY-GYEBYEB-MEIH5MD-MRAD53E-MLSE9VS",
                    "TYPE:G6ICAY-MREIH14-MHUDKS1-GPCEPCE-GPC2PC2-GPS1PS1-GYECYEC-MEIH5MD-MRAD53E-MLSE9VS",
                    "TYPE:G6ICBY-MREIH14-MHUDKS1-GPC2PC2-GPS2PS2-GYEDYED-MEIH5MD-MRAD53E-MLSE9VS");
            if (q6SpecialLine.contains(vos.get(0).getAccbTypeCode())){
                // 处理黑武士/密法师
                for (ModelLineVo vo : vos){
                    OptionParamDto optionParamDto = new OptionParamDto();
                    optionParamDto.setModelLineId(vo.getModelLineId());
                    optionParamDto.setOptionCode(omdModelParam.getColorCode());
                    optionParamDto.setCategory(OptionCategoryEnum.OUTCOLOR.getValue());
                    optionParamDto.setChannel(Constant.MASTER_CHANNEL);
                    optionParamDto.setStatus(2);
                    optionParamDto.setDelFlag(0);
                    List<ModelLineOptionVo> optionVos = modelLineOptionService.modelLineOptionQueryStrict(optionParamDto);
                    if (CollectionUtils.isNotEmpty(optionVos)){
                        return vo;
                    }
                }
            }

            log.error("omd 数据查询配置线失败, params: {}, res: {}", JSONObject.toJSONString(omdModelParam), JSONObject.toJSONString(vos));
            return null;
        }
        return vos.get(0);
    }

    @Override
    public ModelLineConfigVo modelLineConfigToDrm(String channel, String modelLineId, Integer status) throws NoSuchFieldException, IllegalAccessException {
        ModelLineConfigVo configVo = new ModelLineConfigVo();
        List<ModelLineParameterVo> parameterVos = parameterService.listModelLineParameter(modelLineId);
        ModelLineOptionVo param = new ModelLineOptionVo();
        param.setModelLineId(modelLineId);
        param.setChannel(channel);
        param.setDelFlag(0);
        param.setStatus(status);
        param.setHasCode("1");
        List<ModelLineOptionVo> optionVos = modelLineOptionService.listModelLineOptionWithoutRel(param);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            optionVos = ChannelDataUtils.channelData(optionVos, ModelLineOptionVo.class, channel, "optionId", false);
        }
        OptionRelateParam optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLineId);
        optionRelateParam.setRelateTypes(Arrays.asList("conflict", "depend"));
        List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        Map<String, List<OptionRelDto>> oMap = new HashMap<>();
        relDtos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel == null) {
                oRel = new ArrayList<>();
                oMap.put(i.getOptionId(), oRel);
            }
            oRel.add(i);
        });
        optionVos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel != null){
                i.setOptionRelates(oRel);
            }
        });
        List<ModelLineOptionVo> packetItems = modelLineOptionService.listModelLinePacketItem(channel, modelLineId, 0);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            packetItems = ChannelDataUtils.channelData(packetItems, ModelLineOptionVo.class, channel, "optionId", false);
        }
        HashMap<String , List<ModelLineOptionVo>> packageItemMap = new HashMap<>();
        packetItems.forEach(i->{
            List<ModelLineOptionVo> items = packageItemMap.get(i.getPackageId());
            if (items == null){
                items = new ArrayList<>();
                packageItemMap.put(i.getPackageId(), items);
            }
            items.add(i);
        });
        // 价格筛选
        optionVos = optionVos.stream().map(i->{
            if (i.getOptionType() != null && i.getOptionType().equals("packet")){
                i.setPacketItems(packageItemMap.get(i.getOptionId()));
            }
            if (i.getStatus() != null && i.getStatus().intValue() != 1) {
                try {
                    if (handlePriceControl(i.getModelLineId(), channel)){
                        i.setPrice(Constant.PRICE_NOT_HANDLE);
                    }else {
                        i.setPrice(priceTypeService.optionPrice(modelLineId, i.getOptionCode(), i.getCategory()));
                    }
                } catch (ServiceException e) {
                    i.setPrice(null);
                }
            }
            CarOptionDetailVo carOptionDetailVo = new CarOptionDetailVo();
            carOptionDetailVo.setOptionId(i.getOptionId());
            carOptionDetailVo.setDelFlag(0);
            carOptionDetailVo.setChannel(channel);
            try {
                i.setOptionDetailList(optionDetailService.listOptionDetailVo(carOptionDetailVo));
            } catch (Exception e) {
                log.error("配置项详情查询异常", e);
            }
            return i;
        }).filter(i->this.priceFilter(i)).collect(Collectors.toList());
        configVo.setParameter(parameterVos);
        configVo.setModelLineOption(optionVos);
        return configVo;
    }

    @Override
    public List<ModelLineOptionVo> optionQueryByOptionIds(String channel, String customSeriesId, String modelLineId, List<String> optionIds) throws Exception {
        OptionParamDto param = new OptionParamDto();
        param.setModelLineId(modelLineId);
        param.setChannel(channel);
        param.setNotOptionTypes(Arrays.asList("packet-item"));
        param.setOptionIds(optionIds);
        param.setCustomSeriesId(customSeriesId);
        List<ModelLineOptionVo> optionVos = modelLineOptionService.optionQuery(param);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            optionVos = ChannelDataUtils.channelData(optionVos, ModelLineOptionVo.class, channel, "optionId", false);
        }
        List<String> optionCodes = optionVos.stream().map(ModelLineOptionVo::getOptionCode).collect(Collectors.toList());
        for (ModelLineOptionVo i : optionVos){
            CarOptionDetailVo carOptionDetailVo = new CarOptionDetailVo();
            carOptionDetailVo.setOptionId(i.getOptionId());
            carOptionDetailVo.setDelFlag(0);
            carOptionDetailVo.setChannel(channel);
            i.setOptionDetailList(optionDetailService.listOptionDetailVo(carOptionDetailVo));
            // 内饰没有价格
            if (i.getCategory() != null && i.getCategory().equals(OptionCategoryEnum.INCOLOR.getValue())){
                continue;
            }
            // 选择包所包含的配件不用算价格
            if(i.getCategory() != null && !i.getCategory().equals(OptionCategoryEnum.PACKET.getValue())) {
                List<ModelLineOptionVo> items = modelLineOptionMapper.findPaketItemFromCodes(customSeriesId, modelLineId, i.getOptionCode(), optionCodes);
                if (CollectionUtils.isNotEmpty(items)) {
                    continue;
                }
            }
            if (i.getStatus() == null || i.getStatus().intValue() != 1) {
                i.setPrice(priceTypeService.optionPrice(modelLineId, i.getOptionCode(), i.getCategory()));
                if (i.getPrice() == null){
                    throw new ServiceException("50001", "价格查询异常", "");
                }
            }
        }
        return optionVos;
    }

    @Override
    @Cacheable(cacheNames = {"cc_cache"}, keyGenerator = "springCacheKeyGenerator")
    public SeriesConfigsVo seriesConfigs(String channel, String customSeriesId) throws Exception {
        CustomSeriesParam seriesParam = new CustomSeriesParam();
        seriesParam.setCustomSeriesId(customSeriesId);
        List<CarCustomSeries> data = customSeriesService.listCustomSeries(channel, seriesParam);
        if (CollectionUtils.isEmpty(data)){
            throw new ServiceException("400401", "参数异常: customSeriesId", "");
        }
        SeriesConfigsVo configsVo = new SeriesConfigsVo();
        BeanUtils.copyProperties(data.get(0), configsVo);
        ModelParamDto paramDto = new ModelParamDto();
        paramDto.setCustomSeriesId(customSeriesId);
        paramDto.setDelFlag(0);
        paramDto.setChannel(channel);
        List<ModelLineVo> modelLines = this.listModelLine(paramDto);
        List<SeriesConfigsVo.ModelLineInfo> modelLineInfos = new ArrayList<>();
        configsVo.setModelLineInfos(modelLineInfos);
        for (ModelLineVo modelLineVo : modelLines) {
            SeriesConfigsVo.ModelLineInfo modelLineInfo = new SeriesConfigsVo.ModelLineInfo();
            modelLineInfos.add(modelLineInfo);
            String modelLineId = modelLineVo.getModelLineId();
            modelLineInfo.setModelLineId(modelLineId);
            String category = "RAD";
            ModelParamDto modelParamDto = new ModelParamDto();
            modelParamDto.setDelFlag(0);
            modelParamDto.setModelLineId(modelLineId);
            modelParamDto.setChannel(channel);
            List<ModelLineOptionVo> radVos = this.modelLineOption(channel, modelLineId, category);
            modelLineInfo.setRadOption(radVos);

            category = "COLOR_EXTERIEUR";
            modelParamDto.setDelFlag(0);
            modelParamDto.setModelLineId(modelLineId);
            modelParamDto.setChannel(channel);
            List<ModelLineOptionVo> exterVos = this.modelLineOption(channel, modelLineId, category);
            modelLineInfo.setExterieurOption(exterVos);
        }
        return configsVo;
    }

    @Override
    public List<ModelLineOptionVo> optionQueryByOptionId(String channel, String customSeriesId, String modelLineId, String optionId, boolean priceHandle) throws Exception {
        OptionParamDto param = new OptionParamDto();
        param.setModelLineId(modelLineId);
        param.setChannel(channel);
        param.setNotOptionTypes(Arrays.asList("packet-item"));
        param.setOptionIds(Arrays.asList(optionId));
        param.setCustomSeriesId(customSeriesId);
        List<ModelLineOptionVo> optionVos = modelLineOptionService.optionQuery(param);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            optionVos = ChannelDataUtils.channelData(optionVos, ModelLineOptionVo.class, channel, "optionId", false);
        }
        for (ModelLineOptionVo i : optionVos){
            CarOptionDetailVo carOptionDetailVo = new CarOptionDetailVo();
            carOptionDetailVo.setOptionId(i.getOptionId());
            carOptionDetailVo.setDelFlag(0);
            carOptionDetailVo.setChannel(channel);
            i.setOptionDetailList(optionDetailService.listOptionDetailVo(carOptionDetailVo));
            // 内饰没有价格
            if (i.getCategory() != null && i.getCategory().equals(OptionCategoryEnum.INCOLOR.getValue())){
                continue;
            }
            if (priceHandle) {
                if (i.getStatus() == null || i.getStatus().intValue() != 1) {
                    if (handlePriceControl(i.getModelLineId(), channel)){
                        i.setPrice(Constant.PRICE_NOT_HANDLE);
                    }else {
                        i.setPrice(priceTypeService.optionPrice(modelLineId, i.getOptionCode(), i.getCategory()));
                    }
                    if (i.getPrice() == null) {
                        throw new ServiceException("50001", "价格查询异常", "");
                    }
                }
            }
        }
        return optionVos;
    }

    @Override
    public EstimateDeliveryRes estimateQuery(EstimateDeliveryParam estimateDeliveryParam) throws Exception {
        CustomModelDto modelDto = this.getModelByLineOptions(estimateDeliveryParam.getCustomSeriesId(), estimateDeliveryParam.getModelLineId(), estimateDeliveryParam.getOptionIds());
        EstimateDeliveryBody body = new EstimateDeliveryBody();
        BeanUtils.copyProperties(modelDto, body);
        body.setModelCode(modelDto.getAccbTypeCode());
        body.setDealerNetCode(StringUtils.isNotEmpty(estimateDeliveryParam.getDealerNetCode()) ? estimateDeliveryParam.getDealerNetCode(): appConfig.getHqDealerCode());
        body.setTotalPrice(this.computePrice(estimateDeliveryParam));
        if (body.getTotalPrice() instanceof BigDecimal) {
            OmdObjectRes<EstimateDeliveryRes> res = omdService.estimateDeliveryQuery(body);
            return res.getResult().getResultData();
        }
        return null;
    }

    @Override
    public List<ModelLineOptionVo> modelLineOptionQueryWithOutPriceFilter(OptionParamDto param) throws NoSuchFieldException, IllegalAccessException {
        String channel = param.getChannel();
        String modelLineId = param.getModelLineId();
        List<ModelLineOptionVo> optionVos = modelLineOptionService.modelLineOptionQuery(param);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            optionVos = ChannelDataUtils.channelData(optionVos, ModelLineOptionVo.class, channel, "optionId", false);
        }
        optionVos = optionVos.stream().map(i->{
            if (i.getStatus() != null && i.getStatus().intValue() != 1) {
                try {
                    if (handlePriceControl(i.getModelLineId(), channel)){
                        i.setPrice(Constant.PRICE_NOT_HANDLE);
                    }else {
                        i.setPrice(priceTypeService.optionPrice(modelLineId, i.getOptionCode(), i.getCategory()));
                    }
                } catch (ServiceException e) {
                    i.setPrice(null);
                }
            }
            CarOptionDetailVo carOptionDetailVo = new CarOptionDetailVo();
            carOptionDetailVo.setOptionId(i.getOptionId());
            carOptionDetailVo.setDelFlag(0);
            carOptionDetailVo.setChannel(channel);
            try {
                i.setOptionDetailList(optionDetailService.listOptionDetailVo(carOptionDetailVo));
            } catch (Exception e) {
                log.error("配置项详情查询异常", e);
            }
            return i;
        }).collect(Collectors.toList());
        return optionVos;
    }

    @Override
    public Object measurePriceCompute(PriceComputeParam priceComputeParam) throws ServiceException {
        if (handlePriceControl(priceComputeParam.getModelLineId(), Constant.MASTER_CHANNEL)){
            return Constant.PRICE_NOT_HANDLE;
        }
        BigDecimal totalPrice = priceTypeService.typePrice(priceComputeParam.getModelLineId());
        // 特定配置线加上选装价格
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setModelLineId(priceComputeParam.getModelLineId());
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        modelParamDto.setDelFlag(0);
        List<ModelLineVo> vos = modelLineMapper.listModelLine(modelParamDto);
        ModelLineVo modelLine = vos.get(0);
        if (totalPrice != null && modelLine.getSpecialLine().intValue() == 1){
            BigDecimal specialOptionPrice = specialOptionService.totalOptionPrice(modelLine.getModelLineId());
            totalPrice = totalPrice.add(specialOptionPrice);
        }
        if (totalPrice == null){
            throw new ServiceException("50001", "价格异常");
        }
        List<String> options = priceComputeParam.getOptionIds();
        Map<String, ModelLineOptionVo> packageItemMap = new HashMap<>();
        List<String> duplicateCodes = new ArrayList<>();
        String sibCode = null;
        if (!CollectionUtils.isEmpty(options)){
            OptionParamDto optionParamDto = new OptionParamDto();
            optionParamDto.setModelLineId(priceComputeParam.getModelLineId());
            optionParamDto.setCustomSeriesId(priceComputeParam.getCustomSeriesId());
            optionParamDto.setOptionIds(options);
            optionParamDto.setChannel(Constant.MASTER_CHANNEL);
            optionParamDto.setDelFlag(0);
            List<ModelLineOptionVo> optionTotalVos = modelLineOptionService.modelLineOptionQuery(optionParamDto);
            Map<String, ModelLineOptionVo> vosTemp = new HashMap<>();
            Set<String> optionCodes = optionTotalVos.stream().map(ModelLineOptionVo::getOptionCode).collect(Collectors.toSet());
            log.debug("价格计算调试，params: {} , modelLine: {},  optionCodes: {}", priceComputeParam, modelLine, optionCodes);
            optionTotalVos.forEach(i->{
//                if (vosTemp.get(i.getOptionId()) == null){
//                    vosTemp.put(i.getOptionId(), new ArrayList<>());
//                }
                vosTemp.put(i.getOptionId(), i);
            });
            for(String optionId: options){
                ModelLineOptionVo option = vosTemp.get(optionId);
                if (option == null){
                    throw new ServiceException("400401", "参数异常：optionIds", "optionId: "+ optionId );
                }
                // 选装包的件不算价格
                if (option.getOptionType().equals("packet-item")){
                    continue;
                }
                if (option.getCategory() != null && option.getCategory().equals("SIB")){
                    sibCode = option.getOptionCode();
                }
                if (option.getCategory() != null && option.getCategory().equals("PACKET")) {
                    OptionParamDto paramDto = new OptionParamDto();
                    paramDto.setOptionId(optionId);
                    paramDto.setModelLineId(priceComputeParam.getModelLineId());
                    paramDto.setChannel(Constant.MASTER_CHANNEL);
                    paramDto.setDelFlag(0);
                    List<ModelLineOptionVo> packageItems = modelLineOptionService.listModelLinePacketItem(paramDto);
                    if (packageItems != null){
                        packageItems.forEach(i->{
                            if (packageItemMap.get(i.getOptionCode()) != null){
                                duplicateCodes.add(i.getOptionCode());
                            }else {
                                packageItemMap.put(i.getOptionCode(), i);
                            }
                        });
                    }
                }
                BigDecimal optionPrice = priceTypeService.optionPrice(priceComputeParam.getModelLineId(), option.getOptionCode(), option.getCategory());
                if (optionPrice != null) {
                    // 选装包中有的配件，不计算价格
                    List<ModelLineOptionVo> items = modelLineOptionMapper.findPaketItemFromCodes(modelLine.getCustomSeriesId(), modelLine.getModelLineId(), option.getOptionCode(), optionCodes);
                    if (CollectionUtils.isNotEmpty(items)) {
                        continue;
                    }
                    totalPrice = totalPrice.add(optionPrice);
                }else if (option.getCategory() == null
                        || !Arrays.asList(OptionCategoryEnum.INCOLOR.getValue(),OptionCategoryEnum.SIB.getValue(), OptionCategoryEnum.SEET.getValue())
                        .contains(option.getCategory()) // 半订制化配置过程中面料和座椅可以不算价格

                ){ // 内饰颜色不算价格
                    // 选装需要有价格，排除选装包的件，以及标装
                    if (option.getStatus() == null || option.getStatus().intValue() != 1){
                        List<ModelLineOptionVo> items = modelLineOptionMapper.findPaketItemFromCodes(modelLine.getCustomSeriesId(), modelLine.getModelLineId(), option.getOptionCode(), optionCodes);
                        if (CollectionUtils.isEmpty(items)) {
                            throw new ServiceException("50000", "价格异常", JSONObject.toJSONString(option));
                        }
                    }
                }
            }

            // remove duplicate package item price
            for (String i : duplicateCodes){
                BigDecimal optionPrice = priceTypeService.optionPrice(priceComputeParam.getModelLineId(), i, "");
                if (optionPrice != null)
                    totalPrice = totalPrice.subtract(optionPrice);
            }
        }
        return totalPrice;
    }


    @Cacheable(cacheNames = {"cc_cache"}, keyGenerator = "springCacheKeyGenerator")
    @Override
    public FrontOptions baseFrontOptions(ConfigValidationParam param, String channel) throws Exception {
        FrontOptions frontOptions = new FrontOptions();
        String modelLineId = param.getModelLineId();
        String category = OptionCategoryEnum.OUTCOLOR.getValue();
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = this.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        frontOptions.setExterieurOptions(this.modelLineOption(channel, modelLineId, category));

        List<ModelLineSibInterieurVo> sibInterieurVos = sibInterieurService.modelLineSibInterieur(channel, modelLineId, null);
        OptionRelateParam optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLineId);
        optionRelateParam.setRelateTypes(Arrays.asList("conflict", "depend"));
        List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        Map<String, List<OptionRelDto>> oMap = new HashMap<>();
        relDtos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel == null) {
                oRel = new ArrayList<>();
                oMap.put(i.getOptionId(), oRel);
            }
            oRel.add(i);
        });
        sibInterieurVos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getSibInterieurId());
            if (oRel != null){
                i.setSibInterieurRelates(oRel);
            }
        });

        frontOptions.setRadOptions(this.modelLineOption(channel, modelLineId, OptionCategoryEnum.WHEEL.getValue()));

        frontOptions.setVosOptions(this.modelLineOption(channel, modelLineId, OptionCategoryEnum.SEET.getValue()));

        frontOptions.setEihOptions(this.modelLineOption(channel, modelLineId, OptionCategoryEnum.EIH.getValue()));

        List<String> notInCategory = Arrays.asList("RAD", "COLOR_EXTERIEUR", "COLOR_INTERIEUR", "SIB", "EIH");
        List<ModelLineOptionVo> personalOptionVos = this.modelLinePersonalOption(channel, modelLineId, notInCategory);
        optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLineId);
        optionRelateParam.setRelateTypes(Arrays.asList("conflict", "depend"));
        relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        Map<String, List<OptionRelDto>> relMap = new HashMap<>();
        relDtos.forEach(i->{
            List<OptionRelDto> oRel = relMap.get(i.getOptionId());
            if (oRel == null) {
                oRel = new ArrayList<>();
                relMap.put(i.getOptionId(), oRel);
            }
            oRel.add(i);
        });
        personalOptionVos.forEach(i->{
            List<OptionRelDto> oRel = relMap.get(i.getOptionId());
            if (oRel != null){
                i.setOptionRelates(oRel);
            }
        });
        frontOptions.setPersonalOptions(personalOptionVos);

        return frontOptions;
    }

    @Cacheable(cacheNames = {"cc_cache"}, keyGenerator = "springCacheKeyGenerator")
    private FrontOptions preSelect(ConfigValidationParam param, String channel, FrontOptions frontOptions) throws Exception {
        String optionId = param.getSelectOptionId();
        String modelLineId = param.getModelLineId();
        Set<String> optionIds = param.getOptionIds();
        List<ModelLineOptionVo> switches = new ArrayList<>();
        // 查找与选项冲突的
        List<CarOptionRelate> conflicts = null;
        List<CarOptionRelate> depends = null;
        List<String> categoryFilter = Arrays.asList(OptionCategoryEnum.INCOLOR.getValue(), OptionCategoryEnum.SIB.getValue(), OptionCategoryEnum.IN_SIB.getValue());

        List<String> needSelectCategoryFilter = Arrays.asList(OptionCategoryEnum.OUTCOLOR.getValue(), OptionCategoryEnum.SEET.getValue(),
                OptionCategoryEnum.EIH.getValue(), OptionCategoryEnum.WHEEL.getValue());
        if (CollectionUtils.isNotEmpty(optionIds)) {
            conflicts = optionRelateService.listOptionConflict(param.getModelLineId(), optionId, optionIds);
            depends = optionRelateService.listOptionDepend(param.getModelLineId(), optionId, optionIds);
        }
        if (CollectionUtils.isNotEmpty(conflicts)){
            Set<String> conflictIds = conflicts.stream().filter( i->!categoryFilter.contains(i.getOptionRelateCategory()) )
                    .map(CarOptionRelate::getOptionRelateId).collect(Collectors.toSet());
            Set<String> conflictSibInterIds = conflicts.stream().filter( i->OptionCategoryEnum.IN_SIB.getValue().equals(i.getOptionRelateCategory()) )
                    .map(CarOptionRelate::getOptionRelateId).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(conflictIds)) {
                List<ModelLineOptionVo> optionVos = this.modelLineOptionQuery(channel, modelLineId, conflictIds);
                frontOptions.setNeedDeselect(optionVos);
            }
            if (CollectionUtils.isNotEmpty(conflictSibInterIds)) {
                SibInterieurQueryDto sibInterieurQueryDto = new SibInterieurQueryDto();
                sibInterieurQueryDto.setChannel(channel);
                sibInterieurQueryDto.setSibInterieurIds(conflictSibInterIds);
                sibInterieurQueryDto.setModelLineId(modelLineId);
                List<ModelLineSibInterieurVo> sibInterieurVos = sibInterieurService.modelLineSibInterieur(sibInterieurQueryDto);
                frontOptions.setNeedDeselectSibInterieur(sibInterieurVos);
            }
        }
        if (CollectionUtils.isNotEmpty(depends)){
            Set<String> dependIds = depends.stream().filter( i->!categoryFilter.contains(i.getOptionRelateCategory()) )
                    .map(CarOptionRelate::getOptionRelateId).collect(Collectors.toSet());
            Set<String> dependSibInterIds = depends.stream().filter( i->OptionCategoryEnum.IN_SIB.getValue().equals(i.getOptionRelateCategory()) )
                    .map(CarOptionRelate::getOptionRelateId).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(dependIds)) {
                List<ModelLineOptionVo> optionVos = this.modelLineOptionQuery(channel, modelLineId, dependIds);
                Map<String, List<ModelLineOptionVo>> needMap = new HashMap<>();
                for (ModelLineOptionVo optionVo : optionVos) {
                    if (needSelectCategoryFilter.contains(optionVo.getCategory())) {
                        if (needMap.get(optionVo.getCategory()) == null) {
                            needMap.put(optionVo.getCategory(), new ArrayList<>());
                        }
                        needMap.get(optionVo.getCategory()).add(optionVo);
                    } else {
                        if (needMap.get("personal") == null) {
                            needMap.put("personal", new ArrayList<>());
                        }
                        needMap.get("personal").add(optionVo);
                    }
                }
                frontOptions.setNeedSelect(needMap.values());
            }
            if (CollectionUtils.isNotEmpty(dependSibInterIds)){
                SibInterieurQueryDto sibInterieurQueryDto = new SibInterieurQueryDto();
                sibInterieurQueryDto.setChannel(channel);
                sibInterieurQueryDto.setSibInterieurIds(dependSibInterIds);
                sibInterieurQueryDto.setModelLineId(modelLineId);
                List<ModelLineSibInterieurVo> sibInterieurVos = sibInterieurService.modelLineSibInterieur(sibInterieurQueryDto);
                frontOptions.setNeedSelectSibInterieur(sibInterieurVos);
                frontOptions.setSibInterieurOptions(sibInterieurVos);
            }
        }
        // 选择包包含座椅，switch
        OptionParamDto paramDto = new OptionParamDto();
        paramDto.setOptionId(optionId);
        paramDto.setModelLineId(modelLineId);
        paramDto.setChannel(Constant.MASTER_CHANNEL);
        paramDto.setCategory(OptionCategoryEnum.SEET.getValue());
        paramDto.setDelFlag(0);
        List<ModelLineOptionVo> packageItems = modelLineOptionService.listModelLinePacketItem(paramDto);
        if (CollectionUtils.isNotEmpty(packageItems)){
            switches.add(packageItems.get(0));
            frontOptions.setNeedSwitch(switches);
        }

        // todo 1、过滤掉依赖中有冲突的项
        return frontOptions;
    }

    @Cacheable(cacheNames = {"cc_cache"}, keyGenerator = "springCacheKeyGenerator")
    private FrontOptions preDeselect(ConfigValidationParam param, String channel, FrontOptions frontOptions) throws Exception {
        String optionId = param.getDeselectOptionId();
        String modelLineId = param.getModelLineId();
        Set<String> optionIds = param.getOptionIds();
        List<ModelLineOptionVo> switches = new ArrayList<>();
        // 1、检查是否有依赖

        // 2、选包包含座椅switch
        OptionParamDto paramDto = new OptionParamDto();
        paramDto.setOptionId(optionId);
        paramDto.setModelLineId(modelLineId);
        paramDto.setChannel(Constant.MASTER_CHANNEL);
        paramDto.setCategory(OptionCategoryEnum.SEET.getValue());
        paramDto.setDelFlag(0);
        List<ModelLineOptionVo> packageItems = modelLineOptionService.listModelLinePacketItem(paramDto);
        if (CollectionUtils.isNotEmpty(packageItems)){
            List<ModelLineOptionVo> optionVos = this.modelLineOption(channel, modelLineId, OptionCategoryEnum.SEET.getValue());
            if (optionVos != null && optionVos.size() == 1){
                switches.add(optionVos.get(0));
            }
            frontOptions.setNeedSwitch(switches);
            frontOptions.setVosOptions(optionVos);
        }
        return null;
    }

    @Override
    public FrontOptions listFrontOptions(ConfigValidationParam param, String channel) throws Exception {
        FrontOptions frontOptions = baseFrontOptions(param, channel);
        // 校验之前的选项

        if (StringUtils.isNotBlank(param.getSelectOptionId())){
           // 预选
            this.preSelect(param, channel, frontOptions);
        }else if (StringUtils.isNotBlank(param.getDeselectOptionId())){
            // 预不选
            this.preDeselect(param, channel, frontOptions);
        }
        // 选项过滤所有冲突项
        return frontOptions;
    }

    @Override
    public EstimateDeliveryRes similarityModel(String ccid, String dealerNetCode, String channel) throws Exception {
        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        if (carCustom == null){
            throw new ServiceException("400401", "ccid不存在");
        }
        CarCustomDetail customDetail;
        if (carCustom.getBestRecommendId() != null){
            customDetail = recommendCustomService.getCarConfigDetailOmd(carCustom);
        }else {
            customDetail = carCustomService.getCarConfigDetailOmd(carCustom);
        }
        EstimateDeliveryBody body = new EstimateDeliveryBody();
        body.setModelCode(customDetail.getConfigDetail().getCarModel().getModelCode());
        body.setModelYear(customDetail.getConfigDetail().getCarModel().getModelYear());
        body.setModelVersion(customDetail.getConfigDetail().getCarModel().getOmdModelVersion());
        body.setBrandCode(Constant.BRAND_CODE);
        body.setColorCode(customDetail.getConfigDetail().getOutsideColor().getColorCode());
        body.setInteriorCode(customDetail.getConfigDetail().getInsideColor().getColorCode());
        body.setPrList("");
        if (customDetail.getConfigDetail().getOptionList() != null){
            String prList = customDetail.getConfigDetail().getOptionList().stream().map(Option::getOptionCode)
                    .collect(Collectors.joining(","));
            body.setPrList(prList);
        }
        body.setDealerNetCode(dealerNetCode);
        body.setTotalPrice(customDetail.getConfigDetail().getTotalPrice());
        if (body.getTotalPrice() instanceof BigDecimal) {
            OmdObjectRes<EstimateDeliveryRes> res = omdService.estimateDeliveryQuery(body);
            convertSimilarityModel(res.getResult().getResultData(), channel);
            log.info("相似车 ccid: {}, 相似车结果: {}", ccid, res);
            return res.getResult().getResultData();
        }
        return null;
    }

    @Override
    public EstimateDeliveryRes estimateQueryByCc(String ccid, String dealerNetCode) throws Exception {
        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        if (carCustom == null){
            throw new ServiceException("400401", "ccid不存在");
        }
        dealerNetCode = StringUtils.isNotEmpty(dealerNetCode) ? dealerNetCode: appConfig.getHqDealerCode();
        CarCustomDetail customDetail;
        if (carCustom.getBestRecommendId() != null){
            customDetail = recommendCustomService.getCarConfigDetailOmd(carCustom);
        }else {
            customDetail = carCustomService.getCarConfigDetailOmd(carCustom);
        }
        EstimateDeliveryBody body = new EstimateDeliveryBody();
        body.setModelCode(customDetail.getConfigDetail().getCarModel().getModelCode());
        body.setModelYear(customDetail.getConfigDetail().getCarModel().getModelYear());
        body.setModelVersion(customDetail.getConfigDetail().getCarModel().getOmdModelVersion());
        body.setBrandCode(Constant.BRAND_CODE);
        body.setColorCode(customDetail.getConfigDetail().getOutsideColor().getColorCode());
        body.setInteriorCode(customDetail.getConfigDetail().getInsideColor().getColorCode());
        body.setPrList("");
        if (customDetail.getConfigDetail().getOptionList() != null){
            String prList = customDetail.getConfigDetail().getOptionList().stream().map(Option::getOptionCode)
                    .collect(Collectors.joining(","));
            body.setPrList(prList);
        }
        body.setDealerNetCode(dealerNetCode);
        body.setTotalPrice(customDetail.getConfigDetail().getTotalPrice());
        if (body.getTotalPrice() instanceof BigDecimal) {
            OmdObjectRes<EstimateDeliveryRes> res = omdService.estimateDeliveryQuery(body);
            return res.getResult().getResultData();
        }
        return null;
    }

    @Override
    public boolean measureFilter(String channel, String accbTypeCode, String modelLineId) {
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines;
        try {
             lines = this.listModelLine(modelParamDto);
        } catch (Exception e) {
            log.error("配置线异常", e);
            return false;
        }
        ModelLineVo line = lines.get(0);
        if (line.getMeasure() == null || line.getMeasure().compareTo(1) != 0){
            return true;
        }
        String omdAccbTypeCode = accbTypeCode.replace("TYPE:", "");
        CarMeasureMadeOriginConfig originConfig = new CarMeasureMadeOriginConfig();
        originConfig.setAccbTypeCode(omdAccbTypeCode);
        originConfig.setModelYear(line.getModelYear());
        originConfig.setModelVersion(line.getVersion());
        if (modelLineId.equals("9bb9a4e5-096b-4f3a-8660-96feb126b271")){// 白法师
            originConfig.setColorCode("2YA2");
        }else if (modelLineId.equals("3bcc54ad-28e9-4499-98f3-2a8abf8fdcda")){// 黑武士
            originConfig.setColorCode("2T2T");
        }
        List<String> q6_black = Arrays.asList("cb120ed6-fd89-4816-9c45-7e547b049ba5" ,
                "93fa8cba-e3b5-4c18-9d30-6704af859073");

        List<String> q6_gray = Arrays.asList("0ac42d7a-8eb7-49d3-8679-3d664ab271e8",
                "2ac41b68-3811-45a6-bd66-94e95bc7c57f");

        if (q6_black.contains(modelLineId)){
            originConfig.setColorCode("A2A2");
        } else if (q6_gray.contains(modelLineId)) {
            originConfig.setColorCode("6ZA2");
        }

        // q5e 黑/影/武
        switch (modelLineId){
            case "06db59c6-0b8f-44a9-90ba-36c497f792df":
            case "6267b947-5f36-4402-a29d-0c2c2a6aad74":
                originConfig.setColorCode("2T2T");
                break;
            case "c481fdcd-8882-4793-be93-a0a0cc74e457":
            case "d9b0ad13-f6cf-496a-a5fc-99457e344a45":
                originConfig.setColorCode("2YA1");
                break;
            case "3d6efe23-0f97-4582-b381-62f7cf7e5621":
            case "8be2ec70-fb5c-4b92-a708-e33d1dbcc2d9":
                originConfig.setColorCode("2LA1");
                break;
            default:
                break;
        }

        // a7l 黑/影
        switch (modelLineId){
            case "1d8dd59c-9d4d-47c2-803f-bd3f1fc853c0":
            case "996fdfce-ec40-40a6-b206-ee644ea47b8a":
                originConfig.setColorCode("2T2T");
                break;
            case "65ca3622-5d84-4f39-85ae-79a23e248b21":
            case "0f953089-519e-458a-b7ed-b817407b45e7":
                originConfig.setColorCode("2LA2");
                break;
            default:
                break;
        }

        // Q6 黑影
        switch (modelLineId){
            case "cb120ed6-fd89-4816-9c45-7e547b049ba5":
            case "93fa8cba-e3b5-4c18-9d30-6704af859073":
            case "2b0d0e02-34e8-4523-b4d2-9c617f8d0243":
                originConfig.setColorCode("A2A2");
                break;
            case "a5bd8d13-e883-468d-a88c-41eb3caeb6c8":
            case "2ac41b68-3811-45a6-bd66-94e95bc7c57f":
            case "0ac42d7a-8eb7-49d3-8679-3d664ab271e8":
                originConfig.setColorCode("6ZA2");
                break;
            default:
                break;
        }

        int count = measureMadeConfigService.measureCount(originConfig);
        return count > 0;
    }

    @Override
    public List<AdminModel> listAdminModels(String customSeriesId) {
        return modelLineMapper.listAdminModels(customSeriesId);
    }

    @Override
    public List<String> listBlips(String modelLineId) {
        return modelLineMapper.listBlips(modelLineId);
    }

    /**
     * 待交付时间查询
     * @param estimateDeliveryParam
     * @return
     * @throws ServiceException
     * 查询AB类表
     * A类：4周起
     * B类：3个月起
     * C类：6个月起
     *
     */
    @Override
    public CcEstimateVo ccEstimate(CcEstimateDeliveryParam estimateDeliveryParam) throws ServiceException {
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setModelLineId(estimateDeliveryParam.getModelLineId());
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        modelParamDto.setDelFlag(0);
        List<ModelLineVo> vos = modelLineMapper.listModelLine(modelParamDto);
        ModelLineVo modelLine = vos.get(0);
        CcEstimateVo res = new CcEstimateVo();
        if (estimateDeliveryParam.getMeasure() != null && estimateDeliveryParam.getMeasure().intValue() == 1){
            res.setType("A");
            res.setDeliveryTime(DeliveryTimeEnum.A.getValue());
            res.setEarnestMoney(modelLineService.getModelEarnestMoney(modelLine.getCustomSeriesCode(),
                    modelLine.getAccbTypeCode(), Integer.valueOf(DepositTypeEnum.STOCK.getType())));
            return res;
        }
        List<String> q5e6Seat = Arrays.asList("WE8", "8I6+WE8", "4D3+WE8");
        ModelLineTypeDto modelLineTypeDto = new ModelLineTypeDto();
        modelLineTypeDto.setAccbTypeCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        modelLineTypeDto.setModelYear(modelLine.getModelYear());
        modelLineTypeDto.setModelVersion(modelLine.getVersion());
        modelLineTypeDto.setSeat(estimateDeliveryParam.getSeats());
        List<String> prCodes = new ArrayList<>();
        List<String> options = estimateDeliveryParam.getOptionIds();
        res.setType("C");
        res.setDeliveryTime(DeliveryTimeEnum.C.getValue());
        res.setEarnestMoney(modelLineService.getModelEarnestMoney(modelLine.getCustomSeriesCode(),
                modelLine.getAccbTypeCode(), Integer.valueOf(DepositTypeEnum.SCHEDULE.getType())));
        if (!CollectionUtils.isEmpty(options)) {
            OptionParamDto optionParamDto = new OptionParamDto();
            optionParamDto.setModelLineId(estimateDeliveryParam.getModelLineId());
            optionParamDto.setCustomSeriesId(estimateDeliveryParam.getCustomSeriesId());
            optionParamDto.setOptionIds(options);
            optionParamDto.setChannel(Constant.MASTER_CHANNEL);
            optionParamDto.setDelFlag(0);
            List<ModelLineOptionVo> optionTotalVos = modelLineOptionService.modelLineOptionQuery(optionParamDto);
            for (ModelLineOptionVo vo : optionTotalVos){
                if (modelLine.getCustomSeriesCode().equals("G4") && q5e6Seat.contains(vo.getOptionCode())){
                    modelLineTypeDto.setSeat("6");
                }else if (modelLine.getCustomSeriesCode().equals("G6") && vo.getOptionCode().equals("PS1")){
                    modelLineTypeDto.setSeat("6");
                }
                if (vo.getCategory() == null){
                    prCodes.add(vo.getOptionCode());
                    break;
                }
                if (vo.getCategory().equals(OptionCategoryEnum.OUTCOLOR.getValue())){
                    modelLineTypeDto.setExtCode(vo.getOptionCode());
                }else if (vo.getCategory().equals(OptionCategoryEnum.INCOLOR.getValue())){
                    modelLineTypeDto.setInCode(vo.getOptionCode());
                }else if (vo.getCategory().equals(OptionCategoryEnum.WHEEL.getValue())){
                    modelLineTypeDto.setRadCode(vo.getOptionCode());
                }else if (vo.getCategory().equals(OptionCategoryEnum.SIB.getValue())){
                    modelLineTypeDto.setSibCode(vo.getOptionCode());
                }else if (vo.getCategory().equals(OptionCategoryEnum.EIH.getValue())){
                    modelLineTypeDto.setEihCode(vo.getOptionCode());
                }else if (vo.getCategory().equals(OptionCategoryEnum.SEET.getValue()) && vo.getStatus().intValue() == 1){
                    continue;
                } else {
                    prCodes.add(vo.getOptionCode());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(prCodes)){
            modelLineTypeDto.setPrCodes(prCodes);
            modelLineTypeDto.setPrCodesNum(prCodes.size());
        }
        String type = null;
        if (estimateDeliveryParam.getBeforeCheck()) {
             type = modelLineTypeService.findBeforeCheckType(modelLineTypeDto);
        }else {
            ModelTypeResultDto modelTypeDto = modelLineTypeService.findType(modelLineTypeDto);
            type = modelTypeDto == null ? null : modelTypeDto.getClassify();
            res.setOmdVehicleTypeId(modelTypeDto == null ? null : modelTypeDto.getOmdVehicleTypeId());
        }
        if(type != null){
            Integer depositType = Integer.valueOf(DepositTypeEnum.SCHEDULE.getType());
            if (type.equals("A")){
                res.setDeliveryTime(DeliveryTimeEnum.A.getValue());
                depositType = Integer.valueOf(DepositTypeEnum.STOCK.getType());
            }else if (type.equals("B")){
                res.setDeliveryTime(DeliveryTimeEnum.B.getValue());
                depositType = Integer.valueOf(DepositTypeEnum.STOCK.getType());
            }
            res.setType(type);
            res.setEarnestMoney(modelLineService.getModelEarnestMoney(modelLine.getCustomSeriesCode(),
                    modelLine.getAccbTypeCode(), depositType));
        }
        // 查询配置裸车, q5e和q6的六座包不能计算在内
        if (estimateDeliveryParam.getBeforeCheck()!= null && !estimateDeliveryParam.getBeforeCheck()){
            modelLineTypeDto.setPrCodes(null);
            ModelTypeResultDto modelTypeDto = modelLineTypeService.findType(modelLineTypeDto);
            String noEquipType = modelTypeDto == null ? null : modelTypeDto.getClassify();
            if (noEquipType != null && !"C".equals(noEquipType)) {
                res.setCanNoEquipment(true);
            }
        }
        return res;
    }

    @Override
    public List<ModelLineOptionVo> handleVirtualRad(ModelLineVo line, String seats, List<ModelLineOptionVo> optionVos) throws Exception {
        String channel = line.getChannel();
        String modelLineId = line.getModelLineId();
        List<String> handleChannel = Arrays.asList(Constant.ONEAPP_CHANNEL, Constant.MINIP, Constant.SPHERE, Constant.OFFICIAL);
        if (!handleChannel.contains(channel)){
            return optionVos;
        }
        // c，看选装
        if (StringUtils.isNotBlank(line.getTypeFlag()) && line.getCustomSeriesCode().equals("G6")
                && "2024".equals(line.getModelYear())){
            final String pc2Code = "PC2";
            List<String> notInCategory = Arrays.asList("RAD", "COLOR_EXTERIEUR", "COLOR_INTERIEUR", "SIB", "EIH");
            List<ModelLineOptionVo> personal = modelLineService.modelLinePersonalOption(channel, modelLineId, notInCategory);
            ModelLineOptionVo pc2Option = personal.stream().filter(o->o.getOptionCode().equals(pc2Code)).findFirst().orElse(null);

            TypePrListQueryDto queryDto = new TypePrListQueryDto(line.getAccbTypeCode(), line.getModelYear(), line.getVersion(), personal);
            if (StringUtils.isNotBlank(seats)) {
                queryDto.setSeats(seats);
            }
            List<PersonalOptionVo.PersonalCompose> pc2Compose = new ArrayList<>();
            List<PersonalOptionVo.PersonalCompose> personalComposes = modelLineTypeService.queryPersonalComposes(queryDto);
            if (CollectionUtils.isNotEmpty(personalComposes)){
                pc2Compose.addAll(personalComposes.stream().filter(c->c.getComposePersonalOptions().stream().anyMatch(co->pc2Code.equals(co.getOptionCode()))).collect(Collectors.toList()));
            }
            String radUrlPrefix = "/ccpro-backend/q6/option/master/";
            // ab，看组合，a取交集
            if ((line.getTypeFlag().contains("C") && pc2Option != null && pc2Option.getStatus() != null && pc2Option.getStatus().intValue() == 2)
                    || (line.getTypeFlag().contains("B") && CollectionUtils.isNotEmpty(pc2Compose))){
                List<ModelLineOptionVo> newOptionVos  = new ArrayList<>();
                optionVos.forEach(newO -> {
                    newOptionVos.add(newO);
                    ModelLineOptionVo tmp = new ModelLineOptionVo();
                    BeanUtils.copyProperties(newO, tmp);
                    tmp.setOptionName(tmp.getOptionName()+" "+pc2Option.getOptionName());
                    String fixCode = newO.getOptionCode()+"+"+pc2Code;
                    tmp.setOptionCode(fixCode);
                    try {
                        tmp.setImageUrl(radUrlPrefix + URLEncoder.encode(fixCode, "UTF-8") +".png");
                        tmp.setImageUrlList(radUrlPrefix + URLEncoder.encode(fixCode, "UTF-8") +"_list.png");
                    } catch (UnsupportedEncodingException e) {
                        e.printStackTrace();
                    }
                    if(tmp.getOptionRelates() == null){
                        tmp.setOptionRelates(new ArrayList<>());
                    }
                    List<OptionRelDto> optionRelates = new ArrayList<>();
                    optionRelates.addAll(tmp.getOptionRelates());
                    tmp.setOptionRelates(optionRelates);
                    OptionRelDto relDto = new OptionRelDto();
                    relDto.setRelateType("depend");
                    relDto.setOptionRelateCategory(pc2Option.getCategory());
                    relDto.setOptionRelateCode(pc2Option.getOptionCode());
                    relDto.setOptionRelateId(pc2Option.getOptionId());
                    tmp.getOptionRelates().add(relDto);
                    newOptionVos.add(tmp);
                });
                optionVos = newOptionVos;
            }else if ((line.getTypeFlag().equals("A") && CollectionUtils.isNotEmpty(pc2Compose))){
                List<ModelLineOptionVo> newOptionVos  = new ArrayList<>();
                optionVos.forEach(newO -> {
                    newOptionVos.add(newO);
                    Set<String> typeIdOfA = new HashSet<>();
                    for (PersonalOptionVo.PersonalCompose com : pc2Compose){
                        for (String radTypeId : newO.getTypeIdsOfA()){
                            for (String comTypeId : com.getTypeIdsOfA()){
                                if (comTypeId.equals(radTypeId)) {
                                    typeIdOfA.add(radTypeId);
                                    break;
                                }
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(typeIdOfA)) {
                        ModelLineOptionVo tmp = new ModelLineOptionVo();
                        BeanUtils.copyProperties(newO, tmp);
                        tmp.setOptionName(tmp.getOptionName() + " " + pc2Option.getOptionName());
                        tmp.setTypeIdsOfA(typeIdOfA);
                        String fixCode = newO.getOptionCode()+"+"+pc2Code;
                        tmp.setOptionCode(fixCode);
                        try {
                            tmp.setImageUrl(radUrlPrefix + URLEncoder.encode(fixCode, "UTF-8") +".png");
                            tmp.setImageUrlList(radUrlPrefix + URLEncoder.encode(fixCode, "UTF-8") +"_list.png");
                        } catch (UnsupportedEncodingException e) {
                            e.printStackTrace();
                        }
                        if(tmp.getOptionRelates() == null){
                            tmp.setOptionRelates(new ArrayList<>());
                        }
                        List<OptionRelDto> optionRelates = new ArrayList<>();
                        optionRelates.addAll(tmp.getOptionRelates());
                        tmp.setOptionRelates(optionRelates);
                        OptionRelDto relDto = new OptionRelDto();
                        relDto.setRelateType("depend");
                        relDto.setOptionRelateCategory(pc2Option.getCategory());
                        relDto.setOptionRelateCode(pc2Option.getOptionCode());
                        relDto.setOptionRelateId(pc2Option.getOptionId());
                        tmp.getOptionRelates().add(relDto);
                        newOptionVos.add(tmp);
                    }
                });
                optionVos = newOptionVos;
            }
        }
        return optionVos;
    }

    @Cacheable(cacheNames = {"cc_cache"}, keyGenerator = "springCacheKeyGenerator")
    @Override
    public BigDecimal getModelEarnestMoney(String customSeriesCode, String accbTypeCode, int depositType) {
        JSONObject resultJson = copProdQueryFeign.selectProdCar(customSeriesCode,1);
        JSONObject dataJson = resultJson.getJSONObject("data");
        String prodId = dataJson.getString("prodId");
        resultJson = copProdQueryFeign.ngaProdPrice(String.valueOf(depositType),
                accbTypeCode.replaceFirst("TYPE:", ""), prodId, customSeriesCode);
        return BigDecimal.valueOf(resultJson.getJSONObject("data").getLong("prodNgaDownPayPrice"));
    }

    @Override
    public List<AmsQueryVo> convertModelQuery(List<ModelQueryRes> models, String channel, String accbTypeCode) {
        List<AmsQueryVo> modelQueryVos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(models)){
            Iterator<ModelQueryRes> it = models.iterator();
            while (it.hasNext()){
                ModelQueryRes i = it.next();
                AmsQueryVo vo = new AmsQueryVo();
                vo.setList(i.getList());

                OmdModelDto omdModelDto = new OmdModelDto();
                BeanUtils.copyProperties(i, omdModelDto);
                omdModelDto.setClassCode(Constant.BRAND_CODE);
                omdModelDto.setModelCode(accbTypeCode);
                try {
                    BestRecommendCarVo recommendCarVo = recommendService.bestRecommendToVo(omdModelDto, channel);
                    if (recommendCarVo == null){
                        it.remove();
                        continue;
                    }
                    vo.setModelLine(recommendCarVo.getModelLine());
                    vo.setOptions(recommendCarVo.getOptions());
                    vo.setSibInterieur(recommendCarVo.getModelLineSibInterieurVo());
                    Set<String> categories = vo.getOptions().stream().map(ModelLineOptionVo::getCategory).collect(Collectors.toSet());
                    if (!categories.contains(OptionCategoryEnum.WHEEL.getValue())){
                        String category = OptionCategoryEnum.WHEEL.getValue();
                        List<ModelLineOptionVo> optionVos = this.modelLineOption(channel, recommendCarVo.getModelLine().getModelLineId(), category);
                        for(ModelLineOptionVo o : optionVos){
                            if (o.getStatus() != null && o.getStatus().intValue() == 1) {
                                vo.getOptions().add(o);
                                break;
                            }
                        }
                    }
                    if(!categories.contains(OptionCategoryEnum.SEET.getValue())){
                        vo.getOptions().add(recommendCarVo.getVosOption());
                    }

                    if (!categories.contains(OptionCategoryEnum.EIH.getValue())){
                        String category = OptionCategoryEnum.EIH.getValue();
                        List<ModelLineOptionVo> optionVos = this.modelLineOption(channel, recommendCarVo.getModelLine().getModelLineId(), category);
                        for(ModelLineOptionVo o : optionVos){
                            if (o.getStatus() != null && o.getStatus().intValue() == 1){
                                vo.getOptions().add(o);
                                break;
                            }
                        }
                    }
                    PriceComputeParam priceComputeParam = new PriceComputeParam();
                    priceComputeParam.setCustomSeriesId(vo.getModelLine().getCustomSeriesId());
                    priceComputeParam.setModelLineId(vo.getModelLine().getModelLineId());
                    priceComputeParam.setOptionIds(vo.getOptions().stream().map(ModelLineOptionVo::getOptionId).collect(Collectors.toList()));
                    if(!priceComputeParam.getOptionIds().contains(vo.getSibInterieur().getSibOptionId())){
                        priceComputeParam.getOptionIds().add(vo.getSibInterieur().getSibOptionId());
                    }
                    if(!priceComputeParam.getOptionIds().contains(vo.getSibInterieur().getInterieurOptionId())){
                        priceComputeParam.getOptionIds().add(vo.getSibInterieur().getInterieurOptionId());
                    }
                    vo.setTotalPrice(computePrice(priceComputeParam));
                } catch (Exception e) {
                    log.error("相似车数据转换异常", e);
                    it.remove();
                    continue;
                }
                String prList = i.getPrList();
                if (i.getPrList() == null){
                    prList= "";
                }
                vo.setUniqueCode(i.getModelCode() + i.getColorCode() + i.getInteriorCode() + prList);
                modelQueryVos.add(vo);

            }
        }
        return modelQueryVos;
    }

    private EstimateDeliveryRes convertSimilarityModel(EstimateDeliveryRes estimateDeliveryRes, String channel) throws Exception {
        if (CollectionUtils.isNotEmpty(estimateDeliveryRes.getSimilarityModelList())){
            estimateDeliveryRes.setCcSimilarityModelList(new ArrayList<>());
            Iterator<EstimateDeliveryRes.SimilarityModelList> it = estimateDeliveryRes.getSimilarityModelList().iterator();
            int num =0;
            while (it.hasNext()){
                EstimateDeliveryRes.SimilarityModelList i = it.next();
                if (num >= 3){
                    it.remove();
                    continue;
                }
                EstimateDeliveryRes.SimilarityModel similarityModel = new EstimateDeliveryRes.SimilarityModel();
                similarityModel.setEstimateDeliveryTime(i.getEstimateDeliveryTime());
                if (StringUtils.isNotBlank(i.getEstimateDeliveryTime())) {
                    similarityModel.setEstimateDate(LocalDate.now().plusDays(Integer.valueOf(i.getEstimateDeliveryTime())));
                }
                similarityModel.setSimilarity(i.getSimilarity());
                OmdModelDto omdModelDto = new OmdModelDto();
                BeanUtils.copyProperties(i, omdModelDto);
                omdModelDto.setClassCode(Constant.BRAND_CODE);
                try {
                    BestRecommendCarVo recommendCarVo = recommendService.bestRecommendToVo(omdModelDto, channel);
                    if (recommendCarVo == null){
                        it.remove();
                        continue;
                    }
                    similarityModel.setModelLine(recommendCarVo.getModelLine());
                    similarityModel.setOptions(recommendCarVo.getOptions());
                    similarityModel.setSibInterieur(recommendCarVo.getModelLineSibInterieurVo());
                    Set<String> categories = similarityModel.getOptions().stream().map(ModelLineOptionVo::getCategory).collect(Collectors.toSet());
                    if (!categories.contains(OptionCategoryEnum.WHEEL.getValue())){
                        String category = OptionCategoryEnum.WHEEL.getValue();
                        List<ModelLineOptionVo> optionVos = this.modelLineOption(channel, recommendCarVo.getModelLine().getModelLineId(), category);
                        for(ModelLineOptionVo o : optionVos){
                            if (o.getStatus() != null && o.getStatus().intValue() == 1) {
                                similarityModel.getOptions().add(o);
                                break;
                            }
                        }
                    }
                    if(!categories.contains(OptionCategoryEnum.SEET.getValue())){
                        similarityModel.getOptions().add(recommendCarVo.getVosOption());
                    }

                    if (!categories.contains(OptionCategoryEnum.EIH.getValue())){
                        String category = OptionCategoryEnum.EIH.getValue();
                        List<ModelLineOptionVo> optionVos = this.modelLineOption(channel, recommendCarVo.getModelLine().getModelLineId(), category);
                        for(ModelLineOptionVo o : optionVos){
                            if (o.getStatus() != null && o.getStatus().intValue() == 1){
                                similarityModel.getOptions().add(o);
                                break;
                            }
                        }
                    }
                    PriceComputeParam priceComputeParam = new PriceComputeParam();
                    priceComputeParam.setCustomSeriesId(similarityModel.getModelLine().getCustomSeriesId());
                    priceComputeParam.setModelLineId(similarityModel.getModelLine().getModelLineId());
                    priceComputeParam.setOptionIds(similarityModel.getOptions().stream().map(ModelLineOptionVo::getOptionId).collect(Collectors.toList()));
                    if(!priceComputeParam.getOptionIds().contains(similarityModel.getSibInterieur().getSibOptionId())){
                        priceComputeParam.getOptionIds().add(similarityModel.getSibInterieur().getSibOptionId());
                    }
                    if(!priceComputeParam.getOptionIds().contains(similarityModel.getSibInterieur().getInterieurOptionId())){
                        priceComputeParam.getOptionIds().add(similarityModel.getSibInterieur().getInterieurOptionId());
                    }
                    similarityModel.setTotalPrice(computePrice(priceComputeParam));
                } catch (Exception e) {
                    log.error("相似车数据转换异常", e);
                    it.remove();
                    continue;
                }
                String prList = i.getPrList();
                if (i.getPrList() == null){
                    prList= "";
                }
                similarityModel.setUniqueCode(i.getModelCode() + i.getColorCode() + i.getInteriorCode() + prList);
                estimateDeliveryRes.getCcSimilarityModelList().add(similarityModel);
                num += 1;
            }
        }
        return estimateDeliveryRes;
    }

    private CustomModelDto getModelByLineOptions(String customSeriesId, String modelLineId, List<String> optionIds) throws Exception {
        CustomModelDto modelDto = new CustomModelDto();
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineVo> lines = this.listModelLine(modelParamDto);
        if (lines == null ||  lines.size() != 1){
            throw new ServiceException("500", "服务异常", null);
        }
        ModelLineVo modelLine = lines.get(0);
        CustomSeriesDto customSeriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
        modelDto.setBrandCode("A");
        modelDto.setAccbTypeCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        modelDto.setModelCode(modelDto.getAccbTypeCode().split("-")[0]);
        modelDto.setModelYear(modelLine.getModelYear());
        modelDto.setModelVersion(modelLine.getVersion());

        OptionRelateParam optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLine.getModelLineId());
        optionRelateParam.setRelateTypes(Arrays.asList("attach"));
        List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        OptionRelateParam combineRelateParam = new OptionRelateParam();
        combineRelateParam.setRelateTypes(Arrays.asList("combine"));
        List<OptionRelDto> relCombineDtos =  optionRelateService.listOptionRelDto(combineRelateParam);
        relDtos.addAll(relCombineDtos);
        Map<String, List<OptionRelDto>> oMap = new HashMap<>();
        relDtos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel == null) {
                oRel = new ArrayList<>();
                oMap.put(i.getOptionId(), oRel);
            }
            oRel.add(i);
        });

        OptionParamDto optionParamDto = new OptionParamDto();
        optionParamDto.setModelLineId(modelLine.getModelLineId());
        optionParamDto.setOptionIds(optionIds);
        optionParamDto.setChannel(Constant.MASTER_CHANNEL);
        optionParamDto.setDelFlag(0);
        List<ModelLineOptionVo> optionVos = modelLineOptionService.modelLineOptionQuery(optionParamDto);
        if (optionVos == null || optionVos.size() == 0){
            throw new ServiceException("400401", "参数异常：optionIds", null);
        }
        List<String> prCodes = new ArrayList<>();
        Collection<String> optionCodes = optionVos.stream().map(ModelLineOptionVo::getOptionCode).collect(Collectors.toSet());
        for(ModelLineOptionVo optionVo : optionVos){

            // 过滤选装包的件与标装
            if (optionVo.getOptionType().equals("packet-item") || (optionVo.getStatus() != null && optionVo.getStatus().intValue() == 1)){
                continue;
            }
            List<ModelLineOptionVo> items =  modelLineOptionMapper.findPaketItemFromCodes(customSeriesDto.getCustomSeriesId(), modelLine.getModelLineId(), optionVo.getOptionCode(), optionCodes);
            if (CollectionUtils.isNotEmpty(items)){
                continue;
            }
            if (optionVo.getCategory() != null && optionVo.getCategory().equals("COLOR_EXTERIEUR")){
                String code = optionVo.getOptionCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                if (code.startsWith("COLOR_EXTERIEUR:")){
                    code = code.replaceFirst("COLOR_EXTERIEUR:", "");
                }
                modelDto.setColorCode(code);
                if (oMap.get(optionVo.getOptionId()) != null) {
                    for (OptionRelDto relDto : oMap.get(optionVo.getOptionId())) {
                        if (relDto.getRelateType().equals("attach")) {
                            prCodes.add(relDto.getOptionRelateCode());
                        }
                    }
                }
            }else if (optionVo.getCategory() != null && optionVo.getCategory().equals("COLOR_INTERIEUR")){
                String code = optionVo.getOptionCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                if (code.startsWith("COLOR_INTERIEUR:")){
                    code = code.replaceFirst("COLOR_INTERIEUR:", "");
                }
                modelDto.setInteriorCode(code);
                if (oMap.get(optionVo.getOptionId()) != null) {
                    for (OptionRelDto relDto : oMap.get(optionVo.getOptionId())) {
                        if (relDto.getRelateType().equals("attach")) {
                            prCodes.add(relDto.getOptionRelateCode());
                        }
                    }
                }
            }else {
                String code = optionVo.getOptionCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                String[] codes = code.split(":");
                if (codes.length == 1){
                    code = codes[0];
                }else if (codes.length == 2){
                    code = codes[1];
                }
                boolean combine = false;
                if (oMap.get(optionVo.getOptionId()) != null){
                    for (OptionRelDto relDto: oMap.get(optionVo.getOptionId())) {
                        if (relDto.getRelateType().equals("combine")) {
                            prCodes.add(relDto.getOptionRelateCode());
                            combine = true;
                        }else if (relDto.getRelateType().equals("attach")) {
                            prCodes.add(relDto.getOptionRelateCode());
                        }
                    }
                }
                if (!combine) {
                    prCodes.add(code);
                }
            }
        }
        modelDto.setPrList(Strings.join(prCodes, ','));
        return modelDto;
    }

    private boolean priceFilter(ModelLineOptionVo option){
        // 标装不算价格
        if(option.getStatus() != null && option.getStatus().intValue() == 1) {
            option.setPrice(null);
            return true;
        } else if(option.getPrice() == null){
            return false;
        }
        return true;
    }

    private boolean personalOptionFilter(ModelLineOptionVo optionVo){
        if (!priceFilter(optionVo)){
            return false;
        }
        if (optionVo.getStatus() != null
                && (optionVo.getStatus().intValue() == 1 || optionVo.getStatus().intValue() == 2)
                && StringUtils.isBlank(optionVo.getImageUrl())){
            return false;
        }
        return true;
    }

    private boolean handlePriceControl(String modelLineId, String channel){
        LambdaQueryWrapper<CarModelLineExt> lineExtQuery = new LambdaQueryWrapper<>();
        lineExtQuery.eq(CarModelLineExt::getModelLineId, modelLineId).
                eq(CarModelLineExt::getPriceUnhandle, 1).
                eq(CarModelLineExt::getChannel, channel).
                eq(CarModelLineExt::getDelFlag, 0);
        return CollectionUtils.isNotEmpty(lineExtService.list(lineExtQuery));
    }


}
