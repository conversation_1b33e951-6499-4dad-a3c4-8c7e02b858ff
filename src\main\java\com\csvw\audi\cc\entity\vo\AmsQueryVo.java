package com.csvw.audi.cc.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AmsQueryVo {
    @ApiModelProperty("配置线id")
    private ModelLineVo modelLine;
    @ApiModelProperty("配置选装")
    private List<ModelLineOptionVo> options;
    @ApiModelProperty("配置内饰")
    private ModelLineSibInterieurVo sibInterieur;
    private String uniqueCode;
    private Object totalPrice;
    @ApiModelProperty("库存车辆信息")
    private List<QueryModelInfo> list;

    @Data
    public static class QueryModelInfo{
        @ApiModelProperty("车架号")
        private String vin;
        @ApiModelProperty("资源类型: 1：代理商现车；2：高意向；3：高意向共享；4：总部库存；5：其他代理商高意向")
        private String resourceType;
        @ApiModelProperty("资源所属：代理商名称信息或者”总部“")
        private String resourceTheir;
    }

}
