package com.csvw.audi.cc.service.impl;

import cn.hutool.http.HttpUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSClientBuilder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.config.OssConfig;
import com.csvw.audi.cc.entity.dto.okapi.*;
import com.csvw.audi.cc.entity.enumeration.OptionCategoryEnum;
import com.csvw.audi.cc.entity.enumeration.OptionTypeEnum;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.feign.OkapiFeign;
import com.csvw.audi.cc.mapper.CarModelLineHighlightMapper;
import com.csvw.audi.cc.mapper.CarTypeMapper;
import com.csvw.audi.cc.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OkapiService {
    @Autowired
    OkapiFeign okapiFeign;

    private final OssConfig ossConfig;

    private final OSS ossClient;

    public OkapiService(OssConfig ossConfig){
        this.ossConfig = ossConfig;
        ossClient = (new OSSClientBuilder()).build(ossConfig.getEndpoint(), ossConfig.getAccessKeyId(), ossConfig.getAccessKeySecret());
    }

    @Autowired
    private ICarModelService modelService;
    @Autowired
    private ICarSeriesService seriesService;
    @Autowired
    private ICarModelLineService modelLineService;
    @Autowired
    private ICarCustomSeriesService customSeriesService;
    @Autowired
    private ICarModelLineParameterService modelLineParameterService;
    @Autowired
    private ICarParameterService parameterService;
    @Autowired
    private ICarOptionService optionService;
    @Autowired
    private CarTypeMapper typeMapper;
    @Autowired
    private ICarModelLineOptionService modelLineOptionService;
    @Autowired
    private ICarPackageItemService packageItemService;
    @Autowired
    private ICarSibInterieurService sibInterieurService;
    @Autowired
    private ICarModelLineSibInterieurService modelLineSibInterieurService;
    @Autowired
    private CarModelLineHighlightMapper highlightMapper;

    public void syncOkapiData(){
        List<CarModel> carModels = modelService.list(Wrappers.<CarModel>lambdaQuery().
                eq(CarModel::getChannel, Constant.MASTER_CHANNEL).
                eq(CarModel::getFromOmd, 1).
                eq(CarModel::getDelFlag, 0));
        List<String> seriesId = carModels.stream().map(CarModel::getSeriesId).collect(Collectors.toList());
        List<List<ModelsDto>> sm = seriesId.stream().map(s->{
            CarSeries series = seriesService.getOne(Wrappers.<CarSeries>lambdaQuery().eq(CarSeries::getSeriesId, s).eq(CarSeries::getChannel, Constant.MASTER_CHANNEL));
            OkapiRes<List<ModelsDto>> modelsFeign = okapiFeign.models("A", series.getOmdSeriesCode());
            return modelsFeign.getResult();
        }).collect(Collectors.toList());
        List<ModelsDto> models = new ArrayList<>();
        sm.forEach(s->models.addAll(s));
        modelLineService.update(Wrappers.<CarModelLine>lambdaUpdate().set(CarModelLine::getOmdModelStatus, 0)
                .eq(CarModelLine::getOmdModelStatus,1));
        List<String> omdModelUnicodeAll = new ArrayList<>();
        for(ModelsDto m : models){
            for(ModelsDto.ModelInfoDto ml : m.getChildren()) {
                for (CarModel ccProModel : carModels) {
                    List<CarCustomSeries> carCustomSeries = customSeriesService.list(Wrappers.<CarCustomSeries>lambdaQuery().
                            eq(CarCustomSeries::getChannel, Constant.MASTER_CHANNEL).
                            eq(CarCustomSeries::getSeriesId, ccProModel.getSeriesId()).
                            eq(CarCustomSeries::getDelFlag, 0));
                    if (CollectionUtils.isEmpty(carCustomSeries)){
                        continue;
                    }
                    String customSeriesId = carCustomSeries.get(0).getCustomSeriesId();
                    if (ml.getModelYear().equals(ccProModel.getModelYear()) && ml.getModelCode().startsWith(ccProModel.getModelCode())){
                        String modelLineId;
                        // 新增配置线，以及配置线参数
                        List<CarModelLine> modelLines = modelLineService.list(Wrappers.<CarModelLine>lambdaQuery().
                                eq(CarModelLine::getOmdModelUnicode, ml.getModelUnicode()).
                                eq(CarModelLine::getDelFlag,0).
                                eq(CarModelLine::getChannel, Constant.MASTER_CHANNEL));
                        if (CollectionUtils.isNotEmpty(modelLines)){
                            modelLineId = modelLines.get(0).getModelLineId();
                            updateModelLine(m.getExternalFullModelName(), ml.getModelUnicode(), ml.getModelUnicodeShort());
                        }else {
                            modelLineId = addNewModelLine(ccProModel.getModelId(), customSeriesId, m.getExternalFullModelName(), ml.getModelVersion(), ml.getModelUnicode(), ml.getModelUnicodeShort(), ccProModel.getModelCode());
                        }
                        // 参数
                        updateModelLineParameter(modelLineId, ml.getParamChildren());
                        // 配置
                        LabelConfigParamDto paramDto = new LabelConfigParamDto();
                        paramDto.setBrandCode("A");
                        paramDto.setSeriesCode(m.getSeriesCode());
                        paramDto.setModelUnicode(ml.getModelUnicode());
                        OkapiRes<List<LabelConfigResDto>> configFeign = okapiFeign.modelLabelConfig(paramDto);
                        List<String> interieurCodes = new ArrayList<>();
                        if(CollectionUtils.isNotEmpty(configFeign.getResult())){
                            updateModelLineOption(modelLineId, ml.getModelUnicode(), customSeriesId, configFeign.getResult().get(0).getChildren());
                            interieurCodes = configFeign.getResult().get(0).getChildren().stream().filter(o->"II".equals(o.getLabelCode())).
                                    map(LabelConfigResDto.LabelResDto::getFeatureCode).collect(Collectors.toList());
                        }
                        // 处理内饰面料
                        updateModelLineInterieur(modelLineId, ml.getModelUnicode(), customSeriesId, interieurCodes);

                        // 处理亮点
                        updateModelLineHighLightOption(modelLineId, ml.getModelUnicode());
                        // 组合选装包
//                        updateModelLineComPacketOption(modelLineId, ml.getModelUnicode(), customSeriesId);
                    }
                }
                omdModelUnicodeAll.add(ml.getModelUnicode());
            }
        }
        modelLineService.update(Wrappers.<CarModelLine>lambdaUpdate().set(CarModelLine::getOmdModelStatus, 1)
                .in(CarModelLine::getOmdModelUnicode, omdModelUnicodeAll)
                .eq(CarModelLine::getOmdModelStatus,0));
    }

    private void updateModelLineHighLightOption(String modelLineId, String modelUnicode) {
        highlightMapper.delete(Wrappers.<CarModelLineHighlight>lambdaQuery().eq(CarModelLineHighlight::getModelLineId, modelLineId));
        Integer weight = 1;
        OkapiRes<HighLightConfigResDto> res = okapiFeign.highlight("A", modelUnicode, null);
        if (res.getResult() == null){
            return;
        }
        for(HighLightConfigResDto.LabelResDto labelResDto : res.getResult().getChildren()){
            String listUrl = "/ccpro-backend/default/1000x1000.png";
            String detailUrl = "/ccpro-backend/default/1500x800.png";
            String imageUrl = "/ccpro-backend/default/1372x520.png";
            if (labelResDto.getMaterialList() != null) {
                for (HighLightConfigResDto.MaterialInfo materialInfo : labelResDto.getMaterialList()) {
                    String url = materialInfo.getMaterialUrl();
                    if("2".equals(materialInfo.getMaterialType()) && StringUtils.isNotBlank(url)){
                        String ccUrl = url;
                        listUrl = ccUrl;
                        detailUrl = ccUrl;
                        imageUrl = ccUrl;
                        if ("1000".equals(materialInfo.getMaterialWidth()) && "1000".equals(materialInfo.getMaterialHeight())) {
                            listUrl = ccUrl;
                        }else if ("1500".equals(materialInfo.getMaterialWidth()) && "800".equals(materialInfo.getMaterialHeight())) {
                            detailUrl = ccUrl;
                        }else if ("1372".equals(materialInfo.getMaterialWidth()) && "520".equals(materialInfo.getMaterialHeight())) {
                            imageUrl = ccUrl;
                        }
                    }
                }
            }

            CarModelLineHighlight option = new CarModelLineHighlight();
            option.setId(UUID.randomUUID().toString());
            option.setOptionName(labelResDto.getExternalFeatureNameZh());
            option.setOptionCode(labelResDto.getFeatureCode());
            option.setCategory(labelResDto.getFamilyCode());
            option.setOptionType(labelResDto.getLabelCode());
            option.setOptionTypeName(labelResDto.getLabelNameZh());
            option.setOptionWeight(labelResDto.getFeatureWeight());
            option.setModelLineId(modelLineId);
            option.setCreateTime(LocalDateTime.now());
            option.setChannel(Constant.MASTER_CHANNEL);
            option.setImageUrlList(listUrl);
            option.setImageUrlDetail(detailUrl);
            option.setImageUrl(imageUrl);
            option.setPrice(labelResDto.getFeaturePrice());
            option.setDelFlag(0);
            highlightMapper.insert(option);

        }
    }

    private void updateModelLineInterieur(String modelLineId, String modelUnicode, String customSeriesId, List<String> interieurCodes) {
        modelLineSibInterieurService.remove(Wrappers.<CarModelLineSibInterieur>lambdaQuery().eq(CarModelLineSibInterieur::getModelLineId, modelLineId));

        OkapiRes<EquipmentGroupResDto> equipmentGroup = okapiFeign.equipmentGroup("A", modelUnicode, null, null);
        EquipmentGroupResDto equipmentGroupResDto = equipmentGroup.getResult();
        if (equipmentGroup.getResult().getChildren() == null){
            log.error("no equipment from omd");
            return;
        }
        List<EquipmentGroupResDto.GroupResDto> interieurList = equipmentGroupResDto.getChildren().stream().filter(e->"II".equals(e.getLabelCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(interieurList)){
            log.error("no interieur equipment from omd");
            return;
        }
        for(EquipmentGroupResDto.GroupResDto interieurRes : interieurList) {
            EquipmentGroupResDto.LabelResDto sibInfo=null, interieurInfo=null;
            for (EquipmentGroupResDto.LabelResDto pInfo : interieurRes.getLabelChildren()) {
                if ("II".equals(pInfo.getFamilyCode())){
                    interieurInfo = pInfo;
                }
                if (OptionCategoryEnum.SIB.getValue().equals(pInfo.getFamilyCode())){
                    sibInfo = pInfo;
                }
            }
            if (interieurInfo==null || sibInfo == null || !OptionCategoryEnum.SIB.getValue().equals(sibInfo.getFamilyCode())) {
                continue;
            }
            List<CarOption> inColorOptions = optionService.list(Wrappers.<CarOption>lambdaQuery().
                    eq(CarOption::getOptionCode, interieurInfo.getFeatureCode()).
                    ne(CarOption::getOptionType, OptionTypeEnum.PACKETITEM.getValue()).
                    eq(CarOption::getChannel, Constant.MASTER_CHANNEL).
                    eq(CarOption::getCustomSeriesId, customSeriesId).
                    eq(CarOption::getDelFlag, 0));
            if (CollectionUtils.isEmpty(inColorOptions)) {
                continue;
            }
            List<CarOption> sibOptions = optionService.list(Wrappers.<CarOption>lambdaQuery().
                    eq(CarOption::getOptionCode, sibInfo.getFeatureCode()).
                    ne(CarOption::getOptionType, OptionTypeEnum.PACKETITEM.getValue()).
                    eq(CarOption::getChannel, Constant.MASTER_CHANNEL).
                    eq(CarOption::getCustomSeriesId, customSeriesId).
                    eq(CarOption::getDelFlag, 0));
            if (CollectionUtils.isEmpty(sibOptions)) {
                continue;
            }
            List<CarSibInterieur> sibInterieurs = sibInterieurService.list(Wrappers.<CarSibInterieur>lambdaQuery().
                    eq(CarSibInterieur::getSibOptionCode, sibInfo.getFeatureCode()).
                    eq(CarSibInterieur::getInterieurOptionCode, interieurInfo.getFeatureCode()).
                    eq(CarSibInterieur::getChannel, Constant.MASTER_CHANNEL).
                    eq(CarSibInterieur::getCustomSeriesId, customSeriesId).
                    eq(CarSibInterieur::getDelFlag, 0));
            String sibInterieurId = UUID.randomUUID().toString();

            String listUrl = "/ccpro-backend/default/1000x1000.png";
            String detailUrl = "/ccpro-backend/default/1500x800.png";
            String imageUrl = "/ccpro-backend/default/1372x520.png";
            if (interieurRes.getMaterialList() != null) {
                for (EquipmentGroupResDto.MaterialInfo materialInfo : interieurRes.getMaterialList()) {
                    String url = materialInfo.getMaterialUrl();
                    if("2".equals(materialInfo.getMaterialType()) && StringUtils.isNotBlank(url)){
                        String ccUrl = url;
                        listUrl = ccUrl;
                        detailUrl = ccUrl;
                        imageUrl = ccUrl;
                        if ("1000".equals(materialInfo.getMaterialWidth()) && "1000".equals(materialInfo.getMaterialHeight())) {
                            listUrl = ccUrl;
                        }else if ("1500".equals(materialInfo.getMaterialWidth()) && "800".equals(materialInfo.getMaterialHeight())) {
                            detailUrl = ccUrl;
                        }else if ("1372".equals(materialInfo.getMaterialWidth()) && "520".equals(materialInfo.getMaterialHeight())) {
                            imageUrl = ccUrl;
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(sibInterieurs)) {
                sibInterieurId = sibInterieurs.get(0).getSibInterieurId();
                sibInterieurService.update(Wrappers.<CarSibInterieur>lambdaUpdate().eq(CarSibInterieur::getSibInterieurId, sibInterieurId)
                        .set(CarSibInterieur::getImageUrl, imageUrl)
                        .set(CarSibInterieur::getImageUrlList, listUrl)
                        .set(CarSibInterieur::getImageUrlDetail, detailUrl));
            } else {
                CarOption inColor = inColorOptions.get(0);
                CarOption sib = sibOptions.get(0);
                CarSibInterieur sibInterieur = new CarSibInterieur();
                sibInterieur.setId(UUID.randomUUID().toString());
                sibInterieur.setChannel(Constant.MASTER_CHANNEL);
                sibInterieur.setSibInterieurId(sibInterieurId);
                sibInterieur.setSibInterieurCode(sibInfo.getFeatureCode() + "-" + interieurInfo.getFeatureCode());
                sibInterieur.setDefaultConfig(0);
                sibInterieur.setCustomSeriesId(customSeriesId);
                sibInterieur.setSibInterieurCategory("F_SIB_COLOR_INTERIEUR");
                sibInterieur.setDescription(interieurRes.getLabelValueNameZh());
                sibInterieur.setInterieurName(inColor.getOptionName());
                sibInterieur.setInterieurOptionId(inColor.getOptionId());
                sibInterieur.setInterieurOptionCategory(inColor.getCategory());
                sibInterieur.setInterieurOptionCode(inColor.getOptionCode());
                sibInterieur.setSibName(sib.getOptionName());
                sibInterieur.setSibOptionId(sib.getOptionId());
                sibInterieur.setSibOptionCategory(sib.getCategory());
                sibInterieur.setSibOptionCode(sib.getOptionCode());
                sibInterieur.setCreateTime(LocalDateTime.now());
                sibInterieur.setDelFlag(0);
                sibInterieur.setImageUrlList(listUrl);
                sibInterieur.setImageUrlDetail(detailUrl);
                sibInterieur.setImageUrl(imageUrl);
                sibInterieurService.save(sibInterieur);
            }
            CarModelLineSibInterieur modelLineSibInterieur = new CarModelLineSibInterieur();
            modelLineSibInterieur.setId(UUID.randomUUID().toString());
            modelLineSibInterieur.setModelLineId(modelLineId);
            modelLineSibInterieur.setSibInterieurId(sibInterieurId);
            modelLineSibInterieur.setDefaultConfig(0);
            modelLineSibInterieur.setStatus(2);
            modelLineSibInterieur.setChannel(Constant.MASTER_CHANNEL);
            modelLineSibInterieur.setDelFlag(0);
            modelLineSibInterieur.setDescription(interieurRes.getLabelValueNameZh());
            modelLineSibInterieur.setCreateTime(LocalDateTime.now());
            modelLineSibInterieurService.save(modelLineSibInterieur);

        }

    }

    private void updateModelLineInterieurFabric(String modelLineId, String modelUnicode, String customSeriesId, List<String> interieurCodes) {
        modelLineSibInterieurService.remove(Wrappers.<CarModelLineSibInterieur>lambdaQuery().eq(CarModelLineSibInterieur::getModelLineId, modelLineId));
        for(String interieurCode : interieurCodes) {
            OkapiRes<LabelInterieurResDto> labelInterieurResDtoOkapiRes = okapiFeign.modelSibInterieur("A", modelUnicode, null, interieurCode);
            LabelInterieurResDto interieurResDto = labelInterieurResDtoOkapiRes.getResult();
            if (labelInterieurResDtoOkapiRes.getResult().getChildren() == null){
                log.error("no sib interieur from omd");
                continue;
            }
            for(LabelInterieurResDto.InterieurResDto interieurRes : interieurResDto.getChildren()){
                if (! OptionCategoryEnum.SIB.getValue().equals(interieurRes.getFamilyCode())){
                    continue;
                }
                List<CarOption> inColorOptions = optionService.list(Wrappers.<CarOption>lambdaQuery().
                        eq(CarOption::getOptionCode, interieurRes.getInteriorCode()).
                        ne(CarOption::getOptionType, OptionTypeEnum.PACKETITEM.getValue()).
                        eq(CarOption::getChannel, Constant.MASTER_CHANNEL).
                        eq(CarOption::getCustomSeriesId, customSeriesId).
                        eq(CarOption::getDelFlag, 0));
                if (CollectionUtils.isEmpty(inColorOptions)){
                    continue;
                }
                List<CarOption> sibOptions = optionService.list(Wrappers.<CarOption>lambdaQuery().
                        eq(CarOption::getOptionCode, interieurRes.getFeatureCode()).
                        ne(CarOption::getOptionType, OptionTypeEnum.PACKETITEM.getValue()).
                        eq(CarOption::getChannel, Constant.MASTER_CHANNEL).
                        eq(CarOption::getCustomSeriesId, customSeriesId).
                        eq(CarOption::getDelFlag, 0));
                if (CollectionUtils.isEmpty(sibOptions)){
                    continue;
                }
                List<CarSibInterieur> sibInterieurs = sibInterieurService.list(Wrappers.<CarSibInterieur>lambdaQuery().
                        eq(CarSibInterieur::getSibOptionCode, interieurRes.getFeatureCode()).
                        eq(CarSibInterieur::getInterieurOptionCode, interieurRes.getInteriorCode()).
                        eq(CarSibInterieur::getChannel, Constant.MASTER_CHANNEL).
                        eq(CarSibInterieur::getCustomSeriesId, customSeriesId).
                        eq(CarSibInterieur::getDelFlag, 0));
                String sibInterieurId = UUID.randomUUID().toString();
                if (CollectionUtils.isNotEmpty(sibInterieurs)){
                    sibInterieurId = sibInterieurs.get(0).getSibInterieurId();
                }else {
                    CarOption inColor = inColorOptions.get(0);
                    CarOption sib = sibOptions.get(0);
                    CarSibInterieur sibInterieur = new CarSibInterieur();
                    sibInterieur.setId(UUID.randomUUID().toString());
                    sibInterieur.setChannel(Constant.MASTER_CHANNEL);
                    sibInterieur.setSibInterieurId(sibInterieurId);
                    sibInterieur.setSibInterieurCode(interieurRes.getFeatureCode()+ "-" + interieurRes.getInteriorCode());
                    sibInterieur.setDefaultConfig(0);
                    sibInterieur.setCustomSeriesId(customSeriesId);
                    sibInterieur.setSibInterieurCategory("F_SIB_COLOR_INTERIEUR");
                    sibInterieur.setDescription(interieurRes.getExternalFeatureNameZh());
                    sibInterieur.setInterieurName(inColor.getOptionName());
                    sibInterieur.setInterieurOptionId(inColor.getOptionId());
                    sibInterieur.setInterieurOptionCategory(inColor.getCategory());
                    sibInterieur.setInterieurOptionCode(inColor.getOptionCode());
                    sibInterieur.setSibName(sib.getOptionName());
                    sibInterieur.setSibOptionId(sib.getOptionId());
                    sibInterieur.setSibOptionCategory(sib.getCategory());
                    sibInterieur.setSibOptionCode(sib.getOptionCode());
                    sibInterieur.setCreateTime(LocalDateTime.now());
                    sibInterieur.setDelFlag(0);
                    sibInterieur.setImageUrlList(sib.getImageUrlList());
                    sibInterieur.setImageUrlDetail(sib.getImageUrlDetail());
                    sibInterieurService.save(sibInterieur);
                }
                CarModelLineSibInterieur modelLineSibInterieur = new CarModelLineSibInterieur();
                modelLineSibInterieur.setId(UUID.randomUUID().toString());
                modelLineSibInterieur.setModelLineId(modelLineId);
                modelLineSibInterieur.setSibInterieurId(sibInterieurId);
                modelLineSibInterieur.setDefaultConfig(0);
                modelLineSibInterieur.setStatus(2);
                modelLineSibInterieur.setChannel(Constant.MASTER_CHANNEL);
                modelLineSibInterieur.setDelFlag(0);
                modelLineSibInterieur.setDescription(interieurRes.getExternalFeatureNameZh());
                modelLineSibInterieur.setCreateTime(LocalDateTime.now());
                modelLineSibInterieurService.save(modelLineSibInterieur);
            }
        }
    }

    @Autowired
    private ICarOptionRelateService optionRelateService;
    private void updateModelLineComPacketOption(String modelLineId, String modelUnicode, String customSeriesId) {
        Integer weight = 1;
        OkapiRes<EquipmentGroupResDto> equipmentGroup = okapiFeign.equipmentGroup("A", modelUnicode, null, null);
        EquipmentGroupResDto equipmentGroupResDto = equipmentGroup.getResult();
        if (equipmentGroup.getResult().getChildren() == null){
            log.error("no equipment from omd");
            return;
        }
        List<EquipmentGroupResDto.GroupResDto> cmbpacketList = equipmentGroupResDto.getChildren().stream().filter(e->"CMBPACKET".equals(e.getLabelCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cmbpacketList)){
            log.error("no cmbpacket equipment from omd");
            return;
        }
        for(EquipmentGroupResDto.GroupResDto packetResDto : cmbpacketList) {
            String listUrl = "/ccpro-backend/default/1000x1000.png";
            String detailUrl = "/ccpro-backend/default/1500x800.png";
            String imageUrl = "/ccpro-backend/default/1372x520.png";
            if (packetResDto.getMaterialList() != null) {
                for (EquipmentGroupResDto.MaterialInfo materialInfo : packetResDto.getMaterialList()) {
                    String url = materialInfo.getMaterialUrl();
                    if("2".equals(materialInfo.getMaterialType()) && StringUtils.isNotBlank(url)){
                        String ccUrl = url;
                        listUrl = ccUrl;
                        detailUrl = ccUrl;
                        imageUrl = ccUrl;
                        if ("1000".equals(materialInfo.getMaterialWidth()) && "1000".equals(materialInfo.getMaterialHeight())) {
                            listUrl = ccUrl;
                        }else if ("1500".equals(materialInfo.getMaterialWidth()) && "800".equals(materialInfo.getMaterialHeight())) {
                            detailUrl = ccUrl;
                        }else if ("1372".equals(materialInfo.getMaterialWidth()) && "520".equals(materialInfo.getMaterialHeight())) {
                            imageUrl = ccUrl;
                        }
                    }
                }
            }
            String featureCode = packetResDto.getLabelChildren().stream().map(EquipmentGroupResDto.LabelResDto::getFeatureCode).sorted().collect(Collectors.joining("+"));
            List<CarOption> carOptions = optionService.list(Wrappers.<CarOption>lambdaQuery().
                    eq(CarOption::getOptionCode, featureCode).
                    eq(CarOption::getOptionType, "packet").
                    eq(CarOption::getChannel, Constant.MASTER_CHANNEL).
                    eq(CarOption::getCustomSeriesId, customSeriesId).
                    eq(CarOption::getDelFlag, 0));
            String optionId;
            if (CollectionUtils.isNotEmpty(carOptions)){
                optionId = carOptions.get(0).getOptionId();
            }else {
                optionId = UUID.randomUUID().toString();
                CarOption option = new CarOption();
                option.setId(UUID.randomUUID().toString());
                option.setOptionId(optionId);
                option.setOptionName(packetResDto.getLabelValueNameZh());
                option.setOptionCode(featureCode);
                String category = OptionCategoryEnum.PACKET.getValue();

                option.setCategory(category);
                option.setOptionType("packet");
                option.setOptionWeight(weight);
                option.setCustomSeriesId(customSeriesId);
                option.setDefaultConfig(0);
                option.setCreateTime(LocalDateTime.now());
                option.setChannel(Constant.MASTER_CHANNEL);
                option.setImageUrlList(listUrl);
                option.setImageUrlDetail(detailUrl);
                option.setImageUrl(imageUrl);
                option.setDelFlag(0);
                optionService.save(option);
            }
            optionRelateService.remove(Wrappers.<CarOptionRelate>lambdaQuery().eq(CarOptionRelate::getOptionId, optionId).eq(CarOptionRelate::getRelateType, "combine"));
            packetResDto.getLabelChildren().forEach(cmbChild->{
                CarOptionRelate relate = new CarOptionRelate();
                relate.setId(UUID.randomUUID().toString());
                relate.setCustomSeriesId(customSeriesId);
                relate.setOptionId(optionId);
                relate.setOptionCode(featureCode);
                relate.setOptionCategory(OptionCategoryEnum.PACKET.getValue());
                relate.setOptionRelateCode(cmbChild.getFeatureCode());
                relate.setOptionRelateCategory(cmbChild.getFamilyCode());
                relate.setDelFlag(0);
                relate.setRelateType("combine");
                relate.setDefaultDepend(0);
                relate.setCreateTime(LocalDateTime.now());
                optionRelateService.save(relate);
            });
            CarModelLineOption modelLineOption = new CarModelLineOption();
            modelLineOption.setId(UUID.randomUUID().toString());
            modelLineOption.setModelLineId(modelLineId);
            modelLineOption.setOptionId(optionId);
            modelLineOption.setChannel(Constant.MASTER_CHANNEL);
            modelLineOption.setCreateTime(LocalDateTime.now());
            modelLineOption.setDefaultConfig(0);
            modelLineOption.setOptionName(packetResDto.getLabelValueNameZh());
            Integer status = 2;
            modelLineOption.setStatus(status);
            modelLineOption.setCondition(status);
            modelLineOption.setImageUrlList(listUrl);
            modelLineOption.setImageUrlDetail(detailUrl);
            modelLineOption.setImageUrl(imageUrl);
            modelLineOption.setDelFlag(0);
            modelLineOption.setOptionWeight(weight);
            modelLineOption.setEquipmentRights(packetResDto.getEquipmentRights());
            modelLineOptionService.save(modelLineOption);
            weight++;

        }
    }

    private void updateModelLineOption(String modelLineId, String modelUnicode, String customSeriesId, List<LabelConfigResDto.LabelResDto> labelResDtos) {
        Integer weight = 1;
        modelLineOptionService.remove(Wrappers.<CarModelLineOption>lambdaQuery().eq(CarModelLineOption::getModelLineId, modelLineId));
        for(LabelConfigResDto.LabelResDto labelResDto : labelResDtos){
            // 强制转换选装包类型编码
            if ("PACKET".equalsIgnoreCase(labelResDto.getLabelCode())){
                labelResDto.setLabelCode("packet");
            }
            List<CarType> carTypes = typeMapper.selectList(Wrappers.<CarType>lambdaQuery().
                    eq(CarType::getType, labelResDto.getLabelCode()).
                    eq(CarType::getCustomSeriesId,customSeriesId));
            if (carTypes.size()>1){
                for (int i=1;i<carTypes.size();i++){
                    typeMapper.deleteById(carTypes.get(i).getId());
                }
            }
            if (CollectionUtils.isNotEmpty(carTypes)){
                CarType type = new CarType();
                type.setId(UUID.randomUUID().toString());
                type.setCustomSeriesId(customSeriesId);
                type.setType(labelResDto.getLabelCode());
                type.setTypeName(labelResDto.getLabelNameZh());
                typeMapper.insert(type);
            }
            String listUrl = "/ccpro-backend/default/1000x1000.png";
            String detailUrl = "/ccpro-backend/default/1500x800.png";
            String imageUrl = "/ccpro-backend/default/1372x520.png";
            if (labelResDto.getMaterialList() != null) {
                for (LabelConfigResDto.MaterialInfo materialInfo : labelResDto.getMaterialList()) {
                    String url = materialInfo.getMaterialUrl();
                    if("2".equals(materialInfo.getMaterialType()) && StringUtils.isNotBlank(url)){
                        String ccUrl = url;
                        listUrl = ccUrl;
                        detailUrl = ccUrl;
                        imageUrl = ccUrl;
                        if ("1000".equals(materialInfo.getMaterialWidth()) && "1000".equals(materialInfo.getMaterialHeight())) {
                            listUrl = ccUrl;
                        }else if ("1500".equals(materialInfo.getMaterialWidth()) && "800".equals(materialInfo.getMaterialHeight())) {
                            detailUrl = ccUrl;
                        }else if ("1372".equals(materialInfo.getMaterialWidth()) && "520".equals(materialInfo.getMaterialHeight())) {
                            imageUrl = ccUrl;
                        }
                    }
                }
            }
            List<CarOption> carOptions = optionService.list(Wrappers.<CarOption>lambdaQuery().
                    eq(CarOption::getOptionCode, labelResDto.getFeatureCode()).
                    eq(CarOption::getOptionType, labelResDto.getLabelCode()).
                    eq(CarOption::getChannel, Constant.MASTER_CHANNEL).
                    eq(CarOption::getCustomSeriesId, customSeriesId).
                    eq(CarOption::getDelFlag, 0));
            String optionId;
            if (CollectionUtils.isNotEmpty(carOptions)){
                optionId = carOptions.get(0).getOptionId();
            }else {
                optionId = UUID.randomUUID().toString();
                CarOption option = new CarOption();
                option.setId(UUID.randomUUID().toString());
                option.setOptionId(optionId);
                option.setOptionName(labelResDto.getExternalFeatureNameZh());
                option.setOptionCode(labelResDto.getFeatureCode());
                String category = labelResDto.getFamilyCode();
                if("AADD".equals(labelResDto.getLabelCode())){
                    category = OptionCategoryEnum.OUTCOLOR.getValue();
                }else if("II".equals(labelResDto.getLabelCode())){
                    category = OptionCategoryEnum.INCOLOR.getValue();
                }else if ("PACKET".equalsIgnoreCase(labelResDto.getLabelCode())){
                    category = OptionCategoryEnum.PACKET.getValue();
                }
                option.setCategory(category);
                option.setOptionType(labelResDto.getLabelCode());
                option.setOptionWeight(weight);
                option.setCustomSeriesId(customSeriesId);
                option.setDefaultConfig(0);
                option.setCreateTime(LocalDateTime.now());
                option.setChannel(Constant.MASTER_CHANNEL);
                option.setImageUrlList(listUrl);
                option.setImageUrlDetail(detailUrl);
                option.setImageUrl(imageUrl);
                option.setDelFlag(0);
                optionService.save(option);
            }
            CarModelLineOption modelLineOption = new CarModelLineOption();
            modelLineOption.setId(UUID.randomUUID().toString());
            modelLineOption.setModelLineId(modelLineId);
            modelLineOption.setOptionId(optionId);
            modelLineOption.setChannel(Constant.MASTER_CHANNEL);
            modelLineOption.setCreateTime(LocalDateTime.now());
            modelLineOption.setDefaultConfig(0);
            modelLineOption.setOptionName(labelResDto.getExternalFeatureNameZh());
            Integer status = 0;
            if ("E".equals(labelResDto.getFeatureStatusCode())){
                status = 2;
            }else if ("L".equals(labelResDto.getFeatureStatusCode())){
                status = 1;
            }
            modelLineOption.setStatus(status);
            modelLineOption.setCondition(status);
            modelLineOption.setImageUrlList(listUrl);
            modelLineOption.setImageUrlDetail(detailUrl);
            modelLineOption.setImageUrl(imageUrl);
            modelLineOption.setDelFlag(0);
            modelLineOption.setOptionWeight(weight);
            modelLineOption.setEquipmentRights(labelResDto.getEquipmentRights());
            modelLineOptionService.save(modelLineOption);
            weight++;

            // 处理选装包件
            if (labelResDto.getLabelCode().equals("packet")){
                OkapiRes<LabelConfigItemResDto> modelLabelConfigItem = okapiFeign.modelLabelConfigItem("A", modelUnicode, labelResDto.getFeatureCode());
                updateModelLinePacketItem(modelLineId, optionId, customSeriesId, modelLabelConfigItem.getResult().getChildren());
            }
        }
    }

    private void updateModelLinePacketItem(String modelLineId, String packetId, String customSeriesId, List<LabelConfigItemResDto.LabelResDto> labelResDtos) {
        int weight = 0;
        String optionType = OptionTypeEnum.PACKETITEM.getValue();
        // 清除packetItem
        packageItemService.remove(Wrappers.<CarPackageItem>lambdaQuery().eq(CarPackageItem::getPackageId, packetId));
        for(LabelConfigItemResDto.LabelResDto labelResDto : labelResDtos){
            String listUrl = "/ccpro-backend/default/1000x1000.png";
            String detailUrl = "/ccpro-backend/default/1500x800.png";
            String imageUrl = "/ccpro-backend/default/1372x520.png";
            if (labelResDto.getMaterialList() != null) {
                for (LabelConfigItemResDto.MaterialInfo materialInfo : labelResDto.getMaterialList()) {
                    String url = materialInfo.getMaterialUrl();
                    if("2".equals(materialInfo.getMaterialType()) && StringUtils.isNotBlank(url)){
                        String ccUrl = url;
                        listUrl = ccUrl;
                        detailUrl = ccUrl;
                        imageUrl = ccUrl;
                        if ("1000".equals(materialInfo.getMaterialWidth()) && "1000".equals(materialInfo.getMaterialHeight())) {
                            listUrl = ccUrl;
                        }else if ("1500".equals(materialInfo.getMaterialWidth()) && "800".equals(materialInfo.getMaterialHeight())) {
                            detailUrl = ccUrl;
                        }else if ("1372".equals(materialInfo.getMaterialWidth()) && "520".equals(materialInfo.getMaterialHeight())) {
                            imageUrl = ccUrl;
                        }
                    }
                }
            }
            List<CarOption> carOptions = optionService.list(Wrappers.<CarOption>lambdaQuery().
                    eq(CarOption::getOptionCode, labelResDto.getFeatureCode()).
                    eq(CarOption::getOptionType, optionType).
                    eq(CarOption::getChannel, Constant.MASTER_CHANNEL).
                    eq(CarOption::getCustomSeriesId, customSeriesId).
                    eq(CarOption::getDelFlag, 0));
            String optionId;
            if (CollectionUtils.isNotEmpty(carOptions)){
                optionId = carOptions.get(0).getOptionId();
            }else {
                optionId = UUID.randomUUID().toString();
                CarOption option = new CarOption();
                option.setId(UUID.randomUUID().toString());
                option.setOptionId(optionId);
                option.setOptionName(labelResDto.getExternalFeatureNameZh());
                option.setOptionCode(labelResDto.getFeatureCode());
                option.setCategory(labelResDto.getFamilyCode());
                option.setOptionType(optionType);
                option.setOptionWeight(weight);
                option.setCustomSeriesId(customSeriesId);
                option.setDefaultConfig(0);
                option.setCreateTime(LocalDateTime.now());
                option.setChannel(Constant.MASTER_CHANNEL);
                option.setImageUrlList(listUrl);
                option.setImageUrlDetail(detailUrl);
                option.setImageUrl(imageUrl);
                option.setDelFlag(0);
                optionService.save(option);
            }
            // 维护packetItem
            CarPackageItem packageItem = new CarPackageItem();
            packageItem.setId(UUID.randomUUID().toString());
            packageItem.setPackageId(packetId);
            packageItem.setPackageItemId(optionId);
            packageItem.setCreateTime(LocalDateTime.now());
            packageItem.setWeight(weight);
            packageItem.setDelFlag(0);
            packageItem.setUpdateTime(LocalDateTime.now());
            packageItemService.save(packageItem);

            CarModelLineOption modelLineOption = new CarModelLineOption();
            modelLineOption.setId(UUID.randomUUID().toString());
            modelLineOption.setModelLineId(modelLineId);
            modelLineOption.setOptionId(optionId);
            modelLineOption.setChannel(Constant.MASTER_CHANNEL);
            modelLineOption.setCreateTime(LocalDateTime.now());
            modelLineOption.setDefaultConfig(0);
            modelLineOption.setOptionName(labelResDto.getExternalFeatureNameZh());
            Integer status = 2;
            modelLineOption.setStatus(status);
            modelLineOption.setCondition(status);
            modelLineOption.setDelFlag(0);
            modelLineOption.setOptionWeight(weight);
            modelLineOption.setImageUrlList(listUrl);
            modelLineOption.setImageUrlDetail(detailUrl);
            modelLineOption.setImageUrl(imageUrl);
            modelLineOptionService.save(modelLineOption);
            weight++;
        }
    }

    private void updateModelLineParameter(String modelLineId, List<ModelsDto.ParamInfo> paramChildren) {
        modelLineParameterService.remove(Wrappers.<CarModelLineParameter>lambdaQuery().eq(CarModelLineParameter::getModelLineId, modelLineId));
        Integer weight = 0;
        for(ModelsDto.ParamInfo p : paramChildren) {
            if(!"TEC".equals(p.getParamSubCategory())){
                continue;
            }
            weight ++;
            List<CarParameter> carParameters = parameterService.list(Wrappers.<CarParameter>lambdaQuery().
                    eq(CarParameter::getParameterCode, p.getProdCode()).
                    eq(CarParameter::getParameterName, p.getProdNameZh()));
            String parameterId = null;
            if (CollectionUtils.isEmpty(carParameters)) {
                parameterId = UUID.randomUUID().toString();
                CarParameter parameter = new CarParameter();
                parameter.setId(UUID.randomUUID().toString());
                parameter.setParameterId(parameterId);
                parameter.setParameterCode(p.getProdCode());
                parameter.setParameterName(p.getProdNameZh());
                parameter.setWeight(weight);
                parameter.setDelFlag(0);
                parameter.setCreateTime(LocalDateTime.now());
                parameter.setUpdateTime(LocalDateTime.now());
                parameterService.save(parameter);
            } else {
                parameterId = carParameters.get(0).getParameterId();
            }
            CarModelLineParameter modelLineParameter = new CarModelLineParameter();
            modelLineParameter.setId(UUID.randomUUID().toString());
            modelLineParameter.setModelLineId(modelLineId);
            modelLineParameter.setParameterId(parameterId);
            modelLineParameter.setParameterValue(p.getProdValue());
            modelLineParameter.setParameterWeight(weight);
            modelLineParameter.setDelFlag(0);
            modelLineParameter.setCreateTime(LocalDateTime.now());
            modelLineParameter.setUpdateTime(LocalDateTime.now());
            modelLineParameterService.save(modelLineParameter);
        }
    }


    private void updateModelLine( String modelLineName, String modelUnicode, String modelUnicodeShort){
        modelLineService.update(Wrappers.<CarModelLine>lambdaUpdate().
                eq(CarModelLine::getOmdModelUnicode, modelUnicode).
                set(CarModelLine::getModelLineName, modelLineName).
                set(CarModelLine::getAccbTypeCode, "TYPE:" + modelUnicodeShort));
    }


    private String addNewModelLine(String modelId, String customSeriesId, String modelLineName, String modelVersion, String modelUnicode, String modelUnicodeShort, String modelCode){
        List<String> channels = Arrays.asList("ams-minip","dealer-display","drm","drm-local","master","minip","official-pc","oneapp","sphere","vsearch");
        String modelLineId = UUID.randomUUID().toString();
        int num = 1;
        AtomicReference<String> modelLineCode = new AtomicReference<>();
        while (true){
            DecimalFormat df = new DecimalFormat("000");
            modelLineCode.set(modelCode + df.format(num));
            if(modelLineService.count(Wrappers.<CarModelLine>lambdaQuery().eq(CarModelLine::getModelLineCode, modelLineCode.get())) == 0){
                break;
            }
            num ++;
        }
        channels.forEach(c->{
            CarModelLine modelLine = new CarModelLine();
            modelLine.setId(UUID.randomUUID().toString());
            modelLine.setModelId(modelId);
            modelLine.setCustomSeriesId(customSeriesId);
            modelLine.setModelLineId(modelLineId);
            modelLine.setModelLineName(modelLineName);
            modelLine.setVersion(modelVersion);
            modelLine.setAccbTypeCode(modelUnicodeShort);
            modelLine.setOmdModelUnicode(modelUnicode);
            modelLine.setModelLineCode(modelLineCode.get());
            modelLine.setChannel(c);
            modelLine.setFrontStatus(1);
            modelLine.setPersonal(1);
            modelLine.setCreateTime(LocalDateTime.now());
            modelLine.setUpdateTime(LocalDateTime.now());
            modelLine.setDelFlag(0);
            modelLineService.save(modelLine);
        });
        return modelLineId;
    }

    /**
     * https://brand2.svw-volkswagen.com/cms/dev/audi/I/20250708/6575410c855f52b2988b4aeccd91da85.png
     * cms/dev/audi/I/20250708/6575410c855f52b2988b4aeccd91da85.png
     * @param url
     * @return
     */
    private String urlRemoveDomain(String url) {
        return url.replaceAll("^https://[^/]*/", "");
    }

}
