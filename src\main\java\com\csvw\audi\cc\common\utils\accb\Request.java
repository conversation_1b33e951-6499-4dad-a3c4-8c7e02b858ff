package com.csvw.audi.cc.common.utils.accb;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;

public class Request {
    private String key = null;

    private String secret = null;

    private String method = null;

    private String url = null;

    private String body = null;

    private String fragment = null;

    private Map<String, String> headers = new Hashtable<>();

    private Map<String, List<String>> queryString = new Hashtable<>();

    @Deprecated
    public String getRegion() {
        return "";
    }

    @Deprecated
    public String getServiceName() {
        return "";
    }

    public String getKey() {
        return this.key;
    }

    public String getSecrect() {
        return this.secret;
    }

    public HttpMethodName getMethod() {
        return HttpMethodName.valueOf(this.method.toUpperCase());
    }

    public String getBody() {
        return this.body;
    }

    public Map<String, String> getHeaders() {
        return this.headers;
    }

    @Deprecated
    public void setRegion(String region) {}

    @Deprecated
    public void setServiceName(String serviceName) {}

    public void setAppKey(String appKey) throws Exception {
        if (null == appKey || appKey.trim().isEmpty())
            throw new Exception("appKey can not be empty");
        this.key = appKey;
    }

    public void setAppSecrect(String appSecret) throws Exception {
        if (null == appSecret || appSecret.trim().isEmpty())
            throw new Exception("appSecrect can not be empty");
        this.secret = appSecret;
    }

    public void setKey(String appKey) throws Exception {
        if (null == appKey || appKey.trim().isEmpty())
            throw new Exception("appKey can not be empty");
        this.key = appKey;
    }

    public void setSecret(String appSecret) throws Exception {
        if (null == appSecret || appSecret.trim().isEmpty())
            throw new Exception("appSecrect can not be empty");
        this.secret = appSecret;
    }

    public void setMethod(String method) throws Exception {
        if (null == method)
            throw new Exception("method can not be empty");
        if (!method.equalsIgnoreCase("post") &&
                !method.equalsIgnoreCase("put") &&
                !method.equalsIgnoreCase("patch") &&
                !method.equalsIgnoreCase("delete") &&
                !method.equalsIgnoreCase("get") &&
                !method.equalsIgnoreCase("options") &&
                !method.equalsIgnoreCase("head"))
            throw new Exception("unsupported method");
        this.method = method;
    }

    public String getUrl() {
        String uri = this.url;
        if (this.queryString.size() > 0) {
            uri = uri + "?";
            int loop = 0;
            for (Map.Entry<String, List<String>> entry : this.queryString.entrySet()) {
                for (String value : entry.getValue()) {
                    if (loop > 0)
                        uri = uri + "&";
                    uri = uri + HttpUtils.urlEncode(entry.getKey(), false);
                    uri = uri + "=";
                    uri = uri + HttpUtils.urlEncode(value, false);
                    loop++;
                }
            }
        }
        if (this.fragment != null) {
            uri = uri + "#";
            uri = uri + this.fragment;
        }
        return uri;
    }

    public void setUrl(String url) throws Exception {
        if (null == url || url.trim().isEmpty())
            throw new Exception("url can not be empty");
        int i = url.indexOf('#');
        if (i >= 0)
            url = url.substring(0, i);
        i = url.indexOf('?');
        if (i >= 0) {
            String query = url.substring(i + 1, url.length());
            for (String item : query.split("&")) {
                String[] spl = item.split("=", 2);
                String key = spl[0];
                String value = "";
                if (spl.length > 1)
                    value = spl[1];
                if (!key.trim().isEmpty()) {
                    key = URLDecoder.decode(key, "UTF-8");
                    value = URLDecoder.decode(value, "UTF-8");
                    addQueryStringParam(key, value);
                }
            }
            url = url.substring(0, i);
        }
        this.url = url;
    }

    public String getPath() {
        String url = this.url;
        int i = url.indexOf("://");
        if (i >= 0)
            url = url.substring(i + 3);
        i = url.indexOf('/');
        if (i >= 0)
            return url.substring(i);
        return "/";
    }

    public String getHost() {
        String url = this.url;
        int i = url.indexOf("://");
        if (i >= 0)
            url = url.substring(i + 3);
        i = url.indexOf('/');
        if (i >= 0)
            url = url.substring(0, i);
        return url;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public void addQueryStringParam(String name, String value) throws UnsupportedEncodingException {
        List<String> paramList = this.queryString.get(name);
        if (paramList == null) {
            paramList = new ArrayList<>();
            this.queryString.put(name, paramList);
        }
        paramList.add(value);
    }

    public Map<String, List<String>> getQueryStringParams() {
        return this.queryString;
    }

    public String getFragment() {
        return this.fragment;
    }

    public void setFragment(String fragment) throws Exception {
        if (null == fragment || fragment.trim().isEmpty())
            throw new Exception("fragment can not be empty");
        this.fragment = URLEncoder.encode(fragment, "UTF-8");
    }

    public void addHeader(String name, String value) {
        if (null == name || name.trim().isEmpty())
            return;
        this.headers.put(name, value);
    }

}
