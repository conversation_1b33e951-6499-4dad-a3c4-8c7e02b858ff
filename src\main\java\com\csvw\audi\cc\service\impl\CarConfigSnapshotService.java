package com.csvw.audi.cc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.elasticsearch.ContractIndex;
import com.csvw.audi.cc.elasticsearch.CustomVoIndex;
import com.csvw.audi.cc.elasticsearch.DetailIndex;
import com.csvw.audi.cc.elasticsearch.OmdDetailIndex;
import com.csvw.audi.cc.entity.dto.CcEstimateDeliveryParam;
import com.csvw.audi.cc.entity.dto.ModelParamDto;
import com.csvw.audi.cc.entity.dto.SnapshotUpdateDto;
import com.csvw.audi.cc.entity.dto.vwcc.CarCustomDetail;
import com.csvw.audi.cc.entity.enumeration.*;
import com.csvw.audi.cc.entity.po.CarCustom;
import com.csvw.audi.cc.entity.po.CarCustomOption;
import com.csvw.audi.cc.entity.po.CarOption;
import com.csvw.audi.cc.entity.po.CarSibInterieur;
import com.csvw.audi.cc.entity.vo.CarCustomVo;
import com.csvw.audi.cc.entity.vo.CcEstimateVo;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.entity.vo.ModelLineVo;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.feign.AudiEshopFeign;
import com.csvw.audi.cc.service.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CarConfigSnapshotService {

    @Autowired
    private ICarCustomService carCustomService;

    @Autowired
    private ICarCustomSnapshotService customSnapshotService;

    @Autowired
    private CarConfigSnapshotService configSnapshotService;

    @Autowired
    private CarRecommendCustomServiceImpl recommendCustomService;

    @Autowired
    private RestHighLevelClient client;

    @Autowired
    private ICarModelLineService modelLineService;

    @Autowired
    private ICarCustomOptionService customOptionService;

    @Autowired
    private ICarSibInterieurService sibInterieurService;

    @Autowired
    private ICarOptionService optionService;

    @Autowired
    private AudiEshopFeign eshopFeign;

    public CarCustomDetail carConfigOmd(CarCustom carCustom) throws Exception {
        carCustom = carCustomService.getById(carCustom.getCcid());
        CarCustomDetail customDetail;
        if (carCustom.getSnapshotId() == null){
            customSnapshotService.snapshotCarCustom(carCustom);
        }
        configSnapshotService.matchAbcAndUpdate(carCustom);
        carCustom = carCustomService.getById(carCustom.getCcid());
        OmdDetailIndex omdDetailIndex = new OmdDetailIndex(client);
        customDetail = omdDetailIndex.getDetailBySnapshotId(carCustom.getSnapshotId());
        customDetail.setValid(carCustom.getValid());
        customDetail.setInvalidReason(carCustom.getInvalidReason());
        customDetail.setUpdateFlag(carCustom.getUpdateFlag());
        customDetail.setUpdateContent(carCustom.getUpdateContent());
        log.info("cop carConfig detail, ccid: " + carCustom.getCcid() + "customDetail: " + JSONObject.toJSONString(customDetail));
        return customDetail;
    }

    public CarCustomDetail carConfigContract(CarCustom carCustom) throws Exception {
        carCustom = carCustomService.getById(carCustom.getCcid());
        CarCustomDetail customDetail;
        if (carCustom.getSnapshotId() == null){
            customSnapshotService.snapshotCarCustom(carCustom);
        }
        configSnapshotService.matchAbcAndUpdate(carCustom);
        carCustom = carCustomService.getById(carCustom.getCcid());
        ContractIndex contractIndex = new ContractIndex(client);
        customDetail = contractIndex.getDetailBySnapshotId(carCustom.getSnapshotId());
        customDetail.setValid(carCustom.getValid());
        customDetail.setInvalidReason(carCustom.getInvalidReason());
        customDetail.setUpdateFlag(carCustom.getUpdateFlag());
        customDetail.setUpdateContent(carCustom.getUpdateContent());
        log.info("contract, ccid: " + carCustom.getCcid() + "customDetail: " + JSONObject.toJSONString(customDetail));
        return customDetail;
    }

    public CarCustomVo getCarCustomVo(CarCustom carCustom) throws Exception {
        carCustom = carCustomService.getById(carCustom.getCcid());
        CarCustomVo customVo;
        if (carCustom.getSnapshotId() == null){
            customSnapshotService.snapshotCarCustom(carCustom);
        }
        configSnapshotService.matchAbcAndUpdate(carCustom);
        carCustom = carCustomService.getById(carCustom.getCcid());
        CustomVoIndex customVoIndex = new CustomVoIndex(client);
        customVo = customVoIndex.getDetailBySnapshotId(carCustom.getSnapshotId());
        customVo.setValid(carCustom.getValid());
        customVo.setInvalidReason(carCustom.getInvalidReason());
        customVo.setUpdateFlag(carCustom.getUpdateFlag());
        customVo.setUpdateContent(carCustom.getUpdateContent());
        log.info("contract, ccid: " + carCustom.getCcid() + "customDetail: " + JSONObject.toJSONString(customVo));
        return customVo;
    }

    public CarCustomDetail carConfigDetail(String channel, CarCustom carCustom) throws Exception {
        carCustom = carCustomService.getById(carCustom.getCcid());
        CarCustomDetail customDetail;
        if (carCustom.getSnapshotId() == null) {
            customSnapshotService.snapshotCarCustom(carCustom);
        }
        configSnapshotService.matchAbcAndUpdate(carCustom);
        carCustom = carCustomService.getById(carCustom.getCcid());
        DetailIndex detailIndex = new DetailIndex(client, channel);
        customDetail = detailIndex.getDetailBySnapshotId(carCustom.getSnapshotId());
        customDetail.setValid(carCustom.getValid());
        customDetail.setInvalidReason(carCustom.getInvalidReason());
        customDetail.setUpdateFlag(carCustom.getUpdateFlag());
        customDetail.setUpdateContent(carCustom.getUpdateContent());
        customDetail.setAccountId(carCustom.getUserId());
        customDetail.setEntryPoint(carCustom.getEntryPoint());
        return customDetail;
    }

    private void matchAbcAndUpdate(CarCustom carCustom) throws Exception {
        if (carCustom.getValid() != null && carCustom.getValid() != 1){
            return;
        }
        //查询订单，订单的不处理
        String orderStatus = eshopFeign.getCcOrderStatus(carCustom.getCcid()).getData();
        if(StringUtils.isNotBlank(orderStatus)){
            return;
        }
        // 长库龄不处理
        if (EntryPointEnum.LONG_STOCK.getValue().equals(carCustom.getEntryPoint())){
            return;
        }
        // 半定制不处理
        if (carCustom.getMeasureId() != null){
            return;
        }
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(carCustom.getModelLineId());
        modelParamDto.setChannel(Constant.ONEAPP_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (lines == null || lines.size() != 1){
            throw new ServiceException("400401", "参数异常：modelLineId", null);
        }
        ModelLineVo modelLine = lines.get(0);
        // omd同步车型不处理
        if(StringUtils.isNotBlank(modelLine.getOmdModelUnicode())){
            return;
        }
        if (carCustom.getClassifyVersion() != null && modelLine.getClassifyVersion() != null
                && carCustom.getClassifyVersion().intValue() == modelLine.getClassifyVersion().intValue()){
            return;
        }
        List<CarCustomOption> cos = customOptionService.listByCcid(carCustom.getCcid());
        List<String> optionIds = configSnapshotService.optionQueryCheck(modelLine, carCustom.getSibInterieurId(), cos);

        CcEstimateDeliveryParam estimateDeliveryParam = new CcEstimateDeliveryParam();
        estimateDeliveryParam.setCustomSeriesId(modelLine.getCustomSeriesId());
        estimateDeliveryParam.setModelLineId(modelLine.getModelLineId());
        estimateDeliveryParam.setOptionIds(optionIds);
        estimateDeliveryParam.setBeforeCheck(false);
        CcEstimateVo estimateVo = modelLineService.ccEstimate(estimateDeliveryParam);

        // 经销商库存车特殊处理，A则改，非A则B
        if (carCustom.getBestRecommendId() != null){
            // A 且以前非A
            if ("A".equals(estimateVo.getType())
                    && (!estimateVo.getType().equals(carCustom.getClassify()) ||
                    !estimateVo.getDeliveryTime().equals(carCustom.getEstimateDelivery()))){
                configSnapshotService.updateABC(carCustom.getCcid(), new SnapshotUpdateDto(estimateVo.getOmdVehicleTypeId(),
                        estimateVo.getType(), modelLine.getClassifyVersion(),
                        estimateVo.getDeliveryTime(), DepositTypeEnum.STOCK.getType()));
                return;
            }
            // 不一致更新成B
            if (!estimateVo.getType().equals(carCustom.getClassify()) ||
                    !estimateVo.getDeliveryTime().equals(carCustom.getEstimateDelivery())){
                configSnapshotService.updateABC(carCustom.getCcid(), new SnapshotUpdateDto(estimateVo.getOmdVehicleTypeId(),
                        "B", modelLine.getClassifyVersion(),
                        DeliveryTimeEnum.B.getValue(), DepositTypeEnum.STOCK.getType()));
                return;
            }
            return;
        }

        // 非库存车小订匹配失败失效
        if (modelLine.getTypeFlag() == null || !modelLine.getTypeFlag().contains(estimateVo.getType()) ){
            //报价单失效文案：当前配置已下架，配置单失效，请重新配置
            //小订失效文案：当前订单所选配置已经下架，订单已失效，请退订后重新下单
            String invalidReason = "当前配置已下架，配置单失效，请重新配置";
            if (StringUtils.isNotBlank(orderStatus)){
                invalidReason = "当前订单所选配置已经下架，订单已失效，请退订后重新下单";
            }
            LambdaUpdateWrapper<CarCustom> cU = new LambdaUpdateWrapper<>();
            cU.eq(CarCustom::getCcid, carCustom.getCcid())
                    .set(CarCustom::getValid, 0)
                    .set(CarCustom::getInvalidReason, invalidReason);
            carCustomService.update(cU);
        }

        // 非库存车
        if (carCustom.getBestRecommendId() == null){
            if (!estimateVo.getType().equals(carCustom.getClassify()) ||
                    !estimateVo.getDeliveryTime().equals(carCustom.getEstimateDelivery())) {
                // 配置单类型不同，更新配置单
                if (estimateVo.getType().equals("C")) {
                    configSnapshotService.updateABC(carCustom.getCcid(), new SnapshotUpdateDto(estimateVo.getOmdVehicleTypeId(),
                            estimateVo.getType(), modelLine.getClassifyVersion(),
                            estimateVo.getDeliveryTime(), DepositTypeEnum.SCHEDULE.getType()));
                } else {
                    configSnapshotService.updateABC(carCustom.getCcid(), new SnapshotUpdateDto(estimateVo.getOmdVehicleTypeId(),
                            estimateVo.getType(), modelLine.getClassifyVersion(),
                            estimateVo.getDeliveryTime(), DepositTypeEnum.STOCK.getType()));
                }
            }else {
                // 配置单类型相同，更新版本
                LambdaUpdateWrapper<CarCustom> cU = new LambdaUpdateWrapper<>();
                cU.eq(CarCustom::getCcid, carCustom.getCcid())
                        .set(CarCustom::getClassifyVersion, modelLine.getClassifyVersion());
                carCustomService.update(cU);
            }
        }
    }

    private void updateABC(Long ccid, SnapshotUpdateDto updateDto) throws Exception {
        if (ccid == null){
            throw new ServiceException("400401", "参数异常：ccid", null);
        }
        customSnapshotService.updateSnapshotCarCustom(ccid, updateDto, LocalDateTime.now());
    }

    private List<String> optionQueryCheck(ModelLineVo modelLine, String sibInterieurId, List<CarCustomOption> cos) throws Exception {
        List<String> optionIds = new ArrayList<>();
        // 1、排除选装包里的座椅
        // 2、排除标装座椅
        // 3、内饰面料里的面料
        // 4、补充标装轮毂，标装饰条
        boolean handleStdRad = true, handleStdEih = true, handleStdSib = true;
        List<ModelLineOptionVo> optionVos;
        String channel = Constant.MASTER_CHANNEL;
        for (CarCustomOption co : cos){
            if (co.getCategory() != null){
                if (OptionCategoryEnum.WHEEL.getValue().equals(co.getCategory())){
                    handleStdRad = false;
                    optionIds.add(co.getOptionId());
                }else if (OptionCategoryEnum.EIH.getValue().equals(co.getCategory())){
                    handleStdEih = false;
                    optionIds.add(co.getOptionId());
                }else if (OptionCategoryEnum.SIB.getValue().equals(co.getCategory())){
                    handleStdSib = false;
                    optionIds.add(co.getOptionId());
                }else if (OptionCategoryEnum.SEET.getValue().equals(co.getCategory())){
                    // 排除标装，和选装包里的
                    boolean handlePacket = true;
                    boolean add = true;
                    String category = OptionCategoryEnum.SEET.getValue();
                    optionVos = modelLineService.modelLineOption(channel, modelLine.getModelLineId(), category);
                    for (ModelLineOptionVo o : optionVos) {
                        if (o.getStatus() != null && o.getStatus().intValue() == 1) {
                            handlePacket = false;
                            add = false;
                            break;
                        }
                    }
                    if (handlePacket) {
                        // 校验选装包中的内容，是否包含座椅。
                        List<CarOption> items = optionService.listPacketSpecialItem(cos.stream().map(CarCustomOption::getOptionId).collect(Collectors.toSet()),
                                modelLine.getModelLineId());
                        if (CollectionUtils.isNotEmpty(items)) {
                            for (CarOption item : items) {
                                if (OptionCategoryEnum.SEET.getValue().equals(item.getCategory())) {
                                    if (co.getCode().equals(item.getOptionCode())) {
                                        add = false;
                                    }
                                }
                            }
                        }
                    }
                    if (add){
                        optionIds.add(co.getOptionId());
                    }
                }else {
                    optionIds.add(co.getOptionId());
                }
            }else {
                optionIds.add(co.getOptionId());
            }
        }

        if (handleStdRad){
            String category = OptionCategoryEnum.WHEEL.getValue();
            optionVos = modelLineService.modelLineOption(channel, modelLine.getModelLineId(), category);
            for (ModelLineOptionVo o : optionVos) {
                if (o.getStatus() != null && o.getStatus().intValue() == 1) {
                    optionIds.add(o.getOptionId());
                    break;
                }
            }
        }
        if (handleStdEih){
            String category = OptionCategoryEnum.EIH.getValue();
            optionVos = modelLineService.modelLineOption(channel, modelLine.getModelLineId(), category);
            for (ModelLineOptionVo o : optionVos) {
                if (o.getStatus() != null && o.getStatus().intValue() == 1) {
                    optionIds.add(o.getOptionId());
                    break;
                }
            }
        }
        if (handleStdSib){
            CarSibInterieur sibInterieur = sibInterieurService.getSibInterieur(sibInterieurId, channel);
            optionIds.add(sibInterieur.getSibOptionId());
        }
        return optionIds;
    }

}
