package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.dto.SibInterieurQueryDto;
import com.csvw.audi.cc.entity.po.CarModelLineSibInterieur;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.vo.ModelLineSibInterieurVo;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 配置线内饰面料关联 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-19
 */
public interface ICarModelLineSibInterieurService extends IService<CarModelLineSibInterieur> {

    List<ModelLineSibInterieurVo> modelLineSibInterieur(String channel, String modelLineId, String sibInterieurId) throws NoSuchFieldException, IllegalAccessException, Exception;

    List<ModelLineSibInterieurVo> modelLineSibInterieur(ModelLineSibInterieurVo param) throws NoSuchFieldException, IllegalAccessException;

    List<ModelLineSibInterieurVo> modelLineSibInterieur(SibInterieurQueryDto param) throws NoSuchFieldException, IllegalAccessException;
}
