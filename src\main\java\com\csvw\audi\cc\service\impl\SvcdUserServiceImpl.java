package com.csvw.audi.cc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csvw.audi.cc.entity.po.SvcdUser;
import com.csvw.audi.cc.entity.vo.bbs.UpdateUserNoInviteVO;
import com.csvw.audi.cc.entity.vo.svcd.SvcdUserListRO;
import com.csvw.audi.cc.entity.vo.svcd.SvcdUserListVO;
import com.csvw.audi.cc.feign.AudiBBSFeignService;
import com.csvw.audi.cc.feign.AudiServiceAdapter;
import com.csvw.audi.cc.mapper.SvcdUserMapper;
import com.csvw.audi.cc.service.ISvcdUserImageAssistant;
import com.csvw.audi.cc.service.ISvcdUserService;
import com.csvw.audi.common.entity.dto.AjaxMessage;
import com.csvw.audi.common.model.ams.AmsResVo;
import com.csvw.audi.common.model.ams.ConsQrcodeVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 渠道商人员信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-06
 */
@Service
@Slf4j
public class SvcdUserServiceImpl extends ServiceImpl<SvcdUserMapper, SvcdUser> implements ISvcdUserService {
    @Autowired
    AudiServiceAdapter audiServiceAdapter;
    @Autowired
    ISvcdUserImageAssistant iSvcdUserImageAssistant;
    @Autowired
    private AudiBBSFeignService audiBBSFeignService;

    @Override
    public List<SvcdUserListRO> listNoPage(SvcdUserListVO vo) {
        // 最早的用户数据是有重复的 所以你看查询sql会很别扭
        List<SvcdUser> svcdUsers = this.baseMapper.listNoPage(vo);
        if (CollectionUtil.isEmpty(svcdUsers)) {
            return new ArrayList<>();
        }
        List<SvcdUserListRO> collect = svcdUsers.stream().map(a -> {
            SvcdUserListRO ro = new SvcdUserListRO();
            BeanUtils.copyProperties(a, ro);
            ro.setWorkStatus(a.getWorkStatus() == null ? null : a.getWorkStatus().intValue());
            ro.setStatus(a.getStatus() == null ? null : a.getStatus().intValue());
            if (StrUtil.isNotBlank(ro.getPic())) {
                ro.setPic(iSvcdUserImageAssistant.getSvcdUserPicOssFullPathURI(a));
            }
            ro.setUserId(a.getUserId() + "");
            // 前端用到了 不应该展示
            ro.setProfile(null);
            return ro;
        }).collect(Collectors.toList());
        return collect;
    }

    @Override
    public String audiStewardLink(Long userId, String userMobile) {
        SvcdUser svcdUser = this.checkUserStatus(userId);
        String url = null;
        try {
            ConsQrcodeVO vo = new ConsQrcodeVO();
            vo.setIdpId(svcdUser.getUserUid());
            vo.setMobile(userMobile);
            vo.setOrgCode(svcdUser.getDealerCode());
            AmsResVo<String> stringAmsResVo = audiServiceAdapter.consQrcode(vo);
            url = stringAmsResVo.getRetData();
        } catch (Exception e) {
            log.warn("查询AMS企微信息异常,{},{}", e.getMessage(), e);
        }
        return url;
    }

    @Override
    public SvcdUser checkUserStatus(Long userId) {
        Assert.notNull(userId, "请输入用户ID");
        SvcdUser one = this.lambdaQuery().eq(SvcdUser::getUserId, userId).one();
        Assert.notNull(one, "请用户不存在");
        // Assert.isTrue(one.getStatus().equals(1L),"该人员已停用");
        Assert.isTrue(one.getWorkStatus().equals(1L), "该人员无岗或者已离职");
        return one;
    }

    // corePoolSize:线程池的核心线程数量 线程池创建出来后就会 new Thread() 5个
    // maximumPoolSize:最大的线程数量，线程池支持的最大的线程数
    // keepAliveTime:存活时间，当线程数大于核心线程，空闲的线程的存活时间 50-5=45
    // unit:存活时间的单位
    // BlockingQueue<Runnable> workQueue:阻塞队列 当线程数超过了核心线程数据，那么新的请求到来的时候会加入到阻塞的队列中
    // new LinkedBlockingQueue<>() 默认队列的长度是 Integer.MAX 那这个就太大了，所以我们需要指定队列的长度
    // threadFactory:创建线程的工厂对象
    // RejectedExecutionHandler handler:当线程数大于最大线程数的时候会执行的淘汰策略
    private static ThreadPoolExecutor executor = new ThreadPoolExecutor(3, 5, 2, TimeUnit.SECONDS,
            new LinkedBlockingDeque(1000), Executors.defaultThreadFactory(), new ThreadPoolExecutor.AbortPolicy());

    @Override
    public void asyncUpdateNoInvite(SvcdUser user) {
        if (Objects.isNull(user) || StringUtils.isEmpty(user.getMobile())) {
            return;
        }
        try {
            UpdateUserNoInviteVO vo = new UpdateUserNoInviteVO();
            vo.setMobile(user.getMobile()); // 业务根据手机号进行
            // 0:无岗,1:在岗,2:离职
            if (user.getWorkStatus() == 0 || user.getWorkStatus() == 1) {
                vo.setNoInviteStatus(true);
                vo.setChannelType(1);
                vo.setDays(-1);
            } else {
                vo.setNoInviteStatus(false);
                vo.setChannelType(0);
                vo.setDays(0);
            }
            log.info("渠道商人员信息同步 {} 更新封禁状态 {}", JSON.toJSONString(user), JSON.toJSONString(vo));
            AjaxMessage ajaxMessage = audiBBSFeignService.updateNoInvite(vo);
            log.info("返回渠道商人员信息同步 {}", JSON.toJSONString(ajaxMessage));
        } catch (Exception e) {
            log.info("渠道商人员信息更新邀请好友功能状态异常", e);
        }
    }
}
