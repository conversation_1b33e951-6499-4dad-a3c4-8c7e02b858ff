<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.SvcdUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.SvcdUser">
        <id column="user_id" property="userId" />
        <result column="user_uid" property="userUid" />
        <result column="work_no" property="workNo" />
        <result column="user_name" property="userName" />
        <result column="dealer_code" property="dealerCode" />
        <result column="dealer_name" property="dealerName" />
        <result column="brand" property="brand" />
        <result column="mobile" property="mobile" />
        <result column="email" property="email" />
        <result column="birthday" property="birthday" />
        <result column="status" property="status" />
        <result column="post_code" property="postCode" />
        <result column="age" property="age" />
        <result column="gender" property="gender" />
        <result column="identity_type" property="identityType" />
        <result column="identity_code" property="identityCode" />
        <result column="nation" property="nation" />
        <result column="political_status" property="politicalStatus" />
        <result column="education" property="education" />
        <result column="computer_grade" property="computerGrade" />
        <result column="is_driving_license" property="isDrivingLicense" />
        <result column="is_used_car_license" property="isUsedCarLicense" />
        <result column="work_status" property="workStatus" />
        <result column="tech_title" property="techTitle" />
        <result column="hire_date" property="hireDate" />
        <result column="leave_date" property="leaveDate" />
        <result column="bank_card_type" property="bankCardType" />
        <result column="bank_card_num" property="bankCardNum" />
        <result column="bank_of_deposit" property="bankOfDeposit" />
        <result column="remark" property="remark" />
        <result column="p_code" property="pCode" />
        <result column="pic" property="pic" />
        <result column="profile" property="profile" />
        <result column="spare1" property="spare1" />
        <result column="spare2" property="spare2" />
        <result column="spare3" property="spare3" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, user_uid, work_no, user_name, dealer_code, dealer_name, brand, mobile, email, birthday, status, post_code, age, gender, identity_type, identity_code, nation, political_status, education, computer_grade, is_driving_license, is_used_car_license, work_status, tech_title, hire_date, leave_date, bank_card_type, bank_card_num, bank_of_deposit, remark, p_code, pic, profile, spare1, spare2, spare3
    </sql>

    <select id="listNoPage" resultMap="BaseResultMap" parameterType="com.csvw.audi.cc.entity.vo.svcd.SvcdUserListVO">
        SELECT a.* FROM
            (select a.* FROM `svcd_user` a
                RIGHT join ( SELECT
                        max( `user_id`) user_id
                        FROM
                        `svcd_user`
                        GROUP BY
                        `user_uid`) b on a.`user_id` = b.`user_id`
                where 1=1
                    <if test="null != vo.status and vo.status !='' ">
                        and status = #{vo.status}
                    </if>
                    <if test="null != vo.workStatus and vo.workStatus !='' ">
                        and work_status = #{vo.workStatus}
                    </if>
                    <if test="null != vo.dealerCode and vo.dealerCode !='' ">
                        and dealer_code = #{vo.dealerCode}
                    </if>
                    <if test="vo.workStatusList != null and vo.workStatusList.size() > 0">
                        AND work_status in
                        <foreach collection="vo.workStatusList" separator="," item="item" open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
            )   a
        left join svcd_positions b on a.user_id = b.user_id
        where 1=1
        <if test="null != vo.positionCode">
            and b.position_code = #{vo.positionCode}
        </if>
        <if test="null != vo.checkStatus">
            and b.check_status = #{vo.checkStatus}
        </if>
        order by a.dealer_code desc ,b.sort is null asc ,b.sort asc ,a.created_at desc
    </select>

</mapper>
