package com.csvw.audi.cc.common.utils.accb;

import okhttp3.MediaType;
import okhttp3.RequestBody;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Map;

public class AccessServiceOkhttpImpl extends AccessServiceOkhttp {
    private static final String UTF8 = "UTF-8";

    public AccessServiceOkhttpImpl(String ak, String sk) {
        super(ak, sk);
    }

    public okhttp3.Request access(String url, Map<String, String> headers, String entity, HttpMethodName httpMethod) throws Exception {
        Request request = new Request();
        request.setAppKey(this.ak);
        request.setAppSecrect(this.sk);
        request.setMethod(httpMethod.name());
        request.setUrl(url);
        for (String k : headers.keySet())
            request.addHeader(k, headers.get(k));
        request.setBody(entity);
        Signer signer = new Signer();
        signer.sign(request);
        return createRequest(url, request.getHeaders(), entity, httpMethod);
    }

    public okhttp3.Request access(String url, Map<String, String> headers, InputStream content, Long contentLength, HttpMethodName httpMethod) throws Exception {
        ByteArrayOutputStream result = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length;
        while ((length = content.read(buffer)) != -1)
            result.write(buffer, 0, length);
        String body = result.toString("UTF-8");
        return access(url, headers, body, httpMethod);
    }

    private static okhttp3.Request createRequest(String url, Map<String, String> headers, String body, HttpMethodName httpMethod) throws Exception {
        okhttp3.Request httpRequest;
        if (body == null)
            body = "";
        RequestBody entity = RequestBody.create(MediaType.parse(""), body.getBytes("UTF-8"));
        if (httpMethod == HttpMethodName.POST) {
            httpRequest = (new okhttp3.Request.Builder()).url(url).post(entity).build();
        } else if (httpMethod == HttpMethodName.PUT) {
            httpRequest = (new okhttp3.Request.Builder()).url(url).put(entity).build();
        } else if (httpMethod == HttpMethodName.PATCH) {
            httpRequest = (new okhttp3.Request.Builder()).url(url).patch(entity).build();
        } else if (httpMethod == HttpMethodName.DELETE) {
            httpRequest = (new okhttp3.Request.Builder()).url(url).delete(entity).build();
        } else if (httpMethod == HttpMethodName.GET) {
            httpRequest = (new okhttp3.Request.Builder()).url(url).get().build();
        } else if (httpMethod == HttpMethodName.HEAD) {
            httpRequest = (new okhttp3.Request.Builder()).url(url).head().build();
        } else if (httpMethod == HttpMethodName.OPTIONS) {
            httpRequest = (new okhttp3.Request.Builder()).url(url).method("OPTIONS", null).build();
        } else {
            throw new RuntimeException("Unknown HTTP method name: " + httpMethod);
        }
        for (String key : headers.keySet())
            httpRequest = httpRequest.newBuilder().addHeader(key, headers.get(key)).build();
        return httpRequest;
    }
}
