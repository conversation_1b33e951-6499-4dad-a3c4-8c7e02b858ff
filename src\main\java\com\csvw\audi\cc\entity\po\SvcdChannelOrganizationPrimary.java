package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 渠道商组织信息-区域商务经理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SvcdChannelOrganizationPrimary 对象", description="渠道商组织信息-区域商务经理")
public class SvcdChannelOrganizationPrimary extends Model<SvcdChannelOrganizationPrimary> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "数据id")
      @TableId(value = "channel_organization_primary_id", type = IdType.AUTO)
    private Long channelOrganizationPrimaryId;

    @ApiModelProperty(value = "渠道商组织信息id")
    private Long channelOrganizationId;

    @ApiModelProperty(value = "渠道商编码")
    private String dealerCode;

    @ApiModelProperty(value = "真实名字")
    @TableField("`primary_name`")
    private String name;

    @ApiModelProperty(value = "在职状态(1:在职 2:离职)")
    @TableField("`status`")
    private String status;

    @ApiModelProperty(value = "登陆账号")
    @TableField("`username`")
    private String username;

    @ApiModelProperty(value = "账号创建时间(时间戳)")
    @TableField("`create_time`")
    private String createTime;

    @ApiModelProperty(value = "性别(0：男 1：女)")
    @TableField("`gender`")
    private String gender;

    @ApiModelProperty(value = "员工工号")
    @TableField("`employ_no`")
    private String employNo;

    @ApiModelProperty(value = "员工类型")
    @TableField("`employ_type`")
    private String employType;

    @ApiModelProperty(value = "出生日期(时间戳)")
    @TableField("`birthday`")
    private String birthday;

    @ApiModelProperty(value = "身份证")
    @TableField("`id_card`")
    private String idCard;

    @ApiModelProperty(value = "教育程度(0：博士研究生 1：硕士研究生 2：本科 3：大专 4：高中 5:中专技校 6:中学 7：小学)")
    @TableField("`education`")
    private String education;

    @ApiModelProperty(value = "民族")
    @TableField("`nation`")
    private String nation;

    @ApiModelProperty(value = "政治面貌(0：团员 1：党员 2：群众)")
    @TableField("`polity`")
    private String polity;

    @ApiModelProperty(value = "邮编")
    @TableField("`post_code`")
    private String postCode;

    @ApiModelProperty(value = "联系电话")
    @TableField("`telephone`")
    private String telephone;

    @ApiModelProperty(value = "手机")
    @TableField("`mobile`")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    @TableField("`email`")
    private String email;

    @ApiModelProperty(value = "联系地址")
    @TableField("`address`")
    private String address;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;

    @Override
    protected Serializable pkVal() {
        return this.channelOrganizationPrimaryId;
    }

}
