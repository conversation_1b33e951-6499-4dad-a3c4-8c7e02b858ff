spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      filters: stat,config
      initial-size: '8'
      max-active: '10'
      min-idle: '5'
      query-timeout: '6000'
      remove-abandoned-timeout: '1800'
      transaction-query-timeout: '6000'
      type: com.alibaba.druid.pool.DruidDataSource
      url: *********************************************************************************************************************
      username: root
      password: 123456
  kafka:
    initAutoStart: false
    bootstrap-servers: **************:9092
    groupId: audi-kmsp-dealer-sync-action-group-2
    setAutoStartup: true
    org-topic: AUDI-SVCD-OPERATION-ORG-TOPIC
    org-init-topic: AUDI-SVCD-OPERATION-ORG-INIT-TOPIC
    position-topic: AUDI-SVCD-OPERATION-POSITION-TOPIC
    position-init-topic: AUDI-SVCD-OPERATION-POSITION-INIT-TOPIC
    user-topic: AUDI-SVCD-OPERATION-USER-TOPIC
    user-init-topic: AUDI-SVCD-OPERATION-USER-INIT-TOPIC
    city-topic: AUDI-SVCD-OPERATION-CITY-TOPIC
    city-init-topic: AUDI-SVCD-OPERATION-CITY-INIT-TOPIC
    province-topic: AUDI-SVCD-OPERATION-PROVINCE-TOPIC
    province-init-topic: AUDI-SVCD-OPERATION-PROVINCE-INIT-TOPIC
    area-topic: AUDI-SVCD-OPERATION-AREA-TOPIC
    area-init-topic: AUDI-SVCD-OPERATION-AREA-INIT-TOPIC
    company-topic: AUDI-SVCD-OPERATION-COMPANY-TOPIC
    company-init-topic: AUDI-SVCD-OPERATION-COMPANY-INIT-TOPIC
    investor-topic: AUDI-SVCD-OPERATION-INVESTOR-TOPIC
    investor-init-topic: AUDI-SVCD-OPERATION-INVESTOR-INIT-TOPIC
    after-sales-topic: AUDI-SVCD-OPERATION-AFTER-SALES-TOPIC
    after-sales-init-topic: AUDI-SVCD-OPERATION-AFTER-SALES-INIT-TOPIC
    sales-agent-topic: AUDI-SVCD-OPERATION-SALES-AGENT-TOPIC
    sales-agent-init-topic: AUDI-SVCD-OPERATION-SALES-AGENT-INIT-TOPIC

mybatis-plus:
  configuration:
    mapUnderscoreToCamelCase: 'true'
  mapperLocations: classpath:mybatis/*.xml
  typeAliasesPackage: com.csvw.audi.cc.entity
audi-car-config:
  accb:
#    url: https://accb-api.audibrand.cn
#    host: accb-api.audibrand.cn
#    secret: bc5adda0388a496d91a88fd6fbb4511e
#    key: 16d9980fa26345e88c8b371303fdda72
    host: pre-accb-api.audibrand.cn
    url: https://pre-accb-api.audibrand.cn
    key: e0f97ac39ba64668838ccc8921d68550
    secret: f50123af146c40049c83802d8f90be8b
    lang: en
    product: CHINA_SAIC
  infra:
    appId: Vu3rx4iuPqZD
    appSecret: dnQAVhjD5qbHkI1Yn9LoWggitDc1pUEd
    url: http://*************:60066/audi_noprod/infra/test

api:
  swagger:
    enabled: true
    basePackage: com.csvw.audi.cc.controller
    contact: <EMAIL>
    description: 车辆配置器服务
    title: audi-car-config
    version: '2.0'
logging:
  level:
    com.csvw.audi: debug
