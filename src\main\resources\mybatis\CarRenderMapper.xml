<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarRenderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarRender">
        <id column="id" property="id" />
        <result column="preview" property="preview" />
        <result column="config" property="config" />
        <result column="model_code" property="modelCode" />
        <result column="type_code" property="typeCode" />
        <result column="exterior_code" property="exteriorCode" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, preview, config, model_code, type_code, exterior_code, del_flag
    </sql>

</mapper>
