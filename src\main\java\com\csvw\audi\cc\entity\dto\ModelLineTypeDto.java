package com.csvw.audi.cc.entity.dto;

import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ModelLineTypeDto {
    public ModelLineTypeDto(String accbTypeCode, String modelYear, String modelVersion){
        this.accbTypeCode = accbTypeCode.replaceFirst("TYPE:", "");
        this.modelYear = modelYear;
        this.modelVersion = modelVersion;
    }
    private String accbTypeCode;
    private String modelYear;
    private String modelVersion;
    private String extCode;
    private List<String> extCodes;
    private String inCode;
    private String radCode;
    private String eihCode;
    private String sibCode;
    private List<String> prCodes;
    private Integer prCodesNum;
    private String seat;

}
