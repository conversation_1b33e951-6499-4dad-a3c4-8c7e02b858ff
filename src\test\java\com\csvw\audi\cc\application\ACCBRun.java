package com.csvw.audi.cc.application;

import com.csvw.audi.cc.config.KafkaConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.web.bind.annotation.RestController;

@EnableFeignClients(basePackages = { "com.csvw.audi.cc.feign" })
//@EnableEurekaClient
@SpringBootApplication(exclude = {
        org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration.class
})
@RestController
@ComponentScan(basePackages = {"com.csvw.audi.cc.config", "com.csvw.audi.cc.common.utils"},
        excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE,
                classes = KafkaConfig.class))
@MapperScan(basePackages = {"com.csvw.audi.cc.entity"})
public class ACCBRun {
    public static void main(String[] args) {
        SpringApplication.run(ACCBRun.class, args);
    }
}
