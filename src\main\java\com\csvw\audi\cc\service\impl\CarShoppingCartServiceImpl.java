package com.csvw.audi.cc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.common.utils.StringUtil;
import com.csvw.audi.cc.entity.dto.CarCustomDto;
import com.csvw.audi.cc.entity.dto.CarShoppingCartDto;
import com.csvw.audi.cc.entity.dto.vwcc.*;
import com.csvw.audi.cc.entity.dto.vwcc.Option;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.entity.vo.CarCustomVo;
import com.csvw.audi.cc.entity.vo.CarShoppingCartParam;
import com.csvw.audi.cc.entity.vo.CarShoppingCartVo;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.feign.AudiTaskFeign;
import com.csvw.audi.cc.feign.CopProdQueryFeign;
import com.csvw.audi.cc.mapper.CarShoppingCartMapper;
import com.csvw.audi.cc.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 购物车 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Service
@Slf4j
public class CarShoppingCartServiceImpl extends ServiceImpl<CarShoppingCartMapper, CarShoppingCart> implements ICarShoppingCartService {

    @Autowired
    private ICarCustomService carCustomService;

    @Autowired
    private ICarCustomOptionService carCustomOptionService;

    @Autowired
    private CopProdQueryFeign copProdQueryFeign;

    @Autowired
    private CarShoppingCartMapper carShoppingCartMapper;

    @Autowired
    private ICarModelLineService carModelLineService;

    @Autowired
    private ICarModelLineSibInterieurService carModelLineSibInterieurService;

    @Autowired
    private ICarModelLineOptionService carModelLineOptionService;

    @Autowired
    private ICarConfigImageService configImageService;

    @Autowired
    private AudiTaskFeign audiTaskFeign;

    @Autowired
    private ICarCustomSeriesService carCustomSeriesService;

    @Autowired
    private CarRecommendCustomServiceImpl recommendCustomService;

    @Autowired
    private CarConfigSnapshotService carConfigSnapshot;

    @Autowired
    private ICarCustomSourceService sourceService;

    @Override
    public List<CarShoppingCartVo> getCarShoppingCartList(String userId) {
        String channel = Constant.MASTER_CHANNEL;
        List<CarShoppingCartVo> list = new ArrayList();
        List<CarShoppingCart> shoppingCartList;
        CarShoppingCartVo carShoppingCartVo;
        CarCustomDetail customDetail;
        //查询购物车列表
        QueryWrapper<CarShoppingCart> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId).eq("del_flag",0).last(" order by auto_save desc,update_time desc");
        shoppingCartList = this.list(queryWrapper);

        for(CarShoppingCart carShoppingCart : shoppingCartList) {
            if(carShoppingCart.getCcid() == null) {
                log.info("购物车列表 无ccid shoppingCartId:"+carShoppingCart.getShoppingCartId());
                continue;
            }

            CarCustom carCustom = new CarCustom();
            carCustom = carCustom.selectById(carShoppingCart.getCcid());
            String dealerCode = null;
            if (carCustom.getSourceId() != null){
                CarCustomSource source = sourceService.getById(carCustom.getSourceId());
                if (source != null){
                    dealerCode = source.getDealerCode();
                }
            }
            if (carCustom == null){
                log.info("购物车列表 无配置单数据 shoppingCartId:"+carShoppingCart.getShoppingCartId());
                continue;
            }

            //参考接口 /api/v1/cc/detail
            try {
                customDetail = carConfigSnapshot.carConfigDetail(channel, carCustom);
            } catch (Exception e) {
                log.info("购物车列表 配置单获取异常 shoppingCartId:"+carShoppingCart.getShoppingCartId(),e);
                continue;
            }

            carShoppingCartVo = new CarShoppingCartVo();
            carShoppingCartVo.setDealerCode(dealerCode);
            carShoppingCartVo.setShoppingCartId(String.valueOf(carShoppingCart.getShoppingCartId()));
            carShoppingCartVo.setUserId(carShoppingCart.getUserId());
            carShoppingCartVo.setCcid(String.valueOf(carShoppingCart.getCcid()));
            carShoppingCartVo.setCreateTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(carShoppingCart.getCreateTime()));
            carShoppingCartVo.setUpdateTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(carShoppingCart.getUpdateTime()));
            carShoppingCartVo.setTotalPrice(new BigDecimal(0));
            carShoppingCartVo.setSkuid(carShoppingCart.getSkuid());
            carShoppingCartVo.setInvitationCode(carShoppingCart.getInvitationCode());
            carShoppingCartVo.setConfigTotalPrice(customDetail.getConfigDetail().getTotalPrice());
            carShoppingCartVo.setCarSeries(customDetail.getConfigDetail().getCarSeries());
            carShoppingCartVo.setCarModel(customDetail.getConfigDetail().getCarModel());
            carShoppingCartVo.setOutsideColor(customDetail.getConfigDetail().getOutsideColor());
            carShoppingCartVo.setInsideColor(customDetail.getConfigDetail().getInsideColor());
            carShoppingCartVo.setOptionList(customDetail.getConfigDetail().getOptionList());
            carShoppingCartVo.setAutoSave(carShoppingCart.getAutoSave());
            carShoppingCartVo.setValid(carCustom.getValid());
            carShoppingCartVo.setInvalidReason(carCustom.getInvalidReason());
            carShoppingCartVo.setUpdateFlag(carCustom.getUpdateFlag());
            carShoppingCartVo.setUpdateContent(carCustom.getUpdateContent());

            //获取意向金
            if(StringUtil.isInteger(carShoppingCart.getSkuid())) {
                try {
                    ConfigDetail configDetail = customDetail.getConfigDetail();

                    JSONObject resultJson = copProdQueryFeign.findProdSkuById(Long.parseLong(carShoppingCart.getSkuid()));
                    JSONObject dataJson = resultJson.getJSONObject("data");
                    String prodId  = dataJson.getString("prodId");
                    JSONObject priceObj = copProdQueryFeign.ngaProdPrice( customDetail.getDepositType(), configDetail.getCarModel().getModelCode(),
                            prodId, configDetail.getCarSeries().getSeriesCode());
                    if(priceObj.containsKey("data")){
                        carShoppingCartVo.setTotalPrice(new BigDecimal(priceObj.getJSONObject("data").getString("prodNgaDownPayPrice")));//意向金
                    }
                } catch(Exception e) {
                    log.info("购物车，获取意向金失败，Skuid :"+carShoppingCart.getSkuid(),e);
                }
            }

            list.add(carShoppingCartVo);
        }
        return list;
    }

    //废弃，待新逻辑运行无误后删除
    public List<CarShoppingCartVo> getCarShoppingCartListOld(String userId) {
        String channel = Constant.MASTER_CHANNEL;
        CarShoppingCartVo carShoppingCartVo;
        CarCustom carCustom;
        ColorDetail outsideColor;
        ColorDetail insideColor;
        Set<Long> ccids;
        List<CarShoppingCartVo> list = new ArrayList();
        List<Option> options;
        List<CarShoppingCart> shoppingCartList;
        List<CarCustom> carCustomList;
        List<CarCustomOption> carCustomOptionList;
        List<ModelLineOptionVo> modelLineOptions;
        ModelLineOptionVo modelLineOptionVo;
        Map<String, CarCustom> carCustomMap = new HashMap<>();
        Map<String, CarCustomSeries> carCustomSeriesMap = new HashMap<>();
        Map<String, CarModelLine> carModelLineMap = new HashMap<>();
        List<String> modelLineIds = new ArrayList();

        //查询购物车列表
        QueryWrapper<CarShoppingCart> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId).eq("del_flag",0).last(" order by auto_save desc,update_time desc");
        shoppingCartList = this.list(queryWrapper);
        ccids = shoppingCartList.stream().map(i->i.getCcid()).collect(Collectors.toSet());

        if(ccids.size() > 0) {
            //查询用户车辆信息
            QueryWrapper<CarCustom> carCustomQuery = new QueryWrapper<>();
            carCustomQuery.in("ccid",ccids);
            carCustomList = carCustomService.list(carCustomQuery);
            carCustomList.forEach(i-> carCustomMap.put(String.valueOf(i.getCcid()), i));
            carCustomList.forEach(i-> modelLineIds.add(String.valueOf(i.getModelLineId())));

            //获取所有车系code
            QueryWrapper<CarCustomSeries> carCustomSeriesQuery = new QueryWrapper<>();
            carCustomSeriesQuery.select("DISTINCT custom_series_code","custom_series_id").eq("channel",Constant.MASTER_CHANNEL);
            List<CarCustomSeries> carCustomSeriesList = carCustomSeriesService.list(carCustomSeriesQuery);
            carCustomSeriesList.forEach(i-> carCustomSeriesMap.put(String.valueOf(i.getCustomSeriesId()), i));

            //获取所有 modelLineId
            QueryWrapper<CarModelLine> carModelLineQuery = new QueryWrapper<>();
            carCustomQuery.in("model_line_id",modelLineIds).eq("channel",Constant.MASTER_CHANNEL);
            List<CarModelLine> carModelLineList = carModelLineService.list(carModelLineQuery);
            carModelLineList.forEach(i-> carModelLineMap.put(String.valueOf(i.getModelLineId()), i));
        }

        for(CarShoppingCart carShoppingCart : shoppingCartList) {
            carCustom = carCustomMap.get(String.valueOf(carShoppingCart.getCcid()));

            options = new ArrayList<>();
            carShoppingCartVo = new CarShoppingCartVo();
            carShoppingCartVo.setShoppingCartId(String.valueOf(carShoppingCart.getShoppingCartId()));
            carShoppingCartVo.setUserId(carShoppingCart.getUserId());
            carShoppingCartVo.setCcid(String.valueOf(carShoppingCart.getCcid()));
            carShoppingCartVo.setCreateTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(carShoppingCart.getCreateTime()));
            carShoppingCartVo.setUpdateTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(carShoppingCart.getUpdateTime()));
            carShoppingCartVo.setTotalPrice(new BigDecimal(0));
            carShoppingCartVo.setSkuid(carShoppingCart.getSkuid());
            carShoppingCartVo.setInvitationCode(carShoppingCart.getInvitationCode());

            //查询车辆系列
            SeriesDetail seriesDetail = new SeriesDetail();
            try {
                seriesDetail.setSeriesCode(carCustomSeriesMap.get(carModelLineMap.get(carCustomMap.get(String.valueOf(carShoppingCart.getCcid())).getModelLineId()).getCustomSeriesId()).getCustomSeriesCode());
            } catch (NullPointerException npe) {
                seriesDetail.setSeriesCode("");
                log.info("购物车列表，carCustomSeriesCode 获取失败，ccid:"+carShoppingCart.getCcid());
            }
            seriesDetail.setSeriesNameCn(carCustom.getModelDesc());
            carShoppingCartVo.setCarSeries(seriesDetail);

            //查询车型信息
            ModelDetail modelDetail = new ModelDetail();
            modelDetail.setModelCode("498B2Y");
            modelDetail.setModelNameCn(carCustom.getAccbTypeDesc());
            modelDetail.setModelYear("2022");
            modelDetail.setOmdModelVersion("0");

            //查询外饰颜色
            outsideColor = new ColorDetail();
            //查询内饰颜色
            insideColor = new ColorDetail();
            //查询用户装备信息
            QueryWrapper<CarCustomOption> carOptionQuery = new QueryWrapper<>();
            carOptionQuery.eq("ccid",carShoppingCart.getCcid());
            carCustomOptionList = carCustomOptionService.list(carOptionQuery);

            //返回信息
            for(CarCustomOption i : carCustomOptionList){
                modelLineOptionVo = null;
                //查询装备
                ModelLineOptionVo optionParam = new ModelLineOptionVo();
                optionParam.setModelLineId(carCustom.getModelLineId());
                optionParam.setOptionId(i.getOptionId());
                optionParam.setDelFlag(0);
                optionParam.setChannel(channel);
                modelLineOptions = carModelLineOptionService.listModelLineOption(optionParam);

                for(ModelLineOptionVo mov : modelLineOptions) {
                    //非默认选项(不含轮毂)、channel 数据
                    if((mov.getStatus() != 1 || "RAD".equals(mov.getCategory())) && channel.equals(mov.getChannel())) {
                        modelLineOptionVo = mov;
                    }
                }
//                if (modelLineOptionVo == null){
//                    for(ModelLineOptionVo mov : modelLineOptions) {
//                        //过滤 默认选项(不含轮毂)、channel 数据
//                        if((mov.getStatus() != 1 || "RAD".equals(mov.getCategory())) && Constant.MASTER_CHANNEL.equals(mov.getChannel())) {
//                            modelLineOptionVo = mov;
//                        }
//                    }
//                }
                if(modelLineOptionVo == null || !modelLineOptionVo.getOptionId().equals(i.getOptionId())) {
//                if(modelLineOptionVo == null) {
                    continue;
                }

                //外饰
                if ("COLOR_EXTERIEUR".equals(i.getCategory())) {
                    //暂时没有轮毂
                    //获取车辆图片
//                    modelDetail.setImageUrl("test/2021/06/02/a7l/order-preview.png");
                    modelDetail.setImageUrl(configImageService.getCcLeftImageUrl(channel, carCustom.getModelLineId(), i.getCode(), null));

                    outsideColor.setColorCode(i.getCode());
                    outsideColor.setColorNameCn(modelLineOptionVo.getOptionName());
                    outsideColor.setImageUrl(modelLineOptionVo.getImageUrlList());
//                    if(!modelLineOptionVo.getOptionId().equals(i.getOptionId())){
//                        continue;
//                    }
                }
                //内饰
                else if ("COLOR_INTERIEUR".equals(i.getCategory())) {
                    insideColor.setColorCode(i.getCode());
                    insideColor.setColorNameCn(modelLineOptionVo.getOptionName());
                    insideColor.setImageUrl(modelLineOptionVo.getImageUrlList());
                }
                //装备
                else {
                    Option option = new Option();
                    option.setImageUrl(modelLineOptionVo.getImageUrl());
                    option.setOptionNameCn(modelLineOptionVo.getOptionName());
                    option.setOptionCode(modelLineOptionVo.getOptionCode());
                    options.add(option);
                }
            }

            carShoppingCartVo.setCarModel(modelDetail);
            carShoppingCartVo.setOutsideColor(outsideColor);
            carShoppingCartVo.setInsideColor(insideColor);
            carShoppingCartVo.setOptionList(options);

            //获取意向金
            if(carShoppingCart.getSkuid() != null && StringUtil.isInteger(carShoppingCart.getSkuid())) {
                try {
                    JSONObject resultJson = copProdQueryFeign.findProdSkuById(Long.parseLong(carShoppingCart.getSkuid()));
                    JSONObject dataJson = resultJson.getJSONObject("data");
//                    log.info("购物车，获取意向金 "+resultJson);
                    if(dataJson == null) {
                        log.info("购物车，意向金获取失败，无数据，Skuid :"+carShoppingCart.getSkuid());
                    } else {
                        carShoppingCartVo.setTotalPrice(new BigDecimal(dataJson.getString("reserveOrderAmount")));//意向金
//                    carShoppingCartVo.setTotalPrice(new BigDecimal(dataJson.getString("confirmOrderAmount")));//定金
                    }

                } catch(Exception e) {
                    log.info("购物车，获取意向金失败，Skuid :"+carShoppingCart.getSkuid(),e);
                }
            }

            list.add(carShoppingCartVo);
        }

        return list;
    }

    @Override
    public List<String> delCarShoppingCartByIds(CarShoppingCartVo carShoppingCartVo) {
        LocalDateTime nowDate = LocalDateTime.now();
        String userId = carShoppingCartVo.getUserId();
        List<String> errorIds = new ArrayList<>();
        for(String shoppingCartId : carShoppingCartVo.getShoppingCartIds()) {
            if(!StringUtil.isInteger(shoppingCartId)) {
                continue;
            }
            CarShoppingCart carShoppingCart = new CarShoppingCart();
            carShoppingCart.setDelFlag(1);
            carShoppingCart.setUpdateTime(nowDate);
            carShoppingCart.setUpdateUser(userId);
            UpdateWrapper<CarShoppingCart> wrapper = new UpdateWrapper<>();
            wrapper.eq("shopping_cart_id",shoppingCartId).eq("user_id",userId);
            int num = carShoppingCartMapper.update(carShoppingCart,wrapper);
            if(num == 0) {
                errorIds.add(shoppingCartId);
            }
        }
        LambdaQueryWrapper<CarShoppingCart> ccidQ = new LambdaQueryWrapper<>();
        ccidQ.eq(CarShoppingCart::getCcid, carShoppingCartVo.getCcid())
                .eq(CarShoppingCart::getUserId, userId)
                .eq(CarShoppingCart::getDelFlag, 0);
        if (StringUtils.isNotBlank(carShoppingCartVo.getCcid())
                && CollectionUtils.isNotEmpty(carShoppingCartMapper.selectList(ccidQ))){
            CarShoppingCart carShoppingCart = new CarShoppingCart();
            carShoppingCart.setDelFlag(1);
            carShoppingCart.setUpdateTime(nowDate);
            carShoppingCart.setUpdateUser(userId);
            carShoppingCartMapper.update(carShoppingCart,ccidQ);
        }
        return errorIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CarShoppingCart saveCarShoppingCart(CarShoppingCartParam carShoppingCartParam) {
        String userId = carShoppingCartParam.getUserId();

        //去重复
        if(StringUtil.isNotEmpty(carShoppingCartParam.getCcid())) {
            String channel = Constant.MASTER_CHANNEL;
            CarCustomDetail customDetailNew,customDetailTemp;
            ConfigDetail configDetailNew,configDetailTemp;
            CarCustom carCustom = new CarCustom();
            boolean repeatBool = false;

            carCustom = carCustom.selectById(carShoppingCartParam.getCcid());
            if (carCustom == null){
                log.info("购物车去重复 新配置-无配置单数据 ccid:"+carShoppingCartParam.getCcid());
                return new CarShoppingCart();
            }
            try {
                customDetailNew = carConfigSnapshot.carConfigDetail(channel, carCustom);
            } catch (Exception e) {
                log.info("购物车去重复 新配置-配置单获取异常 ccid:"+carShoppingCartParam.getCcid(),e);
                return new CarShoppingCart();
            }
            configDetailNew = customDetailNew.getConfigDetail();

            //查询 非自动保存的购物车数据
            QueryWrapper<CarShoppingCart> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId).eq("del_flag",0).eq("auto_save",0);
            List<CarShoppingCart> shoppingCartList = this.list(queryWrapper);
            for(CarShoppingCart carShoppingCart: shoppingCartList) {
                repeatBool = false;
                if(carShoppingCart.getCcid() == null) {
                    log.info("购物车去重复 无ccid shoppingCartId:"+carShoppingCart.getShoppingCartId());
                    continue;
                }
                carCustom = carCustom.selectById(carShoppingCart.getCcid());
                if (carCustom == null){
                    log.info("购物车去重复 无配置单数据 shoppingCartId:"+carShoppingCart.getShoppingCartId());
                    continue;
                }
                try {
                    customDetailTemp = carConfigSnapshot.carConfigDetail(channel, carCustom);
                } catch (Exception e) {
                    log.info("购物车去重复 配置单获取异常 shoppingCartId:"+carShoppingCart.getShoppingCartId(),e);
                    continue;
                }
                configDetailTemp = customDetailTemp.getConfigDetail();

                //对比-车系
                if(configDetailNew.getCarSeries().getSeriesCode().equals(configDetailTemp.getCarSeries().getSeriesCode())) {
                    repeatBool = true;
                } else {
                    repeatBool = false;
                }
                //对比-车型
                if(repeatBool && configDetailNew.getCarModel().getModelCode().equals(configDetailTemp.getCarModel().getModelCode())) {
                    repeatBool = true;
                } else {
                    repeatBool = false;
                }
                //对比-内饰
                if(repeatBool && configDetailNew.getInsideColor().getColorCode().equals(configDetailTemp.getInsideColor().getColorCode())) {
                    repeatBool = true;
                } else {
                    repeatBool = false;
                }
                //对比-外饰
                if(repeatBool && configDetailNew.getOutsideColor().getColorCode().equals(configDetailTemp.getOutsideColor().getColorCode())) {
                    repeatBool = true;
                } else {
                    repeatBool = false;
                }
                //对比-选项配置
                if(repeatBool) {
                    if(configDetailNew.getOptionList() != null
                            && configDetailTemp.getOptionList() != null
                            && configDetailNew.getOptionList().size() == configDetailTemp.getOptionList().size()) {
                        for(Option optionNew : configDetailNew.getOptionList()) {
                            repeatBool = false;
                            for(Option optionTemp : configDetailTemp.getOptionList()) {
                                if(optionNew.getOptionCode().equals(optionTemp.getOptionCode())) {
                                    repeatBool = true;
                                    break;
                                }
                            }
                            if(!repeatBool) {
                                break;
                            }
                        }
                    } else {
                        repeatBool = false;
                    }
                }

                //删除重复数据
                if(repeatBool) {
                    CarShoppingCart del = new CarShoppingCart();
                    del.setShoppingCartId(carShoppingCart.getShoppingCartId());
                    del.setDelFlag(1);
                    del.setUpdateTime(LocalDateTime.now());
                    carShoppingCartMapper.updateById(del);
                }
            }
        }

        //如果从购物车进入的配置器，则删除旧数据
        if(StringUtil.isNotEmpty(carShoppingCartParam.getShoppingCartId())) {
            CarShoppingCart del = new CarShoppingCart();
            del.setShoppingCartId(Long.parseLong(carShoppingCartParam.getShoppingCartId()));
            del.setDelFlag(1);
            del.setUpdateTime(LocalDateTime.now());
            carShoppingCartMapper.updateById(del);
        }

        //保存购物车数据
        CarShoppingCart carShoppingCart = addCarShoppingCart(userId,carShoppingCartParam.getCcid(), carShoppingCartParam.getSkuid(),Integer.parseInt(carShoppingCartParam.getAutoSave()),carShoppingCartParam.getInvitationCode());

        //删除保存配置单时自动创建的数据
        CarShoppingCart delAutoSave = new CarShoppingCart();
        delAutoSave.setDelFlag(1);
        delAutoSave.setUpdateTime(LocalDateTime.now());
        UpdateWrapper<CarShoppingCart> updateWrapper = new UpdateWrapper();
        updateWrapper.eq("auto_save",1).eq("user_id",userId).eq("del_flag",0);
        carShoppingCartMapper.update(delAutoSave,updateWrapper);
        return carShoppingCart;
    }

    private CarShoppingCart addCarShoppingCart(String userId,String ccid, String skuid,Integer autoSaveFlag,String invitationCode) {
        LocalDateTime nowDate = LocalDateTime.now();
        CarShoppingCart carShoppingCart = new CarShoppingCart();
        carShoppingCart.setCcid(Long.parseLong(ccid));
        carShoppingCart.setUserId(userId);
        carShoppingCart.setDelFlag(0);
        carShoppingCart.setAutoSave(autoSaveFlag);
        carShoppingCart.setSkuid(skuid);
        carShoppingCart.setInvitationCode(invitationCode);
        carShoppingCart.setCreateTime(nowDate);
        carShoppingCart.setCreateUser(userId);
        carShoppingCart.setUpdateTime(nowDate);
        carShoppingCart.setUpdateUser(userId);
        carShoppingCart.insert();
        return carShoppingCart;
    }

    @Override
    public CarShoppingCart autoSaveCarShoppingCart(String memberId, String userId, String mobile, String channel, CarShoppingCartVo carShoppingCartVo) throws Exception {
        String ccid = "";
        String skuid = "";
        QueryWrapper<CarShoppingCart> queryWrapper = new QueryWrapper();
        queryWrapper.eq("auto_save",1).eq("user_id",userId).eq("del_flag",0).last(" limit 1");
        CarShoppingCart carShoppingCart = this.getOne(queryWrapper);

        if(carShoppingCart == null) {
            CarCustom carCustom;
            //有配置信息
            if(carShoppingCartVo.getCarCustomDto() != null) {
                carCustom = carCustomService.addCarCustomConfig(memberId, userId,mobile, channel, carShoppingCartVo.getCarCustomDto());
            } else {
                //无配置信息，生成默认配置 ccid
                carCustom = this.createdAutoSaveCarShoppingCart(memberId, userId,mobile, channel);
            }
            ccid = String.valueOf(carCustom.getCcid());

            carShoppingCart = this.addCarShoppingCart(userId,ccid,skuid,1,"");
        } else {
            ccid = String.valueOf(carShoppingCart.getCcid());
            CarCustom cc = carCustomService.getById(ccid);
            if (cc == null || !cc.getUserId().equals(userId)){
                log.info("配置单不可修改");
                return carShoppingCart;
            }

            if (carShoppingCartVo.getCarCustomDto() == null){
                log.info("无可修改配置");
                return carShoppingCart;
            }

            //更新配置单 - 配置线
            CarCustom carCustom = carCustomService.updateCarCustomConfig(memberId, userId,mobile,carShoppingCartVo.getCarCustomDto(),String.valueOf(carShoppingCart.getCcid()));

            //修改购物车更新时间
            UpdateWrapper<CarShoppingCart> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("shopping_cart_id",carShoppingCart.getShoppingCartId());
            CarShoppingCart updateObj = new CarShoppingCart();
            updateObj.setUpdateTime(LocalDateTime.now());
            this.update(updateObj,updateWrapper);
            carShoppingCart.setUpdateTime(updateObj.getUpdateTime());
        }

        //获取 skuid
        CarCustomVo carCustomVo = carCustomService.a7OrQ5Byccid(ccid);
        if(carCustomVo == null || StringUtil.isEmpty(carCustomVo.getCustomSeriesCode())) {
            log.info("添加至购物车配置，获取 skuid 失败，无 customSeriesCode，ccid:"+ ccid);
        }
        JSONObject resultJson = copProdQueryFeign.selectProdCar(carCustomVo.getCustomSeriesCode(),1);
        JSONObject dataJson = resultJson.getJSONObject("data");
        log.info("购物车自动保存，获取 skuid "+resultJson);
        if(dataJson == null) {
            log.info("购物车自动保存，获取 skuid，无数据");
        } else {
            skuid = dataJson.getString("prodSkuId");
            //修改购物车 skuid
            UpdateWrapper<CarShoppingCart> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("shopping_cart_id",carShoppingCart.getShoppingCartId());
            CarShoppingCart updateObj = new CarShoppingCart();
            updateObj.setSkuid(skuid);
            this.update(updateObj,updateWrapper);
        }

        return carShoppingCart;
    }

    public CarCustom createdAutoSaveCarShoppingCart(String memberId, String userId, String mobile, String channel) throws Exception {

        //创建默认配置 - 配置线
        Set<String> optionIds = new HashSet<>();
        CarCustomDto carCustomDto = new CarCustomDto();

        QueryWrapper<CarModelLine> queryCarModelLine = new QueryWrapper();
        queryCarModelLine.eq("channel", Constant.MASTER_CHANNEL).last(" limit 1");
        List<CarModelLine> carModelLineList = carModelLineService.list(queryCarModelLine);
        if(carModelLineList != null && carModelLineList.size() > 0) {
            carCustomDto.setModelLineId(carModelLineList.get(0).getModelLineId());
        } else {
            throw new ServiceException("400401", "无 carModelLine 数据", null);
        }

        QueryWrapper<CarModelLineSibInterieur> querySibInterieur = new QueryWrapper();
        querySibInterieur.eq("model_line_id", carCustomDto.getModelLineId()).eq("channel", Constant.MASTER_CHANNEL).last(" limit 1");
        List<CarModelLineSibInterieur> sibInterieurList = carModelLineSibInterieurService.list(querySibInterieur);
        if(sibInterieurList != null && sibInterieurList.size() > 0) {
            carCustomDto.setSibInterieurId(sibInterieurList.get(0).getSibInterieurId());
        }

        ModelLineOptionVo optionParam = new ModelLineOptionVo();
        optionParam.setModelLineId(carCustomDto.getModelLineId());
        optionParam.setDelFlag(0);
        optionParam.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineOptionVo> modelLineOptions = carModelLineOptionService.listModelLineOption(optionParam);
        if(modelLineOptions != null) {
            for(ModelLineOptionVo optionObj : modelLineOptions) {
                if("PACKET".equals(optionObj.getCategory())) {
                    optionIds.add(optionObj.getOptionId());
                    break;
                }
            }
            for(ModelLineOptionVo optionObj : modelLineOptions) {
                if("COLOR_EXTERIEUR".equals(optionObj.getCategory())) {
                    optionIds.add(optionObj.getOptionId());
                    break;
                }
            }
            for(ModelLineOptionVo optionObj : modelLineOptions) {
                if("COLOR_INTERIEUR".equals(optionObj.getCategory())) {
                    optionIds.add(optionObj.getOptionId());
                    break;
                }
            }
        }

        carCustomDto.setOptionIds(optionIds);

        CarCustom carCustom = carCustomService.addCarCustomConfig(memberId,userId,mobile, channel, carCustomDto);
        return carCustom;
    }

    @Override
    public void finishInvitationIntegral(CarShoppingCartDto dto) {
        //检查是否包含 邀请码
        QueryWrapper<CarShoppingCart> queryCarShoppingCart = new QueryWrapper();
        queryCarShoppingCart.eq("user_id",dto.getUserId()).eq("ccid",dto.getCcid()).isNotNull("invitation_code");
        List<CarShoppingCart> list = this.list(queryCarShoppingCart);
        if(list != null && list.size() > 0 && StringUtil.isNotEmpty(list.get(0).getInvitationCode())) {
            log.info("购物车配置单含邀请码，发放积分，ccid:"+dto.getCcid());
            //发放积分
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("userId", list.get(0).getCreateUser());
            jsonObject.put("key1", list.get(0).getInvitationCode());
            jsonObject.put("key2", list.get(0).getCcid());
            //取车系
            CarCustomVo carCustomVo = carCustomService.a7OrQ5Byccid(dto.getCcid());
            if(carCustomVo == null || StringUtil.isEmpty(carCustomVo.getCustomSeriesCode())) {
                log.info("发放积分，无 customSeriesCode，ccid:"+ dto.getCcid());
            } else {
                jsonObject.put("key3", carCustomVo.getCustomSeriesCode());
            }
            audiTaskFeign.finishReserveInvitation(jsonObject.toJSONString());
        }

    }

    @Override
    public CarShoppingCartVo getCarShoppingCartDetail(String shoppingCartId) {
        CarShoppingCart carShoppingCart;
        QueryWrapper<CarShoppingCart> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("shopping_cart_id", shoppingCartId).eq("del_flag",0);
        List<CarShoppingCart> list = this.list(queryWrapper);
        if(list.size() > 0) {
            carShoppingCart = list.get(0);
        } else {
            return new CarShoppingCartVo();
        }
        CarShoppingCartVo carShoppingCartVo = new CarShoppingCartVo();
        carShoppingCartVo.setShoppingCartId(String.valueOf(carShoppingCart.getShoppingCartId()));
        carShoppingCartVo.setUserId(carShoppingCart.getUserId());
        carShoppingCartVo.setCcid(String.valueOf(carShoppingCart.getCcid()));
        carShoppingCartVo.setCreateTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(carShoppingCart.getCreateTime()));
        carShoppingCartVo.setUpdateTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(carShoppingCart.getUpdateTime()));
        carShoppingCartVo.setTotalPrice(new BigDecimal(0));
        carShoppingCartVo.setSkuid(carShoppingCart.getSkuid());
        carShoppingCartVo.setInvitationCode(carShoppingCart.getInvitationCode());
        return carShoppingCartVo;
    }

    @Override
    public CarShoppingCartVo getLatestCarShoppingCartList(String userId) {
        String channel = Constant.MASTER_CHANNEL;
        List<CarShoppingCartVo> list = new ArrayList();
        CarShoppingCartVo carShoppingCartVo;
        CarCustomDetail customDetail;
        //查询购物车列表
        LambdaQueryWrapper<CarShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CarShoppingCart::getUserId, userId).eq(CarShoppingCart::getDelFlag,0)
                .gt(CarShoppingCart::getUpdateTime, LocalDateTime.of(2022, 12, 22, 22, 0, 0))
                .last(" order by auto_save desc,update_time desc limit 0, 1");
        CarShoppingCart carShoppingCart = this.getOne(queryWrapper);

        if (carShoppingCart == null){
            return null;
        }

        if(carShoppingCart.getCcid() == null) {
            log.info("购物车列表 无ccid shoppingCartId:"+carShoppingCart.getShoppingCartId());
            return null;
        }

        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(carShoppingCart.getCcid());
        if (carCustom == null){
            log.info("购物车列表 无配置单数据 shoppingCartId:"+carShoppingCart.getShoppingCartId());
            return null;
        }

        //参考接口 /api/v1/cc/detail
        try {
            customDetail = carConfigSnapshot.carConfigDetail(channel, carCustom);
        } catch (Exception e) {
            log.info("购物车列表 配置单获取异常 shoppingCartId:"+carShoppingCart.getShoppingCartId(),e);
            return null;
        }

        carShoppingCartVo = new CarShoppingCartVo();
        carShoppingCartVo.setShoppingCartId(String.valueOf(carShoppingCart.getShoppingCartId()));
        carShoppingCartVo.setUserId(carShoppingCart.getUserId());
        carShoppingCartVo.setCcid(String.valueOf(carShoppingCart.getCcid()));
        carShoppingCartVo.setCreateTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(carShoppingCart.getCreateTime()));
        carShoppingCartVo.setUpdateTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(carShoppingCart.getUpdateTime()));
        carShoppingCartVo.setTotalPrice(new BigDecimal(0));
        carShoppingCartVo.setSkuid(carShoppingCart.getSkuid());
        carShoppingCartVo.setInvitationCode(carShoppingCart.getInvitationCode());
        carShoppingCartVo.setConfigTotalPrice(customDetail.getConfigDetail().getTotalPrice());
        carShoppingCartVo.setCarSeries(customDetail.getConfigDetail().getCarSeries());
        carShoppingCartVo.setCarModel(customDetail.getConfigDetail().getCarModel());
        carShoppingCartVo.setOutsideColor(customDetail.getConfigDetail().getOutsideColor());
        carShoppingCartVo.setInsideColor(customDetail.getConfigDetail().getInsideColor());
        carShoppingCartVo.setOptionList(customDetail.getConfigDetail().getOptionList());
        carShoppingCartVo.setAutoSave(carShoppingCart.getAutoSave());
        carShoppingCartVo.setValid(carCustom.getValid());
        carShoppingCartVo.setInvalidReason(carCustom.getInvalidReason());
        carShoppingCartVo.setUpdateFlag(carCustom.getUpdateFlag());
        carShoppingCartVo.setUpdateContent(carCustom.getUpdateContent());

        //获取意向金
        if(StringUtil.isInteger(carShoppingCart.getSkuid())) {
            try {
                JSONObject resultJson = copProdQueryFeign.findProdSkuById(Long.parseLong(carShoppingCart.getSkuid()));
                JSONObject dataJson = resultJson.getJSONObject("data");
                if(dataJson == null) {
                    log.info("购物车，意向金获取失败，无数据，Skuid :"+carShoppingCart.getSkuid());
                } else {
                    carShoppingCartVo.setTotalPrice(new BigDecimal(dataJson.getString("reserveOrderAmount")));//意向金
                }
            } catch(Exception e) {
                log.info("购物车，获取意向金失败，Skuid :"+carShoppingCart.getSkuid(),e);
            }
        }

        return carShoppingCartVo;
    }

}
