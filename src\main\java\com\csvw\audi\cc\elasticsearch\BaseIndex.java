package com.csvw.audi.cc.elasticsearch;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.admin.indices.create.CreateIndexResponse;
import org.elasticsearch.action.admin.indices.get.GetIndexRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.rest.RestStatus;

import java.io.IOException;
import java.time.LocalDateTime;

@Slf4j
public abstract class BaseIndex<T> {

    public BaseIndex(RestHighLevelClient client){
        this.client = client;
    }

    protected String indexPrefix;

    protected RestHighLevelClient client;

    protected String mappings;

    public void createIndex(LocalDateTime dataTime) throws IOException {
        GetIndexRequest request = new GetIndexRequest();
        String indexName = getIndexName(dataTime);
        request.indices(indexName);
        boolean flag = client.indices().exists(request, RequestOptions.DEFAULT);
        if (!flag){
            CreateIndexRequest createRequest = new CreateIndexRequest(indexName);
            JSONObject source = new JSONObject();
            JSONObject settings = new JSONObject();
            settings.put("number_of_shards", 1);
            settings.put("number_of_replicas", 0);
            source.put("settings",settings);
            source.put("mappings", JSONObject.parseObject(mappings));
            createRequest.source(source.toJSONString(), XContentType.JSON);
            CreateIndexResponse response = client.indices().create(createRequest, RequestOptions.DEFAULT);
            log.info("mapping: {}", mappings);
        }
    }

    private String getIndexName(LocalDateTime dataTime){
        return indexPrefix + "-" + dataTime.getYear();
    }

    public String getQueryIndex(){
        return indexPrefix + "-*";
    }

    public void addIndex(String type, String id, Object data, LocalDateTime dateTime) throws IOException {
        createIndex(dateTime);
        IndexRequest request = new IndexRequest(getIndexName(dateTime), type,id);
        request.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        request.source(JSONObject.toJSONString(data), XContentType.JSON);
        IndexResponse response = client.index(request, RequestOptions.DEFAULT);
        int times = 0;
        while (!response.status().equals(RestStatus.CREATED) && times >10){
            log.error("索引错误： {}, 参数：{}", response.toString(), JSONObject.toJSONString(data));
            response = client.index(request, RequestOptions.DEFAULT);
            times++;
        }

    }

    public IndexRequest indexRequest(String type, String id, Object data, LocalDateTime dateTime) throws IOException {
        createIndex(dateTime);
        IndexRequest request = new IndexRequest(getIndexName(dateTime), type,id);
        request.source(JSONObject.toJSONString(data), XContentType.JSON);
        System.out.println(JSONObject.toJSONString(data));
        return request;
    }

}
