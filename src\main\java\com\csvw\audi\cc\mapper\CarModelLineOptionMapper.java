package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.dto.OptionParamDto;
import com.csvw.audi.cc.entity.dto.OptionPriceQuery;
import com.csvw.audi.cc.entity.po.CarModelLineOption;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csvw.audi.cc.entity.po.CarOptionRelate;
import com.csvw.audi.cc.entity.vo.ModelLineOptionCompareVo;
import com.csvw.audi.cc.entity.vo.ModelLineOptionTagParam;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 配置线配置项 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface CarModelLineOptionMapper extends BaseMapper<CarModelLineOption> {

    List<ModelLineOptionVo> listModelLineOption(ModelLineOptionVo optionVo);

    List<ModelLineOptionVo> listModelLinePacketItem(@Param("channel") String channel, @Param("modelLineId") String modelLineId, @Param("delFlag") Integer delFlag);

    List<ModelLineOptionVo> listModelLinePersonalOption(@Param("modelLineId") String modelLineId, @Param("channel") String channel, @Param("delFlag") Integer delFlag, @Param("notInCategory") List<String> notInCategory, @Param("type") String type);

    List<ModelLineOptionVo> listOptionPacketItem(OptionParamDto optionParamDto);

    List<ModelLineOptionVo> modelLineOptionQuery(OptionParamDto param);

    List<ModelLineOptionVo> listModelLineOptionByTag(ModelLineOptionTagParam param);

    List<ModelLineOptionVo> listModelLineOptionWithoutRel(ModelLineOptionVo param);

    List<ModelLineOptionVo> listOptionPacketItemByDrm(OptionPriceQuery param);

    List<ModelLineOptionCompareVo> listModelLineOptionCompare(ModelLineOptionVo param);

    List<ModelLineOptionVo> optionQuery(OptionParamDto param);

    List<ModelLineOptionVo> findPaketItemFromCodes(@Param("customSeriesId") String customSeriesId, @Param("modelLineId") String modelLineId,
                                                   @Param("itemCode") String code, @Param("optionCodes") Collection<String> optionCodes);

    List<ModelLineOptionVo> modelLineOptionQueryStrict(OptionParamDto param);

    List<ModelLineOptionVo> listModelLinePacketItemByCode(@Param("modelLineId") String modelLineId, @Param("optionCode") String optionCode, @Param("optionIds") Set<String> optionIds);
}
