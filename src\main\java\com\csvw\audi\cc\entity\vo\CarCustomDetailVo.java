package com.csvw.audi.cc.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CarCustomDetailVo {

    private Long ccid;

    @ApiModelProperty(value = "accb配置编码")
    private String audiCode;

    @ApiModelProperty(value = "ACCB车系ID")
    private String accbModelId;

    @ApiModelProperty(value = "ACCB车系编码")
    private String accbModelCode;

    @ApiModelProperty(value = "车系描述")
    private String modelDesc;

    @ApiModelProperty(value = "ACCB车型ID")
    private String accbTypeId;

    @ApiModelProperty(value = "ACCB车型编码")
    private String accbTypeCode;

    @ApiModelProperty(value = "ACCB车型描述")
    private String accbTypeDesc;

    @ApiModelProperty(value = "年款")
    private String modelYear;

    @ApiModelProperty(value = "内饰颜色面料id")
    private String sibInterieurId;

    @ApiModelProperty(value = "账号id")
    private String userId;

    @ApiModelProperty(value = "配置线id")
    private String modelLineId;

    @ApiModelProperty(value = "畅销推荐车id")
    private Long bestRecommendId;

    private String customSeriesId;

    private String customSeriesCode;

    private String customSeriesName;
}
