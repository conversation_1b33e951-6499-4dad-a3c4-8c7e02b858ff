package com.csvw.audi.cc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.common.utils.StringUtil;
import com.csvw.audi.cc.entity.dto.AdminOrgBankDto;
import com.csvw.audi.cc.entity.po.LoanRule;
import com.csvw.audi.cc.entity.vo.AdminOrgBankVo;
import com.csvw.audi.cc.entity.vo.LoanRuleVo;
import com.csvw.audi.cc.mapper.LoanRuleMapper;
import com.csvw.audi.cc.service.IAdminOrgBankService;
import com.csvw.audi.cc.service.ILoanRuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-18
 */
@Service
public class LoanRuleServiceImpl extends ServiceImpl<LoanRuleMapper, LoanRule> implements ILoanRuleService {

    @Autowired
    private IAdminOrgBankService adminOrgBankService;

    @Autowired
    private LoanRuleMapper loanRuleMapper;

    @Override
    public List<LoanRuleVo> getLoanRules(String dealerCode, String modelLineId, Integer type) {
        //获取门店的金融机构
        if(StringUtil.isNotEmpty(dealerCode)) {
            AdminOrgBankDto dto = new AdminOrgBankDto();
            dto.setDealerCode(dealerCode);
        }
        List<LoanRule> rules = loanRuleMapper.queryModelLineLoanRule(modelLineId, null, type);
        List<LoanRuleVo> vos = rules.stream().map(i->{
            LoanRuleVo vo = new LoanRuleVo();
            BeanUtils.copyProperties(i, vo);
            vo.setLoanRuleId(i.getLoanRuleId().toString());
            vo.setInterestRates(JSONObject.parseObject(i.getInterestRates()));
            return vo;
        }).collect(Collectors.toList());
        return vos;
    }
}
