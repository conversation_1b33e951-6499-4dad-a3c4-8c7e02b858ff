package com.csvw.audi.cc.component.kafkaService.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csvw.audi.cc.component.kafkaService.SvcdKafkaService;
import com.csvw.audi.cc.entity.po.SvcdAfterSales;
import com.csvw.audi.cc.mapper.SvcdAfterSalesMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service("svcdAfterSalesKafkaService")
public class SvcdAfterSalesKafkaServiceImpl implements SvcdKafkaService {

    @Autowired
    private SvcdAfterSalesMapper svcdAfterSalesMapper;

    @Override
    public void insertData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdAfterSales afterSales = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdAfterSales.class);
        afterSales.setCreatedAt(nowDate);
        afterSales.setUpdatedAt(nowDate);
        afterSales.insert();
    }

    @Override
    public void updateData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdAfterSales afterSales = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdAfterSales.class);

        QueryWrapper<SvcdAfterSales> afterSalesQusery = new QueryWrapper<>();
        afterSalesQusery.eq("dealer_code",afterSales.getDealerCode());
        List<SvcdAfterSales> list = afterSales.selectList(afterSalesQusery);
        if(list == null || list.size() == 0) {
            afterSales.setCreatedAt(nowDate);
            afterSales.setUpdatedAt(nowDate);
            afterSales.insert();
        } else {
            afterSales.setAfterSalesId(list.get(0).getAfterSalesId());
            afterSales.setUpdatedAt(nowDate);
            afterSales.updateById();
        }
    }
}
