package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 配置线角度图
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarConfigImage对象", description="配置线角度图")
public class CarConfigImage extends Model<CarConfigImage> {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "配置线id")
    private String modelLineId;

    @ApiModelProperty(value = "外饰编码")
    private String exterieurCode;

    @ApiModelProperty(value = "轮毂编码")
    private String radCode;

    @ApiModelProperty(value = "图片类型(front:正视，left：左侧)")
    private String type;

    @ApiModelProperty(value = "图片地址")
    private String imageUrl;

    @ApiModelProperty(value = "权重")
    private Integer weight;

    @ApiModelProperty(value = "数据渠道")
    private String channel;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
