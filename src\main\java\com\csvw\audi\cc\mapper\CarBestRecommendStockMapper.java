package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.dto.BestRecommendQueryParam;
import com.csvw.audi.cc.entity.po.CarBestRecommendStock;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * OMD推荐车库存表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
public interface CarBestRecommendStockMapper extends BaseMapper<CarBestRecommendStock> {

    void plusStockNum(@Param("id") Long id, @Param("stockNum") Integer stockNum);

    Integer getRecommendFixStock(BestRecommendQueryParam queryParam);

    Integer getRecommendStock(BestRecommendQueryParam queryParam);
}
