package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 车辆ABC类配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarModelLineType对象", description="车辆ABC类配置")
public class CarModelLineType extends Model<CarModelLineType> {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    private Long omdVehicleTypeId;

    private String accbTypeCode;

    @ApiModelProperty(value = "年款")
    private String modelYear;

    @ApiModelProperty(value = "版本号")
    private String modelVersion;

    @ApiModelProperty(value = "外饰编码")
    private String exterieurColorCode;

    @ApiModelProperty(value = "轮毂编码")
    private String radCode;

    @ApiModelProperty(value = "内饰编码")
    private String interieurColorCode;

    @ApiModelProperty(value = "面料编码")
    private String sibCode;

    @ApiModelProperty(value = "饰条编码")
    private String eihCode;

    @ApiModelProperty(value = "座椅包")
    private String seats;

    @ApiModelProperty(value = "配置线选装")
    private String prList;

    private Integer prNum;

    @ApiModelProperty(value = "分类，不能为空，A、B、C")
    private String classify;

    @ApiModelProperty(value = "配置状态，0:初始化，1:上线")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
