package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.po.CarMeasureMadeOriginConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 半订制化车辆原始配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
public interface CarMeasureMadeOriginConfigMapper extends BaseMapper<CarMeasureMadeOriginConfig> {

    CarMeasureMadeOriginConfig measureOriginConfigByMeasureId(@Param("measureId") Long measureId);
}
