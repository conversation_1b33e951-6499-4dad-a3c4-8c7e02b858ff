<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarOptionDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarOptionDetail">
        <id column="id" property="id" />
        <result column="option_detail_id" property="optionDetailId"/>
        <result column="option_id" property="optionId" />
        <result column="option_code" property="optionCode" />
        <result column="option_desc" property="optionDesc" />
        <result column="image_url" property="imageUrl" />
        <result column="channel" property="channel" />
        <result column="weight" property="weight" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, option_detail_id, option_id, option_code, option_desc, image_url, channel, weight, create_time, update_time, del_flag
    </sql>

    <select id="listOptionDetailVo" resultType="com.csvw.audi.cc.entity.vo.CarOptionDetailVo"
            parameterType="com.csvw.audi.cc.entity.vo.CarOptionDetailVo">
        SELECT `option_detail_id`, `option_desc`, `image_url`, `channel` from `car_option_detail` od
        <where>
            <if test="channel == null or channel == '' or channel == 'master'">
                od.channel = 'master'
            </if>
            <if test="channel != null and channel != '' and channel != 'master'">
                and od.channel in (#{channel}, 'master')
            </if>
            <if test="optionId != null and optionId != ''">
                and od.option_id = #{optionId}
            </if>
            <if test="delFlag != null">
                and od.del_flag = #{delFlag}
            </if>
        </where>
        order by od.weight;
    </select>

</mapper>
