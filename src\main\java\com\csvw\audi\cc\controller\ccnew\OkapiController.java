package com.csvw.audi.cc.controller.ccnew;


import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.dto.okapi.*;
import com.csvw.audi.cc.entity.vo.CouponInfoVo;
import com.csvw.audi.cc.feign.CopOrderQueryFeign;
import com.csvw.audi.cc.feign.OkapiFeign;
import com.csvw.audi.cc.service.impl.CarRecommendServiceImpl;
import com.csvw.audi.cc.service.impl.OkapiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/api/v1/cc")
@Api(tags = "okapi omd 配车接口")
@Slf4j
public class OkapiController extends BaseController {

    @Autowired
    OkapiFeign okapiFeign;

    @Autowired
    CopOrderQueryFeign copOrderQueryFeign;

    @Autowired
    OkapiService okapiService;

    @Autowired
    CarRecommendServiceImpl carRecommendService;

    @ApiOperation("销售配车-车系列表")
    @GetMapping(value = "/public/okapi/series")
    public AjaxMessage<OkapiRes<List<SeriesDto>>> series(){
        OkapiRes<List<SeriesDto>> res = okapiFeign.series("A");
        return successMessage(res);
    }

    @ApiOperation("销售配车-车型名称列表")
    @GetMapping("/public/okapi/models")
    public AjaxMessage<OkapiRes<List<ModelsDto>>> models(String seriesCode){
        OkapiRes<List<ModelsDto>> res = okapiFeign.models("A", seriesCode);
        return successMessage(res);
    }

    @ApiOperation("销售配车-车型价格")
    @GetMapping("/public/okapi/modelsPrice")
    public AjaxMessage<OkapiRes<List<ModelsPriceDto>>> modelsPrice(String seriesCode, String modelUniqueCode){
        OkapiRes<List<ModelsPriceDto>> res = okapiFeign.modelsPrice("A", seriesCode, modelUniqueCode);
        return successMessage(res);
    }

    @ApiOperation("销售配车-车型标签配置")
    @PostMapping("/public/okapi/labelConfig")
    public AjaxMessage<OkapiRes<List<LabelConfigResDto>>> labelConfig(@RequestBody LabelConfigParamDto paramDto){
        OkapiRes<List<LabelConfigResDto>> res = okapiFeign.modelLabelConfig(paramDto);
        return successMessage(res);
    }

    @ApiOperation("销售配车-车系车型素材")
    @GetMapping("/public/okapi/modelMaterial")
    public AjaxMessage<OkapiRes<List<ModelMaterialDto>>> modelMaterial(String seriesCode, String subModelRuleId){
        OkapiRes<List<ModelMaterialDto>> res = okapiFeign.seriesModelMaterial("A", seriesCode, subModelRuleId);
        return successMessage(res);
    }

    @ApiOperation("销售车系车型-车系列表")
    @GetMapping("/public/okapi/salesSeries")
    public AjaxMessage<OkapiRes<List<SalesSeriesDto>>> salesSeries(){
        OkapiRes<List<SalesSeriesDto>> res = okapiFeign.salesSeries("A");
        return successMessage(res);
    }

    @ApiOperation("销售车系车型-车型列表")
    @GetMapping("/public/okapi/salesModels")
    public AjaxMessage<OkapiRes<List<SalesModelsDto>>> salesModels(String seriesCode){
        OkapiRes<List<SalesModelsDto>> res = okapiFeign.salesModels("A", seriesCode);
        return successMessage(res);
    }

    @ApiOperation("销售车系车型-车型明细")
    @GetMapping("/public/okapi/salesModelDetail")
    public AjaxMessage<OkapiRes<List<SalesModelsDetail>>> salesModelDetail(String seriesCode, String modelUniqueCode){
        OkapiRes<List<SalesModelsDetail>> res = okapiFeign.salesModelsDetail("A", seriesCode, modelUniqueCode);
        return successMessage(res);
    }

    @ApiOperation("销售配车-车型内饰对应面料")
    @GetMapping("/public/okapi/interiorFabric/label/config")
    public AjaxMessage<OkapiRes<LabelInterieurResDto>> interiorFabric(@RequestParam String modelUniqueCode,@RequestParam(required = false) String mstMgrpId, @RequestParam String interiorCode){
        OkapiRes<LabelInterieurResDto> res = okapiFeign.modelSibInterieur("A", modelUniqueCode, mstMgrpId, interiorCode);
        return successMessage(res);
    }

    @ApiOperation("销售配车-车型包中件标签配置")
    @GetMapping("/public/okapi/pfCode/label/config")
    public AjaxMessage<OkapiRes<LabelConfigItemResDto>> packetItem(@RequestParam String modelUniqueCode,@RequestParam String featureCode){
        OkapiRes<LabelConfigItemResDto> res = okapiFeign.modelLabelConfigItem("A", modelUniqueCode, featureCode);
        return successMessage(res);
    }

    @ApiOperation("销售配车-装备组合查询接口")
    @GetMapping("/public/okapi/equipmentGroup/query")
    public AjaxMessage<OkapiRes<EquipmentGroupResDto>> equipmentGroup(@RequestParam String modelUniqueCode,String labelCode, String labelValue){
        OkapiRes<EquipmentGroupResDto> res = okapiFeign.equipmentGroup("A", modelUniqueCode, labelCode, labelValue);
        return successMessage(res);
    }

    @ApiOperation("销售配车-车型亮点标签配置")
    @GetMapping("/public/okapi/highlight/config")
    public AjaxMessage<OkapiRes<HighLightConfigResDto>> equipmentGroup(@RequestParam String modelUniqueCode,String mstMgrpId){
        OkapiRes<HighLightConfigResDto> res = okapiFeign.highlight("A", modelUniqueCode, mstMgrpId);
        return successMessage(res);
    }

    @ApiOperation("券码转发-COP查券")
    @GetMapping("/cop/pre-sale/couponInfo")
    public AjaxMessage<List<CouponInfoVo>> couponInfo(@RequestParam String seriesCode, @RequestHeader(value="X-Member-Id") String memberId, @RequestParam(required = false) String ccid){
        JSONObject res = copOrderQueryFeign.couponInfo(ccid, seriesCode, memberId);
        if (!"00".equals(res.getString("code"))){
            return failureMessage(res.getString("code"), res.getString("message"), null);
        }
        if (res.getJSONArray("data")!=null){
            return successMessage(res.getJSONArray("data").toJavaList(CouponInfoVo.class));
        }
        return successMessage(null);
    }

}

