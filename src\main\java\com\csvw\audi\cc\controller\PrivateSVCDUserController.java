package com.csvw.audi.cc.controller;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.po.SvcdUser;
import com.csvw.audi.cc.service.ISvcdUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/20 14:19
 * @description 经销商用户-内部接口
 */
@Slf4j
@RequestMapping("/private/svcd/user")
@RestController
public class PrivateSVCDUserController extends BaseController {

    @Autowired
    private ISvcdUserService svcdUserService;

    /**
     * @description 封禁经销商用户邀请好友状态
     */
    @PutMapping("/prohibit/inviteFriends")
    public AjaxMessage prohibitInviteFriends(String phoneNumber) {
        LambdaQueryChainWrapper<SvcdUser> queryChainWrapper = svcdUserService.lambdaQuery().eq(SvcdUser::getStatus, 1)
            .in(SvcdUser::getWorkStatus, Arrays.asList(0, 1, 2)).isNotNull(SvcdUser::getMobile);
        if (StringUtils.isNotEmpty(phoneNumber)) {
            queryChainWrapper.eq(SvcdUser::getMobile, phoneNumber);
        }
        List<SvcdUser> list = queryChainWrapper.list();
        list.stream().forEach(svcdUser -> svcdUserService.asyncUpdateNoInvite(svcdUser));
        return successMessage("执行完成");
    }

}
