package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 公司信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SvcdCompany对象", description="公司信息")
public class SvcdCompany extends Model<SvcdCompany> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "数据id")
      @TableId(value = "company_id", type = IdType.AUTO)
    private Long companyId;

    @ApiModelProperty(value = "区域名称")
    private String name;

    @ApiModelProperty(value = "区域代码")
    private String companyCode;

    @ApiModelProperty(value = "成立时间")
    private String startTime;

    @ApiModelProperty(value = "注册资本")
    private Long registeredCapital;

    @ApiModelProperty(value = "公司地址")
    private String address;

    @ApiModelProperty(value = "董事长")
    private String chairman;

    @ApiModelProperty(value = "董事长手机号")
    private String chairmanPhone;

    @ApiModelProperty(value = "法人代表")
    private String legalRepresentative;

    @ApiModelProperty(value = "法人代表手机号")
    private String legalRepresentativePhone;

    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    @ApiModelProperty(value = "联系人手机号")
    private String contactPersonMobile;

    @ApiModelProperty(value = "联系人邮箱")
    private String contactPersonEmail;

    @ApiModelProperty(value = "公司性质")
    private String companyNature;

    @ApiModelProperty(value = "控股人涉及行业")
    private String relatedIndustry;

    @ApiModelProperty(value = "营业执照地址")
    private String businessLicenseUrl;

    @ApiModelProperty(value = "承诺协议")
    private String promise;

    @ApiModelProperty(value = "股权结构图")
    private String stockRightPicUrl;

    @ApiModelProperty(value = "企业登记查询报告")
    private String registrationReportUrl;

    @ApiModelProperty(value = "公司章程")
    private String companyRegulationUrl;

    @ApiModelProperty(value = "其他证明文件")
    private String otherSupportDocUrl;

    @ApiModelProperty(value = "财务指标")
    private Long financeYear;

    @ApiModelProperty(value = "净资产收益率")
    private Long financeRoe;

    @ApiModelProperty(value = "综合毛利率")
    private Long financeMarginRate;

    @ApiModelProperty(value = "税前利润率")
    private Long financePretaxMargin;

    @ApiModelProperty(value = "资产负债率")
    private Long financeDebt;

    @ApiModelProperty(value = "首家奥迪开业年份")
    private Long audiStartYear;

    @ApiModelProperty(value = "奥迪品牌税前利润率")
    private Long audiEbtRatio;

    @ApiModelProperty(value = "是否银行授信")
    private Long bankCredit;

    @ApiModelProperty(value = "SAP代码")
    private String sapCode;

    @ApiModelProperty(value = "税务登记号")
    private String taxNo;

    @ApiModelProperty(value = "税务电话")
    private String taxTel;

    @ApiModelProperty(value = "开户银行")
    private String bankName;

    @ApiModelProperty(value = "开户银行账号")
    private String bankAccountNumber;

    @ApiModelProperty(value = "是否删除（0:未删除 1:已删除）")
    private Long deleted;

    @ApiModelProperty(value = "投资人代码")
    private String investorCode;

    @ApiModelProperty(value = "预留字段")
    private String spare1;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;


    @Override
    protected Serializable pkVal() {
        return this.companyId;
    }

}
