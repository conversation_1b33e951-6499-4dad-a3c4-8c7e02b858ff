package com.csvw.audi.cc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csvw.audi.cc.entity.dto.DealerDto;
import com.csvw.audi.cc.entity.dto.SvcdCityDto;
import com.csvw.audi.cc.entity.dto.SvcdProvinceCityListDto;
import com.csvw.audi.cc.entity.po.SvcdCity;
import com.csvw.audi.cc.entity.po.SvcdProvince;
import com.csvw.audi.cc.entity.vo.DealerVo;
import com.csvw.audi.cc.entity.vo.SvcdCityVo;
import com.csvw.audi.cc.mapper.SvcdCityMapper;
import com.csvw.audi.cc.service.ISvcdChannelOrganizationService;
import com.csvw.audi.cc.service.ISvcdCityService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csvw.audi.cc.service.ISvcdProvinceService;
import com.csvw.sx.utils.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 城市信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Service
public class SvcdCityServiceImpl extends ServiceImpl<SvcdCityMapper, SvcdCity> implements ISvcdCityService {

    @Autowired
    private SvcdCityMapper svcdCityMapper;
    @Autowired
    private ISvcdProvinceService svcdProvinceService;

    @Autowired
    private ISvcdChannelOrganizationService svcdChannelOrganizationService;

    @Override
    public List<SvcdCityVo> getCityList(SvcdCityDto cityDto) {
        //默认查询存在代理商的
        if(cityDto.getSearchAll() == null) {
            cityDto.setSearchAll(3);
        }

        QueryWrapper<SvcdCity> queryWrapper = new QueryWrapper<>();
        //按 cityCode 检索
        if(StringUtil.isNotEmpty(cityDto.getCityCode())) {
            queryWrapper.eq("city_code",cityDto.getCityCode());
        }
        //按省份检索
        if(StringUtil.isNotEmpty(cityDto.getProvinceCode())) {
            queryWrapper.eq("province_code",cityDto.getProvinceCode());
        }

        if(cityDto.getSearchAll() == 1) {
            //查询全部城市数据
        } else if(cityDto.getSearchAll() == 2) {
            //查询存在代理商或服务商的城市数据
            DealerDto dealerDto = new DealerDto();
            dealerDto.setHaveImage(0);
            List<DealerVo> dealerVoList = svcdChannelOrganizationService.getOrgList(dealerDto);
            Set<String> provinceCodes = dealerVoList.stream().map(i->i.getProvinceCode()).collect(Collectors.toSet());
            queryWrapper.in("province_code",new ArrayList<>(provinceCodes));
        } else if(cityDto.getSearchAll() == 3) {
            //取存在代理商的城市
            DealerDto dealerDto = new DealerDto();
            dealerDto.setHaveImage(0);
            List<DealerVo> dealerVoList = svcdChannelOrganizationService.getAgentList(dealerDto);
            Set<String> cityCodes = dealerVoList.stream().map(i->i.getCityCode()).collect(Collectors.toSet());
            queryWrapper.in("city_code",new ArrayList<>(cityCodes));
        }
        queryWrapper.isNotNull("quick_code").last(" ORDER BY province_code ASC, quick_code ASC");
        List<SvcdCity> list = svcdCityMapper.selectList(queryWrapper);
        List<SvcdCityVo> voList = new ArrayList<>(list.size());
        SvcdCityVo svcdCityVo;
        for(SvcdCity city : list) {
            svcdCityVo = new SvcdCityVo();
            svcdCityVo.setCityCode(String.valueOf(city.getCityCode()));
            svcdCityVo.setProvinceCode(String.valueOf(city.getProvinceCode()));
            svcdCityVo.setName(city.getName());
            svcdCityVo.setSimpleName(city.getSimpleName());
            svcdCityVo.setQuickCode(city.getQuickCode());
            svcdCityVo.setCityEn(city.getCityEn());
            svcdCityVo.setCapInitials(city.getQuickCode().length()>1?city.getQuickCode().substring(0,1):city.getQuickCode());
            voList.add(svcdCityVo);
        }
        return voList;
    }

    @Override
    public List<SvcdCityVo> getCityListByOfficialWebsite(SvcdCityDto cityDto) {
        QueryWrapper<SvcdCity> queryWrapper = new QueryWrapper<>();
        //按 cityCode 检索
        if(StringUtil.isNotEmpty(cityDto.getCityCode())) {
            queryWrapper.eq("city_code",cityDto.getCityCode());
        }
        //按省份检索
        if(StringUtil.isNotEmpty(cityDto.getProvinceCode())) {
            queryWrapper.eq("province_code",cityDto.getProvinceCode());
        }

        if(cityDto.getSearchAll() != null && cityDto.getSearchAll() == 1 ) {
            //查询全部城市数据
        } else {
            //取存在渠道商的城市
            DealerDto dealerDto = new DealerDto();
            dealerDto.setHaveImage(0);
            dealerDto.setSearchType(cityDto.getSearchType()==null?0:cityDto.getSearchType());
            List<DealerVo> dealerVoList = svcdChannelOrganizationService.getDealerListByOfficialWebsite(dealerDto);
            Set<String> cityCodes = dealerVoList.stream().map(i->i.getCityCode()).collect(Collectors.toSet());
            queryWrapper.in("city_code",new ArrayList<>(cityCodes));
        }
        queryWrapper.isNotNull("quick_code").last(" ORDER BY province_code ASC, quick_code ASC");
        List<SvcdCity> list = svcdCityMapper.selectList(queryWrapper);
        List<SvcdCityVo> voList = new ArrayList<>(list.size());
        SvcdCityVo svcdCityVo;
        for(SvcdCity city : list) {
            svcdCityVo = new SvcdCityVo();
            svcdCityVo.setCityCode(String.valueOf(city.getCityCode()));
            svcdCityVo.setProvinceCode(String.valueOf(city.getProvinceCode()));
            svcdCityVo.setName(city.getName());
            svcdCityVo.setSimpleName(city.getSimpleName());
            svcdCityVo.setQuickCode(city.getQuickCode());
            svcdCityVo.setCityEn(city.getCityEn());
            svcdCityVo.setCapInitials(city.getQuickCode().length()>1?city.getQuickCode().substring(0,1):city.getQuickCode());
            voList.add(svcdCityVo);
        }
        return voList;
    }


    @Override
    public List<SvcdProvinceCityListDto> getCityListForCityWide(SvcdCityDto cityDto) {
        QueryWrapper<SvcdCity> queryWrapper = new QueryWrapper<>();
        // 按 cityCode 检索
        if (StringUtil.isNotEmpty(cityDto.getCityCode())) {
            queryWrapper.eq("city_code", cityDto.getCityCode());
        }
        // 按省份检索
        if (StringUtil.isNotEmpty(cityDto.getProvinceCode())) {
            queryWrapper.eq("province_code", cityDto.getProvinceCode());
        }

        // 取存在代理商的城市
        DealerDto dealerDto = new DealerDto();
        dealerDto.setHaveImage(0);
        // 默认是查询经销商(代理商766)
        dealerDto.setSearchType(cityDto.getSearchType() == null ? 2 : cityDto.getSearchType());
        List<DealerVo> dealerVoList = svcdChannelOrganizationService.getDealerListByOfficialWebsite(dealerDto);
        Set<String> cityCodes = dealerVoList.stream().map(i -> i.getCityCode()).collect(Collectors.toSet());
        queryWrapper.in("city_code", new ArrayList<>(cityCodes));

        queryWrapper.isNotNull("quick_code").last(" ORDER BY province_code ASC, quick_code ASC");
        List<SvcdCity> list = svcdCityMapper.selectList(queryWrapper);

        // 根据市查询省的信息
        List<SvcdProvince> svcdProvinceList = svcdProvinceService.list();
        Map<Long, SvcdProvince> svcdProvinceMap =
            svcdProvinceList.stream().collect(Collectors.toMap(x -> x.getProvinceCode(), x -> x));

        // 省->列表
        Map<Long, List<SvcdCity>> collect = list.stream().collect(Collectors.groupingBy(SvcdCity::getProvinceCode));
        // 数据组装
        List<SvcdProvinceCityListDto> provinceCityListDtoList = new ArrayList<>();
        collect.keySet().stream().forEach(provinceCode -> {
            // 一级省
            SvcdProvince svcdProvince = svcdProvinceMap.get(provinceCode);
            SvcdProvinceCityListDto provinceCityListDto = new SvcdProvinceCityListDto();
            provinceCityListDto.setProvinceCode(String.valueOf(svcdProvince.getProvinceCode()));
            provinceCityListDto.setProvinceName(svcdProvince.getName());
            // 二级市

            List<SvcdCity> svcdCityList = collect.get(provinceCode);
            if (CollectionUtils.isNotEmpty(svcdCityList)) {
                List<SvcdProvinceCityListDto.SvcdInnerCity> innerCities = svcdCityList.stream().map(svcdCity -> {
                    // 市只有一个,且cityCode = provinceCode,说明是直辖市
                    SvcdProvinceCityListDto.SvcdInnerCity innerCity = new SvcdProvinceCityListDto.SvcdInnerCity();
                    innerCity.setCityName(svcdCity.getName());
                    innerCity.setCityCode(String.valueOf(svcdCity.getCityCode()));
                    return innerCity;
                }).collect(Collectors.toList());
                provinceCityListDto.setChildren(innerCities);
            }
            provinceCityListDtoList.add(provinceCityListDto);
        });
        return provinceCityListDtoList;
    }
}
