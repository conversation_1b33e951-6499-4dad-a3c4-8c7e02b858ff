package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.po.CarOmdBestRecommend;
import com.csvw.audi.cc.entity.po.CarOmdStockMap;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.po.CarOmdStockRecommend;

/**
 * <p>
 * OMD库存车映射 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
public interface ICarOmdStockMapService extends IService<CarOmdStockMap> {

    void handleStockMapCcUniqueCode();

    void handleMeasureStockMap() throws Exception;

    void handleRecommendStockMap();

    void handleStockRecommendStockMap();

    Long getStockNumHqOrigin(CarOmdBestRecommend omdBestRecommend);

    Long getStockNumDealerOrigin(CarOmdStockRecommend omdStockRecommend);
}
