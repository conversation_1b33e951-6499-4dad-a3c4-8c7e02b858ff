package com.csvw.audi.cc;

import org.mybatis.spring.annotation.MapperScan;
import org.redisson.api.RedissonClient;
import org.redisson.spring.cache.RedissonSpringCacheManager;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;


/**
 * 程序入口
 *
 * SVW SX
 * Copyright (C) SAIC VOLKSWAGEN, All rights reserved.
 *
 * <AUTHOR>
 * @date 2020-09-17 14:51
 */
@EnableFeignClients(basePackages = { "com.csvw.audi.cc", "com.csvw.audi.common"})
@EnableEurekaClient
@EnableCaching
@SpringBootApplication(scanBasePackages = { "com.csvw.audi.cc", "com.csvw.audi.common"})
@MapperScan(basePackages = {"com.csvw.audi.cc.mapper"})
@ServletComponentScan
public class MainApplication {

    public static void main(String[] args) {
        SpringApplication.run(MainApplication.class, args);
    }

    @Bean
    CacheManager cacheManager(RedissonClient redissonClient){
        return new RedissonSpringCacheManager(redissonClient);
    }

}
