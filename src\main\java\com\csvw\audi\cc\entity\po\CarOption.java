package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 车系配置项
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarOption对象", description="车系配置项")
public class CarOption extends Model<CarOption> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "配置项id")
    private String optionId;

    @ApiModelProperty(value = "配置项名称")
    private String optionName;

    @ApiModelProperty(value = "配置编码")
    private String optionCode;

    @ApiModelProperty(value = "配置种类编码")
    private String category;

    @ApiModelProperty(value = "配置项类型")
    private String optionType;

    @ApiModelProperty(value = "配置项权重")
    private Integer optionWeight;

    @ApiModelProperty(value = "自定义车系id")
    private String customSeriesId;

    @ApiModelProperty(value = "装备图片")
    private String imageUrl;

    @ApiModelProperty(value = "装备详情图片")
    private String imageUrlDetail;

    @ApiModelProperty(value = "装备列表图片")
    private String imageUrlList;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "默认配置项")
    private Integer defaultConfig;

    @ApiModelProperty(value = "数据渠道")
    private String channel;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
