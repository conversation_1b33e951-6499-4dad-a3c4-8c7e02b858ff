package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * ccid来源
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarCustomSource对象", description="ccid来源")
public class CarCustomSource extends Model<CarCustomSource> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "来源ID")
      @TableId(value = "source_id", type = IdType.ASSIGN_ID)
    private Long sourceId;

    @ApiModelProperty(value = "来源名称")
    private String name;

    @ApiModelProperty(value = "来源类型（1:c端来源，2:b端来源）")
    private String type;

    @ApiModelProperty(value = "经销商编码")
    private String dealerCode;

    @ApiModelProperty(value = "经销商名称")
    private String dealerName;

    @ApiModelProperty(value = "应用")
    private String app;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;


    @Override
    protected Serializable pkVal() {
        return this.sourceId;
    }

}
