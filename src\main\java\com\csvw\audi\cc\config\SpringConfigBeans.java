package com.csvw.audi.cc.config;

/**
 * <AUTHOR>
 * @date 2022/9/19 9:55
 * @description 配置Bean
 */

import com.csvw.audi.common.config.OssServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

@Configuration
public class SpringConfigBeans {

    @Autowired
    private OssConfig ossConfig;

    @Bean
    public OssServer ossServer() {
        OssServer ossServer = new OssServer(ossConfig.getEndpoint(), ossConfig.getAccessKeyId(), ossConfig.getAccessKeySecret());
        return ossServer;
    }

    @Bean
    public RestTemplate restTemplate() {
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(10 * 1000);
        httpRequestFactory.setConnectTimeout(10 * 1000);
        httpRequestFactory.setReadTimeout(10 * 1000);
        return new RestTemplate(httpRequestFactory);
    }
}
