package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.dto.OptionParamDto;
import com.csvw.audi.cc.entity.po.CarOption;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.entity.vo.SeriesOptionVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 车系配置项 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface CarOptionMapper extends BaseMapper<CarOption> {

    List<ModelLineOptionVo> carOptionQuery(OptionParamDto param);

    List<CarOption> listPacketSpecialItem(@Param("optionIds") Set<String> optionIds, @Param("modelLineId") String modelLineId);

    List<SeriesOptionVo> seriesOptions(@Param("customSeriesId") String customSeriesId);
}
