package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 购物车
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarShoppingCart对象", description="购物车")
public class CarShoppingCart extends Model<CarShoppingCart> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "购物车id")
      @TableId(value = "shopping_cart_id", type = IdType.ASSIGN_ID)
    private Long shoppingCartId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "配置id")
    private Long ccid;

    @ApiModelProperty(value = "商品id")
    private String skuid;

    @ApiModelProperty(value = "删除标识（1:是 0:否）")
    private Integer delFlag;

    @ApiModelProperty(value = "创建人ID")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "自动保存（1:是 0:否）")
    private Integer autoSave;

    @ApiModelProperty(value = "邀请码")
    private String invitationCode;


    @Override
    protected Serializable pkVal() {
        return this.shoppingCartId;
    }

}
