package com.csvw.audi.cc.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class AudiAMSConfigVo {
    @ApiModelProperty(value = "车系名称")
    private String customSeriesName;
    @ApiModelProperty(value = "配置线名称")
    private String modelLineName;
    @ApiModelProperty(value = "accb type code")
    private String accbTypeCode;
    @ApiModelProperty(value = "车型年款")
    private String modelYear;
    @ApiModelProperty(value = "车型版本")
    private String modelVersion;
    private List<OMDOptionBriefDto> options;

    @Data
    @NoArgsConstructor
    public static class OMDOptionBriefDto {

        public OMDOptionBriefDto(String code, String category, String description){
            this.code = code;
            this.category = category;
            this.description = description;
        }

        @ApiModelProperty("配置项code")
        private String code;
        @ApiModelProperty("配置项类型")
        private String category;
        @ApiModelProperty("配置项描述")
        private String description;
        private List<OMDOptionBriefDto> items;
    }
}
