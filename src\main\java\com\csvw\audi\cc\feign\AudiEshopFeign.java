package com.csvw.audi.cc.feign;

import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.entity.vo.ProdSkuUserRight;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "audi-eshop")
//@FeignClient(value = "audi-eshop", url = "http://dev-audi-copapi.svwsx.cn/audi-eshop")
public interface AudiEshopFeign {

    @GetMapping("/api/v1/userRights/findByCarModelId")
    AjaxMessage<ProdSkuUserRight> findByCarModelId(@RequestParam(value = "carModelId") String carModelId,
                                                   @RequestParam(value = "type") Integer type,
                                                   @RequestParam(value = "modelYear") String modelYear, @RequestParam(value="modelVersion") String modelVersion);

    @PostMapping("/private/order/canUpdateCCConfig")
    AjaxMessage<Integer> canUpdateCCConfig(@RequestParam(value = "ccid") String ccid);

    @GetMapping("/private/order/getOrderStatusByCcid")
    AjaxMessage<String> getCcOrderStatus(@RequestParam(value = "ccid") Long ccid);

    @GetMapping("/private/order/getOrderMessage")
    List<JSONObject> getOrderMessage(@RequestParam(value = "userId") String userId);
}
