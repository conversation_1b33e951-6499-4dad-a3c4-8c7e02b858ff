package com.csvw.audi.cc;

import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.csvw.audi.cc.common.utils.CcEncryptUtils;
import com.csvw.audi.cc.entity.enumeration.OptionCategoryEnum;
import com.csvw.audi.cc.entity.po.CarCustomSeries;
import io.swagger.models.auth.In;
import lombok.Data;
import org.apache.commons.codec.binary.StringUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.logging.log4j.util.Strings;
import org.junit.Test;
import sun.nio.ch.DirectBuffer;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.awt.*;
import java.awt.datatransfer.Clipboard;
import java.awt.datatransfer.StringSelection;
import java.awt.datatransfer.Transferable;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.Inet4Address;
import java.nio.ByteBuffer;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;

public class LoanTest {
    public static double PMT1(double RATE,  int NPER, int PV, double FV, int TYPE)
    {
        return -RATE * (FV + PV * Math.pow(1 + RATE, NPER)) / ((Math.pow(1 + RATE, NPER) - 1) * (1 + RATE * TYPE));
    }
    @Test
    public void test111(){
        int x,p,i,s=0;
        for(x=2;x<5;x++){
            for(p=i=1;i<=x;i++){
                p*=x;
            }
            System.out.println(x);
            System.out.println(p);
            s+=p;
        }
        System.out.println(s);
    }

    public Integer rvlbr(int n){
        List<Integer> fbl = new ArrayList<>();
        if (n == 0){
            return 0;
        }
        if (n == 1){
            return 1;
        }
        fbl.add(0);
        fbl.add(1);
        rv(fbl, n);
        return fbl.get(fbl.size()-1);
    }

    public void rv(List<Integer> fbl, int n){
        n--;
        int size = fbl.size();
        fbl.add(fbl.get(size-2)+fbl.get(size-1));
        if (n >= 2){
            rv(fbl, n);
        }
    }

    public Integer loopfbl(int n){
        List<Integer> fbl = new ArrayList<>();
        if (n == 0){
            return 0;
        }
        if (n == 1){
            return 1;
        }
        fbl.add(0);
        fbl.add(1);
        for (int i=2; i<=n; i++){
            int size = fbl.size();
            fbl.add(fbl.get(size-2)+fbl.get(size-1));
        }
        return fbl.get(fbl.size()-1);
    }

    @Test
    public void generateId() throws InterruptedException {
        Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
        List<Long> ids = new ArrayList<>();
        for(int i=0;i<10;i++) {
            IdentifierGenerator identifierGenerator = new DefaultIdentifierGenerator();
            Thread.sleep(10);
            long id =  identifierGenerator.nextId(this.getClass()).longValue();
            ids.add(id);
            System.out.println(id);
        }
        Transferable transferable = new StringSelection(Strings.join(ids, '\n'));
        clipboard.setContents(transferable, null);
    }

    @Test
    public void testHuo(){
        System.out.println("YEAKLJK".substring(3));
    }

    @Test
    public void testPmt(){
        System.out.println(BigDecimal.valueOf(0.0969).divide(BigDecimal.valueOf(12)));
        System.out.println(-566118+16692);
        System.out.println(PMT1(0.0969/12, 12, -566118+16692, 0, 0));
    }

    @Test
    public void testPmt1(){
        System.out.println(PMT1(0.008075, 12, -549426, 0, 0));
    }

    @Test
    public void test(){
        System.out.println(Arrays.asList("COLOR_INTERIEUR", "SIB").contains(null));
    }

    @Test
    public void fieldTest() throws NoSuchFieldException {
        System.out.println(Double.parseDouble("0.00"));
    }

    @Test
    public void pageTest(){
        int maxStartIndex = 11;
        for (int page=1; page< 10; page ++){
            int startIndex = maxStartIndex  + ((page-1)* 20 +1);
            System.out.println(startIndex+ "  " + page);
        }
    }

    public static <T> List<T> channelData( Class<T> tClass) throws NoSuchFieldException {
        Field field = tClass.getDeclaredField("customSeriesId");
        CarCustomSeries carCustomSeries = new CarCustomSeries();
        System.out.println(field.getName());
        return null;
    }


    @Test
    public void equalLoanPayment(){ //等额本息还款计算函数
        int N = 240;
        double R = 0.0492/12;
        double A = 56*R*Math.pow(1+R,N)/(Math.pow(1+R,N)-1);
        System.out.printf("每月偿还的本息%7.2f\n",A*10000);
        double[] pi = new double[N];
        pi[0] =56*R;
        System.out.printf("第1个月需要偿还的利息:%8.2f  第1个月需要偿还的本金为:%7.2f\n",pi[0]*10000,(A-pi[0])*10000);
        for(int i=1;i<N;i++){
            pi[i] = pi[i-1]*(1+R)-A*R;
            System.out.printf("第%d个月需要偿还的利息:%7.2f  第%d个月需要偿还的本金为:%7.2f\n",i+1,pi[i]*10000,i+1,(A-pi[i])*10000);
        }

    }
    @Test
    public void equalPrincipalPayment(){ //等额本金还款计算函数

        int N = 240; //还款的总月份
        double R = 0.0492/12; //还款的月利率
        double A = 56*1.0/N; //每月需要还得本金
        System.out.printf("每月需要偿还的本金%7.2f\n",A*10000);
        double[] pi = new double[N+1];
        for(int i=1;i<=N;i++){
            pi[i] = -56*R*1.0/N*i+(56/N+56)*R;
            System.out.printf("第%d个月需要偿还的利息:%7.2f.第%d个月需要偿还的本息:%7.2f\n",i+1,pi[i]*10000,i+1,(pi[i]+A)*10000);
        }


    }

    @Test
    public void encryptTest() throws InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
        System.out.println(CcEncryptUtils.encryptAes("{\"userUid\":\"2342343\",\"orgCode\":\"111\"}", "iJK2m-j~D5!DERUY", "audi-ams-app-pod"));
        System.out.println(CcEncryptUtils.decryptAes("8C3F3ECEF1A813F296FEF47C6E0B1BA9C6F97D6B435A36A7AB21196D96A4754A3BAD92418D5D84A838F669C1EDB89DFE", "]Pym-j~D5=]QUyS1", "audi-ams-app-tst"));

    }

    @Test
    public void testInfraTime(){
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        System.out.println(timestamp);
        String xSequenceNo = timestamp + RandomStringUtils.randomNumeric(5);
        System.out.println(xSequenceNo);
        String bodystr = "{\"requestHead\":{\"requestId\":\"ccb8bd56-e63a-42b0-ab96-068621e68002\",\"sourceApp\":\"OneApp\",\"targetApp\":\"OMD\",\"transTime\":1675310025159},\"requestBody\":{\"brandCode\":\"A\",\"accbTypeCode\":\"G6IBAY\",\"modelCode\":\"G6IBAY\",\"optionalPrList\":\"\",\"colorCode\":\"3D3D\",\"interiorCode\":\"TT\",\"modelYear\":\"2023\",\"modelVersion\":\"1\",\"dealerNetCode\":\"111\"}}";
        System.out.println(this.signPostData("PTqPxOYQP3SK", "DkqFRxbYcWmGFX7oy19er0Byf4FSwvr7", xSequenceNo, timestamp, bodystr));
//        System.out.println(this.signPostData("Vu3rx4iuPqZD", "gb4M1vdV0hg9EGxPSsp25l67AggCfVcP", xSequenceNo, timestamp, bodystr));
    }

    public String signPostData(String inputXAppId, String inputSecret, String xSequenceNo, String xTimestamp, String bodyStr){
        List<String> paramSignTextList = new ArrayList<>();
        paramSignTextList.add("Body=" + Base64.getEncoder().encodeToString(StringUtils.getBytesUtf8(bodyStr)));
        paramSignTextList.add("X-App-Id=" + inputXAppId);
        paramSignTextList.add("X-Sequence-No=" + xSequenceNo);
        paramSignTextList.add("X-Timestamp=" + xTimestamp);
        paramSignTextList.add(inputSecret);
        String paramSignText = String.join("&", paramSignTextList);
        paramSignText = Base64.getEncoder().encodeToString(DigestUtils.sha256(paramSignText));
        return paramSignText;
    }

    @Test
    public void bytebuffer(){
        ByteBuffer buffer = ByteBuffer.allocate(10);

        // Write some data into the buffer
        buffer.put((byte) 1);
        buffer.put((byte) 2);
        buffer.put((byte) 3);

        // Reset the position to zero for reading
        buffer.rewind();

        // Read the data from the buffer
        while (buffer.hasRemaining()) {
            System.out.println(buffer.get());
        }
    }
}
