package com.csvw.audi.cc.entity.dto.omd;

import lombok.Data;

import java.util.List;

@Data
public class OmdObjectRes<T> {
    private String exceptionTrace;
    private String code;
    private String message;
    private ResResult<T> result;
    @Data
    public static class ResResult<T>{
        private String errorDesc;
        private String requestId;
        private String rspStatus;
        private T resultData;
    }
}
