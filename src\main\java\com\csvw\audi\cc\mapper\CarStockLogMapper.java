package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.po.CarStockLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 锁库存记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-09
 */
public interface CarStockLogMapper extends BaseMapper<CarStockLog> {

    List<CarStockLog> findLogToSync(@Param("recommendModelId") Long recommendModelId, @Param("type") String type);

}
