package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 车系
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarSeries对象", description="车系")
public class CarSeries extends Model<CarSeries> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "车系id")
    private String seriesId;

    @ApiModelProperty(value = "车系code")
    private String seriesCode;

    @ApiModelProperty(value = "车系名称")
    private String seriesName;

    @ApiModelProperty(value = "accb车系code")
    private String accbModelCode;

    @ApiModelProperty(value = "accb车型ID")
    private String accbModelId;

    @ApiModelProperty(value = "车系图片")
    private String imageUrl;

    @ApiModelProperty(value = "数据渠道")
    private String channel;

    @ApiModelProperty(value = "权重")
    private Integer weight;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;

    private String omdSeriesCode;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
