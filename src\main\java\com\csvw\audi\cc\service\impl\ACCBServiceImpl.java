package com.csvw.audi.cc.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.common.utils.AccbRequestUtil;
import com.csvw.audi.cc.common.utils.accb.ACCBResponse;
import com.csvw.audi.cc.entity.dto.accb.*;
import com.csvw.audi.cc.service.IACCBService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import springfox.documentation.spring.web.json.Json;

import java.text.MessageFormat;
import java.util.List;

@Service
@Slf4j
public class ACCBServiceImpl implements IACCBService {

    @Autowired
    AccbRequestUtil requestUtil;


    @Override
    public List<ACCBCarModels> allModels(String lang, String product) {
        String models = "/v1/models/lang/{0}/{1}.json";
        String modelsApi = MessageFormat.format(models, lang, product);
        ACCBResponse<JSONObject> response = requestUtil.get(modelsApi);
        List<ACCBCarModels> data = response.getData().getJSONArray("data").toJavaList(ACCBCarModels.class);
        return data;
    }

    @Override
    public List<TypeDto> modelTypes(String lang, String product, String modelId) {
        String types = "/v1/type/model/{0}/lang/{1}/{2}.json";
        String typeApi = MessageFormat.format(types, modelId, lang, product);
        ACCBResponse<JSONArray> response = requestUtil.get(typeApi);
        List<TypeDto> data = response.getData().toJavaList(TypeDto.class);
        return data;
    }

    @Override
    public List<OptionDto> typeOptions(String language, String product, String typeId) {
        String options = "/v1/model-option/type/{0}/lang/{1}/{2}.json";
        String optionsApi = MessageFormat.format(options, typeId, language, product);
        ACCBResponse<JSONArray> response = requestUtil.get(optionsApi);
        List<OptionDto> data = response.getData().toJavaList(OptionDto.class);
        return data;
    }

    @Override
    public List<InteriorOptionDto> typeInteriorOptions(String language, String product, String modelId, String typeId) {
        String inOptions = "/v1/interior-option/model/{0}/type/{1}/lang/{2}/{3}.json";
        String inOptionsApi = MessageFormat.format(inOptions, modelId, typeId, language, product);
        ACCBResponse<JSONArray> response = requestUtil.get(inOptionsApi);
        List<InteriorOptionDto> data = response.getData().toJavaList(InteriorOptionDto.class);
        return data;
    }

    @Override
    public String genAudiCode(AudiConfigDto audiConfigDto) {
        String genApi = "/v1/audi-code/generate/lang/en/CHINA_SAIC.json";
        ACCBResponse<String> response = requestUtil.post(genApi, JSONObject.toJSONString(audiConfigDto));
        if (response == null || StringUtils.isNotBlank(response.getErrorCode())
                || StringUtils.isBlank(response.getData()) ){
            String error = "没有返回结果。";
            if (response != null){
                error = JSONObject.toJSONString(response);
            }
            log.error("生成audi code失败："+ error);
            return null;
        }
        return response.getData();
    }

    @Override
    public AudiConfigDto audiConfig(String audiCode) {
        String genApi = "/v1/audi-code/{0}/lang/en/CHINA_SAIC.json";
        genApi = MessageFormat.format(genApi, audiCode);
        ACCBResponse<JSONObject> response = requestUtil.get(genApi);
        if (response == null || StringUtils.isNotBlank(response.getErrorCode())
                || response.getData() == null ) {
            String error = "没有返回结果。";
            if (response != null) {
                error = JSONObject.toJSONString(response);
            }
            log.error("获取accb配置失败：" + error);
            return null;
        }
        AudiConfigDto audiConfigDto = response.getData().toJavaObject(AudiConfigDto.class);
        return audiConfigDto;
    }

    @Override
    public List<ConfigPreviewDto> preview(String typeCode, String modelYear, String exteriorCode, String interiorCode) {
        String preview = "/v1/render/typeCode/{0}/modelYear/{1}/modelVersion/3/exterior/{2}/interior/{3}.json";
        String previewApi = MessageFormat.format(preview, typeCode, modelYear, exteriorCode, interiorCode);
        ACCBResponse<JSONArray> response = requestUtil.get(previewApi);
        List<ConfigPreviewDto> data = response.getData().toJavaList(ConfigPreviewDto.class);
        return data;
    }

    @Override
    public List<OptionDto> refreshOption(String language, String product, RefreshOptionParam optionParam) {
        String options = "/v1/refresh-option/lang/{0}/{1}.json";
        String optionsApi = MessageFormat.format(options, language, product);
        ACCBResponse<JSONArray> response = requestUtil.post(optionsApi, JSONObject.toJSONString(optionParam));
        List<OptionDto> data = response.getData().toJavaList(OptionDto.class);
        return data;
    }

    @Override
    public List<EquipmentDto> standardEquipment(String language, String product, EquipmentParam equipmentParam) {
        String equipment = "/v1/order/lang/{0}/{1}.json";
        String equipmentApi = MessageFormat.format(equipment, language, product);
        ACCBResponse<JSONObject> response = requestUtil.post(equipmentApi, JSONObject.toJSONString(equipmentParam));
        List<EquipmentDto> data = response.getData().getJSONArray("standard_equipment").toJavaList(EquipmentDto.class);
        return data;
    }

    @Override
    public JSONObject getAudiCode(String audiCode) throws Exception{
        String getAudiCodeUrl = "/v1/audi-code/{0}";
        String getAudiCodeApi = MessageFormat.format(getAudiCodeUrl, audiCode);
        ACCBResponse response = requestUtil.get(getAudiCodeApi);
        return (JSONObject) response.getData();
    }
}
