package com.csvw.audi.cc.controller;


import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.vo.ColorVo;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/api/v1/color")
@Api(tags = "车型颜色")
public class ColorController extends BaseController {

    @GetMapping
    public AjaxMessage<List<ColorVo>> color(String colorFlag){
        ColorVo.ColorDesc[] urls1 = {new ColorVo.ColorDesc("test/2021/06/02/a7l/green1.png",""),
                new ColorVo.ColorDesc("test/2021/06/02/a7l/green2.png",""),
                new ColorVo.ColorDesc("test/2021/06/02/a7l/green3.png",""),
                new ColorVo.ColorDesc("test/2021/06/02/a7l/green4.png",""),
                new ColorVo.ColorDesc("test/2021/06/02/a7l/green5.png",""),
                new ColorVo.ColorDesc("test/2021/06/02/a7l/green6.png","")};
        ColorVo.ColorDesc[] urls2 = {new ColorVo.ColorDesc("test/2021/06/02/a7l/blue1.png",""),
                new ColorVo.ColorDesc("test/2021/06/02/a7l/blue2.png",""),
                new ColorVo.ColorDesc("test/2021/06/02/a7l/blue3.png",""),
                new ColorVo.ColorDesc("test/2021/06/02/a7l/blue4.png",""),
                new ColorVo.ColorDesc("test/2021/06/02/a7l/blue5.png",""),
                new ColorVo.ColorDesc("test/2021/06/02/a7l/blue6.png","")
        };
        ColorVo.ColorDesc[] urls3 = {new ColorVo.ColorDesc("test/2021/06/02/a7l/inter1.jpg","超级运动座椅"),
                new ColorVo.ColorDesc("test/2021/06/02/a7l/inter2.jpg","高定琥珀棕"),
                new ColorVo.ColorDesc("test/2021/06/02/a7l/inter3.jpg","竞速雅灰格纹铝饰条")
//                ,new ColorVo.ColorDesc("test/2021/06/02/a7l/inter4.jpg","内饰描述")
        };
        List<ColorVo> res = new ArrayList<>();
        ColorVo v1 = new ColorVo();
        v1.setCustomColorId("COLOR_EXTERIEUR:3ZA2");
        v1.setCustomColorCode("COLOR_EXTERIEUR:3ZA2");
        v1.setColorCode("COLOR_EXTERIEUR:3ZA2");
        v1.setColorNameCn("青山黛");
        v1.setImageUrl("test/2021/06/02/a7l/green.2cd52d60.png");
        v1.setColorFlag("1");
        v1.setDescImageUrls(Arrays.asList(urls1));
        res.add(v1);
        ColorVo v2 = new ColorVo();
        v2.setCustomColorId("COLOR_EXTERIEUR:B9A2");
        v2.setCustomColorCode("COLOR_EXTERIEUR:B9A2");
        v2.setColorCode("COLOR_EXTERIEUR:B9A2");
        v2.setColorNameCn("星河蓝");
        v2.setImageUrl("test/2021/06/02/a7l/blue.1458756a.png");
        v2.setColorFlag("1");
        v2.setDescImageUrls(Arrays.asList(urls2));
        res.add(v2);
        ColorVo v3 = new ColorVo();
        v3.setCustomColorId("COLOR_INTERIEUR:RI");
        v3.setCustomColorCode("COLOR_INTERIEUR:RI");
        v3.setColorCode("COLOR_INTERIEUR:RI");
        v3.setColorFlag("0");
        v3.setColorNameCn("高定琥珀棕");
        v3.setDescImageUrls(Arrays.asList(urls3));
        res.add(v3);
        if (StringUtils.isNotBlank(colorFlag)){
            res = res.stream().filter(i->i.getColorFlag().equals(colorFlag)).collect(Collectors.toList());
        }

        return new AjaxMessage<>("00", "成功", res);

    }

}

