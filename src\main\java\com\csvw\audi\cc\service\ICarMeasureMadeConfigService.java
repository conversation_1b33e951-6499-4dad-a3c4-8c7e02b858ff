package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.dto.MeasureQueryParam;
import com.csvw.audi.cc.entity.po.CarCustom;
import com.csvw.audi.cc.entity.po.CarMeasureMadeConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.po.CarMeasureMadeOriginConfig;
import com.csvw.audi.cc.entity.vo.MeasureVo;
import com.csvw.audi.cc.exception.ServiceException;

/**
 * <p>
 * 半订制化车辆配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
public interface ICarMeasureMadeConfigService extends IService<CarMeasureMadeConfig> {

    MeasureVo measureQuery(MeasureQueryParam measureQueryParam, String channel) throws Exception;

    CarCustom addCarCustomConfig(String userId, String userMobile, Long measureId, Long sourceId, String channel, String entryPoint, String packetEquityId) throws Exception;

    void lockMeasureMadeStock(Long ccid) throws ServiceException;

    void unlockMeasureMadeStock(Long ccid) throws ServiceException;

    int measureCount(CarMeasureMadeOriginConfig originConfig);

    CarCustom updateCarCustomConfig(Long ccid, Long measureId, String userId, String userMobile, String packetEquityId) throws Exception;
}
