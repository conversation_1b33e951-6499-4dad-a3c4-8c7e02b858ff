<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarOmdDataMapMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarOmdDataMap">
        <id column="id" property="id" />
        <result column="origin_model_code" property="originModelCode" />
        <result column="origin_accb_type_code" property="originAccbTypeCode" />
        <result column="origin_model_version" property="originModelVersion" />
        <result column="origin_model_year" property="originModelYear" />
        <result column="origin_interior_code" property="originInteriorCode" />
        <result column="origin_color_code" property="originColorCode" />
        <result column="origin_pr_list" property="originPrList" />
        <result column="origin_class_code" property="originClassCode" />
        <result column="origin_cc_unique_code" property="originCcUniqueCode" />
        <result column="model_code" property="modelCode" />
        <result column="accb_type_code" property="accbTypeCode" />
        <result column="model_version" property="modelVersion" />
        <result column="model_year" property="modelYear" />
        <result column="interior_code" property="interiorCode" />
        <result column="color_code" property="colorCode" />
        <result column="pr_list" property="prList" />
        <result column="class_code" property="classCode" />
        <result column="cc_unique_code" property="ccUniqueCode" />
        <result column="api" property="api" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, origin_model_code, origin_accb_type_code, origin_model_version, origin_model_year, origin_interior_code, origin_color_code, origin_pr_list, origin_class_code, origin_cc_unique_code, model_code, accb_type_code, model_version, model_year, interior_code, color_code, pr_list, class_code, cc_unique_code, api, create_time, update_time, del_flag
    </sql>

</mapper>
