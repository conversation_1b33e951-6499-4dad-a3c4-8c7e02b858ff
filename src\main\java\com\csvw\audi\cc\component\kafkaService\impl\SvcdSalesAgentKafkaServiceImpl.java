package com.csvw.audi.cc.component.kafkaService.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.csvw.audi.cc.component.kafkaService.SvcdKafkaService;
import com.csvw.audi.cc.entity.po.SvcdSalesAgent;
import com.csvw.audi.cc.mapper.SvcdSalesAgentMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service("svcdSalesAgentKafkaService")
public class SvcdSalesAgentKafkaServiceImpl implements SvcdKafkaService {

    @Autowired
    private SvcdSalesAgentMapper svcdSalesAgentMapper;

    @Override
    public void insertData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdSalesAgent salesAgent = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdSalesAgent.class);
        salesAgent.setCreatedAt(nowDate);
        salesAgent.setUpdatedAt(nowDate);
        salesAgent.insert();
    }

    @Override
    public void updateData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdSalesAgent salesAgent = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdSalesAgent.class);
        String dealerCode = salesAgent.getDealerCode();

        QueryWrapper<SvcdSalesAgent> salesAgentQusery = new QueryWrapper<>();
        salesAgentQusery.eq("dealer_code",dealerCode);
        List<SvcdSalesAgent> list = salesAgent.selectList(salesAgentQusery);
        if(list == null || list.size() == 0) {
            salesAgent.setCreatedAt(nowDate);
            salesAgent.setUpdatedAt(nowDate);
            salesAgent.insert();
        } else {
            salesAgent.setUpdatedAt(nowDate);

            UpdateWrapper<SvcdSalesAgent> salesAgentUpdateWrapper = new UpdateWrapper<>();
            salesAgentUpdateWrapper.eq("dealer_code",dealerCode);
            salesAgent.setDealerCode(null);//主键置空，防止数据库更新报错
            svcdSalesAgentMapper.update(salesAgent,salesAgentUpdateWrapper);
        }
    }
}
