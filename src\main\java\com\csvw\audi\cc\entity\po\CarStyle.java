package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 款式
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarStyle对象", description="款式")
public class CarStyle extends Model<CarStyle> {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "款式id")
    private String styleId;

    @ApiModelProperty(value = "款式名称")
    private String styleName;

    @ApiModelProperty(value = "优惠")
    private String preferential;

    @ApiModelProperty(value = "展示图片")
    private String imageUrl;

    private String customSeriesId;

    private Integer frontStatus;

    private Integer recommendStatus;

    @ApiModelProperty(value = "渠道值")
    private String channel;

    @ApiModelProperty(value = "特别描述")
    private String description;

    @ApiModelProperty(value = "权重")
    private Integer weight;

    private Integer delFlag;

    private Integer stockout;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
