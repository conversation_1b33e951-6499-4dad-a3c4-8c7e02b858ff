package com.csvw.audi.cc.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class ParameterVo {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "参数id")
    private Long parameterId;

    @ApiModelProperty(value = "参数名字")
    private String parameterName;

    @ApiModelProperty(value = "值")
    private String parameterValue;

    @ApiModelProperty(value = "类型")
    private String parameterType;


}
