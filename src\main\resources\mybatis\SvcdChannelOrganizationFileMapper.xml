<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.SvcdChannelOrganizationFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.SvcdChannelOrganizationFile">
        <id column="channel_organization_file_id" property="channelOrganizationFileId" />
        <result column="channel_organization_id" property="channelOrganizationId" />
        <result column="dealer_code" property="dealerCode" />
        <result column="file_name" property="fileName" />
        <result column="file_url" property="fileUrl" />
        <result column="file_type" property="fileType" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        channel_organization_file_id, channel_organization_id, dealer_code, file_name, file_url, file_type, created_at, updated_at
    </sql>

</mapper>
