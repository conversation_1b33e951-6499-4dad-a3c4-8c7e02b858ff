package com.csvw.audi.cc.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

@Data
public class FrontOptions implements Serializable {
    private List<ModelLineOptionVo> exterieurOptions;
    private List<ModelLineOptionVo> radOptions;
    private List<ModelLineSibInterieurVo> sibInterieurOptions;
    private List<ModelLineOptionVo> vosOptions;
    private List<ModelLineOptionVo> eihOptions;
    private List<ModelLineOptionVo> personalOptions;
    private Collection<List<ModelLineOptionVo>> needSelect;
    private List<ModelLineSibInterieurVo> needSelectSibInterieur;
    private List<ModelLineOptionVo> needDeselect;
    private List<ModelLineSibInterieurVo> needDeselectSibInterieur;
    private List<ModelLineOptionVo> needSwitch;
}
