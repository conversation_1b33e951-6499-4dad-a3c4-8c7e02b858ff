package com.csvw.audi.cc.feign;

import com.csvw.audi.cc.entity.dto.AudiUserResp;
import com.csvw.audi.cc.entity.vo.bbs.UpdateUserNoInviteVO;
import com.csvw.audi.common.entity.dto.AjaxMessage;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/6/19 17:57
 * @description
 */
@FeignClient(value = "audi-bbs")
public interface AudiBBSFeignService {

    @PutMapping(value = "/private/user/noInvite")
    AjaxMessage updateNoInvite(@RequestBody UpdateUserNoInviteVO param);

    @GetMapping(value = "/private/user/{userId}")
    AudiUserResp findByUserId(@PathVariable Long userId);
}
