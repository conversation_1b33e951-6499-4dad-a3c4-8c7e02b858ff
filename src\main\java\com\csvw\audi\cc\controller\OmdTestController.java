package com.csvw.audi.cc.controller;


import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.config.AppConfig;
import com.csvw.audi.cc.entity.dto.omd.*;
import com.csvw.audi.cc.feign.OmdFeign;
import com.csvw.audi.cc.service.impl.CarRecommendServiceImpl;
import com.csvw.audi.cc.service.impl.SignService;
import feign.Response;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/test/omd")
@Api(tags = "omd")
@Slf4j
public class OmdTestController extends BaseController {

    @Autowired
    AppConfig appConfig;

    @Autowired
    OmdFeign omdFeign;

    @Autowired
    SignService signService;

    @Autowired
    CarRecommendServiceImpl carRecommendService;

//    @PostMapping("/convert")
    public void commendConvert(@RequestBody JSONObject jsonObject) throws Exception {
        carRecommendService.bestRecommendToVo(jsonObject.toJavaObject(BestRecommendRes.class), "master");
    }

    @PostMapping
    public void hqVehicle(@RequestBody HqVehicleBody vehicleBody){
        OmdParam<HqVehicleBody> omdParam = new OmdParam<>(vehicleBody);
        omdFeign.queryAudiHqVehicle(omdParam);
    }

    @PostMapping("/estimateDelivery")
    public Object estimateDelivery( @RequestBody EstimateDeliveryBody body){
        OmdParam omdParam = new OmdParam(body);
        OmdObjectRes<EstimateDeliveryRes> res = omdFeign.estimateDeliveryQuery(omdParam);
        return successMessage(res);
    }

    @GetMapping("/typePriceTest")
    public String typePrice(){
        String timestamp = signService.getTimestamp();
        String sequenceNo = signService.getSequence(timestamp);
        PriceRequestBody requestBody = new PriceRequestBody();
        requestBody.setDate("all");
        requestBody.setBrandCode("A");
        requestBody.setPriceType("130");
        requestBody.setPage("1");
        requestBody.setPageSize("100");
        OmdParam<PriceRequestBody> omdParam = new OmdParam<>(requestBody);
        String json = JSONObject.toJSONString(omdParam);
        String sign = signService.signPostData(sequenceNo, timestamp, json);
        Response response = omdFeign.getCurrentTypePriceTest(appConfig.getInfra().getAppId(), sequenceNo, timestamp, sign, json);
        String res = response.body().toString();
        log.info("=========omd type price test start=========");
        log.info("request: "+response.request().toString());
        log.info("request: "+res);
        log.info("=========omd type price test end=========");
        log.info("type res: "+ res.toString());
        return res;
    }

    @GetMapping("/getCurrentTypeColorPrice")
    public OmdRes<ColorPriceRes> getCurrentTypeColorPrice(){
        String timestamp = signService.getTimestamp();
        String sequenceNo = signService.getSequence(timestamp);
        PriceRequestBody requestBody = new PriceRequestBody();
        requestBody.setDate("all");
        requestBody.setBrandCode("A");
        requestBody.setPriceType("130");
        requestBody.setPage("1");
        requestBody.setPageSize("10");
        OmdParam<PriceRequestBody> omdParam = new OmdParam<>(requestBody);
        String json = JSONObject.toJSONString(omdParam);
        String sign = signService.signPostData(sequenceNo, timestamp, json);
        OmdRes<ColorPriceRes> res = omdFeign.getCurrentTypeColorPrice(appConfig.getInfra().getAppId(), sequenceNo, timestamp, sign, json);
//        String res = response.body().toString();
//        log.info("=========omd type price test start=========");
//        log.info("request: "+response.request().toString());
//        log.info("request: "+res);
//        log.info("=========omd type price test end=========");
        return res;
    }

    @GetMapping("/getCurrentTypePrPrice")
    public OmdRes<PrPriceRes> getCurrentTypePrPrice(Integer page){
        String timestamp = signService.getTimestamp();
        String sequenceNo = signService.getSequence(timestamp);
        PriceRequestBody requestBody = new PriceRequestBody();
        if (page == null){
            page = 0;
        }
        requestBody.setDate("all");
        requestBody.setBrandCode("A");
        requestBody.setPriceType("130");
        requestBody.setPage(String.valueOf(page));
        requestBody.setPageSize("100");
        OmdParam<PriceRequestBody> omdParam = new OmdParam<>(requestBody);
        String json = JSONObject.toJSONString(omdParam);
        String sign = signService.signPostData(sequenceNo, timestamp, json);
        OmdRes<PrPriceRes> response = omdFeign.getCurrentTypePrPrice(appConfig.getInfra().getAppId(), sequenceNo, timestamp, sign, json);
//        String res = response.body().toString();
//        log.info("=========omd type price test start=========");
//        log.info("request: "+response.request().toString());
//        log.info("request: "+res);
//        log.info("=========omd type price test end=========");
        return response;
    }

    @GetMapping("/getCurrentTypePrpkgPrice")
    public OmdRes<PrPkgPriceRes> getCurrentTypePrpkgPrice(String page){
        String timestamp = signService.getTimestamp();
        String sequenceNo = signService.getSequence(timestamp);
        PriceRequestBody requestBody = new PriceRequestBody();
        requestBody.setDate("all");
        requestBody.setBrandCode("A");
        requestBody.setPriceType("130");
        requestBody.setPage(page);
        requestBody.setPageSize("10");
        OmdParam<PriceRequestBody> omdParam = new OmdParam<>(requestBody);
        String json = JSONObject.toJSONString(omdParam);
        String sign = signService.signPostData(sequenceNo, timestamp, json);
        OmdRes<PrPkgPriceRes> response = omdFeign.getCurrentTypePrpkgPrice(appConfig.getInfra().getAppId(), sequenceNo, timestamp, sign, json);
//        String res = response.body().toString();
//        log.info("=========omd type price test start=========");
//        log.info("request: "+response.request().toString());
//        log.info("request: "+res);
//        log.info("=========omd type price test end=========");
        return response;
    }

}

