<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.SvcdPositionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.SvcdPosition">
        <id column="position_id" property="positionId" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="type" property="type" />
        <result column="skill" property="skill" />
        <result column="brand" property="brand" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="interview_require" property="interviewRequire" />
        <result column="evaluation_require" property="evaluationRequire" />
        <result column="train_require" property="trainRequire" />
        <result column="auth_require" property="authRequire" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        position_id, code, name, type, skill, brand, remark, status, interview_require, evaluation_require, train_require, auth_require, deleted
    </sql>

</mapper>
