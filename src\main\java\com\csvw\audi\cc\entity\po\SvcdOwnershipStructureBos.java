package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 股权信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SvcdOwnershipStructureBos对象", description="股权信息")
public class SvcdOwnershipStructureBos extends Model<SvcdOwnershipStructureBos> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "数据id")
      @TableId(value = "ownership_structure_bos_id", type = IdType.AUTO)
    private Long ownershipStructureBosId;

    @ApiModelProperty(value = "渠道商组织信息id")
    private Long channelOrganizationId;

    @ApiModelProperty(value = "渠道商编码")
    private String dealerCode;

    @ApiModelProperty(value = "股东姓名")
    private String shareholderName;

    @ApiModelProperty(value = "投资比例")
    private Long investmentProportion;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;


    @Override
    protected Serializable pkVal() {
        return this.ownershipStructureBosId;
    }

}
