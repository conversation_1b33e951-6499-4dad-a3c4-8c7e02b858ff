package com.csvw.audi.cc.common.utils.accb;

import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.*;
import org.apache.http.entity.StringEntity;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class AccessServiceImpl extends AccessService {
    private static final String UTF8 = "UTF-8";

    public AccessServiceImpl(String ak, String sk) {
        super(ak, sk);
    }

    public HttpRequestBase access(String url, Map<String, String> headers, String content, HttpMethodName httpMethod) throws Exception {
        Request request = new Request();
        request.setAppKey(this.ak);
        request.setAppSecrect(this.sk);
        request.setMethod(httpMethod.name());
        request.setUrl(url);
        for (String k : headers.keySet())
            request.addHeader(k, headers.get(k));
        request.setBody(content);
        Signer signer = new Signer();
        signer.sign(request);
        HttpRequestBase httpRequestBase = createRequest(url, (Header)null, content, httpMethod);
        Map<String, String> requestHeaders = request.getHeaders();
        for (String key : requestHeaders.keySet()) {
            if (key.equalsIgnoreCase("Content-Length".toString()))
                continue;
            String value = requestHeaders.get(key);
            httpRequestBase.addHeader(key, new String(value.getBytes("UTF-8"), "ISO-8859-1"));
        }
        return httpRequestBase;
    }

    public HttpRequestBase access(String url, Map<String, String> headers, InputStream content, Long contentLength, HttpMethodName httpMethod) throws Exception {
        String body = "";
        if (content != null) {
            ByteArrayOutputStream result = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = content.read(buffer)) != -1)
                result.write(buffer, 0, length);
            body = result.toString("UTF-8");
        }
        return access(url, headers, body, httpMethod);
    }

    private static HttpRequestBase createRequest(String url, Header header, String content, HttpMethodName httpMethod) {
        HttpHead httpHead = null;
        if (httpMethod == HttpMethodName.POST) {
            HttpPost postMethod = new HttpPost(url);
            if (content != null) {
                StringEntity entity = new StringEntity(content, StandardCharsets.UTF_8);
                postMethod.setEntity((HttpEntity)entity);
            }
            HttpPost httpPost1 = postMethod;
        } else if (httpMethod == HttpMethodName.PUT) {
            HttpPut putMethod = new HttpPut(url);
            HttpPut httpPut1 = putMethod;
            if (content != null) {
                StringEntity entity = new StringEntity(content, StandardCharsets.UTF_8);
                putMethod.setEntity((HttpEntity)entity);
            }
        } else if (httpMethod == HttpMethodName.PATCH) {
            HttpPatch patchMethod = new HttpPatch(url);
            HttpPatch httpPatch1 = patchMethod;
            if (content != null) {
                StringEntity entity = new StringEntity(content, StandardCharsets.UTF_8);
                patchMethod.setEntity((HttpEntity)entity);
            }
        } else if (httpMethod == HttpMethodName.GET) {
            HttpGet httpGet = new HttpGet(url);
        } else if (httpMethod == HttpMethodName.DELETE) {
            HttpDelete httpDelete = new HttpDelete(url);
        } else if (httpMethod == HttpMethodName.OPTIONS) {
            HttpOptions httpOptions = new HttpOptions(url);
        } else if (httpMethod == HttpMethodName.HEAD) {
            httpHead = new HttpHead(url);
        } else {
            throw new RuntimeException("Unknown HTTP method name: " + httpMethod);
        }
        httpHead.addHeader(header);
        return (HttpRequestBase)httpHead;
    }
}
