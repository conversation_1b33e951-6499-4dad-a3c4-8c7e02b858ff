package com.csvw.audi.cc.feign;

import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "cop-order-query", url = "${audi.purple-int-scq}/cop-order-query")
public interface CopOrderQueryFeign {

    @GetMapping("/open/api/v1/pre-sale/coupon-info")
    JSONObject couponInfo(@RequestParam(name = "ccid", required = false) String ccid, @RequestParam String seriesCode, @RequestParam String memberId);

}
