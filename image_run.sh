#!/usr/bin/env bash

N=kmsp-template-demo
V=1.0.0

TAG=kmsp/${N}:${V}

NET=kmsp

#创建网络
docker network create ${NET}

docker rm -f ${N}

docker run -d --name ${N} --restart unless-stopped \
-p 8080:8080 \
--network ${NET} \
-e JVM_ARGS="-Xms256m -Xmx1024m" \
-e CMD_LINE_ARGS="--spring.profiles.active=default" \
-e DB_HOST_PORT=************* \
-e DB_NAME=kmsp_user \
-e DB_USER=kmsp_user \
-e DB_PASSWORD=Kmsp_user_2019 \
${TAG}

docker ps -a | grep ${N}