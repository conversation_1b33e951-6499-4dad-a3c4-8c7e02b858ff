package com.csvw.audi.cc.common.utils.accb;

import org.apache.http.client.methods.HttpRequestBase;

import java.io.InputStream;
import java.util.Map;

public abstract class AccessService {
    protected String ak = null;

    protected String sk = null;

    public AccessService(String ak, String sk) {
        this.ak = ak;
        this.sk = sk;
    }

    public abstract HttpRequestBase access(String paramString, Map<String, String> paramMap, InputStream paramInputStream, Long paramLong, HttpMethodName paramHttpMethodName) throws Exception;

    public abstract HttpRequestBase access(String paramString1, Map<String, String> paramMap, String paramString2, HttpMethodName paramHttpMethodName) throws Exception;

    public HttpRequestBase access(String url, Map<String, String> header, HttpMethodName httpMethod) throws Exception {
        return access(url, header, null, Long.valueOf(0L), httpMethod);
    }

    public HttpRequestBase access(String url, InputStream content, Long contentLength, HttpMethodName httpMethod) throws Exception {
        return access(url, null, content, contentLength, httpMethod);
    }

    public HttpRequestBase access(String url, HttpMethodName httpMethod) throws Exception {
        return access(url, null, null, Long.valueOf(0L), httpMethod);
    }

    public String getAk() {
        return this.ak;
    }

    public void setAk(String ak) {
        this.ak = ak;
    }

    public String getSk() {
        return this.sk;
    }

    public void setSk(String sk) {
        this.sk = sk;
    }
}
