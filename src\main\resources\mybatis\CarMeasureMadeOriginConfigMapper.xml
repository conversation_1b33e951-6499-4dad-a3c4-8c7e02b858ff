<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarMeasureMadeOriginConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarMeasureMadeOriginConfig">
        <id column="measure_origin_id" property="measureOriginId" />
        <id column="semi_customization_model_id" property="semiCustomizationModelId" />
        <result column="model_code" property="modelCode" />
        <result column="accb_type_code" property="accbTypeCode" />
        <result column="model_version" property="modelVersion" />
        <result column="model_year" property="modelYear" />
        <result column="interior_code" property="interiorCode" />
        <result column="color_code" property="colorCode" />
        <result column="pr_list" property="prList" />
        <result column="class_code" property="classCode" />
        <result column="stock_num" property="stockNum" />
        <result column="unique_code" property="uniqueCode" />
        <result column="cc_unique_code" property="ccUniqueCode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        measure_origin_id, semi_customization_model_id, model_code, accb_type_code, model_version, model_year, interior_code, color_code, pr_list, class_code, stock_num, unique_code, cc_unique_code, create_time, update_time, del_flag
    </sql>

    <select id="measureOriginConfigByMeasureId" resultType="com.csvw.audi.cc.entity.po.CarMeasureMadeOriginConfig">
        select oc.* from car_measure_made_config c left join car_measure_made_origin_config oc on c.measure_origin_id = oc.measure_origin_id
        where c.measure_id = #{measureId}
    </select>

</mapper>
