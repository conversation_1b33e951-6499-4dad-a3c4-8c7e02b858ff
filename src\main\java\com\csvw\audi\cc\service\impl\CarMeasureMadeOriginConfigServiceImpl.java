package com.csvw.audi.cc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.entity.dto.ConfigMeasureDto;
import com.csvw.audi.cc.entity.dto.ModelParamDto;
import com.csvw.audi.cc.entity.dto.OptionParamDto;
import com.csvw.audi.cc.entity.dto.omd.OmdModelDto;
import com.csvw.audi.cc.entity.dto.vwcc.CarCustomDetail;
import com.csvw.audi.cc.entity.dto.vwcc.Option;
import com.csvw.audi.cc.entity.enumeration.DepositTypeEnum;
import com.csvw.audi.cc.entity.enumeration.OptionCategoryEnum;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.entity.vo.BestRecommendCarVo;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.entity.vo.ModelLineVo;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.mapper.CarMeasureMadeConfigMapper;
import com.csvw.audi.cc.mapper.CarMeasureMadeOriginConfigMapper;
import com.csvw.audi.cc.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 半订制化车辆原始配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@Service
@Slf4j
public class CarMeasureMadeOriginConfigServiceImpl extends ServiceImpl<CarMeasureMadeOriginConfigMapper, CarMeasureMadeOriginConfig> implements ICarMeasureMadeOriginConfigService {

    @Autowired
    private ICarRecommendService carRecommendService;

    @Autowired
    private CarMeasureMadeConfigMapper mapper;

    @Autowired
    private CarMeasureMadeOriginConfigMapper originConfigMapper;

    @Autowired
    private ICarModelLineService modelLineService;

    @Autowired
    private ICarMeasureMadeConfigService measureMadeConfigService;

    @Autowired
    private ICarMeasureMadeConfigOptionService measureMadeConfigOptionService;

    @Autowired
    private OmdServiceImpl omdService;

    @Autowired
    private CarRecommendCustomServiceImpl recommendCustomService;

    @Autowired
    private ICarCustomService carCustomService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private CarConfigSnapshotService snapshotService;

    @Autowired
    private ICarCustomSnapshotService customSnapshotService;

    private static final String measureKeyPrefix = "saic_audi:audi_car_config:cc_to_measure_key:";

    @Override
    public void convertMeasureMade() throws Exception {
        List<CarMeasureMadeOriginConfig> originConfigs = mapper.listOriginConfigToConvert();
        if (originConfigs != null) {
            originConfigs.forEach(i->{
                log.info("半订制转换， data: {}", i);
                try {
                    String channel = Constant.MASTER_CHANNEL;
                    OmdModelDto omdModelDto = new OmdModelDto();
                    BeanUtils.copyProperties(i, omdModelDto);
                    omdModelDto.setModelCode(i.getAccbTypeCode());
                    BestRecommendCarVo recommendCarVo = carRecommendService.bestRecommendToVo(omdModelDto, Constant.MASTER_CHANNEL);
                    ModelLineOptionVo color=null, rad=null, vos=null, sib=null, eih=null;

                    List<ModelLineOptionVo> prOptions = new ArrayList<>();
                    prOptions.addAll(recommendCarVo.getOptions());
                    Iterator<ModelLineOptionVo> prIter = prOptions.iterator();
                    List<String> prFilters = Arrays.asList(OptionCategoryEnum.OUTCOLOR.getValue(), OptionCategoryEnum.INCOLOR.getValue(),
                            OptionCategoryEnum.WHEEL.getValue(), OptionCategoryEnum.SEET.getValue(), OptionCategoryEnum.SIB.getValue(),
                            OptionCategoryEnum.EIH.getValue());
                    while (prIter.hasNext()){
                        ModelLineOptionVo optionVo = prIter.next();
                        if (OptionCategoryEnum.WHEEL.getValue().equals(optionVo.getCategory())){
                            rad = optionVo;
                        }else if(OptionCategoryEnum.SEET.getValue().equals(optionVo.getCategory())){
                            vos = optionVo;
                        }else if(OptionCategoryEnum.SIB.getValue().equals(optionVo.getCategory())){
                            sib = optionVo;
                        }else if(OptionCategoryEnum.EIH.getValue().equals(optionVo.getCategory())){
                            eih = optionVo;
                        }else if(OptionCategoryEnum.OUTCOLOR.getValue().equals(optionVo.getCategory())){
                            color = optionVo;
                        }
                        if (prFilters.contains(optionVo.getCategory())){
                            prIter.remove();
                        }
                    }

                    List<ModelLineOptionVo> optionVos = null;
                    if (rad == null){
                        String category = OptionCategoryEnum.WHEEL.getValue();
                        optionVos = modelLineService.modelLineOption(channel, recommendCarVo.getModelLine().getModelLineId(), category);
                        for(ModelLineOptionVo o : optionVos){
                            if (o.getStatus() != null && o.getStatus().intValue() == 1) {
                                rad = o;
                                break;
                            }
                        }
                    }
                    vos = vos == null ? recommendCarVo.getVosOption() : vos;

                    sib = sib == null ? recommendCarVo.getSibOption() : sib;

                    if (eih == null){
                        String category = OptionCategoryEnum.EIH.getValue();
                        optionVos = modelLineService.modelLineOption(channel, recommendCarVo.getModelLine().getModelLineId(), category);
                        for(ModelLineOptionVo o : optionVos){
                            if (o.getStatus() != null && o.getStatus().intValue() == 1){
                                eih = o;
                                break;
                            }
                        }
                    }


                    CarMeasureMadeConfig measureMadeConfig = new CarMeasureMadeConfig();
                    measureMadeConfig.setMeasureOriginId(i.getMeasureOriginId());
                    measureMadeConfig.setModelLineId(recommendCarVo.getModelLine().getModelLineId());
                    if (color != null) {
                        measureMadeConfig.setColorId(color.getOptionId());
                        measureMadeConfig.setColorCode(color.getOptionCode());
                    }
                    measureMadeConfig.setInteriorId(recommendCarVo.getModelLineSibInterieurVo().getInterieurOptionId());
                    measureMadeConfig.setInteriorCode(recommendCarVo.getModelLineSibInterieurVo().getInterieurOptionCode());
                    measureMadeConfig.setSibInterieurId(recommendCarVo.getModelLineSibInterieurVo().getSibInterieurId());
                    if (vos != null) {
                        measureMadeConfig.setVosCode(vos.getOptionCode());
                        measureMadeConfig.setVosId(vos.getOptionId());
                    }
                    if (sib != null) {
                        measureMadeConfig.setSibCode(sib.getOptionCode());
                        measureMadeConfig.setSibId(sib.getOptionId());
                    }
                    if (eih != null) {
                        measureMadeConfig.setEihId(eih.getOptionId());
                        measureMadeConfig.setEihCode(eih.getOptionCode());
                    }
                    if (rad != null) {
                        measureMadeConfig.setRadId(rad.getOptionId());
                        measureMadeConfig.setRadCode(rad.getOptionCode());
                    }
                    int prFlag = 0;
                    if (CollectionUtils.isNotEmpty(prOptions)) {
                        OptionParamDto optionParamDto = new OptionParamDto();
                        optionParamDto.setModelLineId(recommendCarVo.getModelLine().getModelLineId());
                        optionParamDto.setCategories(Arrays.asList(OptionCategoryEnum.SEET.getValue(), OptionCategoryEnum.SIB.getValue()));
                        optionParamDto.setOptionIds(prOptions.stream().map(o->o.getOptionId()).collect(Collectors.toList()));
                        List<ModelLineOptionVo> items = modelLineService.modelLinePacketItem(optionParamDto);
                        boolean hasVos=false, hasSib=false;

                        if (CollectionUtils.isNotEmpty(items)){
                            Set<String> categories = items.stream().map(o->o.getCategory()).collect(Collectors.toSet());
                            if (categories.contains(OptionCategoryEnum.SEET.getValue())){
                                hasVos = true;
                            }
                            if(categories.contains(OptionCategoryEnum.SIB.getValue())){
                                hasSib = true;
                            }
                        }
                        if (hasSib && hasVos){
                            prFlag = 4;
                        }else if (hasSib){
                            prFlag = 3;
                        }else if (hasVos){
                            prFlag = 2;
                        }
                    }
                    measureMadeConfig.setCreateTime(LocalDateTime.now());
                    measureMadeConfig.setPrFlag(prFlag);
                    measureMadeConfigService.save(measureMadeConfig);
                    for(ModelLineOptionVo o : prOptions){
                        CarMeasureMadeConfigOption measureMadeConfigOption = new CarMeasureMadeConfigOption();
                        measureMadeConfigOption.setMeasureId(measureMadeConfig.getMeasureId());
                        measureMadeConfigOption.setOptionId(o.getOptionId());
                        measureMadeConfigOption.setOptionCode(o.getOptionCode());
                        measureMadeConfigOptionService.save(measureMadeConfigOption);
                    }

                } catch (Exception e) {
                    log.error("半订制数据原始数据处理异常, 配置： "+ JSONObject.toJSONString(i, SerializerFeature.PrettyFormat), e);
                }
            });

        }
    }

    @Override
    @Transactional
    public void syncOmdMeasureSemiCustomizationModel() {
        LambdaUpdateWrapper<CarMeasureMadeOriginConfig> originStockInitU = new LambdaUpdateWrapper<>();
        originStockInitU.set(CarMeasureMadeOriginConfig::getStockNum, 0);
        this.update(originStockInitU);
        omdService.semiCustomizationModel(i->{
            log.info("半订制处理，data: {}", i);
            String classCode = i.getAccbTypeCode().substring(0,2);
            if(i.getPrList() != null){
                if ("G6".equals(classCode)) {
                    if (i.getPrList().startsWith("YEA")){
                        i.setPrList(i.getPrList().length() == 3 ? null : i.getPrList().substring(4));
                    }else {
                        i.setPrList(i.getPrList().replace(",YEA", ""));
                    }
                } else if ("49".equals(classCode)) {
                    if (i.getPrList().startsWith("YEG")){
                        i.setPrList(i.getPrList().length() == 3 ? null : i.getPrList().substring(4));
                    }else {
                        i.setPrList(i.getPrList().replace(",YEG", ""));
                    }
                }
            }
            String prList = i.getPrList() == null ? "" : i.getPrList();
            String uniqueCode = i.getModelCode() + i.getColorCode() + i.getInteriorCode() + prList;
            LambdaQueryWrapper<CarMeasureMadeOriginConfig> originQ = new LambdaQueryWrapper<>();
            originQ.eq(CarMeasureMadeOriginConfig::getAccbTypeCode, i.getAccbTypeCode())
                    .eq(CarMeasureMadeOriginConfig::getModelYear, i.getModelYear())
                    .eq(CarMeasureMadeOriginConfig::getModelVersion, i.getModelVersion())
                    .eq(CarMeasureMadeOriginConfig::getModelCode, i.getModelCode())
                    .eq(CarMeasureMadeOriginConfig::getColorCode, i.getColorCode())
                    .eq(CarMeasureMadeOriginConfig::getInteriorCode, i.getInteriorCode());
            if (i.getPrList() == null){
                originQ.isNull(CarMeasureMadeOriginConfig::getPrList);
            }else {
                originQ.eq(CarMeasureMadeOriginConfig::getPrList, i.getPrList());
            }
            List<CarMeasureMadeOriginConfig> originConfigs = this.list(originQ);
            for (CarMeasureMadeOriginConfig originConfig : originConfigs){
                if (originConfig.getSemiCustomizationModelId() == null){
                    // 以前有的半订制，更新omd记录ID和库存
                    LambdaUpdateWrapper<CarMeasureMadeOriginConfig> originU = new LambdaUpdateWrapper<>();
                    originU.set(CarMeasureMadeOriginConfig::getSemiCustomizationModelId, i.getSemiCustomizationModelId())
                            .set(CarMeasureMadeOriginConfig::getStockNum, i.getStockNum())
                            .set(CarMeasureMadeOriginConfig::getUpdateTime, LocalDateTime.now())
                            .set(CarMeasureMadeOriginConfig::getUniqueCode, uniqueCode)
                            .eq(CarMeasureMadeOriginConfig::getMeasureOriginId, originConfig.getMeasureOriginId());
                    this.update(originU);
                    return ;
                }
            }
            for (CarMeasureMadeOriginConfig originConfig : originConfigs){
                if (originConfig.getSemiCustomizationModelId().compareTo(i.getSemiCustomizationModelId()) == 0){
                    // 已同步omd的半订制，更新库存
                    LambdaUpdateWrapper<CarMeasureMadeOriginConfig> originU = new LambdaUpdateWrapper<>();
                    originU.set(CarMeasureMadeOriginConfig::getStockNum, i.getStockNum())
                            .set(CarMeasureMadeOriginConfig::getUpdateTime, LocalDateTime.now())
                            .set(CarMeasureMadeOriginConfig::getUniqueCode, uniqueCode)
                            .eq(CarMeasureMadeOriginConfig::getMeasureOriginId, originConfig.getMeasureOriginId());
                    this.update(originU);
                    return ;
                }
            }
            // 新增
            CarMeasureMadeOriginConfig originConfig = new CarMeasureMadeOriginConfig();
            BeanUtils.copyProperties(i, originConfig);
            originConfig.setUniqueCode(uniqueCode);
            originConfig.setClassCode(classCode);
            originConfig.setCreateTime(LocalDateTime.now());
            this.save(originConfig);
        });
    }

    @Override
    public void handleMeasureCcUniqueCode() {
        LambdaQueryWrapper<CarMeasureMadeOriginConfig> noCcUniqueQ = new LambdaQueryWrapper<>();
        noCcUniqueQ.isNull(CarMeasureMadeOriginConfig::getCcUniqueCode);
        List<CarMeasureMadeOriginConfig> measureMadeOriginConfigs = this.list(noCcUniqueQ);
        measureMadeOriginConfigs.forEach(i->{
            StringBuilder ccUniqueCode = new StringBuilder();
            ccUniqueCode.append(i.getModelCode()).append("/").append(i.getModelYear()).append("/")
                    .append(i.getModelVersion()).append("/").append(i.getColorCode()).append("/")
                    .append(i.getInteriorCode()).append("/");
            if (i.getPrList() != null){
                String[] prs = i.getPrList().split(",");
                ArrayList<String> prList = new ArrayList<>(Arrays.asList(prs));
                prList.sort(String::compareTo);
                ccUniqueCode.append(Strings.join(prList, ','));
            }
            LambdaUpdateWrapper<CarMeasureMadeOriginConfig> ccUniqueU = new LambdaUpdateWrapper<>();
            ccUniqueU.set(CarMeasureMadeOriginConfig::getUpdateTime, LocalDateTime.now())
                    .set(CarMeasureMadeOriginConfig::getCcUniqueCode, ccUniqueCode.toString())
                    .eq(CarMeasureMadeOriginConfig::getMeasureOriginId, i.getMeasureOriginId());
            this.update(ccUniqueU);
        });
    }

    @Override
    public String ccMatchMeasure(Long ccid) throws Exception {
        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        if (carCustom == null){
            throw new ServiceException("500500", "配置单异常");
        }
        if (carCustom.getBestRecommendId() != null || carCustom.getMeasureId() != null){
            throw new ServiceException("500501", "不能转半定");
        }
        CarCustomDetail customDetail = snapshotService.carConfigOmd(carCustom);
        if (customDetail == null){
            throw new ServiceException("500500", "配置单异常");
        }
        handleDetailToMatch(customDetail);
        List<String> prList = new ArrayList<>();
        String[] modelAndPrCodes = customDetail.getConfigDetail().getCarModel().getModelCode().split("-");
        String modelYear = customDetail.getConfigDetail().getCarModel().getModelYear();
        String modelVersion = customDetail.getConfigDetail().getCarModel().getOmdModelVersion();
        String outColorCode = customDetail.getConfigDetail().getOutsideColor().getColorCode();
        String insideColorCode = customDetail.getConfigDetail().getInsideColor().getColorCode();
        StringBuilder ccUniqueCode = new StringBuilder();
        ccUniqueCode.append(modelAndPrCodes[0]).append("/").append(modelYear).append("/")
                .append(modelVersion).append("/").append(outColorCode).append("/")
                .append(insideColorCode).append("/");
        if (modelAndPrCodes.length>1){
            for (int i=1; i<modelAndPrCodes.length; i++){
                int len = modelAndPrCodes[i].length();
                prList.add(modelAndPrCodes[i].substring(len - 3));
            }
        }
        customDetail.getConfigDetail().getOptionList().forEach(i->prList.add(i.getOptionCode()));
        if (prList.size() > 0){
            prList.sort(String::compareTo);
            ccUniqueCode.append(Strings.join(prList, ','));
        }
        LambdaQueryWrapper<CarMeasureMadeOriginConfig> originQ = new LambdaQueryWrapper<>();
        originQ.eq(CarMeasureMadeOriginConfig::getCcUniqueCode, ccUniqueCode.toString()).gt(CarMeasureMadeOriginConfig::getStockNum, 0);
        List<CarMeasureMadeOriginConfig> originConfigs = this.list(originQ);
        if (originConfigs.size()> 0){
            for (CarMeasureMadeOriginConfig originConfig : originConfigs){
                LambdaQueryWrapper<CarMeasureMadeConfig> measureQ = new LambdaQueryWrapper<>();
                measureQ.eq(CarMeasureMadeConfig::getMeasureOriginId, originConfig.getMeasureOriginId());
                CarMeasureMadeConfig measureMadeConfig = measureMadeConfigService.getOne(measureQ);
                if (measureMadeConfig != null){
                    String key = UUID.randomUUID().toString();
                    log.info("高定转半定查询：{} , key: {}", originConfig, key);
                    String redisKey =  measureKeyPrefix + key;
                    RBucket<ConfigMeasureDto> bucket = redissonClient.getBucket(redisKey);
                    bucket.set(new ConfigMeasureDto(ccid, measureMadeConfig.getMeasureId()), 12, TimeUnit.HOURS);
                    return key;
                }
            }
        }
        throw new ServiceException("500502", "没有相关半订制");
    }

    private void handleDetailToMatch(CarCustomDetail customDetail) {
        // 匹配时去掉权益包
        String seriesCode = customDetail.getConfigDetail().getCarSeries().getSeriesCode();
        List<Option> options = customDetail.getConfigDetail().getOptionList();
        Iterator<Option> it = options.iterator();
        if ("G6".equals(seriesCode) && CollectionUtils.isNotEmpty(options)){
            while (it.hasNext()){
                Option option = it.next();
                if (OptionCategoryEnum.PACKET.getValue().equals(option.getOptionClassification())
                        && ("CCPRO-YEB".equals(option.getOptionCode()) || "YEA".equals(option.getOptionCode()))){
                    it.remove();
                }
            }
        }else if ("49".equals(seriesCode) && CollectionUtils.isNotEmpty(options)){
            while (it.hasNext()){
                Option option = it.next();
                if (OptionCategoryEnum.PACKET.getValue().equals(option.getOptionClassification())
                        && ("CCPRO-YEB".equals(option.getOptionCode()) || "YEG".equals(option.getOptionCode()))){
                    it.remove();
                }
            }
        }
    }

    @Override
    public void normalConfigToMeasure(String userId, String key) throws Exception {
        String redisKey =  measureKeyPrefix + key;
        RBucket<ConfigMeasureDto> bucket = redissonClient.getBucket(redisKey);
        ConfigMeasureDto measureDto = bucket.get();
        if (measureDto == null){
            throw new ServiceException("500500", "invalid key");
        }
        log.info("高定转半定操作：{} , key: {}", measureDto, key);
        LambdaUpdateWrapper<CarCustom> ccQ = new LambdaUpdateWrapper<>();
        ccQ.eq(CarCustom::getCcid, measureDto.getCcid())
                .set(CarCustom::getMeasureId, measureDto.getMeasureId())
                .set(CarCustom::getDepositType, DepositTypeEnum.STOCK.getType());
        carCustomService.update(ccQ);
        CarCustom carCustom = new CarCustom();
        carCustom.setCcid(measureDto.getCcid());
        customSnapshotService.snapshotCarCustom(carCustom, LocalDateTime.now());
        bucket.delete();
    }

    @Override
    public void handleMeasureZhuMengYef() {
        LambdaQueryWrapper<CarMeasureMadeOriginConfig> originQ = new LambdaQueryWrapper<>();
        originQ.eq(CarMeasureMadeOriginConfig::getAccbTypeCode, "498BZG").eq(CarMeasureMadeOriginConfig::getDelFlag, 0);
        List<CarMeasureMadeOriginConfig> originConfigs = this.list(originQ);
        if (originConfigs == null || originConfigs.size()<1){
            return;
        }
        originConfigs.forEach(i->{
            String code = "YEF";
            if (StringUtils.isNotBlank(i.getPrList())){
                code = ",YEF";
            }
            String prList = i.getPrList();
            if (i.getPrList() == null){
                prList = "";
            }
            LambdaQueryWrapper<CarMeasureMadeOriginConfig> newOriginQ = new LambdaQueryWrapper<>();
            newOriginQ.eq(CarMeasureMadeOriginConfig::getCcUniqueCode, i.getCcUniqueCode()+code);
            CarMeasureMadeOriginConfig originConfig = this.getOne(newOriginQ);
            if (originConfig == null){
                originConfig = new CarMeasureMadeOriginConfig();
                BeanUtils.copyProperties(i, originConfig);
                originConfig.setMeasureOriginId(null);
                originConfig.setSemiCustomizationModelId(null);
                originConfig.setAccbTypeCode(i.getAccbTypeCode()+"-GYEFYEF");
                originConfig.setPrList(prList+code);
                originConfig.setUniqueCode(i.getUniqueCode()+code);
                originConfig.setCcUniqueCode(i.getCcUniqueCode()+code);
                originConfig.setCreateTime(LocalDateTime.now());
                this.save(originConfig);
            }else {
                originConfig.setStockNum(i.getStockNum());
                this.updateById(originConfig);
            }
        });

    }

    @Override
    public CarMeasureMadeOriginConfig measureOriginConfigByMeasureId(Long measureId) {
        return originConfigMapper.measureOriginConfigByMeasureId(measureId);
    }
}
