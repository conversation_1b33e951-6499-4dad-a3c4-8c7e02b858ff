package com.csvw.audi.cc.common;

public class CopOrderSql {
    public final static String QUERY_ORDER_BY_CCID ="select om.`car_custom_id` , om.`order_id` , om.order_status, sl.`sync_status`   from order_main as om join `omd_nga_sync_log` as sl on sl.rel_id = om.`order_id`  \n" +
            "where sl.`rel_type`  = 1 and om.`car_custom_id`  = ? and (sl.`sync_status`  = 2 or sl.`sync_status` IS NULL ) group by sl.`rel_id`";
}
