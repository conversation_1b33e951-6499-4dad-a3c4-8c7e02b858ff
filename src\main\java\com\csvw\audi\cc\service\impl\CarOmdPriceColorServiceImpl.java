package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.entity.dto.PriceCondition;
import com.csvw.audi.cc.entity.po.CarOmdPriceColor;
import com.csvw.audi.cc.mapper.CarOmdPriceColorMapper;
import com.csvw.audi.cc.service.ICarOmdPriceColorService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <p>
 * 外饰价格 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-18
 */
@Service
public class CarOmdPriceColorServiceImpl extends ServiceImpl<CarOmdPriceColorMapper, CarOmdPriceColor> implements ICarOmdPriceColorService {

    @Autowired
    private CarOmdPriceColorMapper priceColorMapper;

    @Override
    public BigDecimal getPrice(PriceCondition condition) {
        return priceColorMapper.getPrice(condition);
    }
}
