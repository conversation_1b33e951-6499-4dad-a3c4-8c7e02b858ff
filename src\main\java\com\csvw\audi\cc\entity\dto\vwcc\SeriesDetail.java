package com.csvw.audi.cc.entity.dto.vwcc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SeriesDetail {
    private String brandCode;
    private String categoryId;
    private String categoryName; //
    private String categoryWeight; //
    private String classCode;
    private String configStepList;
    private String customSeriesCode;
    @ApiModelProperty("车系id")
    private String customSeriesId;
    private String imageUrl;
    private String isVirtualSeries;
    private String modelYear;
    private String overviewImageUrl;
    @ApiModelProperty("车系编码")
    private String seriesCode;
    @ApiModelProperty("车系名称")
    private String seriesNameCn;
    private String seriesNameEn;
    private String seriesWeight;
    private String omdSeriesCode;
}
