package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.config.AppConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.StringUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@Service
@Slf4j
public class SignService {

    @Autowired
    private AppConfig appConfig;

    public String signPostData(String inputXAppId, String inputSecret, String xSequenceNo, String xTimestamp, String bodyStr){
        List<String> paramSignTextList = new ArrayList<>();
        paramSignTextList.add("Body=" + Base64.getEncoder().encodeToString(StringUtils.getBytesUtf8(bodyStr)));
        paramSignTextList.add("X-App-Id=" + inputXAppId);
        paramSignTextList.add("X-Sequence-No=" + xSequenceNo);
        paramSignTextList.add("X-Timestamp=" + xTimestamp);
        paramSignTextList.add(inputSecret);
        String paramSignText = String.join("&", paramSignTextList);
        paramSignText = Base64.getEncoder().encodeToString(DigestUtils.sha256(paramSignText));
        return paramSignText;
    }

    public String signPostData(String xSequenceNo, String xTimestamp, String bodyStr){
        String signature = this.signPostData(appConfig.getInfra().getAppId(), appConfig.getInfra().getAppSecret(), xSequenceNo, xTimestamp, bodyStr);
        log.info("infra gateway sign, X-Sequence-No: {} , X-Timestamp: {} , X-Signature: {} , body: {}", xSequenceNo, xTimestamp, signature, bodyStr);
        return signature;
    }

    public String signQueryData(String inputXAppId, String inputSecret, String xSequenceNo, String xTimestamp){
        List<String> paramSignTextList = new ArrayList<>();
        paramSignTextList.add("X-App-Id=" + inputXAppId);
        paramSignTextList.add("X-Sequence-No=" + xSequenceNo);
        paramSignTextList.add("X-Timestamp=" + xTimestamp);
        paramSignTextList.add(inputSecret);
        String paramSignText = String.join("&", paramSignTextList);
        paramSignText = Base64.getEncoder().encodeToString(DigestUtils.sha256(paramSignText));
        return paramSignText;
    }

    public String signQueryData(String xSequenceNo, String xTimestamp){
        return this.signQueryData(appConfig.getInfra().getAppId(), appConfig.getInfra().getAppSecret(), xSequenceNo, xTimestamp);
    }

    public String getTimestamp(){
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
    }

    public String getSequence(String timestamp){
        return timestamp + RandomStringUtils.randomNumeric(5);
    }
}
