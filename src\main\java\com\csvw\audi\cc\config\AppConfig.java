package com.csvw.audi.cc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;

/**
 * 应用配置类
 *
 * <AUTHOR>
 * @date 2018/9/5
 */
@Data
@RefreshScope
@Component
@ConfigurationProperties(prefix = "audi-car-config")
public class AppConfig {
    private AccbConf accb;
    private Infra infra;
    private CarConfig cc;
    private String hqDealerCode;
    private BestRecommendConfig bestRecommend;
    private ElasticSearchConfig es;

    @Data
    public static class ElasticSearchConfig{
        private String host;
    }

    @Data
    public static class BestRecommendConfig{
        private Boolean filterEnable;
        private List<String> modelLineIds;
        private List<String> customSeriesIds;
    }

    @Data
    public static class CarConfig{
        private String delivery;
        private String q5eDelivery;
    }

    @Data
    public static class AccbConf{
        private String host;
        private String url;
        private String key;
        private String secret;
        private String lang;
        private String product;
    }

    @Data
    public static class Infra{
        private String url;
        private String appId;
        private String appSecret;
        private String amsAesKey;
        private String amsAesIv;
    }
}
