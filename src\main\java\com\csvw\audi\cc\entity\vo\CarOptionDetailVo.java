package com.csvw.audi.cc.entity.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class CarOptionDetailVo implements Serializable {
    private String optionDetailId;

    @ApiModelProperty(value = "配置项细节描述")
    private String optionDesc;

    @ApiModelProperty(value = "装备图片")
    private String imageUrl;

    @ApiModelProperty(value = "数据渠道")
    private String channel;

    @JsonIgnore
    private Integer delFlag;

    @JsonIgnore
    private String optionId;
}
