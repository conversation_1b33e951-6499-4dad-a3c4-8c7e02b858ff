package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.dto.ModelLineTypeDto;
import com.csvw.audi.cc.entity.dto.ModelTypeResultDto;
import com.csvw.audi.cc.entity.dto.TypePrListQueryDto;
import com.csvw.audi.cc.entity.po.CarModelLineType;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.po.CarOmdVehicleType;
import com.csvw.audi.cc.entity.vo.ModelLineVo;
import com.csvw.audi.cc.entity.vo.PersonalOptionVo;
import com.csvw.audi.cc.entity.vo.TypeIdsBySeats;
import com.csvw.audi.cc.exception.ServiceException;
import java.time.LocalDateTime;

import java.util.List;

/**
 * <p>
 * 车辆ABC类配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
public interface ICarModelLineTypeService extends IService<CarModelLineType> {

    String findBeforeCheckType(ModelLineTypeDto modelLineTypeDto);

    ModelTypeResultDto findType(ModelLineTypeDto modelLineTypeDto);

    List<PersonalOptionVo.PersonalCompose> queryPersonalComposes(TypePrListQueryDto queryDto);

    boolean filterOption(TypePrListQueryDto o);

    boolean filterInterOption(TypePrListQueryDto queryDto);

    void convertOmdAbcData() throws ServiceException;

    void handleOmdVehicleType(CarOmdVehicleType vehicleType) throws Exception;

    void updateModelLineTypeFlagByABC(Integer classifyVersion) throws Exception;

    void exchangeOmdAbcData(LocalDateTime lastTime);

    List<TypeIdsBySeats> listTypeIdsBySeats(ModelLineVo modelLineVo);
}
