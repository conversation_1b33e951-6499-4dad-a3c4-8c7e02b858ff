<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.SvcdRolesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.SvcdRoles">
        <id column="roles_id" property="rolesId" />
        <result column="user_id" property="userId" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="status" property="status" />
        <result column="sort" property="sort" />
        <result column="remark" property="remark" />
        <result column="client_id" property="clientId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        roles_id, user_id, name, code, status, sort, remark, client_id
    </sql>

</mapper>
