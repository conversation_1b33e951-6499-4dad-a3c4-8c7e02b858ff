package com.csvw.audi.cc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csvw.audi.cc.entity.po.CarCustomOption;
import com.csvw.audi.cc.mapper.CarCustomOptionMapper;
import com.csvw.audi.cc.service.ICarCustomOptionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Service
public class CarCustomOptionServiceImpl extends ServiceImpl<CarCustomOptionMapper, CarCustomOption> implements ICarCustomOptionService {

    @Override
    public List<CarCustomOption> listByCcid(Long ccid) {
        LambdaQueryWrapper<CarCustomOption> oQ = new LambdaQueryWrapper<>();
        oQ.eq(CarCustomOption::getCcid, ccid);
        return this.list(oQ);
    }
}
