package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 渠道商组织信息-相关文件
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SvcdChannelOrganizationFile对象", description="渠道商组织信息-相关文件")
public class SvcdChannelOrganizationFile extends Model<SvcdChannelOrganizationFile> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "数据id")
      @TableId(value = "channel_organization_file_id", type = IdType.AUTO)
    private Long channelOrganizationFileId;

    @ApiModelProperty(value = "渠道商组织信息id")
    private Long channelOrganizationId;

    @ApiModelProperty(value = "渠道商编码")
    private String dealerCode;

    @ApiModelProperty(value = "文件名")
    @TableField("`file_name`")
    private String name;

    @ApiModelProperty(value = "文件地址")
    @TableField("`file_url`")
    private String url;

    @ApiModelProperty(value = "文件类型(1:平面图纸 2:意向申请书及附件详情 3:带条件验收申请报告 4:上汽奥迪服务牌现场照片 5:验收审批文件 6:授权合同及附件详情 7:图片简介)")
    @TableField("`file_type`")
    private String type;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;

    @ApiModelProperty(value = "接收地址")
    private String oldUrl;

    @Override
    protected Serializable pkVal() {
        return this.channelOrganizationFileId;
    }

}
