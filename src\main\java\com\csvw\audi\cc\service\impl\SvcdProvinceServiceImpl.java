package com.csvw.audi.cc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csvw.audi.cc.entity.dto.DealerDto;
import com.csvw.audi.cc.entity.dto.SvcdProvinceDto;
import com.csvw.audi.cc.entity.po.SvcdProvince;
import com.csvw.audi.cc.entity.vo.DealerVo;
import com.csvw.audi.cc.entity.vo.SvcdProvinceVo;
import com.csvw.audi.cc.mapper.SvcdProvinceMapper;
import com.csvw.audi.cc.service.ISvcdChannelOrganizationService;
import com.csvw.audi.cc.service.ISvcdProvinceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 省份信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Service
public class SvcdProvinceServiceImpl extends ServiceImpl<SvcdProvinceMapper, SvcdProvince> implements ISvcdProvinceService {

    @Autowired
    private SvcdProvinceMapper svcdProvinceMapper;

    @Autowired
    private ISvcdChannelOrganizationService svcdChannelOrganizationService;

    @Override
    public List<SvcdProvinceVo> getProvinceList(SvcdProvinceDto provinceDto) {
        //默认查询存在代理商的
        if(provinceDto.getSearchAll() == null) {
            provinceDto.setSearchAll(3);
        }

        QueryWrapper<SvcdProvince> queryWrapper = new QueryWrapper<>();
        if(provinceDto.getSearchAll() == 1) {
            //查询全部省份数据
        } else if(provinceDto.getSearchAll() == 2) {
            //查询存在代理商或服务商的省份数据
            DealerDto dealerDto = new DealerDto();
            dealerDto.setHaveImage(0);
            List<DealerVo> dealerVoList = svcdChannelOrganizationService.getOrgList(dealerDto);
            Set<String> provinceCodes = dealerVoList.stream().map(i->i.getProvinceCode()).collect(Collectors.toSet());
            queryWrapper.in("province_code",new ArrayList<>(provinceCodes));
        } else if(provinceDto.getSearchAll() == 3) {
            //取存在代理商的省份
            DealerDto dealerDto = new DealerDto();
            dealerDto.setHaveImage(0);
            List<DealerVo> dealerVoList = svcdChannelOrganizationService.getAgentList(dealerDto);
            Set<String> provinceCodes = dealerVoList.stream().map(i->i.getProvinceCode()).collect(Collectors.toSet());
            queryWrapper.in("province_code",new ArrayList<>(provinceCodes));
        }
        queryWrapper.isNotNull("quick_code").orderByAsc("quick_code");
        List<SvcdProvince> list = svcdProvinceMapper.selectList(queryWrapper);
        List<SvcdProvinceVo> voList = new ArrayList<>(list.size());
        SvcdProvinceVo svcdProvinceVo;
        for(SvcdProvince province : list) {
            svcdProvinceVo = new SvcdProvinceVo();
            svcdProvinceVo.setProvinceCode(String.valueOf(province.getProvinceCode()));
            svcdProvinceVo.setName(province.getName());
            svcdProvinceVo.setSimpleName(province.getSimpleName());
            svcdProvinceVo.setQuickCode(province.getQuickCode());
            svcdProvinceVo.setProvinceEn(province.getProvinceEn());
            svcdProvinceVo.setCapInitials(province.getQuickCode().length()>1?province.getQuickCode().substring(0,1):province.getQuickCode());
            voList.add(svcdProvinceVo);
        }
        return voList;
    }

    @Override
    public List<SvcdProvinceVo> getProvinceListByOfficialWebsite(SvcdProvinceDto provinceDto) {
        QueryWrapper<SvcdProvince> queryWrapper = new QueryWrapper<>();
        if(provinceDto.getSearchAll() != null && provinceDto.getSearchAll() == 1 ) {
            //查询全部省份数据
        } else {
            //取存在渠道商的省份
            DealerDto dealerDto = new DealerDto();
            dealerDto.setHaveImage(0);
            dealerDto.setSearchType(provinceDto.getSearchType()==null?0:provinceDto.getSearchType());
            List<DealerVo> dealerVoList = svcdChannelOrganizationService.getDealerListByOfficialWebsite(dealerDto);
            Set<String> provinceCodes = dealerVoList.stream().map(i->i.getProvinceCode()).collect(Collectors.toSet());
            queryWrapper.in("province_code",new ArrayList<>(provinceCodes));
        }
        queryWrapper.isNotNull("quick_code").orderByAsc("quick_code");
        List<SvcdProvince> list = svcdProvinceMapper.selectList(queryWrapper);
        List<SvcdProvinceVo> voList = new ArrayList<>(list.size());
        SvcdProvinceVo svcdProvinceVo;
        for(SvcdProvince province : list) {
            svcdProvinceVo = new SvcdProvinceVo();
            svcdProvinceVo.setProvinceCode(String.valueOf(province.getProvinceCode()));
            svcdProvinceVo.setName(province.getName());
            svcdProvinceVo.setSimpleName(province.getSimpleName());
            svcdProvinceVo.setQuickCode(province.getQuickCode());
            svcdProvinceVo.setProvinceEn(province.getProvinceEn());
            svcdProvinceVo.setCapInitials(province.getQuickCode().length()>1?province.getQuickCode().substring(0,1):province.getQuickCode());
            voList.add(svcdProvinceVo);
        }
        return voList;
    }
}
