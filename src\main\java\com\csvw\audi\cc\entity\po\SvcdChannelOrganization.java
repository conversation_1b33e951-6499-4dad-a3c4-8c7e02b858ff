package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.util.StringUtils;

/**
 * <p>
 * 渠道商组织信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SvcdChannelOrganization对象", description="渠道商组织信息")
public class SvcdChannelOrganization extends Model<SvcdChannelOrganization> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "数据id")
      @TableId(value = "channel_organization_id", type = IdType.AUTO)
    private Long channelOrganizationId;

    @ApiModelProperty(value = "渠道商网络代码")
    private String dealerCode;

    @ApiModelProperty(value = "渠道商网络名称")
    private String dealerName;

    @ApiModelProperty(value = "渠道商简称")
    private String simpleName;

    @ApiModelProperty(value = "渠道商英文名称")
    private String dealerEnName;

    @ApiModelProperty(value = "渠道商类型(1:售后服务商 2:销售代理商 3：大客户)")
    private Long dealerType;

    @ApiModelProperty(value = "省份代码")
    private String provinceCode;

    @ApiModelProperty(value = "城市代码")
    private String cityCode;

    @ApiModelProperty(value = "区县代码")
    private String areaCode;

    @ApiModelProperty(value = "邮编")
    private String postCode;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "营业地址")
    private String operateAddress;

    @ApiModelProperty(value = "批复通知日期")
    private String replyDate;

    @ApiModelProperty(value = "意向日期")
    private String agreeDate;

    @ApiModelProperty(value = "组织机构验收日期")
    private String orgAcceptDate;

    @ApiModelProperty(value = "入网日期")
    private String injoyDate;

    @ApiModelProperty(value = "合同日期")
    private String contractDate;

    @ApiModelProperty(value = "意向取消日期")
    private String agreeCancelDate;

    @ApiModelProperty(value = "出网受理日期")
    private String outAcceptDate;

    @ApiModelProperty(value = "出网撤销日期")
    private String outRevokeDate;

    @ApiModelProperty(value = "出网日期")
    private String outDate;

    @ApiModelProperty(value = "业务状态（1：意向，2：筹备，3：开业，4：出网，5：PopUP，6：试运营，7：意向终止）")
    private String businessStatus;

    @ApiModelProperty(value = "销售热线")
    private String salesPhone;

    @ApiModelProperty(value = "24小时热线")
    private String hotPhone24;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "联系人")
    private String contact;

    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    @ApiModelProperty(value = "公司代码")
    private String companyCode;

    @ApiModelProperty(value = "投资人代码")
    private String investorCode;

    @ApiModelProperty(value = "是否删除")
    private Long deleted;

    @ApiModelProperty(value = "渠道商所在组织区域代码")
    private String regionCode;

    @ApiModelProperty(value = "联系地址")
    private String contactAddr;

    @ApiModelProperty(value = "工作日营业时间")
    private String workingDay;

    @ApiModelProperty(value = "非工作日营业时间")
    private String nonWorkingDay;

    @ApiModelProperty(value = "服务营业时间备注")
    private String serviceWorkingDayRemarks;

    @ApiModelProperty(value = "工作日服务营业时间")
    private String serviceWorkingDay;

    @ApiModelProperty(value = "非工作日服务营业时间")
    private String nonServiceWorkingDay;

    @ApiModelProperty(value = "评价分数")
    private String evaluateScore;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "售后代码")
    private String serviceCode;

    @ApiModelProperty(value = "开票代码")
    private String dealerBillCode;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;

    @TableField(exist = false)
    private List<SvcdOwnershipStructureBos> ownershipStructureBOS;//股权信息

    @TableField(exist = false)
    private List<SvcdChannelOrganizationFile> fileList;//文件信息

    @TableField(exist = false)
    private SvcdChannelOrganizationPrimary orgPrimary;//区域商务经理

    @TableField(exist = false)
    private List<SvcdChannelOrganizationPolicy> policyList;//政策信息

    @ApiModelProperty(value = "代理商推荐码")
    private String remCode;

    @Override
    protected Serializable pkVal() {
        return this.channelOrganizationId;
    }

    @Override
    public boolean insert(){
        if(StringUtils.isEmpty(this.remCode)){
            this.remCode = RandomStringUtils.randomAlphanumeric(6).toUpperCase();
        }
        return super.insert();
    }
}
