package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.dto.PriceCondition;
import com.csvw.audi.cc.entity.po.CarOmdPriceColor;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;

/**
 * <p>
 * 外饰价格 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-18
 */
public interface ICarOmdPriceColorService extends IService<CarOmdPriceColor> {

    BigDecimal getPrice(PriceCondition condition);
}
