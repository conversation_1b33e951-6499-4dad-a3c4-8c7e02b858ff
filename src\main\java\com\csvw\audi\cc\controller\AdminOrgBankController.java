package com.csvw.audi.cc.controller;


import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.dto.AdminOrgBankDto;
import com.csvw.audi.cc.entity.dto.Pageable;
import com.csvw.audi.cc.entity.po.AdminOrgBank;
import com.csvw.audi.cc.entity.vo.AdminOrgBankVo;
import com.csvw.audi.cc.entity.vo.ImportRes;
import com.csvw.audi.cc.service.IAdminOrgBankService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * <p>
 * 渠道商对应的金融机构 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-17
 */
@Slf4j
@Api(tags = "渠道商对应的金融机构")
@RestController
@RequestMapping("/admin/api/v1/orgBank")
public class AdminOrgBankController extends BaseController {

    @Autowired
    private IAdminOrgBankService adminOrgBankService;

    @ApiOperation("导入数据")
    @PostMapping(value = "/data/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public AjaxMessage<ImportRes> dataImport(@RequestHeader(value = "X-User-Id") String userId, @RequestBody MultipartFile file) throws IOException {
        ImportRes importRes = null;
        InputStream inputStream = file.getInputStream();
        try {
            importRes = adminOrgBankService.dataImport(userId,inputStream);
        } catch (Exception e) {
            log.error("导入失败", e);
            return new AjaxMessage<>("01","失败",null);
        }

        return new AjaxMessage<>("00","成功",importRes);
    }

    @ApiOperation("查询列表")
    @GetMapping("/getOrgBankList")
    public AjaxMessage<PageInfo<AdminOrgBankVo>> getOrgBankList(AdminOrgBankDto dto, Pageable pageable) {
        pageable = new Pageable(pageable);
        PageHelper.startPage(pageable.getPageNum(), pageable.getPageSize());
        List<AdminOrgBankVo> list =  adminOrgBankService.getOrgBankList(dto);
        PageInfo<AdminOrgBankVo> pageInfo = new PageInfo<>(list);
        return new AjaxMessage<>("00", "成功", pageInfo);
    }

    @ApiOperation("删除数据")
    @PostMapping("/delOrgBankByIds")
    public AjaxMessage<String> delOrgBankByIds(@RequestBody AdminOrgBankDto dto) {
        if(dto.getOrgBankIds() == null || dto.getOrgBankIds().isEmpty()) {
            return new AjaxMessage<>("01","失败","id列表为空");
        }
        adminOrgBankService.delOrgBankByIds(dto.getOrgBankIds());
        return new AjaxMessage<>("00","成功","");
    }

    @ApiOperation("金融机构列表")
    @GetMapping("/getBankList")
    public AjaxMessage<List<AdminOrgBank>> getBankList() {
        List<AdminOrgBank> list =  adminOrgBankService.getBankList();
        return new AjaxMessage<>("00", "成功", list);
    }

}

