package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 特定配置线配置
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarModelLineSpecialOption对象", description="特定配置线配置")
public class CarModelLineSpecialOption extends Model<CarModelLineSpecialOption> {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "配置线id")
    private String modelLineId;

    @ApiModelProperty(value = "配置id")
    private String optionId;

    @ApiModelProperty(value = "配置编码")
    private String optionCode;

    @ApiModelProperty(value = "配置分类")
    private String category;

    @ApiModelProperty(value = "权重")
    private Integer weight;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
