package com.csvw.audi.cc.entity.dto.okapi;

import lombok.Data;

import java.util.List;

@Data
public class EquipmentGroupResDto {
    private String brandCode;
    private String subBrandCode;
    private String modelUnicode;
    private String modelUnicodeShort;
    private List<GroupResDto> children;

    @Data
    public static class GroupResDto {
        private String labelCode;
        private String labelNameZh;
        private String labelValue;
        private String labelValueNameZh;
        private String featurePrice;
        private String equipmentRights;
        private List<MaterialInfo> materialList;
        private List<LabelResDto> labelChildren;
    }

    @Data
    public static class LabelResDto {
        private List<String> packetList;
        private String familyCode;
        private String featureCode;
        private String featureStatusCode;
        private String featurePrice;
        private String externalFeatureNameZh;
        private String externalFeatureNameEn;
        private List<MaterialInfo> materialList;
    }

    @Data
    public static class MaterialInfo{
        private String materialName;
        private String materialType;
        private String materialUrl;
        private String materialDesc;
        private String materialRange;
        private String materialSize;
        private String materialHeight;
        private String materialWidth;
    }

}
