package com.csvw.audi.cc.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.common.utils.LoanUtils;
import com.csvw.audi.cc.common.utils.StringUtil;
import com.csvw.audi.cc.entity.dto.LoanParam;
import com.csvw.audi.cc.entity.dto.RentParam;
import com.csvw.audi.cc.entity.po.LoanRule;
import com.csvw.audi.cc.entity.vo.LoanRuleVo;
import com.csvw.audi.cc.entity.vo.LoanVo;
import com.csvw.audi.cc.entity.vo.RentVo;
import com.csvw.audi.cc.service.ILoanRuleService;
import io.swagger.annotations.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.client.support.InterceptingHttpAccessor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-18
 */
@RestController
@RequestMapping("/api/v1/loanRule")
@Api(tags = "贷款计算")
public class LoanRuleController extends BaseController {

    @Autowired
    ILoanRuleService loanRuleService;

    @GetMapping("/list")
    @ApiOperation(value = "贷款规则列表")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "type", value = "规则类型：1:贷款，2:融资租赁零首付，3:融资租赁尾款")})
    public AjaxMessage<List<LoanRuleVo>> loanRules(String dealerCode, String modelLineId, Integer type){
        if (StringUtil.isEmpty(modelLineId)){
            modelLineId = "5dd76400-eef9-4c37-a7a2-df6e4432448a";
        }
        if (type == null) {
            type = 1;
        }
        List<LoanRuleVo> vos = loanRuleService.getLoanRules(dealerCode, modelLineId, type);
        return successMessage(vos);
    }

    @GetMapping("/compute")
    @ApiOperation("贷款计算")
    public AjaxMessage<LoanVo> compute(LoanParam param){
        LambdaQueryWrapper<LoanRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LoanRule::getLoanRuleId, param.getLoanRuleId()).eq(LoanRule::getDelFlag, 0);
        LoanRule rule = loanRuleService.getOne(queryWrapper);
        if (rule == null){
            return new AjaxMessage<>("40004", "参数异常：loanRuleId",null);
        }
        JSONObject interestRates = JSONObject.parseObject(rule.getInterestRates());
        JSONArray ratesJSONArray = interestRates.getJSONArray(param.getMonth());
        if (ratesJSONArray == null){
            return new AjaxMessage<>("40004", "参数异常：month",null);
        }
        Double ratio = null;
        BigDecimal minRatio = null;
        for(int i=0; i<ratesJSONArray.size(); i++){
            JSONObject rate = ratesJSONArray.getJSONObject(i);
            if (param.getPaymentRatio().compareTo(rate.getBigDecimal("minPaymentRatio")) >= 0 &&
                    param.getPaymentRatio().compareTo(rate.getBigDecimal("maxPaymentRatio")) <= 0
            ){
                ratio = rate.getDouble("investRate");
                minRatio = rate.getBigDecimal("minPaymentRatio");
            }
        }
        if (ratio == null){
            return new AjaxMessage<>("40004", "参数异常：paymentRatio",null);
        }
        if (param.getPrice() == null){
            return new AjaxMessage<>("40004", "参数异常：price",null);
        }
        LoanVo vo = new LoanVo();
        BeanUtils.copyProperties(param, vo);
        int N = Integer.valueOf(param.getMonth());
        double R = ratio/100/12;
        double loan;
        if (param.getPayment() != null){
            BigDecimal paymentRatio = param.getPayment().divide(param.getPrice()).multiply(BigDecimal.valueOf(100));
            if (paymentRatio.compareTo(minRatio) < 0){
                return new AjaxMessage<>("40004", "参数异常：paymentRatio",null);
            }
            loan = param.getPrice().subtract(param.getPayment()).doubleValue();
            vo.setPaymentRatio(paymentRatio);
            vo.setLoanRatio(BigDecimal.valueOf(100).subtract(paymentRatio));
        }else {
            if (param.getPaymentRatio() == null || param.getPaymentRatio().compareTo(minRatio) < 0){
                return new AjaxMessage<>("40004", "参数异常：paymentRatio",null);
            }
            BigDecimal payment = param.getPrice().multiply(param.getPaymentRatio()).divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
            loan = param.getPrice().subtract(payment).doubleValue();
            vo.setPayment(payment);
            vo.setPaymentRatio(param.getPaymentRatio());
        }
        double A = loan / N;
        if ( R != 0){
            A = loan*R*Math.pow(1+R,N)/(Math.pow(1+R,N)-1);
        }
        vo.setInterestRate(ratio.toString());
        vo.setMonthPay(BigDecimal.valueOf(A).setScale(2, RoundingMode.HALF_UP));
        vo.setLoan(BigDecimal.valueOf(loan));

        return successMessage(vo);
    }

    @GetMapping("/rentCompute")
    @ApiOperation("融资租赁计算")
    public AjaxMessage<RentVo> RentCompute(RentParam param){
        LambdaQueryWrapper<LoanRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LoanRule::getLoanRuleId, param.getLoanRuleId());
        LoanRule rule = loanRuleService.getOne(queryWrapper);
        if (rule == null || rule.getType() == null || !Arrays.asList(2, 3).contains(rule.getType().intValue())){
            return new AjaxMessage<>("40004", "参数异常：loanRuleId",null);
        }
        JSONObject interestRates = JSONObject.parseObject(rule.getInterestRates());
        JSONArray ratesJSONArray = interestRates.getJSONArray(param.getMonth());
        if (ratesJSONArray == null){
            return new AjaxMessage<>("40004", "参数异常：month",null);
        }
        BigDecimal ratio = null;
        BigDecimal minRatio = null;
        BigDecimal balanceRatio = null;
        BigDecimal discountRatio = null;
        for(int i=0; i<ratesJSONArray.size(); i++){
            JSONObject rate = ratesJSONArray.getJSONObject(i);
            if (param.getPaymentRatio().compareTo(rate.getBigDecimal("minPaymentRatio")) >= 0 &&
                    param.getPaymentRatio().compareTo(rate.getBigDecimal("maxPaymentRatio")) <= 0
            ){
                ratio = rate.getBigDecimal("investRate");
                minRatio = rate.getBigDecimal("minPaymentRatio");
                balanceRatio = rate.getBigDecimal("balanceRatio");
                discountRatio = rate.getBigDecimal("discountRatio").divide(BigDecimal.valueOf(100));
            }
        }
        if (ratio == null){
            return new AjaxMessage<>("40004", "参数异常：paymentRatio",null);
        }
        if (param.getPrice() == null){
            return new AjaxMessage<>("40004", "参数异常：price",null);
        }
        RentVo vo = new RentVo();
        BeanUtils.copyProperties(param, vo);

        BigDecimal payment = null;
        if (param.getPaymentRatio() != null) {
            if (param.getPaymentRatio() == null || param.getPaymentRatio().compareTo(minRatio) < 0){
                return new AjaxMessage<>("40004", "参数异常：paymentRatio",null);
            }
            payment = param.getPrice().multiply(param.getPaymentRatio().divide(BigDecimal.valueOf(100))).setScale(2, RoundingMode.HALF_UP);
            vo.setPayment(payment);
            vo.setPaymentRatio(param.getPaymentRatio());
        }
        BigDecimal balance = param.getPrice().multiply(balanceRatio.divide(BigDecimal.valueOf(100))).setScale(0, RoundingMode.HALF_UP);
        vo.setBalance(balance);
        vo.setBalanceRatio(balanceRatio);
        BigDecimal loanAmount =  param.getPrice().subtract(payment);
        BigDecimal discountAmount = discountRatio.multiply(loanAmount).setScale(3, RoundingMode.HALF_UP);

        double rate = ratio.divide(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(12), 20, RoundingMode.HALF_UP).doubleValue();
        int pv = discountAmount.subtract(loanAmount).setScale(0, RoundingMode.HALF_UP).intValue();
        int nper;
        double fv;
        if (balanceRatio.compareTo(BigDecimal.ZERO) == 0){
            //  零首租
            nper = Integer.valueOf(param.getMonth());
            fv = balance.doubleValue();
            pv = BigDecimal.ZERO.subtract(loanAmount).setScale(0, RoundingMode.HALF_UP).intValue();
        }else {
            // 尾款
            nper = Integer.valueOf(param.getMonth()) - 1;
            fv = balance.divide(BigDecimal.ONE.add(BigDecimal.valueOf(rate)), 20 , RoundingMode.HALF_UP).doubleValue();
        }

        double monthPay = LoanUtils.PMT(rate, nper, pv, fv, 0);
        vo.setInterestRate(ratio.toString());
        vo.setMonthPay(BigDecimal.valueOf(monthPay).setScale(2, RoundingMode.HALF_UP));

        return successMessage(vo);
    }

}

