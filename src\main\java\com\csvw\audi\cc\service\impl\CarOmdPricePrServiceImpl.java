package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.entity.dto.PriceCondition;
import com.csvw.audi.cc.entity.po.CarOmdPricePr;
import com.csvw.audi.cc.mapper.CarOmdPricePrMapper;
import com.csvw.audi.cc.service.ICarOmdPricePrService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <p>
 * 外饰价格 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-18
 */
@Service
public class CarOmdPricePrServiceImpl extends ServiceImpl<CarOmdPricePrMapper, CarOmdPricePr> implements ICarOmdPricePrService {

    @Autowired
    private CarOmdPricePrMapper pricePrMapper;

    @Override
    public BigDecimal getPrice(PriceCondition condition) {
        return pricePrMapper.getPrice(condition);
    }
}
