package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.dto.OptionPriceQuery;
import com.csvw.audi.cc.entity.dto.PriceCondition;
import com.csvw.audi.cc.entity.dto.TypePriceParam;
import com.csvw.audi.cc.entity.po.CarOmdPriceType;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.exception.ServiceException;

import java.math.BigDecimal;

/**
 * <p>
 * 配置线价格 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-18
 */
public interface ICarOmdPriceTypeService extends IService<CarOmdPriceType> {

    BigDecimal typePrice(String modelLineId) throws ServiceException;

    BigDecimal typePrice(PriceCondition condition) throws ServiceException;

    BigDecimal accbTypePrice(TypePriceParam priceParam) throws ServiceException;

    BigDecimal accbOptionPrice(OptionPriceQuery priceParam) throws ServiceException;

    BigDecimal optionPrice(String modelLineId, String optionCode, String category) throws ServiceException;

    BigDecimal getPrice(PriceCondition condition);

}
