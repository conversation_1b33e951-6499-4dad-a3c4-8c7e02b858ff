package com.csvw.audi.cc.entity.po;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarCustomSnapshot对象", description="")
public class CarCustomSnapshot extends Model<CarCustomSnapshot> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "快照id")
      @TableId(value = "snapshot_id", type = IdType.ASSIGN_ID)
    private Long snapshotId;

    @ApiModelProperty(value = "配置id")
    private Long ccid;

    @ApiModelProperty(value = "accb配置编码")
    private String audiCode;

    @ApiModelProperty(value = "ACCB车系ID")
    private String accbModelId;

    @ApiModelProperty(value = "ACCB车系编码")
    private String accbModelCode;

    @ApiModelProperty(value = "车系描述")
    private String modelDesc;

    @ApiModelProperty(value = "ACCB车型ID")
    private String accbTypeId;

    @ApiModelProperty(value = "ACCB车型编码")
    private String accbTypeCode;

    @ApiModelProperty(value = "ACCB车型描述")
    private String accbTypeDesc;

    @ApiModelProperty(value = "年款")
    private String modelYear;

    @ApiModelProperty(value = "配置线id")
    private String modelLineId;

    @ApiModelProperty(value = "配置单内饰面料颜色id")
    private String sibInterieurId;

    @ApiModelProperty(value = "推荐车ID")
    private Long bestRecommendId;

    @ApiModelProperty(value = "半订制id")
    private Long measureId;

    @ApiModelProperty(value = "车辆清单类型")
    private String classify;

    private Integer classifyVersion;

    @ApiModelProperty(value = "预计交付周期")
    private String estimateDelivery;

    @ApiModelProperty(value = "omd清单id")
    private Long omdVehicleTypeId;

    @ApiModelProperty(value = "来源ID")
    private Long sourceId;

    @ApiModelProperty(value = "入口")
    private String entryPoint;

    @ApiModelProperty(value = "定金类型")
    private String depositType;

    @ApiModelProperty(value = "车型价格")
    private BigDecimal modelPrice;

    @ApiModelProperty(value = "总价")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "omd数据快照")
    private String omdSnapshot;

    @ApiModelProperty(value = "合同数据快照")
    private String contractSnapshot;

    @ApiModelProperty(value = "详情数据快照")
    private String detailSnapshot;

    @ApiModelProperty(value = "配置编码")
    @TableField("configCode")
    private String configCode;

    @ApiModelProperty(value = "账号id")
    private String userId;

    @ApiModelProperty(value = "账号手机号")
    private String userMobile;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "备注")
    private String remark;


    @Override
    protected Serializable pkVal() {
        return this.snapshotId;
    }

}
