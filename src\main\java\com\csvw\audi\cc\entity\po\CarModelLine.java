package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 配置线
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarModelLine对象", description="配置线")
public class CarModelLine extends Model<CarModelLine> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "配置线id")
    private String modelLineId;

    @ApiModelProperty(value = "配置线名称")
    private String modelLineName;

    @ApiModelProperty(value = "配置线编码")
    private String modelLineCode;

    @ApiModelProperty(value = "accb配置线code")
    private String accbTypeCode;

    @ApiModelProperty(value = "accb配置线ID")
    private String accbTypeId;

    @ApiModelProperty(value = "车型id")
    private String modelId;

    @ApiModelProperty(value = "自定义车系id")
    private String customSeriesId;

    @ApiModelProperty(value = "配置线图片")
    private String imageUrl;

    @ApiModelProperty(value = "配置线图片1")
    private String imageUrl1;

    @ApiModelProperty(value = "数据渠道")
    private String channel;

    @ApiModelProperty(value = "版本")
    private String version;

    @ApiModelProperty(value = "发动机分组")
    private String engine;

    @ApiModelProperty(value = "前端显示状态")
    private Integer frontStatus;

    @ApiModelProperty(value = "默认配置线")
    private Integer defaultConfig;

    @ApiModelProperty(value = "特定配置线")
    private Integer specialLine;

    @ApiModelProperty(value = "1：半订制")
    private Integer measure;

    @ApiModelProperty(value = "车辆价格加上颜色")
    private Integer priceAddColor;

    @ApiModelProperty(value = "1：私人高定")
    private Integer personal;

    @ApiModelProperty(value = "权重")
    private Integer weight;

    @ApiModelProperty("配车类型开放，1:A类,2:B类,3:C类,12:AB,13:AC,23:BC,123:ABC")
    private String typeFlag;

    @ApiModelProperty("配车类型版本")
    private Integer classifyVersion;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;

    @ApiModelProperty(value = "omd车辆唯一码")
    private String omdModelUnicode;

    @ApiModelProperty(value = "omd车辆状态")
    private String omdModelStatus;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
