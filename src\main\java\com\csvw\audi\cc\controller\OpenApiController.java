package com.csvw.audi.cc.controller;


import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.component.kafkaService.impl.SvcdOrgKafkaServiceImpl;
import com.csvw.audi.cc.entity.dto.CustomSeriesDto;
import com.csvw.audi.cc.entity.dto.DealerDto;
import com.csvw.audi.cc.entity.dto.ModelParamDto;
import com.csvw.audi.cc.entity.dto.SmsDto;
import com.csvw.audi.cc.entity.dto.accb.AudiConfigDto;
import com.csvw.audi.cc.entity.dto.accb.OptionBriefDto;
import com.csvw.audi.cc.entity.dto.vwcc.CarCustomDetail;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.entity.vo.CarRenderVo;
import com.csvw.audi.cc.entity.vo.DealerVo;
import com.csvw.audi.cc.entity.vo.ModelLineVo;
import com.csvw.audi.cc.entity.vo.ProdSkuUserRight;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.feign.AudiEshopFeign;
import com.csvw.audi.cc.feign.AudiInfoFeign;
import com.csvw.audi.cc.service.*;
import com.csvw.sx.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/open/api/v1/carconfig")
@Api(tags = "车辆配置开放接口")
public class OpenApiController extends BaseController {

    @Autowired
    private ICarCustomOptionService optionService;

    @Autowired
    private ICarRenderService renderService;

    @Autowired
    private AudiEshopFeign audiEshopFeign;

    @Autowired
    private SvcdOrgKafkaServiceImpl svcdOrgKafkaService;

    @Autowired
    private ICarModelLineService modelLineService;

    @Autowired
    private ICarCustomSeriesService customSeriesService;

    @Autowired
    private ISvcdChannelOrganizationService svcdChannelOrganizationService;

    @GetMapping
    public AjaxMessage<AudiConfigDto> carconfig(@RequestParam String ccid) throws Exception{
        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        if (carCustom == null){
            return new AjaxMessage<>("501", "ccid not exist", null);
        }
        AudiConfigDto brief = new AudiConfigDto();
        if (StringUtils.isNotBlank(carCustom.getModelLineId())) {
            ModelParamDto modelParamDto = new ModelParamDto();
            modelParamDto.setDelFlag(0);
            modelParamDto.setModelLineId(carCustom.getModelLineId());
            modelParamDto.setChannel(Constant.MASTER_CHANNEL);
            List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
            if (lines == null ||  lines.size() != 1){
                throw new ServiceException("500", "服务异常", null);
            }
            ModelLineVo modelLine = lines.get(0);
            CarCustomDetail detail = new CarCustomDetail();
            detail.setCcId(String.valueOf(carCustom.getCcid()));

            CustomSeriesDto customSeriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
            brief.setSeriesCode(customSeriesDto.getSeriesCode());
        }
        brief.setModelId(carCustom.getAccbModelId());
        brief.setModelCode(carCustom.getAccbModelCode());
        brief.setTypeId(carCustom.getAccbTypeId());
        brief.setTypeCode(carCustom.getAccbTypeCode());
        brief.setTypeDesc(carCustom.getAccbTypeDesc());
        brief.setModelYear(carCustom.getModelYear());
        brief.setModelDesc(carCustom.getModelDesc());
        QueryWrapper<CarCustomOption> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ccid", carCustom.getCcid());
        List<CarCustomOption> optionList = optionService.list(queryWrapper);
        List<OptionBriefDto> briefDtos = new ArrayList<>();
        optionList.forEach(i->{
            OptionBriefDto briefDto = new OptionBriefDto();
            BeanUtils.copyProperties(i, briefDto);
            briefDtos.add(briefDto);
        });
        brief.setOptions(briefDtos);
        return new AjaxMessage<>("00", "成功", brief);
    }

    @GetMapping("render")
    @ApiOperation(value = "车辆渲染图")
    @ApiImplicitParams(value = {@ApiImplicitParam(name="typeCode", value="配置线编码", required = true),
            @ApiImplicitParam(name="exteriorCode", value="外饰编码", required = true)})
    public AjaxMessage<List<CarRenderVo>> render(@RequestParam String typeCode, @RequestParam String exteriorCode){
        LambdaQueryWrapper<CarRender> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CarRender::getDelFlag, 0).eq(CarRender::getTypeCode, typeCode).eq(CarRender::getExteriorCode, exteriorCode);
        List<CarRender> carRenders = renderService.list(queryWrapper);
        List<CarRenderVo> vos = carRenders.stream().map(i->{
            CarRenderVo vo = new CarRenderVo();
            BeanUtils.copyProperties(i, vo);
            return vo;
        }).collect(Collectors.toList());
        return new AjaxMessage<>("00", "成功", vos);
    }

    @Autowired
    private ICarModelService modelService;
    @ApiOperation("根据车型内容获取权益信息")
    @GetMapping(value = "/findByCarModelId")
    public AjaxMessage<ProdSkuUserRight> findByCarModelId(@RequestParam(value = "accbTypeCode") String accbTypeCode, @RequestParam(value = "modelYear") String modelYear,@RequestParam(value = "modelVersion") String modelVersion){
        ProdSkuUserRight prodSkuUserRight = null;
        QueryWrapper<CarModelLine> carModelLineQuery = new QueryWrapper<>();
        carModelLineQuery.eq("accb_type_code",accbTypeCode).eq("channel","master").eq("del_flag",0);
        List<CarModelLine> carModelLineList = modelLineService.list(carModelLineQuery);
        if(carModelLineList != null && carModelLineList.size() > 0) {
            carModelLineList = carModelLineList.stream().filter(m-> modelService.getOne(
                    new LambdaQueryWrapper<CarModel>().eq(CarModel::getModelId, m.getModelId())
                            .eq(CarModel::getModelYear, modelYear).eq(CarModel::getDelFlag, 0)
                            .eq(CarModel::getChannel, Constant.MASTER_CHANNEL)) != null).collect(Collectors.toList());
            AjaxMessage<ProdSkuUserRight> result = audiEshopFeign.findByCarModelId(carModelLineList.get(0).getModelLineCode(), 1, modelYear, modelVersion);
            prodSkuUserRight = result.getData();
        }

        return successMessage(prodSkuUserRight);
    }

    @ApiOperation("获取代理商列表")
    @GetMapping("/getDealerList")
    public AjaxMessage<List<DealerVo>> getAgentList(DealerDto dealerDto) {
        //默认返回总部
        if(dealerDto.getDefaultHeadquarters() == null) {
            dealerDto.setDefaultHeadquarters(1);
        }
        List<DealerVo> list =  svcdChannelOrganizationService.getAgentList(dealerDto);
        return new AjaxMessage<>("00", "成功", list);
    }

    /**
     * 模拟网发下发数据
     * @param param
     */
    @GetMapping("/pushImg")
    public void main(String param) {
        JSON parse = JSONUtil.parse(param);
        JSONObject bean = parse.toBean(JSONObject.class);
        try {
            svcdOrgKafkaService.updateData(bean, LocalDateTime.now());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

