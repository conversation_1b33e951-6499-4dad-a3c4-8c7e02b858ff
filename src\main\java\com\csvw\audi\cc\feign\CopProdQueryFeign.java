package com.csvw.audi.cc.feign;

import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.common.AjaxMessage;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

//@FeignClient(name = "cop-prod-query")
@FeignClient(value = "cop-prod-query", url = "${audi.purple-int-scq}/cop-prod-query")
public interface CopProdQueryFeign {

    @GetMapping("/api/v1/skus/{prodSkuId}")
    JSONObject findProdSkuById(@RequestParam(name = "prodSkuId") Long prodSkuId);

    @GetMapping("/api/v1/otds")
    JSONObject selectProdCar(@RequestParam(name = "seriesId") String seriesId, @RequestParam(name = "useType") Integer useType);

    @GetMapping("/api/v1/prod-nga/price")
    JSONObject ngaProdPrice(@RequestParam String depositType,
                            @RequestParam String modelCode, @RequestParam String prodId,
                            @RequestParam String seriesCode);
}
