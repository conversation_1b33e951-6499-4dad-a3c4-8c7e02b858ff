package com.csvw.audi.cc.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * ccid来源
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CustomSourceDto {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "来源名称")
    @NotBlank
    private String name;

    @ApiModelProperty(value = "经销商编码")
    @NotBlank
    private String dealerCode;

    @ApiModelProperty(value = "经销商名称")
    @NotBlank
    private String dealerName;

    @ApiModelProperty(value = "应用")
    @NotBlank
    private String app;

}
