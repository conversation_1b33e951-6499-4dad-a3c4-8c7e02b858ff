<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.SvcdInvestorMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.SvcdInvestor">
        <id column="investor_id" property="investorId" />
        <result column="code" property="code" />
        <result column="investor_name" property="investorName" />
        <result column="enterprise_type" property="enterpriseType" />
        <result column="regist_capital" property="registCapital" />
        <result column="company_address" property="companyAddress" />
        <result column="chairman_name" property="chairmanName" />
        <result column="chairman_phone" property="chairmanPhone" />
        <result column="legal_representative_name" property="legalRepresentativeName" />
        <result column="legal_representative_phone" property="legalRepresentativePhone" />
        <result column="audi_num" property="audiNum" />
        <result column="dealer_user_name" property="dealerUserName" />
        <result column="dealer_user_phone" property="dealerUserPhone" />
        <result column="dealer_user_email" property="dealerUserEmail" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        investor_id, code, investor_name, enterprise_type, regist_capital, company_address, chairman_name, chairman_phone, legal_representative_name, legal_representative_phone, audi_num, dealer_user_name, dealer_user_phone, dealer_user_email, deleted
    </sql>

</mapper>
