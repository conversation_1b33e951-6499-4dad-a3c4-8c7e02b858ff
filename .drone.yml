---
kind: pipeline
name: skdev
platform:
  os: linux
  arch: amd64
steps:
  - name: kmsp_validate
    image: kmsp/kmsp-plugin-drone:1.0.0
    settings:
      method: validate
      app_version: *********
      server:
        from_secret: kmsp_dev_server
  - name: maven_package
    image: kmsp/maven:jdk8u232-b09-3.6.2
    commands:
#      - find $HOME/.m2/repository -name '*.lastUpdated' | xargs rm -rf
      - mvn clean package -DskipTests -U
    volumes:
      - name: drone_home
        path: /root
#  - name: code-analysis
#    image: mailbyms/drone-sonar-plugin:v1
#    settings:
#      sonar_host:
#        from_secret: sonar_host
#      sonar_token:
#        from_secret: sonar_token
#      java_binaries: target/classes
  - name: docker_build
    image: kmsp/docker:18.09
    settings:
      password:
        from_secret: registry_slave_password
      purge: true
      registry: svwsx-registry-vpc.cn-shanghai.cr.aliyuncs.com
      repo: svwsx-registry-vpc.cn-shanghai.cr.aliyuncs.com/kmsp-${DRONE_REPO_NAMESPACE}/${DRONE_REPO_NAME}
      username: sx-kmsp-audi@svwsx
  - name: kmsp_feedback
    image: kmsp/kmsp-plugin-drone:1.0.0
    settings:
      method: feedback
      server:
        from_secret: kmsp_dev_server
    failure: ignore
    when:
      status:
        - failure
        - success
  - name: notify
    image: kmsp/drone-plugin-dingtalk:1.0.0
    settings:
      url: http://**************/robot/send
      token:
        from_secret: dingtalk_token
    when:
      status:
        - failure
        - success
volumes:
  - name: drone_home
    host:
      path: /data/drone/home
trigger:
  branch:
    - rel20250730
  event:
    - push

...
