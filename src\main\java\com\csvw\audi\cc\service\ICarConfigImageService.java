package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.po.CarConfigImage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 配置线角度图 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
public interface ICarConfigImageService extends IService<CarConfigImage> {
    String getCcHeadImageUrl(String channel, String modelLineId, String exterieurCode);
    String getCcLeftImageUrl(String channel, String modelLineId, String exterieurCode, String radCode);
    List<String> getDiscoveryViewExterieur(String channel, String modelLineId, String exterieurCode);
    List<String> getDiscoveryViewInterieur(String channel, String modelLineId, String interieurCode);
}
