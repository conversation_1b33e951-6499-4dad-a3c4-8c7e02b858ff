package com.csvw.audi.cc.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 渠道商对应的金融机构
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-17
 */
@Data
public class AdminOrgBankVo {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "数据id")
    private String orgBankId;

    @ApiModelProperty(value = "渠道商编号")
    private String dealerCode;

    @ApiModelProperty(value = "渠道商名称")
    private String dealerName;

    @ApiModelProperty(value = "渠道商全称")
    private String dealerFullName;

    @ApiModelProperty(value = "金融机构代码")
    private String bankCode;

    @ApiModelProperty(value = "金融机构名称")
    private String bankName;

    @ApiModelProperty(value = "删除标识（1：删除，0：未删除）")
    private String delFlag;

    @ApiModelProperty(value = "图片地址(OSS)")
    private String imageUrl;

    @ApiModelProperty(value = "金融方案确认文案")
    private String confirmText;

	@ApiModelProperty(value = "创建时间")
	private LocalDateTime createdTime;

	@ApiModelProperty(value = "更新时间")
	private LocalDateTime updatedTime;

}
