package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.dto.OptionRelDto;
import com.csvw.audi.cc.entity.dto.OptionRelateParam;
import com.csvw.audi.cc.entity.po.CarOptionRelate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 选装关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-16
 */
public interface CarOptionRelateMapper extends BaseMapper<CarOptionRelate> {

    List<OptionRelDto> listOptionRelDto(OptionRelateParam optionRelateParam);

    List<OptionRelDto> listCombines(@Param("modelLineId") String modelLineId, @Param("optionCode") String optionCode);

    List<CarOptionRelate> listConflictOptionRelate(@Param("modelLineId") String modelLineId, @Param("optionIds") Set<String> optionIds);

    List<CarOptionRelate> listOptionConflict(@Param("modelLineId") String modelLineId, @Param("optionId") String optionId, @Param("optionRelateIds") Set<String> optionRelateIds);

    List<String> listDependCategory(@Param("modelLineId") String modelLineId, @Param("optionId") String optionId);

    List<CarOptionRelate> listDependsForValid(@Param("modelLineId") String modelLineId, @Param("category") String category, @Param("optionId") String optionId, @Param("optionIds") Set<String> optionIds);

    List<CarOptionRelate> listDependedByOptionId(@Param("modelLineId") String modelLineId, @Param("optionId") String optionId, @Param("optionIds") Set<String> optionIds);

    List<CarOptionRelate> listOptionDepend(@Param("modelLineId") String modelLineId, @Param("optionId") String optionId, @Param("optionIds") Set<String> optionRelateIds);

    List<String> listDependGroup(@Param("modelLineId") String modelLineId, @Param("optionId") String optionId);

    List<CarOptionRelate> listDependsForGroupValid(@Param("modelLineId") String modelLineId, @Param("group") String group, @Param("optionId") String optionId, @Param("optionIds") Set<String> optionIds);

    List<CarOptionRelate> listSibInterieurIdDependedByOptionId(@Param("modelLineId") String modelLineId, @Param("sibInterieurId") String sibInterieurId, @Param("optionIds") Set<String> optionIds);
}
