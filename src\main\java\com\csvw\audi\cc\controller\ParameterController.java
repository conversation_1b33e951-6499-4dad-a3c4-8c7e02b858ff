package com.csvw.audi.cc.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.po.Option;
import com.csvw.audi.cc.entity.po.Parameter;
import com.csvw.audi.cc.entity.vo.OptionDetailVo;
import com.csvw.audi.cc.entity.vo.ParameterVo;
import com.csvw.audi.cc.service.IOptionService;
import com.csvw.audi.cc.service.IParameterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/api/v1/parameter")
@Api(tags = "配置线参数")
public class ParameterController extends BaseController {

    @Autowired
    private IParameterService parameterService;

    @GetMapping("/list")
    @ApiOperation(value = "edition one 参数表")
    public AjaxMessage<List<ParameterVo>> parameter(){
        LambdaQueryWrapper<Parameter> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Parameter::getTypeCode, "TYPE:498B2Y-GPAHPAH-GPDWPDW-GPF3PF3-GPS6PS6-GWA6WA6-MEIH5MD-MRAD53D-MBBO6FJ-MHSW8IZ-MLSE9VS")
                .eq(Parameter::getModelYear, 2022).orderByAsc(Parameter::getWeight);
        List<Parameter> parameters = parameterService.list(queryWrapper);
        List<ParameterVo> vos = parameters.stream().map(i->{
            ParameterVo vo = new ParameterVo();
            BeanUtils.copyProperties(i, vo);
            return vo;
        }).collect(Collectors.toList());
        return successMessage(vos);
    }

}

