package com.csvw.audi.cc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.common.utils.JsonString;
import com.csvw.audi.cc.config.AppConfig;
import com.csvw.audi.cc.entity.dto.*;
import com.csvw.audi.cc.entity.dto.vwcc.Option;
import com.csvw.audi.cc.entity.dto.vwcc.*;
import com.csvw.audi.cc.entity.enumeration.DepositTypeEnum;
import com.csvw.audi.cc.entity.enumeration.OptionCategoryEnum;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.entity.vo.*;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.feign.AudiEshopFeign;
import com.csvw.audi.cc.feign.CopOrderQueryFeign;
import com.csvw.audi.cc.mapper.CarModelLineOptionMapper;
import com.csvw.audi.cc.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Service
@Slf4j
public class CarRecommendCustomServiceImpl {

    @Autowired
    private IACCBService iaccbService;

    @Autowired
    private ICarCustomService carCustomService;

    @Autowired
    private ICarCustomOptionService carCustomOptionService;

    @Autowired
    private ICarOptionService optionService;

    @Autowired
    private ICarModelLineService modelLineService;

    @Autowired
    private ICarCustomSeriesService customSeriesService;

    @Autowired
    private ICarOmdPriceTypeService priceTypeService;

    @Autowired
    private ICarModelLineOptionService modelLineOptionService;

    @Autowired
    private ICarSibInterieurService sibInterieurService;

    @Autowired
    private ICarOptionRelateService optionRelateService;

    @Autowired
    private ICarConfigImageService configImageService;

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private ICarOptionService carOptionService;

    @Autowired
    private ICarSibInterieurService carSibInterieurService;

    @Autowired
    private AudiEshopFeign eshopFeign;

    @Autowired
    private ICarBestRecommendService bestRecommendService;

    @Autowired
    private ICarBestRecommendOptionService bestRecommendOptionService;

    @Autowired
    private ICarModelLineSibInterieurService modelLineSibInterieurService;

    @Autowired
    private CarModelLineOptionMapper modelLineOptionMapper;

    @Autowired
    private CopOrderQueryFeign copOrderQueryFeign;

    ExecutorService audiCodeExecutor = Executors.newFixedThreadPool(10);

    public CarCustomDetail getCarConfigDetailOmd(CarCustom carCustom) throws Exception {

        QueryWrapper<CarCustomOption> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ccid", carCustom.getCcid());
        List<CarCustomOption> optionList = carCustomOptionService.list(queryWrapper);
        List<String> optionCodes = optionList.stream().map(CarCustomOption::getCode).collect(Collectors.toList());
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(carCustom.getModelLineId());
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (lines == null ||  lines.size() != 1){
            throw new ServiceException("500", "服务异常", null);
        }
        ModelLineVo modelLine = lines.get(0);
        CustomSeriesDto customSeriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
        CarCustomDetail detail = new CarCustomDetail();
        if(StringUtils.isNotBlank(carCustom.getMemberId())){
            JSONObject couponRes = copOrderQueryFeign.couponInfo(String.valueOf(carCustom.getCcid()), customSeriesDto.getSeriesCode(), carCustom.getMemberId());
            if (couponRes.getJSONArray("data")!=null && couponRes.getJSONArray("data").size()>0){
                List<CouponInfoVo> couponInfoVos = couponRes.getJSONArray("data").toJavaList(CouponInfoVo.class);
                detail.setCouponNo(couponInfoVos.get(0).getMpEquityNo());
            }
        }
        detail.setValid(carCustom.getValid());
        detail.setInvalidReason(carCustom.getInvalidReason());
        detail.setUpdateFlag(carCustom.getUpdateFlag());
        detail.setUpdateContent(carCustom.getUpdateContent());
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        detail.setDepositType(carCustom.getDepositType());
        detail.setConfigTime(carCustom.getCreateTime());
        detail.setEstimateDelivery(carCustom.getEstimateDelivery());
        detail.setClassify(carCustom.getClassify());
        detail.setMstMgrpId(carCustom.getMstMgrpId());
        if (detail.getDepositType() == null){
            detail.setDepositType(DepositTypeEnum.STOCK.getType());
        }

        ConfigDetail configDetail = new ConfigDetail();
        SeriesDetail seriesDetail = new SeriesDetail();
        seriesDetail.setSeriesCode(customSeriesDto.getSeriesCode());
        seriesDetail.setSeriesNameCn(carCustom.getModelDesc());
        configDetail.setCarSeries(seriesDetail);

        ModelDetail modelDetail = new ModelDetail();
        modelDetail.setModelCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        modelDetail.setModelNameCn(carCustom.getAccbTypeDesc());
        modelDetail.setModelYear(modelLine.getModelYear());
        modelDetail.setOmdModelVersion(modelLine.getVersion());
        modelDetail.setModelPrice(modelLine.getPrice());
        modelDetail.setCustomModelCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        configDetail.setCarModel(modelDetail);

        ColorDetail insideColor = new ColorDetail();
        configDetail.setInsideColor(insideColor);
        configDetail.setOptionList(new ArrayList<>());
        ColorDetail outsideColor = new ColorDetail();
        configDetail.setOutsideColor(outsideColor);
        OptionRelateParam optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLine.getModelLineId());
        optionRelateParam.setRelateTypes(Arrays.asList("attach", "unattach"));
        List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        OptionRelateParam combineRelateParam = new OptionRelateParam();
        combineRelateParam.setRelateTypes(Arrays.asList("combine"));
        List<OptionRelDto> relCombineDtos =  optionRelateService.listOptionRelDto(combineRelateParam);
        relDtos.addAll(relCombineDtos);
        Map<String, List<OptionRelDto>> oMap = new HashMap<>();
        List<OptionRelDto> modelAttach = new ArrayList<>();
        relDtos.forEach(i->{
            if (i.getOptionId() == null && "attach".equals(i.getRelateType())){
                modelAttach.add(i);
            }else {
                List<OptionRelDto> oRel = oMap.get(i.getOptionId());
                if (oRel == null) {
                    oRel = new ArrayList<>();
                    oMap.put(i.getOptionId(), oRel);
                }
                oRel.add(i);
            }
        });
        List<Option> options = new ArrayList<>();
        List<String> optionIds = new ArrayList<>();
        boolean containRad = false;
        List<String> unattachCodes = relDtos.stream().filter(i->"unattach".equals(i.getRelateType()) && optionCodes.contains(i.getOptionCode()))
                .map(OptionRelDto::getOptionRelateCode)
                .collect(Collectors.toList());
        for(CarCustomOption i : optionList){
            List<ModelLineOptionVo> items =  modelLineOptionMapper.findPaketItemFromCodes(customSeriesDto.getCustomSeriesId(), modelLine.getModelLineId(), i.getCode(), optionCodes);
            if (CollectionUtils.isNotEmpty(items)){
                continue;
            }
            List<ModelLineOptionVo> optionVos = modelLineService.optionQueryByOptionId(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId(), modelLine.getModelLineId(), i.getOptionId(), true);
            if (optionVos == null || optionVos.size() != 1){
                throw new ServiceException("400401", "参数异常：optionIds", null);
            }
            ModelLineOptionVo optionVo = optionVos.get(0);
            boolean discount = false;
            // 过滤选装包的件与标装
            if (optionVo.getOptionType().equals("packet-item") || (optionVo.getStatus() != null && optionVo.getStatus().intValue() == 1)){
                continue;
            }
            if(StringUtils.isNotBlank(detail.getCouponNo()) && "1".equals(optionVos.get(0).getEquipmentRights())){
                discount = true;
            }
            optionIds.add(i.getOptionId());
            if (OptionCategoryEnum.OUTCOLOR.getValue().equals(i.getCategory())){
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                if (code.startsWith("COLOR_EXTERIEUR:")){
                    code = code.replaceFirst("COLOR_EXTERIEUR:", "");
                }
                outsideColor.setColorCode(code);
                outsideColor.setColorNameCn(i.getDescription());
                BigDecimal outsideColorPrice = priceTypeService.optionPrice(modelLine.getModelLineId(), code, i.getCategory());
                outsideColor.setPrice(outsideColorPrice);
                if (discount){
                    outsideColor.setDiscount(outsideColor.getPrice());
                }
                if (oMap.get(i.getOptionId()) != null) {
                    for (OptionRelDto relDto : oMap.get(i.getOptionId())) {
                        if (relDto.getRelateType().equals("attach")) {
                            Option optionItem = new Option();
                            optionItem.setOptionCode(relDto.getOptionRelateCode());
                            options.add(optionItem);
                        }
                    }
                }
            }else if (OptionCategoryEnum.INCOLOR.getValue().equals(i.getCategory())){
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                if (code.startsWith("COLOR_INTERIEUR:")){
                    code = code.replaceFirst("COLOR_INTERIEUR:", "");
                }
                insideColor.setColorCode(code);
                insideColor.setColorNameCn(i.getDescription());
                if (oMap.get(i.getOptionId()) != null) {
                    for (OptionRelDto relDto : oMap.get(i.getOptionId())) {
                        if (relDto.getRelateType().equals("attach")) {
                            Option optionItem = new Option();
                            optionItem.setOptionCode(relDto.getOptionRelateCode());
                            options.add(optionItem);
                        }
                    }
                }
            }else {
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                String[] codes = code.split(":");
                if (codes.length == 1){
                    code = codes[0];
                }else if (codes.length == 2){
                    code = codes[1];
                }
                // a7l 45黑影特殊规则，给到omd切换配置线去掉选装包
                if(modelDetail.getModelCode().equals("498BZY-GPGCPGC-GWAEWAE-GYEHYEH")
                        && modelDetail.getModelYear().equals("2022") && modelDetail.getOmdModelVersion().equals("0")){
                    switch (code){
                        case "GZ2+PT1+2C7+8IZ+YEI":
                            modelDetail.setModelCode("498BZY-MESTGZ2-GPGCPGC-GPT1PT1-GWAEWAE-GYEIYEI-MLSS2C7-MHSW8IZ");
                            modelDetail.setCustomModelCode("498BZY-MESTGZ2-GPGCPGC-GPT1PT1-GWAEWAE-GYEIYEI-MLSS2C7-MHSW8IZ");
                            modelDetail.setModelPrice(priceTypeService.typePrice(new PriceCondition("498BZY-MESTGZ2-GPGCPGC-GPT1PT1-GWAEWAE-GYEIYEI-MLSS2C7-MHSW8IZ",
                                    "498BZY", "2022", "0")));
                            continue;
                        case "PCY+PT1+2V3+9VS+YEJ":
                            modelDetail.setModelCode("498BZY-GPCYPCY-GPGCPGC-GPT1PT1-GWAEWAE-GYEJYEJ-MFRI2V3-MLSE9VS");
                            modelDetail.setCustomModelCode("498BZY-GPCYPCY-GPGCPGC-GPT1PT1-GWAEWAE-GYEJYEJ-MFRI2V3-MLSE9VS");
                            modelDetail.setModelPrice(priceTypeService.typePrice(new PriceCondition("498BZY-GPCYPCY-GPGCPGC-GPT1PT1-GWAEWAE-GYEJYEJ-MFRI2V3-MLSE9VS",
                                    "498BZY", "2022", "0")));
                            continue;
                    }
                }

                // a7l 2024款 45 黑影规则
                List<String> a7lTypeChange = Arrays.asList("70acb326-11d4-4354-adac-da7ac1ea3479", "bc19d515-1ab9-4494-9297-947fba48b5d0");
                if(a7lTypeChange.contains(carCustom.getModelLineId())){
                    switch (code){
                        case "PCY+9VS+PDW+WA6+YEB":
                            String accbTypeCode = "498BZY-MREII56-GPAHPAH-GPCYPCY-GPDWPDW-GPS6PS6-GWA6WA6-GYEBYEB-MEIH5MK-MRAD53D-MLSE9VS";
                            modelDetail.setModelCode(accbTypeCode);
                            modelDetail.setCustomModelCode(accbTypeCode);
                            modelDetail.setModelPrice(priceTypeService.typePrice(new PriceCondition(accbTypeCode,
                                    "498BZY", "2024", "0")));
                            continue;
                        case "5MK":
                            continue;
                    }
                }
                Option option = new Option();
                option.setOptionNameCn(i.getDescription());
                option.setOptionCode(code);
                option.setOptionClassification(i.getCategory());
                option.setOptionClassification2nd(i.getCategory());
                boolean combine = false;
                if (oMap.get(i.getOptionId()) != null){
                    for (OptionRelDto relDto: oMap.get(i.getOptionId())) {
                        if (relDto.getRelateType().equals("combine")) {
                            Option optionItem = new Option();
                            optionItem.setOptionCode(relDto.getOptionRelateCode());
                            String itemCategory = relDto.getOptionRelateCategory() == null?"":relDto.getOptionRelateCategory();
                            optionItem.setOptionPrice(priceTypeService.optionPrice(modelLine.getModelLineId(), relDto.getOptionRelateCode(), itemCategory));
                            if(discount) {
                                optionItem.setDiscount(optionItem.getOptionPrice());
                            }
                            options.add(optionItem);
                            combine = true;
                        }else if (relDto.getRelateType().equals("attach")) {
                            Option optionItem = new Option();
                            optionItem.setOptionCode(relDto.getOptionRelateCode());
                            options.add(optionItem);
                        }
                    }
                }
                if (!combine) {
                    options.add(option);
                }
                BigDecimal optionPrice = priceTypeService.optionPrice(modelLine.getModelLineId(), code, i.getCategory());
                option.setOptionPrice(optionPrice);
                if (discount){
                    option.setDiscount(option.getOptionPrice());
                }

            }
        }
        // 车辆自带编码
        for(OptionRelDto relDto : modelAttach){
            Option optionItem = new Option();
            optionItem.setOptionCode(relDto.getOptionRelateCode());
            options.add(optionItem);
        }
        if (CollectionUtils.isNotEmpty(unattachCodes)){
            options.removeIf(o->unattachCodes.contains(o.getOptionCode()));
        }

        PriceComputeParam priceComputeParam = new PriceComputeParam();
        priceComputeParam.setModelLineId(modelLine.getModelLineId());
        priceComputeParam.setOptionIds(optionIds);
        priceComputeParam.setCouponNo(detail.getCouponNo());
        configDetail.setTotalPrice(modelLineService.computePrice(priceComputeParam));
        configDetail.setDiscount(modelLineService.computeDiscount(priceComputeParam));
        configDetail.setOptionList(options);
        detail.setConfigDetail(configDetail);
        detail.setBestRecommendId(JsonString.valueOf(carCustom.getBestRecommendId()));
        return detail;
    }

    public CarCustomDetail getCarConfigDetailOmdAll(CarCustom carCustom) throws Exception {

        QueryWrapper<CarCustomOption> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ccid", carCustom.getCcid());
        List<CarCustomOption> optionList = carCustomOptionService.list(queryWrapper);
        List<String> optionCodes = optionList.stream().map(CarCustomOption::getCode).collect(Collectors.toList());
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setModelLineId(carCustom.getModelLineId());
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (lines == null ||  lines.size() != 1){
            throw new ServiceException("500", "服务异常", null);
        }
        ModelLineVo modelLine = lines.get(0);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setValid(carCustom.getValid());
        detail.setInvalidReason(carCustom.getInvalidReason());
        detail.setUpdateFlag(carCustom.getUpdateFlag());
        detail.setUpdateContent(carCustom.getUpdateContent());
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        detail.setConfigTime(carCustom.getCreateTime());

        CustomSeriesDto customSeriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
        ConfigDetail configDetail = new ConfigDetail();
        SeriesDetail seriesDetail = new SeriesDetail();
        seriesDetail.setSeriesCode(customSeriesDto.getSeriesCode());
        seriesDetail.setSeriesNameCn(carCustom.getModelDesc());
        configDetail.setCarSeries(seriesDetail);

        ModelDetail modelDetail = new ModelDetail();
        modelDetail.setModelCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        modelDetail.setModelNameCn(carCustom.getAccbTypeDesc());
        modelDetail.setModelYear(modelLine.getModelYear());
        modelDetail.setOmdModelVersion(modelLine.getVersion());
        modelDetail.setModelPrice(modelLine.getPrice());
        modelDetail.setCustomModelCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        configDetail.setCarModel(modelDetail);

        ColorDetail insideColor = new ColorDetail();
        configDetail.setInsideColor(insideColor);
        configDetail.setOptionList(new ArrayList<>());
        ColorDetail outsideColor = new ColorDetail();
        configDetail.setOutsideColor(outsideColor);
        BigDecimal totalPrice = null;
        if (modelLine.getPrice() instanceof BigDecimal) {
            totalPrice = (BigDecimal) modelLine.getPrice();
        }
        OptionRelateParam optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLine.getModelLineId());
        optionRelateParam.setRelateTypes(Arrays.asList("attach"));
        List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        OptionRelateParam combineRelateParam = new OptionRelateParam();
        combineRelateParam.setRelateTypes(Arrays.asList("combine"));
        List<OptionRelDto> relCombineDtos =  optionRelateService.listOptionRelDto(combineRelateParam);
        relDtos.addAll(relCombineDtos);
        Map<String, List<OptionRelDto>> oMap = new HashMap<>();
        relDtos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel == null) {
                oRel = new ArrayList<>();
                oMap.put(i.getOptionId(), oRel);
            }
            oRel.add(i);
        });
        List<Option> options = new ArrayList<>();
        List<String> optionIds = new ArrayList<>();
        for(CarCustomOption i : optionList){
            List<ModelLineOptionVo> items =  modelLineOptionMapper.findPaketItemFromCodes(customSeriesDto.getCustomSeriesId(), modelLine.getModelLineId(), i.getCode(), optionCodes);
            if (CollectionUtils.isNotEmpty(items)){
                continue;
            }
            List<ModelLineOptionVo> optionVos = modelLineService.optionQueryByOptionId(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId(), modelLine.getModelLineId(), i.getOptionId(), true);
            if (optionVos == null || optionVos.size() != 1){
                throw new ServiceException("400401", "参数异常：optionIds", null);
            }
            ModelLineOptionVo optionVo = optionVos.get(0);
            // 过滤选装包的件与标装
            if (optionVo.getOptionType().equals("packet-item") || (optionVo.getStatus() != null && optionVo.getStatus().intValue() == 1)){
                continue;
            }
            optionIds.add(i.getOptionId());
            if (OptionCategoryEnum.OUTCOLOR.getValue().equals(i.getCategory())){
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                if (code.startsWith("COLOR_EXTERIEUR:")){
                    code = code.replaceFirst("COLOR_EXTERIEUR:", "");
                }
                outsideColor.setColorCode(code);
                outsideColor.setColorNameCn(i.getDescription());
                BigDecimal outsideColorPrice = priceTypeService.optionPrice(modelLine.getModelLineId(), code, i.getCategory());
                outsideColor.setPrice(outsideColorPrice);
                if (oMap.get(i.getOptionId()) != null) {
                    for (OptionRelDto relDto : oMap.get(i.getOptionId())) {
                        if (relDto.getRelateType().equals("attach")) {
                            Option optionItem = new Option();
                            optionItem.setOptionCode(relDto.getOptionRelateCode());
                            options.add(optionItem);
                        }
                    }
                }
                if (totalPrice != null && outsideColorPrice != null)
                    totalPrice = totalPrice.add(outsideColorPrice);
            }else if (OptionCategoryEnum.INCOLOR.getValue().equals(i.getCategory())){
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                if (code.startsWith("COLOR_INTERIEUR:")){
                    code = code.replaceFirst("COLOR_INTERIEUR:", "");
                }
                insideColor.setColorCode(code);
                insideColor.setColorNameCn(i.getDescription());
                if (oMap.get(i.getOptionId()) != null) {
                    for (OptionRelDto relDto : oMap.get(i.getOptionId())) {
                        if (relDto.getRelateType().equals("attach")) {
                            Option optionItem = new Option();
                            optionItem.setOptionCode(relDto.getOptionRelateCode());
                            options.add(optionItem);
                        }
                    }
                }
            }else {
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                String[] codes = code.split(":");
                if (codes.length == 1){
                    code = codes[0];
                }else if (codes.length == 2){
                    code = codes[1];
                }
                Option option = new Option();
                option.setOptionNameCn(i.getDescription());
                option.setOptionCode(code);
                option.setOptionClassification(i.getCategory());
                option.setOptionClassification2nd(i.getCategory());
                boolean combine = false;
                if (oMap.get(i.getOptionId()) != null){
                    for (OptionRelDto relDto: oMap.get(i.getOptionId())) {
                        if (relDto.getRelateType().equals("combine")) {
                            Option optionItem = new Option();
                            optionItem.setOptionCode(relDto.getOptionRelateCode());
                            String itemCategory = relDto.getOptionRelateCategory() == null?"":relDto.getOptionRelateCategory();
                            optionItem.setOptionPrice(priceTypeService.optionPrice(modelLine.getModelLineId(), relDto.getOptionRelateCode(), itemCategory));
                            options.add(optionItem);
                            combine = true;
                        }else if (relDto.getRelateType().equals("attach")) {
                            Option optionItem = new Option();
                            optionItem.setOptionCode(relDto.getOptionRelateCode());
                            options.add(optionItem);
                        }
                    }
                }
                if (!combine) {
                    options.add(option);
                }
                BigDecimal optionPrice = priceTypeService.optionPrice(modelLine.getModelLineId(), code, i.getCategory());
                option.setOptionPrice(optionPrice);
                if (totalPrice != null && optionPrice != null) {
                    totalPrice = totalPrice.add(optionPrice);
                }
            }
        }
        PriceComputeParam priceComputeParam = new PriceComputeParam();
        priceComputeParam.setModelLineId(modelLine.getModelLineId());
        priceComputeParam.setOptionIds(optionIds);
//        configDetail.setTotalPrice(modelLineService.computePrice(priceComputeParam));
        configDetail.setOptionList(options);
        detail.setConfigDetail(configDetail);
        detail.setBestRecommendId(JsonString.valueOf(carCustom.getBestRecommendId()));
        return detail;
    }

    public CarCustomDetail getCarConfigDetail(String channel, CarCustom carCustom) throws Exception {

        QueryWrapper<CarCustomOption> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ccid", carCustom.getCcid());
        List<CarCustomOption> optionList = carCustomOptionService.list(queryWrapper);
        List<String> optionCodes = optionList.stream().map(CarCustomOption::getCode).collect(Collectors.toList());
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(carCustom.getModelLineId());
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (lines == null || lines.size() != 1){
            throw new ServiceException("500", "服务异常", null);
        }
        ModelLineVo modelLine = lines.get(0);
        CustomSeriesDto customSeriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
        CarCustomDetail detail = new CarCustomDetail();
        if(StringUtils.isNotBlank(carCustom.getMemberId())){
            JSONObject couponRes = copOrderQueryFeign.couponInfo(String.valueOf(carCustom.getCcid()), customSeriesDto.getSeriesCode(), carCustom.getMemberId());
            if (couponRes.getJSONArray("data")!=null && couponRes.getJSONArray("data").size()>0){
                List<CouponInfoVo> couponInfoVos = couponRes.getJSONArray("data").toJavaList(CouponInfoVo.class);
                detail.setCouponNo(couponInfoVos.get(0).getMpEquityNo());
            }
        }
        detail.setValid(carCustom.getValid());
        detail.setInvalidReason(carCustom.getInvalidReason());
        detail.setUpdateFlag(carCustom.getUpdateFlag());
        detail.setUpdateContent(carCustom.getUpdateContent());
        detail.setBestRecommendId(JsonString.valueOf(carCustom.getBestRecommendId()));
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        detail.setConfigTime(carCustom.getCreateTime());
        detail.setDepositType(carCustom.getDepositType());
        detail.setEstimateDelivery(carCustom.getEstimateDelivery());
        detail.setClassify(carCustom.getClassify());
        detail.setMstMgrpId(carCustom.getMstMgrpId());
        if (detail.getDepositType() == null){
            detail.setDepositType(DepositTypeEnum.STOCK.getType());
        }

        ConfigDetail configDetail = new ConfigDetail();
        SeriesDetail seriesDetail = new SeriesDetail();
        seriesDetail.setCustomSeriesId(customSeriesDto.getCustomSeriesId());
        seriesDetail.setSeriesCode(customSeriesDto.getSeriesCode());
        seriesDetail.setSeriesNameCn(carCustom.getModelDesc());
        seriesDetail.setOmdSeriesCode(customSeriesDto.getOmdSeriesCode());
        configDetail.setCarSeries(seriesDetail);

        ModelDetail modelDetail = new ModelDetail();
        modelDetail.setModellineId(modelLine.getModelLineId());
        modelDetail.setModelCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        modelDetail.setModelNameCn(carCustom.getAccbTypeDesc());
        modelDetail.setModelYear(modelLine.getModelYear());
        modelDetail.setOmdModelVersion(modelLine.getVersion());
        if (customSeriesDto.getCustomSeriesCode().equals("49")){
            modelDetail.setDeliveryTime(appConfig.getCc().getDelivery());
        }else if (customSeriesDto.getCustomSeriesCode().equals("G4")){
            modelDetail.setDeliveryTime(appConfig.getCc().getQ5eDelivery());
        }
        modelDetail.setModelLineCode(modelLine.getModelLineCode());
        modelDetail.setModelPrice(modelLine.getPrice());
        modelDetail.setCustomModelCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        configDetail.setCarModel(modelDetail);
        ColorDetail insideColor = new ColorDetail();
        if (carCustom.getSibInterieurId() != null) {
            SibInterieurQueryDto sibInterieurQueryDto = new SibInterieurQueryDto();
            sibInterieurQueryDto.setChannel(channel);
            sibInterieurQueryDto.setModelLineId(carCustom.getModelLineId());
            sibInterieurQueryDto.setSibInterieurId(carCustom.getSibInterieurId());
            sibInterieurQueryDto.setDelFlag(0);
            List<ModelLineSibInterieurVo> sibInterieurs = modelLineSibInterieurService.modelLineSibInterieur(sibInterieurQueryDto);
            ModelLineSibInterieurVo sibInterieur = sibInterieurs.get(0);
            insideColor.setColorNameCn(sibInterieur.getDescription());
            insideColor.setColorCode(sibInterieur.getSibInterieurCode());
            insideColor.setImageUrl(sibInterieur.getImageUrl());
            insideColor.setPrice(sibInterieur.getPrice());
        }
        configDetail.setInsideColor(insideColor);
        configDetail.setOptionList(new ArrayList<>());
        ColorDetail outsideColor = new ColorDetail();
        configDetail.setOutsideColor(outsideColor);
        BigDecimal totalPrice = null;
        if (modelLine.getPrice() instanceof BigDecimal) {
            totalPrice = (BigDecimal) modelLine.getPrice();
        }
        List<Option> options = new ArrayList<>();
        String exterieurCode = null;
        String radCode = null;
        List<String> optionIds = new ArrayList<>();
        for(CarCustomOption i : optionList){
            List<ModelLineOptionVo> items =  modelLineOptionMapper.findPaketItemFromCodes(customSeriesDto.getCustomSeriesId(), modelLine.getModelLineId(), i.getCode(), optionCodes);
            boolean priceHandle = true;
            if (CollectionUtils.isNotEmpty(items)){
                priceHandle = false;
            }
            optionIds.add(i.getOptionId());
            List<ModelLineOptionVo> optionVos = modelLineService.optionQueryByOptionId(channel, modelLine.getCustomSeriesId(), modelLine.getModelLineId(), i.getOptionId(), priceHandle);
            String imageUrlList = null;
            String imageUrl = null;
            boolean discount = false;
            if (!CollectionUtils.isEmpty(optionVos)){
                for(ModelLineOptionVo o : optionVos){
                    imageUrlList = o.getImageUrlList();
                    imageUrl = o.getImageUrl();
                }
                if (optionVos.size() == 1){
                    imageUrlList = optionVos.get(0).getImageUrlList();
                    imageUrl = optionVos.get(0).getImageUrl();
                    if(StringUtils.isNotBlank(detail.getCouponNo()) && "1".equals(optionVos.get(0).getEquipmentRights())){
                        discount = true;
                    }
                }
            }
            if (OptionCategoryEnum.OUTCOLOR.getValue().equals(i.getCategory())){
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                if (code.startsWith("COLOR_EXTERIEUR:")){
                    code = code.replaceFirst("COLOR_EXTERIEUR:", "");
                }
                exterieurCode = code;
                outsideColor.setColorCode(code);
                outsideColor.setColorNameCn(i.getDescription());
                outsideColor.setImageUrl(imageUrl);
                BigDecimal outsideColorPrice = priceTypeService.optionPrice(modelLine.getModelLineId(), code, i.getCategory());
                outsideColor.setPrice(outsideColorPrice);
                if (discount){
                    outsideColor.setDiscount(outsideColor.getPrice());
                }
                if (totalPrice != null && outsideColorPrice != null)
                    totalPrice = totalPrice.add(outsideColorPrice);
            }else if (OptionCategoryEnum.INCOLOR.getValue().equals(i.getCategory()) || OptionCategoryEnum.SIB.getValue().equals(i.getCategory())){
                continue;
            }else {
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                String[] codes = code.split(":");
                if (codes.length == 1){
                    code = codes[0];
                }else if (codes.length == 2){
                    code = codes[1];
                }
                if (OptionCategoryEnum.WHEEL.getValue().equals(i.getCategory())){
                    radCode = code;
                }
                Option option = new Option();
                option.setOptionNameCn(i.getDescription());
                option.setOptionCode(code);
                option.setOptionClassification(i.getCategory());
                option.setOptionClassification2nd(i.getCategory());
                option.setImageUrl(imageUrlList);
                options.add(option);
                BigDecimal optionPrice = priceTypeService.optionPrice(modelLine.getModelLineId(), code, i.getCategory());
                option.setOptionPrice(optionPrice);
                if (discount){
                    option.setDiscount(option.getOptionPrice());
                }
                if (totalPrice != null && optionPrice != null)
                    totalPrice = totalPrice.add(optionPrice);
            }
        }
        // 查询默认轮毂
        if (radCode == null){
            ModelLineOptionVo optionVo = new ModelLineOptionVo();
            optionVo.setModelLineId(modelLine.getModelLineId());
            optionVo.setStatus(1);
            optionVo.setCategory("RAD");
            optionVo.setDelFlag(0);
            List<ModelLineOptionVo> optionVos = modelLineOptionService.listModelLineOption(optionVo);
            if (CollectionUtils.isEmpty(optionVos)){
                throw new ServiceException("50001", "数据异常");
            }
            radCode = optionVos.get(0).getOptionCode();
        }
        modelDetail.setHeadImageUrl(configImageService.getCcHeadImageUrl(channel, carCustom.getModelLineId(), exterieurCode));
        modelDetail.setImageUrl(configImageService.getCcLeftImageUrl(channel, carCustom.getModelLineId(), exterieurCode, radCode));
        PriceComputeParam priceComputeParam = new PriceComputeParam();
        priceComputeParam.setModelLineId(modelLine.getModelLineId());
        priceComputeParam.setOptionIds(optionIds);
        priceComputeParam.setCustomSeriesId(customSeriesDto.getCustomSeriesId());
        priceComputeParam.setCouponNo(detail.getCouponNo());
        configDetail.setTotalPrice(modelLineService.computePrice(priceComputeParam));
        configDetail.setDiscount(modelLineService.computeDiscount(priceComputeParam));
        configDetail.setOptionList(options);
        detail.setConfigDetail(configDetail);
        detail.setBestRecommendId(JsonString.valueOf(carCustom.getBestRecommendId()));
        return detail;
    }


    public CarCustomDetail getCarConfigDetailContract(CarCustom carCustom) throws Exception {
        QueryWrapper<CarCustomOption> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ccid", carCustom.getCcid());
        List<CarCustomOption> optionList = carCustomOptionService.list(queryWrapper);
        List<String> optionCodes = optionList.stream().map(CarCustomOption::getCode).collect(Collectors.toList());
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(carCustom.getModelLineId());
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (lines == null ||  lines.size() != 1){
            throw new ServiceException("500", "服务异常", null);
        }
        ModelLineVo modelLine = lines.get(0);
        CustomSeriesDto customSeriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
        CarCustomDetail detail = new CarCustomDetail();
        if(StringUtils.isNotBlank(carCustom.getMemberId())){
            JSONObject couponRes = copOrderQueryFeign.couponInfo(String.valueOf(carCustom.getCcid()), customSeriesDto.getSeriesCode(), carCustom.getMemberId());
            if (couponRes.getJSONArray("data")!=null && couponRes.getJSONArray("data").size()>0){
                List<CouponInfoVo> couponInfoVos = couponRes.getJSONArray("data").toJavaList(CouponInfoVo.class);
                detail.setCouponNo(couponInfoVos.get(0).getMpEquityNo());
            }
        }
        detail.setValid(carCustom.getValid());
        detail.setInvalidReason(carCustom.getInvalidReason());
        detail.setUpdateFlag(carCustom.getUpdateFlag());
        detail.setUpdateContent(carCustom.getUpdateContent());
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        detail.setConfigTime(carCustom.getCreateTime());
        detail.setDepositType(carCustom.getDepositType());
        detail.setEstimateDelivery(carCustom.getEstimateDelivery());
        detail.setClassify(carCustom.getClassify());
        detail.setMstMgrpId(carCustom.getMstMgrpId());
        if (detail.getDepositType() == null){
            detail.setDepositType(DepositTypeEnum.STOCK.getType());
        }

        ConfigDetail configDetail = new ConfigDetail();
        SeriesDetail seriesDetail = new SeriesDetail();
        seriesDetail.setSeriesCode(customSeriesDto.getSeriesCode());
        seriesDetail.setSeriesNameCn(carCustom.getModelDesc());
        configDetail.setCarSeries(seriesDetail);

        ModelDetail modelDetail = new ModelDetail();
        modelDetail.setModelCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        modelDetail.setModelNameCn(carCustom.getAccbTypeDesc());
        modelDetail.setModelYear(modelLine.getModelYear());
        modelDetail.setOmdModelVersion(modelLine.getVersion());
        modelDetail.setModelPrice(modelLine.getPrice());
        modelDetail.setCustomModelCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        configDetail.setCarModel(modelDetail);

        ColorDetail insideColor = new ColorDetail();
        configDetail.setInsideColor(insideColor);
        configDetail.setOptionList(new ArrayList<>());
        ColorDetail outsideColor = new ColorDetail();
        configDetail.setOutsideColor(outsideColor);
        BigDecimal totalPrice = null;
        if (modelLine.getPrice() instanceof BigDecimal) {
            totalPrice = (BigDecimal) modelLine.getPrice();
        }
        OptionRelateParam optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLine.getModelLineId());
        optionRelateParam.setRelateTypes(Arrays.asList("attach"));
        List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        OptionRelateParam combineRelateParam = new OptionRelateParam();
        combineRelateParam.setRelateTypes(Arrays.asList("combine"));
        List<OptionRelDto> relCombineDtos =  optionRelateService.listOptionRelDto(combineRelateParam);
        relDtos.addAll(relCombineDtos);
        Map<String, List<OptionRelDto>> oMap = new HashMap<>();
        relDtos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel == null) {
                oRel = new ArrayList<>();
                oMap.put(i.getOptionId(), oRel);
            }
            oRel.add(i);
        });
        List<Option> options = new ArrayList<>();
        List<String> optionIds = new ArrayList<>();
        for(CarCustomOption i : optionList){
            List<ModelLineOptionVo> items =  modelLineOptionMapper.findPaketItemFromCodes(customSeriesDto.getCustomSeriesId(), modelLine.getModelLineId(), i.getCode(), optionCodes);
            boolean priceHandle = true;
            if (CollectionUtils.isNotEmpty(items)){
                priceHandle = false;
            }
            List<ModelLineOptionVo> optionVos = modelLineService.optionQueryByOptionId(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId(), modelLine.getModelLineId(), i.getOptionId(), priceHandle);
            if (optionVos == null || optionVos.size() != 1){
                throw new ServiceException("400401", "参数异常：optionIds", null);
            }
            ModelLineOptionVo optionVo = optionVos.get(0);
            boolean discount = false;
            // 过滤选装包的件与标装
            if (optionVo.getOptionType().equals("packet-item") || (optionVo.getStatus() != null && optionVo.getStatus().intValue() == 1)){
                continue;
            }
            if(StringUtils.isNotBlank(detail.getCouponNo()) && "1".equals(optionVos.get(0).getEquipmentRights())){
                discount = true;
            }
            optionIds.add(i.getOptionId());
            if (i.getCategory() != null && i.getCategory().equals("COLOR_EXTERIEUR")){
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                if (code.startsWith("COLOR_EXTERIEUR:")){
                    code = code.replaceFirst("COLOR_EXTERIEUR:", "");
                }
                outsideColor.setColorCode(code);
                outsideColor.setColorNameCn(i.getDescription());
                BigDecimal outsideColorPrice = priceTypeService.optionPrice(modelLine.getModelLineId(), code, i.getCategory());
                outsideColor.setPrice(outsideColorPrice);
                if (discount){
                    outsideColor.setDiscount(outsideColor.getPrice());
                }
                if (totalPrice != null && outsideColorPrice != null)
                    totalPrice = totalPrice.add(outsideColorPrice);
                CarSibInterieur sibInterieur = sibInterieurService.getSibInterieur(carCustom.getSibInterieurId(), Constant.MASTER_CHANNEL);
                insideColor.setColorCode(sibInterieur.getSibInterieurCode());
                insideColor.setColorNameCn(sibInterieur.getDescription());
            }else if (Arrays.asList("COLOR_INTERIEUR", "SIB").contains(i.getCategory()) ){
                // 内饰面料不单独发到合同
                continue;
            }else {
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                String[] codes = code.split(":");
                if (codes.length == 1){
                    code = codes[0];
                }else if (codes.length == 2){
                    code = codes[1];
                }
                Option option = new Option();
                option.setOptionNameCn(i.getDescription());
                option.setOptionCode(code);
                option.setOptionClassification(i.getCategory());
                option.setOptionClassification2nd(i.getCategory());
                options.add(option);
                BigDecimal optionPrice = priceTypeService.optionPrice(modelLine.getModelLineId(), code, i.getCategory());
                option.setOptionPrice(optionPrice);
                if (discount){
                    option.setDiscount(option.getOptionPrice());
                }
                if (totalPrice != null && optionPrice != null) {
                    totalPrice = totalPrice.add(optionPrice);
                }
            }
        }
        PriceComputeParam priceComputeParam = new PriceComputeParam();
        priceComputeParam.setModelLineId(modelLine.getModelLineId());
        priceComputeParam.setOptionIds(optionIds);
        priceComputeParam.setCouponNo(detail.getCouponNo());
        configDetail.setTotalPrice(modelLineService.computePrice(priceComputeParam));
        configDetail.setDiscount(modelLineService.computeDiscount(priceComputeParam));
        configDetail.setOptionList(options);
        detail.setConfigDetail(configDetail);
        detail.setBestRecommendId(JsonString.valueOf(carCustom.getBestRecommendId()));
        return detail;
    }

}
