package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.csvw.audi.cc.entity.enumeration.CcInvalidReasonEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarCustom对象", description="")
public class CarCustom extends Model<CarCustom> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "配置id")
      @TableId(value = "ccid", type = IdType.ASSIGN_ID)
    private Long ccid;

    @ApiModelProperty(value = "accb配置编码")
    private String audiCode;

    @ApiModelProperty(value = "ACCB车系ID")
    private String accbModelId;

    @ApiModelProperty(value = "ACCB车系编码")
    private String accbModelCode;

    @ApiModelProperty(value = "车系描述")
    private String modelDesc;

    @ApiModelProperty(value = "ACCB车型ID")
    private String accbTypeId;

    @ApiModelProperty(value = "ACCB车型编码")
    private String accbTypeCode;

    @ApiModelProperty(value = "ACCB车型描述")
    private String accbTypeDesc;

    @ApiModelProperty(value = "年款")
    private String modelYear;

    @ApiModelProperty(value = "内饰颜色面料id")
    private String sibInterieurId;

    @ApiModelProperty(value = "账号id")
    private String userId;

    @ApiModelProperty(value = "账号手机号")
    private String userMobile;

    @ApiModelProperty(value = "创建人ID")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "配置线id")
    private String modelLineId;

    @ApiModelProperty(value = "畅销推荐车id")
    private Long bestRecommendId;

    @ApiModelProperty(value = "半订制id")
    private Long measureId;

    @ApiModelProperty(value = "omd清单id")
    private Long omdVehicleTypeId;

    @ApiModelProperty(value = "车辆清单类型")
    private String classify;

    private Integer classifyVersion;

    @ApiModelProperty(value = "预计交付周期")
    private String estimateDelivery;

    @ApiModelProperty(value = "来源ID")
    private Long sourceId;

    @ApiModelProperty(value = "入口")
    private String entryPoint;

    @ApiModelProperty(value = "定金类型，1：排产，2：库存")
    private String depositType;

    @ApiModelProperty(value = "配置单是否有效，0:失效，1:有效")
    private Integer valid;

    private String invalidReason;

    @ApiModelProperty(value = "配置单更新标识，0:无更新，1:有更新")
    private Integer updateFlag;

    @ApiModelProperty(value = "配置单更新内容")
    private String updateContent;

    private Long snapshotId;

    private String mstMgrpId;

    private String memberId;

    @Override
    protected Serializable pkVal() {
        return this.ccid;
    }

}
