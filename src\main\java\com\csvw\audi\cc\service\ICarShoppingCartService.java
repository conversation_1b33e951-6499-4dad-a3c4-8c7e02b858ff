package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.dto.CarShoppingCartDto;
import com.csvw.audi.cc.entity.po.CarCustom;
import com.csvw.audi.cc.entity.po.CarShoppingCart;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.vo.CarShoppingCartParam;
import com.csvw.audi.cc.entity.vo.CarShoppingCartVo;

import java.util.List;

/**
 * <p>
 * 购物车 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
public interface ICarShoppingCartService extends IService<CarShoppingCart> {

    public List<CarShoppingCartVo> getCarShoppingCartList(String userId);

    public List<String> delCarShoppingCartByIds(CarShoppingCartVo carShoppingCartVo);

    public CarShoppingCart saveCarShoppingCart(CarShoppingCartParam carShoppingCartParam);

    public CarShoppingCart autoSaveCarShoppingCart(String memberId, String userId, String mobile, String channel, CarShoppingCartVo carShoppingCartVo) throws Exception;

    public CarCustom createdAutoSaveCarShoppingCart(String memberId, String userId, String mobile, String channel) throws Exception;

    public void finishInvitationIntegral(CarShoppingCartDto dto);

    public CarShoppingCartVo getCarShoppingCartDetail(String shoppingCartId);

    CarShoppingCartVo getLatestCarShoppingCartList(String userId);
}
