package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.dto.omd.OmdModelDto;
import com.csvw.audi.cc.entity.po.CarOmdStockMap;
import com.csvw.audi.cc.entity.po.CarRecommend;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.vo.BestRecommendCarVo;
import com.csvw.audi.cc.entity.vo.RecommendCarSphereVo;
import com.csvw.audi.cc.entity.vo.RecommendCarVo;
import com.csvw.audi.cc.exception.ServiceException;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 推荐车 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
public interface ICarRecommendService extends IService<CarRecommend> {

    List<RecommendCarVo> recommendCar(String channel, String customSeriesId);

    List<RecommendCarSphereVo> recommendCarSphere(String channel, String customSeriesId);

    List<BestRecommendCarVo> omdBestRecommendCar(String channel, String dealerCode, String customSeriesId);

    void convertStockRecommendCarStockMap();

    void convertStockRecommendCar();

    void syncOmdBestRecommendCar();

    @Transactional
    void syncOmdBestRecommendCarStockMap(List<CarOmdStockMap> stockMaps);

    void syncOmdBestRecommendCarHqVehicle();

    void syncOmdBestRecommendCarStockVehicle();

    void lockBestRecommendStock(Long ccid, String dealerCode) throws ServiceException;

    void unlockBestRecommendStock(Long ccid) throws ServiceException;

    List<BestRecommendCarVo> omdBestRecommendCarFix(String channel, String dealerCode, String customSeriesId);

    BestRecommendCarVo bestRecommendToVo(OmdModelDto omdModelDto, String channel) throws Exception;

    boolean validHqType(BestRecommendCarVo i, Integer type);

    Long bestRecommendStockNum(Long ccid, String dealerCode) throws ServiceException;

    Long findHqRecommendId(Long bestSellRecommendModelId);

    Long findDealerRecommendId(Long stockRecommendModelId);
}
