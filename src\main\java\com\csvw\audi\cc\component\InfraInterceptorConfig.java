package com.csvw.audi.cc.component;

import com.csvw.audi.cc.config.AppConfig;
import com.csvw.audi.cc.service.impl.SignService;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;

import java.util.Collection;
import java.util.Map;

public class InfraInterceptorConfig {

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private SignService signService;

    @Bean("infraInterceptor")
    public RequestInterceptor getRequestInterceptor() {
        return new InfraInterceptor(appConfig, signService);
    }

}

//拦截器实现
@Slf4j
class InfraInterceptor implements RequestInterceptor {

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private SignService signService;

    InfraInterceptor(AppConfig appConfig, SignService signService){
        this.appConfig = appConfig;
        this.signService = signService;
    }

    @Override
    public void apply(RequestTemplate requestTemplate) {
        Map<String, Collection<String>> headers = requestTemplate.headers();
        if (headers.containsKey("X-App-Id")){
            return;
        }
        if (requestTemplate.method().equalsIgnoreCase("POST")) {
            String timestamp = signService.getTimestamp();
            String sequenceNo = signService.getSequence(timestamp);
            String requestBody = requestTemplate.requestBody().asString();
            String sign = signService.signPostData(sequenceNo, timestamp, requestBody);
            log.info("INFRA FEIGN 拦截签名处理，url: {} , X-Sequence-No, {} , body: {}",
                    requestTemplate.url(), sequenceNo, requestBody);
            requestTemplate.header("X-App-Id", appConfig.getInfra().getAppId());
            requestTemplate.header("X-Sequence-No", sequenceNo);
            requestTemplate.header("X-Timestamp", timestamp);
            requestTemplate.header("X-Signature", sign);
        }else if(requestTemplate.method().equalsIgnoreCase("GET")){
            String timestamp = signService.getTimestamp();
            String sequenceNo = signService.getSequence(timestamp);
            String sign = signService.signQueryData(sequenceNo, timestamp);
            log.info("INFRA FEIGN 拦截签名处理，url: {} , X-Sequence-No, {}",
                    requestTemplate.url(), sequenceNo);
            requestTemplate.header("X-App-Id", appConfig.getInfra().getAppId());
            requestTemplate.header("X-Sequence-No", sequenceNo);
            requestTemplate.header("X-Timestamp", timestamp);
            requestTemplate.header("X-Signature", sign);
        }

    }

}
