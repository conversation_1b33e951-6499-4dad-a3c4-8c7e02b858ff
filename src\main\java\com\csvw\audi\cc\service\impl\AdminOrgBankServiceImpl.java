package com.csvw.audi.cc.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.csvw.audi.cc.entity.dto.AdminOrgBankDto;
import com.csvw.audi.cc.entity.po.AdminOrgBank;
import com.csvw.audi.cc.entity.vo.AdminOrgBankVo;
import com.csvw.audi.cc.entity.vo.ImportOrgBank;
import com.csvw.audi.cc.entity.vo.ImportRes;
import com.csvw.audi.cc.mapper.AdminOrgBankMapper;
import com.csvw.audi.cc.service.IAdminOrgBankService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csvw.sx.utils.StringUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 渠道商对应的金融机构 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-17
 */
@Service
public class AdminOrgBankServiceImpl extends ServiceImpl<AdminOrgBankMapper, AdminOrgBank> implements IAdminOrgBankService {

    //图片OSS路径
    static String imageUrl = "2022/01/25/";

    @Autowired
    private AdminOrgBankMapper adminOrgBankMapper;

    @Override
    public ImportRes dataImport(String userId,InputStream inputStream) throws Exception {
        ImportParams params = new ImportParams();
        params.setSheetNum(1);
        params.setNeedVerify(true);
        List<ImportOrgBank> importData = ExcelImportUtil.importExcel(inputStream, ImportOrgBank.class, params);
        ImportRes importRes = new ImportRes();
        ImportOrgBank orgBank;//临时变量
        List<String> failedInfos = new ArrayList<>();//导入失败的信息
        int failureNum = 0;//导入失败的数量
        boolean failureFlag = false;
        ArrayList<AdminOrgBank> successData = new ArrayList<>(importData.size());
        AdminOrgBank adminOrgBank;//临时变量
        LocalDateTime nowDate = LocalDateTime.now();
        for(int i=0;i<importData.size();i++){
            orgBank = importData.get(i);
            StringBuffer sb = new StringBuffer();
            failureFlag = false;
            if (StringUtils.isBlank(orgBank.getDealerCode())){
                sb.append("，代理商代码为空");
                failureFlag = true;
            }
            if (StringUtils.isBlank(orgBank.getBankName())){
                sb.append("，金融机构名称为空");
                failureFlag = true;
            }
            if (StringUtils.isBlank(orgBank.getBankCode())){
                sb.append("，金融机构代码为空");
                failureFlag = true;
            }
            if(failureFlag) {
                failedInfos.add("第"+(i+2)+"行"+sb.toString());
                failureNum++;
                continue;
            }
            //导入成功的数据
            adminOrgBank = new AdminOrgBank();
            adminOrgBank.setDealerCode(orgBank.getDealerCode());
            adminOrgBank.setDealerName(orgBank.getDealerName());
            adminOrgBank.setDealerFullName(orgBank.getDealerFullName());
            adminOrgBank.setBankCode(orgBank.getBankCode());
            adminOrgBank.setBankName(orgBank.getBankName());
            adminOrgBank.setDelFlag("0");
            adminOrgBank.setCreatedUser(userId);
            adminOrgBank.setUpdatedUser(userId);
            adminOrgBank.setCreatedTime(nowDate);
            adminOrgBank.setUpdatedTime(nowDate);
            successData.add(adminOrgBank);
        }
        //更新金融方案确认文案
        QueryWrapper<AdminOrgBank> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT bank_code","confirm_text").isNotNull("confirm_text");
        List<AdminOrgBank> list = this.list(queryWrapper);
        for(AdminOrgBank successDataObj : successData) {
            for(AdminOrgBank historyObj : list) {
                if(successDataObj.getBankCode().equals(historyObj.getBankCode())) {
                    successDataObj.setConfirmText(historyObj.getConfirmText());
                }
            }
        }

        //保存数据
        this.saveBatch(successData);

        //返回导入完成响应集
        importRes.setTotal(importData.size());
        importRes.setFailureNum(failureNum);
        importRes.setSuccessNum(importData.size()-failureNum);
        importRes.setFailedInfos(failedInfos);
        return importRes;
    }

    @Override
    public List<AdminOrgBankVo> getOrgBankList(AdminOrgBankDto dto) {
        List<AdminOrgBankVo> list = adminOrgBankMapper.getOrgBankList(dto);

        //图片处理
        for(AdminOrgBankVo vo : list) {
            vo.setImageUrl(imageUrl+vo.getBankCode()+"_bank.png");
        }
        return list;
    }

    @Override
    public PageInfo<AdminOrgBankVo> getOrgBankList(AdminOrgBankDto dto, Page<AdminOrgBankVo> page) {
        PageHelper.startPage(Long.valueOf(page.getCurrent()).intValue(),Long.valueOf(page.getSize()).intValue());
        List<AdminOrgBankVo> list = adminOrgBankMapper.getOrgBankList(dto);
        //图片处理
        for(AdminOrgBankVo vo : list) {
            vo.setImageUrl(imageUrl+vo.getBankCode()+"_bank.png");
        }
        PageInfo<AdminOrgBankVo> pageInfo = new PageInfo<>(list);
        return pageInfo;
    }

    @Override
    public void delOrgBankByIds(List<String> orgBankIds) {
        AdminOrgBank adminOrgBank = new AdminOrgBank();
        adminOrgBank.setDelFlag("1");
        UpdateWrapper<AdminOrgBank> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("org_bank_id", new ArrayList<>(orgBankIds));
        this.update(adminOrgBank,updateWrapper);
    }

    @Override
    public List<AdminOrgBank> getBankList() {
	    List<AdminOrgBank> adminOrgBankList = this.lambdaQuery().eq(AdminOrgBank::getDelFlag, 0).list();
		if (CollectionUtil.isNotEmpty(adminOrgBankList)){
			List<AdminOrgBank> adminOrgBanks = adminOrgBankList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(s -> s.getBankCode() + ";" + s.getBankName()))), ArrayList::new));
			if (CollectionUtil.isNotEmpty(adminOrgBanks)){
                return adminOrgBanks;
			}
		}
        return new ArrayList<>();
    }
}
