package com.csvw.audi.cc.controller.ccnew;


import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.common.utils.CcEncryptUtils;
import com.csvw.audi.cc.config.AppConfig;
import com.csvw.audi.cc.entity.dto.*;
import com.csvw.audi.cc.entity.dto.omd.AmsModelQueryRecordParam;
import com.csvw.audi.cc.entity.dto.omd.ModelQueryBody;
import com.csvw.audi.cc.entity.dto.omd.ModelQueryRes;
import com.csvw.audi.cc.entity.dto.vwcc.Option;
import com.csvw.audi.cc.entity.enumeration.OptionCategoryEnum;
import com.csvw.audi.cc.entity.po.CarModelLineSpecialOption;
import com.csvw.audi.cc.entity.vo.AmsModelQueryVo;
import com.csvw.audi.cc.entity.vo.AmsQueryVo;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.entity.vo.ModelLineVo;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.feign.AudiServiceAdapter;
import com.csvw.audi.cc.service.ICarConfigImageService;
import com.csvw.audi.cc.service.ICarModelLineService;
import com.csvw.audi.cc.service.ICarModelLineSpecialOptionService;
import com.csvw.audi.cc.service.ICarOptionRelateService;
import com.csvw.audi.cc.service.impl.OmdServiceImpl;
import feign.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


@RestController
@RequestMapping( value = {"/api/v1/cc/public/ams"})
@Slf4j
@Api(tags = "AMS小程序车辆订单查询")
public class AmsController extends BaseController {

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private ICarModelLineService modelLineService;

    @Autowired
    private AudiServiceAdapter serviceAdapter;

    @Autowired
    private OmdServiceImpl omdService;

    @Autowired
    private ICarOptionRelateService optionRelateService;

    @Autowired
    private ICarConfigImageService configImageService;

    @Autowired
    private ICarModelLineSpecialOptionService specialOptionService;

    ExecutorService executorService = Executors.newFixedThreadPool(5);

    @PostMapping(value = {"/modelQuery"})
    @ApiOperation("AMS经销商车辆查询")
    public AjaxMessage<AmsModelQueryVo> modelQuery(@RequestHeader(name="channel", defaultValue = Constant.AMS_MINI_APP_CHANNEL) String channel,
                                                   @Validated @RequestBody AmsQueryDto amsQueryDto,
                                                   BindingResult bindingResult) throws Exception {
        super.validParam(bindingResult);
        AmsModelQueryVo resVo = new AmsModelQueryVo();
        String amsParamJson = CcEncryptUtils.decryptAes(amsQueryDto.getAmsParam(),
                appConfig.getInfra().getAmsAesKey(),
                appConfig.getInfra().getAmsAesIv());
        log.info("ams decrypt json: {}", amsQueryDto);
        JSONObject amsParam = JSONObject.parseObject(amsParamJson);
        String orgCode = amsParam.getString("orgCode");
        String userUid = amsParam.getString("userUid");
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(amsQueryDto.getModelLineId());
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        ModelLineVo modelLine = lines.get(0);
        List<ModelLineOptionVo> vos = modelLineService.optionQueryByOptionIds(channel, modelLine.getCustomSeriesId(),
                modelLine.getModelLineId(),amsQueryDto.getOptionIds());
        Iterator<ModelLineOptionVo> vosIt = vos.iterator();
        while (vosIt.hasNext()){
            ModelLineOptionVo vo = vosIt.next();
            if (OptionCategoryEnum.SIB.getValue().equals(vo.getCategory()) && vo.getStatus() == 1){
                vosIt.remove();
            }
        }
        AmsModelQueryRecordParam recordParam = new AmsModelQueryRecordParam();
        recordParam.setModelName(modelLine.getModelLineName());
        recordParam.setModelCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        recordParam.setQueryTime(LocalDateTime.now());
        recordParam.setOrgCode(orgCode);
        recordParam.setUserUid(userUid);
        recordParam.setSeriesCode(modelLine.getCustomSeriesCode());
        recordParam.setSeriesName(modelLine.getCustomSeriesName());

        String colorCode = null;
        String interiorCode = null;
        List<String> prs = new ArrayList<>();
        List<AmsModelQueryRecordParam.Idzs> idzsList = new ArrayList<>();
        for (ModelLineOptionVo vo : vos){
            if (OptionCategoryEnum.OUTCOLOR.getValue().equals(vo.getCategory())){
                colorCode = vo.getOptionCode();
                recordParam.setColorCode(vo.getOptionCode());
                recordParam.setColorName(vo.getOptionName());
            }else if (OptionCategoryEnum.INCOLOR.getValue().equals(vo.getCategory())){
                interiorCode = vo.getOptionCode();
                recordParam.setColorInsideCode(vo.getOptionCode());
                recordParam.setColorInsideName(vo.getOptionName());
            } else {
                AmsModelQueryRecordParam.Idzs idzs = new AmsModelQueryRecordParam.Idzs();
                idzs.setIdzCode(vo.getOptionCode());
                idzs.setIdzName(vo.getOptionName());
                idzsList.add(idzs);
                // 组合
                OptionRelateParam optionRelateParam = new OptionRelateParam();
                optionRelateParam.setOptionId(vo.getOptionId());
                optionRelateParam.setRelateTypes(Arrays.asList("combine"));
                List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
                if(CollectionUtils.isNotEmpty(relDtos)){
                    relDtos.forEach(i->prs.add(i.getOptionRelateCode()));
                }else {
                    prs.add(vo.getOptionCode());
                }
            }
        }
        if (StringUtils.isBlank(colorCode)){
            throw new ServiceException("400400", "请选择外饰");
        }
        recordParam.setIdzs(idzsList);
        executorService.submit(()->{
            log.info("AMS记录埋点，参数：{}", recordParam);
            Response response = serviceAdapter.amsModelQueryRecord(recordParam);
        });

        if (modelLine.getSpecialLine().intValue() == 1){
            // 处理特殊配置线
            List<CarModelLineSpecialOption> specialOptions = specialOptionService.listModelLineSpecialOption(modelLine.getModelLineId());
            if(CollectionUtils.isEmpty(specialOptions)){
                throw new ServiceException("50000", "特殊配置线，配置单异常");
            }
            specialOptions.forEach(i->{
                prs.add(i.getOptionCode());
            });
        }
        ModelQueryBody modelQueryBody = new ModelQueryBody();
        modelQueryBody.setDealerNetCode(orgCode);
        modelQueryBody.setColorCode(colorCode);
        modelQueryBody.setInteriorCode(interiorCode);
        modelQueryBody.setAccbTypeCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        modelQueryBody.setModelCode(modelLine.getModelCode());
        modelQueryBody.setModelYear(modelLine.getModelYear());
        modelQueryBody.setModelVersion(modelLine.getVersion());
        modelQueryBody.setBrandCode(Constant.BRAND_CODE);
        modelQueryBody.setOptionalPrList(Strings.join(prs, ','));
        List<ModelQueryRes> models =  omdService.omdModelQuery(modelQueryBody);
        List<AmsQueryVo> modelVos = modelLineService.convertModelQuery(models, channel, modelQueryBody.getAccbTypeCode());
        resVo.setModelTypes(modelVos);
        resVo.setModelLineVo(modelLine);
        resVo.setHeadImageUrl(configImageService.getCcHeadImageUrl(channel, modelLine.getModelLineId(), recordParam.getColorCode()));
        resVo.setParamOptions(vos);
        return new AjaxMessage<>("00", "成功", resVo);
    }



}

