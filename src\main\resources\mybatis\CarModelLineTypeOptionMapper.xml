<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarModelLineTypeOptionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarModelLineTypeOption">
        <result column="id" property="id" />
        <result column="model_line_type_id" property="modelLineTypeId" />
        <result column="code" property="code" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, model_line_type_id, code
    </sql>

</mapper>
