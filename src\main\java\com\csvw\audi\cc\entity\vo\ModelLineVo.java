package com.csvw.audi.cc.entity.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ModelLineVo implements Serializable {

    @ApiModelProperty(value = "配置线id")
    private String modelLineId;

    @ApiModelProperty(value = "配置线名称")
    private String modelLineName;

    @ApiModelProperty(value = "配置线编码")
    private String modelLineCode;

    @ApiModelProperty(value = "accb配置线code")
    private String accbTypeCode;

    @ApiModelProperty(value = "accb配置线ID")
    private String accbTypeId;

    @ApiModelProperty(value = "配置线图片")
    private String imageUrl;

    @ApiModelProperty(value = "配置线图片1")
    private String imageUrl1;

    @ApiModelProperty(value = "车型id")
    private String modelId;

    @ApiModelProperty(value = "自定义车系id")
    private String customSeriesId;

    @ApiModelProperty(value = "车系code")
    private String customSeriesCode;

    @ApiModelProperty(value = "车系名称")
    private String customSeriesName;

    @ApiModelProperty(value = "omd车型编码")
    private String modelCode;

    @ApiModelProperty(value = "车型年款")
    private String modelYear;

    @ApiModelProperty(value = "预计交付时间")
    private String deliveryTime;

    @ApiModelProperty(value = "默认配置线")
    private Integer defaultConfig;

    @JsonIgnore
    private String channel;

    private String version;

    @JsonIgnore
    private Integer specialLine;

    @JsonIgnore
    private Integer weight;

    @ApiModelProperty(value = "1：半订制")
    private Integer measure;

    @ApiModelProperty(value = "1：私人高定")
    private Integer personal;

    @ApiModelProperty(value = "车辆价格加上颜色")
    private Integer priceAddColor;

    @ApiModelProperty(value = "发动机分组")
    private String engine;

    @ApiModelProperty(value = "前端显示状态")
    private Integer frontStatus;

    @ApiModelProperty(value = "配置线套装")
    private String suit;

    @ApiModelProperty(value = "套装图片")
    private String suitImageUrl;

    @ApiModelProperty("价格")
    private Object price;

    @ApiModelProperty("配车类型开放，1:A类,2:B类,3:C类,12:AB,13:AC,23:BC,123:ABC")
    private String typeFlag;

    private Integer classifyVersion;

    @ApiModelProperty(value = "omd车辆唯一码")
    private String omdModelUnicode;

    @ApiModelProperty(value = "omd车辆状态")
    private Integer omdModelStatus;

    private Integer fromOmd;

    @ApiModelProperty("标签")
    private List<CarTagVo> tags;

    @ApiModelProperty("车辆权益")
    private List<ModelLineOptionVo> packetEquity;

    @ApiModelProperty("销售亮点")
    private List<String> sellBlips;

    private List<TypeIdsBySeats> typeIdsBySeats;


}
