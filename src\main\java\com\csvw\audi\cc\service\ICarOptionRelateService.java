package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.dto.OptionRelDto;
import com.csvw.audi.cc.entity.dto.OptionRelateParam;
import com.csvw.audi.cc.entity.po.CarOptionRelate;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.exception.ServiceException;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 选装关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-16
 */
public interface ICarOptionRelateService extends IService<CarOptionRelate> {

    List<OptionRelDto> listOptionRelDto(OptionRelateParam optionRelateParam);

    List<OptionRelDto> listCombines(String modelLineId, String optionCode);

    List<CarOptionRelate> listConflictOptionRelate(String modelLineId, Set<String> optionIds);

    List<CarOptionRelate> listOptionConflict(String modelLineId, String optionId, Set<String> optionRelateIds);

    List<CarOptionRelate> listOptionDepend(String modelLineId, String optionId, Set<String> optionRelateIds);

    Boolean validDepends(String modelLineId, Set<String> optionIds) throws ServiceException;

    List<CarOptionRelate> listDependedByOptionId(String modelLineId, String optionId, Set<String> optionIds);

    List<CarOptionRelate> listSibInterieurDependedByOptionId(String modelLineId, String sibInterieurId, Set<String> optionIds);
}
