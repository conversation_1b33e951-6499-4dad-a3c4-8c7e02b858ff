package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 渠道商岗位信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SvcdPosition对象", description="渠道商岗位信息")
public class SvcdPosition extends Model<SvcdPosition> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "数据id")
      @TableId(value = "position_id", type = IdType.AUTO)
    private Long positionId;

    @ApiModelProperty(value = "岗位编号")
    private String code;

    @ApiModelProperty(value = "岗位名称")
    private String name;

    @ApiModelProperty(value = "岗位类型（1:关键岗位 2:重要岗位 3:一般岗位）")
    private Long type;

    @ApiModelProperty(value = "是否属于技术岗位（1:是 0:否）")
    private Long skill;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "岗位描述")
    private String remark;

    @ApiModelProperty(value = "状态（1:有效 0:失效）")
    private Long status;

    @ApiModelProperty(value = "面试要求（1:面试 0:不面试）")
    private Long interviewRequire;

    @ApiModelProperty(value = "测评要求（1:线上测评 2:线下测评 0:不测评）")
    private Long evaluationRequire;

    @ApiModelProperty(value = "培训要求（1:线上培训 2:线下培训 3:线上/线下培训 0:不培训")
    private Long trainRequire;

    @ApiModelProperty(value = "认证要求（1:线下认证 0:不认证）")
    private Long authRequire;

    @ApiModelProperty(value = "售后代码")
    private String serviceCode;

    @ApiModelProperty(value = "是否删除（0:未删除 1:已删除）")
    private Long deleted;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;


    @Override
    protected Serializable pkVal() {
        return this.positionId;
    }

}
