package com.csvw.audi.cc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.entity.dto.OptionPriceQuery;
import com.csvw.audi.cc.entity.dto.OptionRelDto;
import com.csvw.audi.cc.entity.dto.PriceCondition;
import com.csvw.audi.cc.entity.dto.TypePriceParam;
import com.csvw.audi.cc.entity.po.CarOmdPricePr;
import com.csvw.audi.cc.entity.po.CarOmdPriceType;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.mapper.CarOmdPriceTypeMapper;
import com.csvw.audi.cc.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Locale;

/**
 * <p>
 * 配置线价格 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-18
 */
@Service
public class CarOmdPriceTypeServiceImpl extends ServiceImpl<CarOmdPriceTypeMapper, CarOmdPriceType> implements ICarOmdPriceTypeService {

    @Autowired
    private ICarModelLineService modelLineService;

    @Autowired
    private ICarOmdPriceTypeService priceTypeService;

    @Autowired
    private CarOmdPriceTypeMapper priceTypeMapper;

    @Autowired
    private ICarOmdPricePrService pricePrService;

    @Autowired
    private ICarOmdPricePrpkgService pricePrpkgService;

    @Autowired
    private ICarOmdPriceColorService priceColorService;

    @Autowired
    private ICarOptionRelateService optionRelateService;

    @Override
    public BigDecimal typePrice(String modelLineId) throws ServiceException {
        PriceCondition condition = modelLineService.listPriceCondition(modelLineId);
        if (condition == null){
            String error = "配置线查询失败";
            if (modelLineId != null){
                error += ": modelLineId"+ modelLineId;
            }
            throw new ServiceException("5000", "价格查询失败", error);
        }
        return this.getPrice(condition);
    }

    @Override
    public BigDecimal typePrice(PriceCondition condition) throws ServiceException {
        return this.getPrice(condition);
    }

    @Override
    public BigDecimal accbTypePrice(TypePriceParam priceParam) throws ServiceException {
        PriceCondition condition = modelLineService.listAccbPriceCondition(priceParam);
        if (condition == null){
            String error = "配置线查询失败";
            if (priceParam != null){
                error += ": priceParam"+ JSONObject.toJSONString(priceParam);
            }
            throw new ServiceException("5000", "价格查询失败", error);
        }
        return this.getPrice(condition);
    }

    @Override
    public BigDecimal accbOptionPrice(OptionPriceQuery priceParam) throws ServiceException {
        PriceCondition condition = modelLineService.listAccbPriceCondition(priceParam);
        if (condition == null){
            String error = "配置线查询失败";
            if (priceParam != null){
                error += ": priceParam"+ JSONObject.toJSONString(priceParam);
            }
            throw new ServiceException("5000", "价格查询失败", error);
        }
        String code = priceParam.getCode();
        String[] codes = priceParam.getCode().split(":");
        if (codes.length > 1){
            code = codes[1];
        }
        condition.setOptionCode(code);
        switch (priceParam.getCategory()){
            case "COLOR_EXTERIEUR":
                return priceColorService.getPrice(condition);
            case "PACKET":
                return pricePrpkgService.getPrice(condition);
            default:
                return pricePrService.getPrice(condition);

        }
    }

    @Override
    public BigDecimal optionPrice(String modelLineId, String optionCode, String category) throws ServiceException {
        PriceCondition condition = modelLineService.listPriceCondition(modelLineId);
        if (condition == null){
            String error = "配置线查询失败";
            if (modelLineId != null){
                error += ": modelLineId"+ modelLineId;
            }
            throw new ServiceException("5000", "价格查询失败", error);
        }
        condition = condition.clone();
        // a7l 45黑影特殊规则，选装包价格=配置线价格差价
        if(condition.getAccbTypeCode().equals("498BZY-GPGCPGC-GWAEWAE-GYEHYEH")
                && condition.getModelYear().equals("2022") && condition.getModelVersion().equals("0")){
            BigDecimal baseTypePrice = typePrice(condition);
            BigDecimal sLinePrice = null;
            switch (optionCode){
                case "GZ2+PT1+2C7+8IZ+YEI":
                    condition.setAccbTypeCode("498BZY-MESTGZ2-GPGCPGC-GPT1PT1-GWAEWAE-GYEIYEI-MLSS2C7-MHSW8IZ");
                    sLinePrice = typePrice(condition);
                    if (baseTypePrice == null || sLinePrice == null){
                        return null;
                    }
                    return sLinePrice.subtract(baseTypePrice);
                case "PCY+PT1+2V3+9VS+YEJ":
                    condition.setAccbTypeCode("498BZY-GPCYPCY-GPGCPGC-GPT1PT1-GWAEWAE-GYEJYEJ-MFRI2V3-MLSE9VS");
                    sLinePrice = typePrice(condition);
                    if (baseTypePrice == null || sLinePrice == null){
                        return null;
                    }
                    return sLinePrice.subtract(baseTypePrice);
            }
        }
        // a7l 45黑影特殊规则，选装包价格=配置线价格差价
        if(condition.getAccbTypeCode().equals("498BZY-MREII56-GPAHPAH-GPS6PS6-GYEAYEA-MLRA2PF-MRAD53D")
                && condition.getModelYear().equals("2024") && condition.getModelVersion().equals("0")) {
            BigDecimal baseTypePrice = typePrice(condition);
            BigDecimal sLinePrice = null;
            switch (optionCode) {
                case "PCY+9VS+PDW+WA6+YEB":
                    condition.setAccbTypeCode("498BZY-MREII56-GPAHPAH-GPCYPCY-GPDWPDW-GPS6PS6-GWA6WA6-GYEBYEB-MEIH5MK-MRAD53D-MLSE9VS");
                    sLinePrice = typePrice(condition);
                    if (baseTypePrice == null || sLinePrice == null) {
                        return null;
                    }
                    // 扣掉5mk价格
                    return sLinePrice.subtract(baseTypePrice).subtract(this.optionPrice(modelLineId, "5MK", "EIH"));
            }
        }
        condition.setOptionCode(optionCode);
        category = category==null?"":category;
        String codes[];
        switch (category){
            case "COLOR_EXTERIEUR":
                return priceColorService.getPrice(condition);
            case "PACKET":
                codes = optionCode.split("\\+");
                if (codes.length > 1){
                    return combineOptionPrice(modelLineId, optionCode);
                }
                return pricePrpkgService.getPrice(condition);
            default:
                codes = optionCode.split("\\+");
                if (codes.length > 1){
                    return combineOptionPrice(modelLineId, optionCode);
                }
                return pricePrService.getPrice(condition);

        }
    }

    @Override
    public BigDecimal getPrice(PriceCondition condition) {
        return priceTypeMapper.getPrice(condition);
    }

    private BigDecimal combineOptionPrice(String modelLineId, String optionCode) throws ServiceException {
        List<OptionRelDto> relDtoList = optionRelateService.listCombines(modelLineId, optionCode);
        BigDecimal price = BigDecimal.ZERO;
        for (OptionRelDto relDto : relDtoList){
            String itemCategory = relDto.getOptionRelateCategory() == null?"":relDto.getOptionRelateCategory();
            BigDecimal itemPrice = optionPrice(modelLineId, relDto.getOptionRelateCode(), itemCategory);
            if (itemPrice != null){
                price = price.add(itemPrice);
            }else {
                return null;
            }
        }
        BigDecimal subPrice = this.priceSubRule(modelLineId, optionCode);
        price = price.subtract(subPrice);
        return price;
    }


    public BigDecimal priceSubRule(String modelLineId, String optionCode) throws ServiceException {
        // 圣殿减掉GS5价格
        if (modelLineId.equals("ee85594c-02ee-481a-9f2c-a071a6fec525") && optionCode.equals("PS8+4D8+PDW+WA6")){
            BigDecimal gs5Price = this.optionPrice(modelLineId, "GS5", null);
            return gs5Price;
        }
        return BigDecimal.ZERO;
    }
}
