package com.csvw.audi.cc.entity.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * sphere推荐车
 */
@Data
public class RecommendCarSphereVo implements Serializable {
    private String accbTypeCode;
    private String accbTypeId;
    private String customSeriesCode;
    private String modelLineCode;
    private String modelYear;
    @ApiModelProperty(value = "1：半订制")
    private Integer measure;
    @ApiModelProperty(value = "1：私人高定")
    private Integer personal;
    @ApiModelProperty("自定义车系id")
    private String customSeriesId;
    @ApiModelProperty("配置线名称")
    private String modelLineName;
    @ApiModelProperty("配置线图片地址")
    private String imageUrl;
    @ApiModelProperty("正视图地址")
    private String headImageUrl;
    @ApiModelProperty("价格")
    private Object price;
    @ApiModelProperty("交付时间")
    private String deliverTime;
    @ApiModelProperty("配置线id")
    private String modelLineId;
    @ApiModelProperty("外饰颜色id")
    private String exterieurOptionId;
    @ApiModelProperty("内饰颜色id")
    private String interieurOptionId;
    @ApiModelProperty("轮毂id")
    private String radOptionId;
    @ApiModelProperty("座椅id")
    private String vosOptionId;
    @ApiModelProperty("面料")
    private String sibOptionId;
    @ApiModelProperty("内饰颜色面料id")
    private String sibInterieurOptionId;
    @ApiModelProperty("私人订制项id")
    private List<String> personalOptionIds;
    @ApiModelProperty("外饰颜色code")
    private String exterieurOptionCode;
    @ApiModelProperty("内饰颜色id")
    private String interieurOptionCode;
    @ApiModelProperty("轮毂id")
    private String radOptionCode;
    @ApiModelProperty("座椅id")
    private String vosOptionCode;
    @ApiModelProperty("面料")
    private String sibOptionCode;
    @ApiModelProperty("内饰颜色面料id")
    private String sibInterieurOptionCode;
    @ApiModelProperty("私人订制项id")
    private List<String> personalOptionCodes;
    @ApiModelProperty
    private String audiCode;
    @ApiModelProperty(value = "配置线套装")
    private String suit;
    @ApiModelProperty(value = "权益选装包")
    private List<ModelLineOptionVo> packetEquity;
    @ApiModelProperty(value = "套装图片")
    private String suitImageUrl;
    @ApiModelProperty("配置线标签")
    private List<CarTagVo> tags;

    private String typeFlag;
    @JsonIgnore
    private ModelLineVo modelLineVo;

    @ApiModelProperty("销售亮点")
    private List<String> sellBlips;

    private Integer omdModelStatus;
}
