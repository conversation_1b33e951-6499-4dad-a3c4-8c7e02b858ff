package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 组织区划信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SvcdOrgRegion 对象", description="组织区划信息")
public class SvcdOrgRegion extends Model<SvcdOrgRegion> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "数据id")
      @TableId(value = "org_region_id", type = IdType.AUTO)
    private Long orgRegionId;

    @ApiModelProperty(value = "组织编码")
    private String code;

    @ApiModelProperty(value = "行政区划代码")
    private String regionCode;

    @ApiModelProperty(value = "行政区划类型(类型：region 地区;province 省;city 市;area 区县)")
    private String regionType;

    @ApiModelProperty(value = "组织区划名称")
    @TableField("`region_name`")
    private String name;

    @ApiModelProperty(value = "父级组织编码")
    private String pcode;

    @ApiModelProperty(value = "简称")
    private String simpleName;

    @ApiModelProperty(value = "速查码")
    private String quickCode;

    @ApiModelProperty(value = "组织区划拼音")
    private String divisionEn;

    @ApiModelProperty(value = "排序")
    private String sort;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;


    @Override
    protected Serializable pkVal() {
        return this.orgRegionId;
    }

}
