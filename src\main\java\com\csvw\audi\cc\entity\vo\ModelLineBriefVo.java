package com.csvw.audi.cc.entity.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ModelLineBriefVo {

    @ApiModelProperty(value = "车型id")
    private String modelLineId;

    @ApiModelProperty(value = "车型名字")
    private String modelLineName;

    @ApiModelProperty(value = "车系id")
    private String customSeriesId;

    @ApiModelProperty(value = "车系名字")
    private String customSeriesName;

    @ApiModelProperty(value = "accb配置线code")
    private String accbTypeCode;

    @ApiModelProperty(value = "年款")
    private String modelYear;

    @ApiModelProperty(value = "版本")
    private String version;

    @JsonIgnore
    private String channel;

}
