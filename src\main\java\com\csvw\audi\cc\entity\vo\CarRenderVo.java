package com.csvw.audi.cc.entity.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * SC车辆渲染图
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CarRenderVo {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "详图")
    private String preview;

    @ApiModelProperty(value = "配置")
    private String config;

}
