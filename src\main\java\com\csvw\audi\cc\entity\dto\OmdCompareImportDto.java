package com.csvw.audi.cc.entity.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class OmdCompareImportDto {
    @Excel(name = "OneApp订单编号")
    private String orderId;
    @Excel(name = "配置线代码")
    private String modelLineCode;
    @Excel(name = "车型年")
    private String modelYear;
    @Excel(name = "车型版本")
    private String modelVersion;
    @Excel(name = "内饰")
    private String insideCode;
    @Excel(name = "颜色代码")
    private String outsideCode;
    @Excel(name = "选装列表")
    private String prCodes;
}
