package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.entity.po.ModelLineLoanRule;
import com.csvw.audi.cc.mapper.ModelLineLoanRuleMapper;
import com.csvw.audi.cc.service.IModelLineLoanRuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
@Service
public class ModelLineLoanRuleServiceImpl extends ServiceImpl<ModelLineLoanRuleMapper, ModelLineLoanRule> implements IModelLineLoanRuleService {

    @Autowired
    private ModelLineLoanRuleMapper modelLineLoanRuleMapper;

    @Override
    public List<String> loanRuleModelLineIds(String customSeriesId) {
        return modelLineLoanRuleMapper.selectModelLineIds(customSeriesId);
    }
}
