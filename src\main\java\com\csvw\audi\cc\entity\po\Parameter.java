package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 配置线参数
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="Parameter对象", description="配置线参数")
public class Parameter extends Model<Parameter> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "参数id")
      @TableId(value = "parameter_id", type = IdType.ASSIGN_ID)
    private Long parameterId;

    @ApiModelProperty(value = "参数名字")
    private String parameterName;

    @ApiModelProperty(value = "值")
    private String parameterValue;

    @ApiModelProperty(value = "类型")
    private String parameterType;

    @ApiModelProperty(value = "配置线编码")
    private String typeCode;

    @ApiModelProperty(value = "年款")
    private String modelYear;

    @ApiModelProperty(value = "权重")
    private Integer weight;

    @ApiModelProperty(value = "备注")
    private String remark;


    @Override
    protected Serializable pkVal() {
        return this.parameterId;
    }

}
