<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarSibInterieurMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarSibInterieur">
        <id column="id" property="id" />
        <result column="sib_interieur_id" property="sibInterieurId" />
        <result column="sib_interieur_code" property="sibInterieurCode" />
        <result column="sib_interieur_category" property="sibInterieurCategory" />
        <result column="sib_option_id" property="sibOptionId" />
        <result column="sib_option_code" property="sibOptionCode" />
        <result column="sib_option_category" property="sibOptionCategory" />
        <result column="sib_name" property="sibName" />
        <result column="interieur_option_id" property="interieurOptionId" />
        <result column="interieur_option_code" property="interieurOptionCode" />
        <result column="interieur_option_category" property="interieurOptionCategory" />
        <result column="interieur_name" property="interieurName" />
        <result column="description" property="description" />
        <result column="remark" property="remark" />
        <result column="image_url" property="imageUrl" />
        <result column="image_url_detail" property="imageUrlDetail" />
        <result column="image_url_list" property="imageUrlList" />
        <result column="channel" property="channel" />
        <result column="weight" property="weight" />
        <result column="default_config" property="defaultConfig" />
        <result column="custom_series_id" property="customSeriesId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sib_interieur_id, sib_interieur_code, sib_interieur_category, sib_option_id, sib_option_code, sib_option_category, sib_name, interieur_option_id, interieur_option_code, interieur_option_category, interieur_name, description, remark, image_url, image_url_detail, image_url_list, channel, weight, default_config, custom_series_id, create_time, update_time, del_flag
    </sql>

</mapper>
