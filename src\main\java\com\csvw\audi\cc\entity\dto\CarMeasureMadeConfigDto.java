package com.csvw.audi.cc.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = false)
public class CarMeasureMadeConfigDto {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "半订制车辆id")
    private String measureId;

    @ApiModelProperty(value = "配置线id")
    private String modelLineId;

    @ApiModelProperty(value = "外饰颜色id")
    private String colorId;

    @ApiModelProperty(value = "外饰颜色code")
    private String colorCode;

    @ApiModelProperty(value = "内饰颜色id")
    private String interiorId;

    @ApiModelProperty(value = "内饰颜色code")
    private String interiorCode;

    @ApiModelProperty(value = "轮毂id")
    private String radId;

    @ApiModelProperty(value = "轮毂code")
    private String radCode;

    @ApiModelProperty(value = "面料id")
    private String sibId;

    @ApiModelProperty(value = "面料code")
    private String sibCode;

    @ApiModelProperty(value = "座椅id")
    private String vosId;

    @ApiModelProperty(value = "座椅code")
    private String vosCode;

    @ApiModelProperty(value = "饰条id")
    private String eihId;

    @ApiModelProperty(value = "饰条code")
    private String eihCode;

    @ApiModelProperty(value = "内饰颜色面料id")
    private String sibInterieurId;

    @ApiModelProperty(value = "私人订制标识，0:无前置选装，1:前置选装包含轮毂，2:前置选装包含座椅，3:前置选装包含面料，4:前置选装包好座椅和面料")
    private Integer prFlag;

    @ApiModelProperty(value = "半订制车辆id")
    private Long measureOriginId;

    @ApiModelProperty(value = "配置唯一码")
    private String ccUniqueCode;

}
