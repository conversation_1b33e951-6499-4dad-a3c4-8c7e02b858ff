package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.entity.dto.TagQueryDto;
import com.csvw.audi.cc.entity.enumeration.TagTypeEnum;
import com.csvw.audi.cc.entity.po.CarTagRelate;
import com.csvw.audi.cc.entity.vo.CarTagVo;
import com.csvw.audi.cc.mapper.CarTagRelateMapper;
import com.csvw.audi.cc.service.ICarTagRelateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 车配标签 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Service
public class CarTagRelateServiceImpl extends ServiceImpl<CarTagRelateMapper, CarTagRelate> implements ICarTagRelateService {

    @Autowired
    private CarTagRelateMapper mapper;

    @Override
    public List<CarTagVo> modelLineTag(String modelLineId) {
        TagQueryDto queryDto = new TagQueryDto();
        queryDto.setTagType(TagTypeEnum.MODEL_LINE.getValue());
        queryDto.setModelLineId(modelLineId);
        return mapper.tagQuery(queryDto);
    }

    @Override
    public List<CarTagVo> modelLineOptionTag(String modelLineId, String optionId) {
        TagQueryDto queryDto = new TagQueryDto();
        queryDto.setTagType(TagTypeEnum.MODEL_LINE_OPTION.getValue());
        queryDto.setModelLineId(modelLineId);
        queryDto.setOptionId(optionId);
        return mapper.tagQuery(queryDto);
    }
}
