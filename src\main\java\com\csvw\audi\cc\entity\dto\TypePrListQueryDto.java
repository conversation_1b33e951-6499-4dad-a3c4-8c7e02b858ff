package com.csvw.audi.cc.entity.dto;

import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.entity.vo.ModelLineSibInterieurVo;
import com.csvw.audi.cc.entity.vo.ModelLineVo;
import lombok.Data;

import java.util.List;

@Data
public class TypePrListQueryDto {
    public TypePrListQueryDto(String accbTypeCode, String modelYear, String modelVersion){
        this.accbTypeCode = accbTypeCode.replaceFirst("TYPE:", "");
        this.modelYear = modelYear;
        this.modelVersion = modelVersion;
    }
    public TypePrListQueryDto(String accbTypeCode, String modelYear, String modelVersion, List<ModelLineOptionVo> optionVoList){
        this.accbTypeCode = accbTypeCode.replaceFirst("TYPE:", "");
        this.modelYear = modelYear;
        this.modelVersion = modelVersion;
        this.optionVoList = optionVoList;
    }
    private String accbTypeCode;
    private String modelYear;
    private String modelVersion;
    private String prList;
    private String seats;
    private ModelLineOptionVo optionVo;
    private ModelLineSibInterieurVo sibInterieurVo;
    private List<ModelLineOptionVo> optionVoList;
    private ModelLineVo modelLineVo;
}
