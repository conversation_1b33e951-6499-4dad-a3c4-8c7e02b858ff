<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarStyleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarStyle">
        <id column="id" property="id" />
        <result column="style_id" property="styleId" />
        <result column="style_name" property="styleName" />
        <result column="image_url" property="imageUrl" />
        <result column="channel" property="channel" />
        <result column="description" property="description" />
        <result column="weight" property="weight" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, style_id, style_name, image_url, channel, description, weight
    </sql>

    <select id="styleModelLines" resultType="String">
        select model_line_id from car_model_line_style where style_id = #{styleId}
    </select>

    <select id="listBlips" resultType="String">
        select sell_blips from car_style_blips where style_id = #{styleId} order by weight
    </select>

</mapper>
