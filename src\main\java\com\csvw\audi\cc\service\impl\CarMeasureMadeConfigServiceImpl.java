package com.csvw.audi.cc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.entity.dto.*;
import com.csvw.audi.cc.entity.dto.accb.AudiConfigDto;
import com.csvw.audi.cc.entity.dto.accb.OptionBriefDto;
import com.csvw.audi.cc.entity.enumeration.*;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.entity.vo.*;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.mapper.CarMeasureMadeConfigMapper;
import com.csvw.audi.cc.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 半订制化车辆配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@Service
@Slf4j
public class CarMeasureMadeConfigServiceImpl extends ServiceImpl<CarMeasureMadeConfigMapper, CarMeasureMadeConfig> implements ICarMeasureMadeConfigService {

    @Autowired
    private CarMeasureMadeConfigMapper measureMadeConfigMapper;

    @Autowired
    private ICarModelLineOptionService modelLineOptionService;

    @Autowired
    private ICarModelLineService modelLineService;

    @Autowired
    private ICarModelLineSibInterieurService modelLineSibInterieurService;

    @Autowired
    private ICarMeasureMadeConfigOptionService measureMadeConfigOptionService;

    @Autowired
    private ICarCustomSeriesService customSeriesService;

    @Autowired
    private ICarOptionService optionService;

    @Autowired
    private IACCBService iaccbService;

    @Autowired
    private ICarCustomService carCustomService;

    @Autowired
    private ICarCustomOptionService carCustomOptionService;

    @Autowired
    private ICarMeasureMadeOriginConfigService measureMadeOriginConfigService;

    @Autowired
    private ICarStockLogService stockLogService;

    @Autowired private ICarCustomSnapshotService customSnapshotService;

    ExecutorService audiCodeExecutor = Executors.newFixedThreadPool(10);

    private final String MEASURE_MADE_STOCK_LOCK_PREFIX = "saic_audi:applock:audi_car_config:measure_made_stock_lock:";

    @Autowired
    private ICarCustomEntryPointService entryPointService;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public MeasureVo measureQuery(MeasureQueryParam measureQueryParam, String channel) throws Exception {
        MeasureVo measureVo = new MeasureVo();
        List<Long> measureIds = new ArrayList<>();
        measureVo.setMeasureConfigCodeList(new ArrayList<>());
        MeasureQueryDto dto = new MeasureQueryDto();
        dto.setModelLineId(measureQueryParam.getModelLineId());
        dto.setOptionCodes(new ArrayList<>());
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setModelLineId(measureQueryParam.getModelLineId());
        modelParamDto.setDelFlag(0);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> modelLineVos = modelLineService.listModelLine(modelParamDto);
        ModelLineVo lineVo = modelLineVos.get(0);
        List<CarMeasureMadeConfigDto> config6Dtos = null;
        // 23 款q6。查询变种是否有六座选装
        if (lineVo.getCustomSeriesCode().equals("G6") && lineVo.getModelYear().equals("2023")){
            List<String> seatsCode = Arrays.asList("PS1");
            List<String> notInCategory = Arrays.asList("RAD", "COLOR_EXTERIEUR", "COLOR_INTERIEUR", "SIB", "EIH");
            List<ModelLineOptionVo> optionVos = modelLineService.modelLinePersonalOption(channel, lineVo.getModelLineId(), notInCategory)
                    .stream().filter(i->seatsCode.contains(i.getOptionCode()) && i.getStatus().intValue() == 2).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(optionVos)){
                measureVo.setSeatPacketOptional6(0);
                measureVo.setSeatPacketOptional7(0);
                MeasureQueryDto seatsParamDto = new MeasureQueryDto();
                seatsParamDto.setModelLineId(measureQueryParam.getModelLineId());
                List<CarMeasureMadeConfigDto> allConfigDtos = measureMadeConfigMapper.measureQuery(seatsParamDto);
                seatsParamDto.setContainOptionCodes(optionVos.stream().map(ModelLineOptionVo::getOptionCode).collect(Collectors.toList()));
                config6Dtos = measureMadeConfigMapper.measureQuery(seatsParamDto);
                List<String> configMeasure6Ids = config6Dtos.stream().map(CarMeasureMadeConfigDto::getMeasureId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(config6Dtos)){
                    measureVo.setSeatPacketOptional6(1);
                }
                if (allConfigDtos.stream().anyMatch(i->!configMeasure6Ids.contains(i.getMeasureId()))){
                    measureVo.setSeatPacketOptional7(1);
                }
            }
        }
        boolean modelLineHasNotMeasure = false;
        if (CollectionUtils.isNotEmpty(measureQueryParam.getOptionIds())) {
            OptionParamDto optionParamDto = new OptionParamDto();
            optionParamDto.setModelLineId(measureQueryParam.getModelLineId());
            optionParamDto.setOptionIds(measureQueryParam.getOptionIds());
            optionParamDto.setDelFlag(0);
            optionParamDto.setChannel(Constant.MASTER_CHANNEL);
            List<ModelLineOptionVo> optionVos = modelLineOptionService.modelLineOptionQuery(optionParamDto);
            if (optionVos.size() != measureQueryParam.getOptionIds().size()){
                return null;
            }
            optionVos.forEach(i -> {
                if (OptionCategoryEnum.OUTCOLOR.getValue().equals(i.getCategory())) {
                    dto.setColorCode(i.getOptionCode());
                } else if (OptionCategoryEnum.INCOLOR.getValue().equals(i.getCategory())) {
                    dto.setInteriorCode(i.getOptionCode());
                } else if (OptionCategoryEnum.WHEEL.getValue().equals(i.getCategory())) {
                    dto.setRadCode(i.getOptionCode());
                } else if (OptionCategoryEnum.EIH.getValue().equals(i.getCategory())) {
                    dto.setEihCode(i.getOptionCode());
                } else if (OptionCategoryEnum.SIB.getValue().equals(i.getCategory())) {
                    dto.setSibCode(i.getOptionCode());
                } else if (OptionCategoryEnum.SEET.getValue().equals(i.getCategory())) {
                    dto.setVosCode(i.getOptionCode());
                } else {
                    dto.getOptionCodes().add(i.getOptionCode());
                }
            });
            dto.setOptionCodeNum(dto.getOptionCodes().size());
        }else {
            modelLineHasNotMeasure = true;
        }
        if (lineVo.getCustomSeriesCode().equals("G6") && lineVo.getModelYear().equals("2023") && measureVo.getSeatPacketOptional6() != null && measureVo.getSeatPacketOptional6().intValue() == 1 && "6".equals(measureQueryParam.getSeats())){
            List<String> seatsCode = Arrays.asList("PS1");
            if (!dto.getOptionCodes().stream().anyMatch(i->seatsCode.contains(i))){
                // 没有六座筛选六座
                dto.setContainOptionCodes(seatsCode);
            }
        }
        List<CarMeasureMadeConfigDto> configDtos = measureMadeConfigMapper.measureQuery(dto);
        if(lineVo.getCustomSeriesCode().equals("G6") && lineVo.getModelYear().equals("2023") && measureVo.getSeatPacketOptional7() != null && measureVo.getSeatPacketOptional7().intValue() == 1 && "7".equals(measureQueryParam.getSeats())){
            // 七座过滤六座
            if (CollectionUtils.isNotEmpty(config6Dtos)) {
                List<String> configMeasure6Ids = config6Dtos.stream().map(CarMeasureMadeConfigDto::getMeasureId).collect(Collectors.toList());
                configDtos = configDtos.stream().filter(i -> !configMeasure6Ids.contains(i.getMeasureId())).collect(Collectors.toList());
            }
        }
        if(lineVo.getCustomSeriesCode().equals("G6") && lineVo.getModelYear().equals("2023") && measureVo.getSeatPacketOptional7() != null && measureVo.getSeatPacketOptional7().intValue() == 0 && "7".equals(measureQueryParam.getSeats())){
            // 七座过滤六座
            configDtos = new ArrayList<>();
        }
        Set<String> colorCodes=new HashSet<>(), radCodes=new HashSet<>(),
                eihCodes=new HashSet<>(), vosCodes=new HashSet<>(),
                insibIds=new HashSet<>(), prCodes=new HashSet<>();
        if (CollectionUtils.isEmpty(configDtos)){
            if (modelLineHasNotMeasure){
                throw new ServiceException("200001", "配置线无半订制化车辆");
            }
            return null;
        }
        if (CollectionUtils.isEmpty(configDtos)){
            if (modelLineHasNotMeasure){
                throw new ServiceException("200001", "配置线无半订制化车辆");
            }
            return null;
        }
        configDtos.forEach(i->{
            MeasureConfigCode configCode = new MeasureConfigCode();
            BeanUtils.copyProperties(i, configCode);
            measureVo.getMeasureConfigCodeList().add(configCode);
            colorCodes.add(i.getColorCode());
            radCodes.add(i.getRadCode());
            vosCodes.add(i.getVosCode());
            eihCodes.add(i.getEihCode());
            insibIds.add(i.getSibInterieurId());
            LambdaQueryWrapper<CarMeasureMadeConfigOption> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CarMeasureMadeConfigOption::getMeasureId, i.getMeasureId());
            List<CarMeasureMadeConfigOption> configOptions = measureMadeConfigOptionService.list(queryWrapper);
            List<String> measureOptionCodes = new ArrayList<>();
            configOptions.forEach(co->{
                prCodes.add(co.getOptionCode());
                measureOptionCodes.add(co.getOptionCode());
                measureIds.add(co.getMeasureId());
            });
            configCode.setPrCodes(measureOptionCodes);
            configCode.setCcUniqueCode(i.getCcUniqueCode());
        });

        OptionParamDto measureOptionParam = new OptionParamDto();
        measureOptionParam.setCustomSeriesId(lineVo.getCustomSeriesId());
        measureOptionParam.setModelLineId(measureQueryParam.getModelLineId());
        measureOptionParam.setCategories(Arrays.asList(OptionCategoryEnum.OUTCOLOR.getValue(),
                OptionCategoryEnum.SEET.getValue(),
                OptionCategoryEnum.EIH.getValue(),
                OptionCategoryEnum.WHEEL.getValue()));
        measureOptionParam.setNotOptionTypes(Arrays.asList(OptionTypeEnum.PACKETITEM.getValue()));
        measureOptionParam.setDelFlag(0);
        measureOptionParam.setChannel(channel);

        measureOptionParam.setOptionCodes(colorCodes);
        measureVo.setColorExterieur(modelLineService.modelLineOptionQuery(measureOptionParam).stream().filter(i->i.getStatus() != null && i.getStatus().intValue() == 2).collect(Collectors.toList()));
        measureOptionParam.setOptionCodes(vosCodes);
        // 部分选装包里的座椅可能没有价格
        measureVo.setVos(modelLineService.modelLineOptionQueryWithOutPriceFilter(measureOptionParam));
        measureOptionParam.setOptionCodes(eihCodes);
        measureVo.setEih(modelLineService.modelLineOptionQuery(measureOptionParam));
        measureOptionParam.setOptionCodes(radCodes);
        measureVo.setRad(modelLineService.modelLineOptionQuery(measureOptionParam));
        SibInterieurQueryDto queryDto = new SibInterieurQueryDto();
        queryDto.setChannel(channel);
        queryDto.setDelFlag(0);
        queryDto.setModelLineId(measureQueryParam.getModelLineId());
        queryDto.setSibInterieurIds(insibIds);
        measureVo.setSibInterieur(modelLineSibInterieurService.modelLineSibInterieur(queryDto));
        List<ModelLineOptionVo> personalOptions = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(prCodes)) {
            List<ModelLineOptionVo> prVos = modelLineService.modelLineOptionQueryByOptionCodes(channel, null, measureOptionParam.getModelLineId(), prCodes);
            Map<String, List<ModelLineOptionVo>> prMap = new HashMap<>();
            prVos.forEach(i -> {
                if (prMap.get(i.getOptionCode()) == null) {
                    prMap.put(i.getOptionCode(), new ArrayList<>());
                }
                prMap.get(i.getOptionCode()).add(i);
            });
            for (String code : prCodes){
                ModelLineOptionVo pr = validModelLineVo(prMap.get(code));
                if ( pr != null ){
                    if (OptionCategoryEnum.PACKET.getValue().equals(pr.getCategory())){
                        OptionParamDto optionParamDto = new OptionParamDto();
                        optionParamDto.setChannel(channel);
                        optionParamDto.setModelLineId(measureQueryParam.getModelLineId());
                        optionParamDto.setOptionId(pr.getOptionId());
                        List<ModelLineOptionVo> items = modelLineService.modelLinePacketItem(optionParamDto);
                        pr.setPacketItems(items);
                        /*Set<String> itemCodes = items.stream().map(ModelLineOptionVo::getCategory).collect(Collectors.toSet());
                        if (itemCodes.contains(OptionCategoryEnum.SIB.getValue())){
                            ModelLineMeasureOptionVo modelLineMeasureOptionVo = new ModelLineMeasureOptionVo();
                            BeanUtils.copyProperties(pr, modelLineMeasureOptionVo);
                            List<String> dependSibInterieurs = measureMadeConfigMapper.listDependSibInterieur(measureIds, pr.getOptionCode());
                            if (CollectionUtils.isNotEmpty(dependSibInterieurs)) {
                                queryDto.setSibInterieurIds(dependSibInterieurs);
                                modelLineMeasureOptionVo.setSibInterieurDepends(modelLineSibInterieurService.modelLineSibInterieur(queryDto));
                                pr = modelLineMeasureOptionVo;
                            }
                        }*/
                    }
                    personalOptions.add(pr);
                }
            }
        }
        personalOptions = personalOptions.stream().sorted((a,b)-> {
            if (a.getPrice() == null || b.getPrice() == null) {
                return 0;
            }
            if (a.getPrice() instanceof BigDecimal && b.getPrice() instanceof BigDecimal) {
                return ((BigDecimal) b.getPrice()).compareTo((BigDecimal) a.getPrice());
            } else {
                return 0;
            }
        }).collect(Collectors.toList());
        List<ModelLineOptionVo> sortedPersonal = new ArrayList<>();
        List<ModelLineOptionVo> pList = new ArrayList<>();
        List<ModelLineOptionVo> npList = new ArrayList<>();
        personalOptions.forEach(i->{
            if (i.getCategory() != null && i.getCategory().equals(OptionCategoryEnum.PACKET.getValue())){
                pList.add(i);
            }else {
                npList.add(i);
            }
        });
        sortedPersonal.addAll(pList);
        sortedPersonal.addAll(npList);
        measureVo.setPersonals(sortedPersonal);
        return measureVo;
    }

    @Override
    public CarCustom addCarCustomConfig(String userId, String userMobile, Long measureId, Long sourceId, String channel, String entryPoint, String packetEquityId) throws Exception {
        String trackId = UUID.randomUUID().toString();
        if (userId != null && measureId != null) {
            log.info("add cc userId: " + userId + " measureId: " + measureId + " trackId:" + trackId);
        }
        LambdaQueryWrapper<CarCustomEntryPoint> entryPointQ = new LambdaQueryWrapper<>();
        entryPointQ.eq(CarCustomEntryPoint::getChannel, channel)
                .eq(CarCustomEntryPoint::getEntryPoint, entryPoint)
                .eq(CarCustomEntryPoint::getStatus, 1);
        if (entryPointService.getOne(entryPointQ) == null){
            throw new ServiceException("400401", "参数异常：entryPoint", null);
        }
        CarMeasureMadeConfig measureMadeConfig = this.getById(measureId);
        if (measureMadeConfig == null){
            throw new ServiceException("400401", "参数异常：measureId", null);
        }
        ModelLineOptionVo equity = null;
        if (StringUtils.isNotBlank(packetEquityId)){
            String category = OptionCategoryEnum.PACKET.getValue();
            List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(Constant.MASTER_CHANNEL, measureMadeConfig.getModelLineId(), category).stream().filter(i-> OptionTypeEnum.PACKET_EQUITY.getValue().equals(i.getOptionType())).collect(Collectors.toList());
            List<ModelLineOptionVo> equities = optionVos.stream().filter(i->i.getOptionId().equals(packetEquityId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(equities)){
                throw new ServiceException("400401", "参数异常：packetEquityId", null);
            }
            equity = equities.get(0);
        }
        CarMeasureMadeOriginConfig measureMadeOriginConfig = measureMadeOriginConfigService.getById(measureMadeConfig.getMeasureOriginId());
        if (measureMadeOriginConfig.getStockNum().intValue() < 1){
            throw new ServiceException("400401", "无库存", null);
        }
        List<String> optionIds = new ArrayList<>();
        optionIds.add(measureMadeConfig.getColorId());
        optionIds.add(measureMadeConfig.getInteriorId());
        optionIds.add(measureMadeConfig.getRadId());
        optionIds.add(measureMadeConfig.getSibId());
        optionIds.add(measureMadeConfig.getVosId());
        optionIds.add(measureMadeConfig.getEihId());
        if (equity != null){
            optionIds.add(packetEquityId);
        }
        LambdaQueryWrapper<CarMeasureMadeConfigOption> measureOptionWrapper = new LambdaQueryWrapper<>();
        measureOptionWrapper.eq(CarMeasureMadeConfigOption::getMeasureId, measureId);
        List<CarMeasureMadeConfigOption> configOptions = measureMadeConfigOptionService.list(measureOptionWrapper);
        if (CollectionUtils.isNotEmpty(configOptions)){
            configOptions.forEach(i->optionIds.add(i.getOptionId()));
        }
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(measureMadeConfig.getModelLineId());
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (lines == null || lines.size() != 1){
            throw new ServiceException("400401", "参数异常：measureId", null);
        }
        ModelLineVo modelLine = lines.get(0);
        CustomSeriesDto seriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
        AudiConfigDto audiConfigDto = new AudiConfigDto();
        audiConfigDto.setModelId(seriesDto.getAccbModelId());
        audiConfigDto.setModelCode(seriesDto.getAccbModelCode());
        audiConfigDto.setModelDesc(seriesDto.getSeriesName());
        audiConfigDto.setTypeId(modelLine.getAccbTypeId());
        audiConfigDto.setTypeCode(modelLine.getAccbTypeCode());
        audiConfigDto.setTypeDesc(modelLine.getModelLineName());
        audiConfigDto.setModelYear(modelLine.getModelYear());
        audiConfigDto.setModelVersion(modelLine.getVersion());
        audiConfigDto.setHeadline(modelLine.getModelLineName());
        List<OptionBriefDto> optionBriefDtoList = new ArrayList<>();
        LambdaQueryWrapper<CarOption> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CarOption::getDelFlag, 0).eq(CarOption::getChannel, Constant.MASTER_CHANNEL).in(CarOption::getOptionId, optionIds);
        List<CarOption> options = optionService.list(queryWrapper);
        if (CollectionUtils.isEmpty(options)){
            throw new ServiceException("400401", "参数异常：optionIds", null);
        }
        options.forEach(i->
                optionBriefDtoList.add(new OptionBriefDto(i.getCategory()+":"+i.getOptionCode(), i.getCategory(),i.getOptionName())));
        audiConfigDto.setOptions(optionBriefDtoList);

        CarCustom carCustom = new CarCustom();
        carCustom.setEntryPoint(entryPoint);
        carCustom.setEstimateDelivery(DeliveryTimeEnum.A.getValue());
        carCustom.setDepositType(DepositTypeEnum.STOCK.getType());
        carCustom.setMeasureId(measureId);
        carCustom.setSibInterieurId(measureMadeConfig.getSibInterieurId());
        carCustom.setAccbModelId(seriesDto.getAccbModelId());
        carCustom.setAccbModelCode(seriesDto.getAccbModelCode());
        carCustom.setModelDesc(seriesDto.getCustomSeriesName());
        carCustom.setAccbTypeId(modelLine.getAccbTypeId());
        carCustom.setAccbTypeCode(modelLine.getAccbTypeCode());
        carCustom.setAccbTypeDesc(modelLine.getModelLineName());
        carCustom.setModelLineId(modelLine.getModelLineId());
        carCustom.setModelYear(modelLine.getModelYear());
        carCustom.setUserId(userId);
        carCustom.setUserMobile(userMobile);
        carCustom.setCreateTime(LocalDateTime.now());
        carCustom.setCreateUser(userId);
        if (sourceId != null) {
            CarCustomSource source = new CarCustomSource();
            if (source.selectById(sourceId) == null) {
                throw new ServiceException("400401", "参数异常：sourceId", null);
            }
            carCustom.setSourceId(Long.valueOf(sourceId));
        }
        carCustom.insert();
        options.forEach(i->{
            CarCustomOption carCustomOption = new CarCustomOption();
            carCustomOption.setCcid(carCustom.getCcid());
            carCustomOption.setOptionId(i.getOptionId());
            carCustomOption.setCategory(i.getCategory());
            carCustomOption.setCode(i.getOptionCode());
            carCustomOption.setDescription(i.getOptionName());
            carCustomOption.insert();
        });
        log.info("add cc userId: " + userId + " measureId: " +
                JSONObject.toJSONString(measureId) + " trackId: " + trackId + " ccid: "+ String.valueOf(carCustom.getCcid()));
        audiCodeExecutor.execute(()->this.ccGenAudiCode(carCustom, audiConfigDto));
        customSnapshotService.snapshotCarCustom(carCustom);
        return carCustom;
    }

    @Override
    @Transactional
    public void lockMeasureMadeStock(Long ccid) throws ServiceException {
        CarCustom carCustom = carCustomService.getById(ccid);
        if (carCustom == null || carCustom.getMeasureId() == null){
            throw new ServiceException("400401", "半订制锁库存异常", "配置单存在/不是半订制配置单");
        }
        String key = MEASURE_MADE_STOCK_LOCK_PREFIX + carCustom.getMeasureId();
        RLock lock = redissonClient.getLock(key);
        try {
            lock.lock(2, TimeUnit.MINUTES);
            CarMeasureMadeConfig measureMadeConfig = this.getById(carCustom.getMeasureId());
            if (measureMadeConfig == null){
                throw new ServiceException("400401", "半订制锁库存异常", "半订制车辆不存在");
            }
            CarMeasureMadeOriginConfig measureMadeOriginConfig = measureMadeOriginConfigService.getById(measureMadeConfig.getMeasureOriginId());
            // 半订制下单解除库存限制
            /*if (measureMadeOriginConfig.getStockNum() < 1){
                throw new ServiceException("50000", "半订制车库存不足", "半订制车辆库存不足");
            }*/
            LambdaQueryWrapper<CarStockLog> stockLogQ = new LambdaQueryWrapper<>();
            stockLogQ.eq(CarStockLog::getCcid, ccid).orderByDesc(CarStockLog::getCreateTime);
            List<CarStockLog> logs = stockLogService.list(stockLogQ);
            if (CollectionUtils.isNotEmpty(logs)) {
                if (logs.get(0).getOperate().intValue() == 1) {
                    throw new ServiceException("50000", "当前ccid已存在库存锁定", "推荐车库存异常");
                }
            }
            LambdaUpdateWrapper<CarMeasureMadeOriginConfig> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(CarMeasureMadeOriginConfig::getStockNum, measureMadeOriginConfig.getStockNum() - 1)
                    .eq(CarMeasureMadeOriginConfig::getMeasureOriginId, measureMadeConfig.getMeasureOriginId());
            measureMadeOriginConfigService.update(updateWrapper);
            CarStockLog stockLog = new CarStockLog();
            stockLog.setCcid(ccid);
            stockLog.setCreateTime(LocalDateTime.now());
            stockLog.setType(StockTypeEnum.MEASURE.getValue());
            stockLog.setMeasureId(carCustom.getMeasureId());
            stockLog.setOperate(1);
            stockLog.setRecommendModelId(-1l);
            stockLog.insert();
        }finally {
            lock.unlock();
        }
    }

    @Override
    @Transactional
    public void unlockMeasureMadeStock(Long ccid) throws ServiceException {
        CarCustom carCustom = carCustomService.getById(ccid);
        if (carCustom == null || carCustom.getMeasureId() == null){
            throw new ServiceException("400401", "半订制锁库存异常", "配置单存在/不是半订制配置单");
        }
        String key = MEASURE_MADE_STOCK_LOCK_PREFIX + carCustom.getMeasureId();
        RLock lock = redissonClient.getLock(key);
        try {
            lock.lock(2, TimeUnit.MINUTES);
            CarMeasureMadeConfig measureMadeConfig = this.getById(carCustom.getMeasureId());
            if (measureMadeConfig == null){
                throw new ServiceException("400401", "半订制锁库存异常", "半订制车辆不存在");
            }
            LambdaQueryWrapper<CarStockLog> stockLogQ = new LambdaQueryWrapper<>();
            stockLogQ.eq(CarStockLog::getCcid, ccid).orderByDesc(CarStockLog::getCreateTime).last("for update");
            List<CarStockLog> logs = stockLogService.list(stockLogQ);
            CarStockLog lastLog = null;
            if (CollectionUtils.isNotEmpty(logs)) {
                lastLog = logs.get(0);
                if (lastLog.getOperate().intValue() == 2) {
                    // 已解锁库存
                    return;
                }
            } else {
                throw new ServiceException("50000", "未上锁", "推荐车库存异常");
            }
            CarMeasureMadeOriginConfig measureMadeOriginConfig = measureMadeOriginConfigService.getById(measureMadeConfig.getMeasureOriginId());
            LambdaUpdateWrapper<CarMeasureMadeOriginConfig> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(CarMeasureMadeOriginConfig::getStockNum, measureMadeOriginConfig.getStockNum() + 1)
                    .eq(CarMeasureMadeOriginConfig::getMeasureOriginId, measureMadeConfig.getMeasureOriginId());
            measureMadeOriginConfigService.update(updateWrapper);
            CarStockLog stockLog = new CarStockLog();
            stockLog.setCcid(ccid);
            stockLog.setCreateTime(LocalDateTime.now());
            stockLog.setType(StockTypeEnum.MEASURE.getValue());
            stockLog.setMeasureId(carCustom.getMeasureId());
            stockLog.setOperate(2);
            stockLog.setRecommendModelId(-1l);
            stockLog.insert();
        }finally {
            lock.unlock();
        }
    }

    @Override
    public int measureCount(CarMeasureMadeOriginConfig originConfig) {
        return measureMadeConfigMapper.measureCount(originConfig);
    }

    @Override
    public CarCustom updateCarCustomConfig(Long ccid, Long measureId, String userId, String userMobile, String packetEquityId) throws Exception {
        String trackId = UUID.randomUUID().toString();
        if (measureId != null) {
            log.info("update cc" + " measureId: " + measureId + " trackId:" + trackId);
        }
        CarMeasureMadeConfig measureMadeConfig = this.getById(measureId);
        if (measureMadeConfig == null){
            throw new ServiceException("400401", "参数异常：measureId", null);
        }
        CarMeasureMadeOriginConfig measureMadeOriginConfig = measureMadeOriginConfigService.getById(measureMadeConfig.getMeasureOriginId());
        if (measureMadeOriginConfig.getStockNum().intValue() < 1){
            throw new ServiceException("400401", "无库存", null);
        }
        ModelLineOptionVo equity = null;
        if (StringUtils.isNotBlank(packetEquityId)){
            String category = OptionCategoryEnum.PACKET.getValue();
            List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(Constant.MASTER_CHANNEL, measureMadeConfig.getModelLineId(), category).stream().filter(i-> OptionTypeEnum.PACKET_EQUITY.getValue().equals(i.getOptionType())).collect(Collectors.toList());
            List<ModelLineOptionVo> equities = optionVos.stream().filter(i->i.getOptionId().equals(packetEquityId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(equities)){
                throw new ServiceException("400401", "参数异常：packetEquityId", null);
            }
            equity = equities.get(0);
        }
        List<String> optionIds = new ArrayList<>();
        optionIds.add(measureMadeConfig.getColorId());
        optionIds.add(measureMadeConfig.getInteriorId());
        optionIds.add(measureMadeConfig.getRadId());
        optionIds.add(measureMadeConfig.getSibId());
        optionIds.add(measureMadeConfig.getVosId());
        optionIds.add(measureMadeConfig.getEihId());
        if (equity != null){
            optionIds.add(packetEquityId);
        }
        LambdaQueryWrapper<CarMeasureMadeConfigOption> measureOptionWrapper = new LambdaQueryWrapper<>();
        measureOptionWrapper.eq(CarMeasureMadeConfigOption::getMeasureId, measureId);
        List<CarMeasureMadeConfigOption> configOptions = measureMadeConfigOptionService.list(measureOptionWrapper);
        if (CollectionUtils.isNotEmpty(configOptions)){
            configOptions.forEach(i->optionIds.add(i.getOptionId()));
        }
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(measureMadeConfig.getModelLineId());
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (lines == null || lines.size() != 1){
            throw new ServiceException("400401", "参数异常：measureId", null);
        }
        ModelLineVo modelLine = lines.get(0);
        CustomSeriesDto seriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
        AudiConfigDto audiConfigDto = new AudiConfigDto();
        audiConfigDto.setModelId(seriesDto.getAccbModelId());
        audiConfigDto.setModelCode(seriesDto.getAccbModelCode());
        audiConfigDto.setModelDesc(seriesDto.getSeriesName());
        audiConfigDto.setTypeId(modelLine.getAccbTypeId());
        audiConfigDto.setTypeCode(modelLine.getAccbTypeCode());
        audiConfigDto.setTypeDesc(modelLine.getModelLineName());
        audiConfigDto.setModelYear(modelLine.getModelYear());
        audiConfigDto.setModelVersion(modelLine.getVersion());
        audiConfigDto.setHeadline(modelLine.getModelLineName());
        List<OptionBriefDto> optionBriefDtoList = new ArrayList<>();
        LambdaQueryWrapper<CarOption> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CarOption::getDelFlag, 0).eq(CarOption::getChannel, Constant.MASTER_CHANNEL).in(CarOption::getOptionId, optionIds);
        List<CarOption> options = optionService.list(queryWrapper);
        if (CollectionUtils.isEmpty(options)){
            throw new ServiceException("400401", "参数异常：optionIds", null);
        }
        options.forEach(i->
                optionBriefDtoList.add(new OptionBriefDto(i.getCategory()+":"+i.getOptionCode(), i.getCategory(),i.getOptionName())));
        audiConfigDto.setOptions(optionBriefDtoList);

        CarCustom carCustom = new CarCustom();
        carCustom.setCcid(ccid);
        carCustom.setMeasureId(measureId);
        carCustom.setDepositType(DepositTypeEnum.STOCK.getType());
        carCustom.setMeasureId(measureId);
        carCustom.setSibInterieurId(measureMadeConfig.getSibInterieurId());
        carCustom.setAccbModelId(seriesDto.getAccbModelId());
        carCustom.setAccbModelCode(seriesDto.getAccbModelCode());
        carCustom.setModelDesc(seriesDto.getCustomSeriesName());
        carCustom.setAccbTypeId(modelLine.getAccbTypeId());
        carCustom.setAccbTypeCode(modelLine.getAccbTypeCode());
        carCustom.setAccbTypeDesc(modelLine.getModelLineName());
        carCustom.setModelLineId(modelLine.getModelLineId());
        carCustom.setModelYear(modelLine.getModelYear());
        carCustom.setUserId(userId);
        carCustom.setUserMobile(userMobile);
        carCustom.setUpdateTime(LocalDateTime.now());
        carCustom.setUpdateUser(userId);
        carCustom.updateById();

        LambdaUpdateWrapper<CarCustom> ccU = new LambdaUpdateWrapper<>();
        ccU.eq(CarCustom::getCcid, ccid).set(CarCustom::getInvalidReason, null).set(CarCustom::getValid, 1);
        carCustomService.update(ccU);

        LambdaQueryWrapper<CarCustomOption> deleteQueryWrapper = new LambdaQueryWrapper<>();
        deleteQueryWrapper.eq(CarCustomOption::getCcid, ccid);
        carCustomOptionService.remove(deleteQueryWrapper);

        options.forEach(i->{
            CarCustomOption carCustomOption = new CarCustomOption();
            carCustomOption.setCcid(ccid);
            carCustomOption.setOptionId(i.getOptionId());
            carCustomOption.setCategory(i.getCategory());
            carCustomOption.setCode(i.getOptionCode());
            carCustomOption.setDescription(i.getOptionName());
            carCustomOption.insert();
        });
        log.info("update cc" + " measureId: " +
                JSONObject.toJSONString(measureId) + " trackId: " + trackId + " ccid: "+ ccid);
        audiCodeExecutor.execute(()->this.ccGenAudiCode(carCustom, audiConfigDto));
        customSnapshotService.snapshotCarCustom(carCustom, LocalDateTime.now());
        return carCustom;
    }

    private void ccGenAudiCode(CarCustom carCustom, AudiConfigDto audiConfigDto){
        String audiCode = iaccbService.genAudiCode(audiConfigDto);
        LambdaUpdateWrapper<CarCustom> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CarCustom::getCcid, carCustom.getCcid()).set(CarCustom::getAudiCode, audiCode);
        carCustomService.update(updateWrapper);
    }

    private ModelLineOptionVo validModelLineVo(List<ModelLineOptionVo> vos){
        if(CollectionUtils.isEmpty(vos)){
            return null;
        }
        for(ModelLineOptionVo vo : vos){
            if ("personal".equals(vo.getOptionType())){
                return vo;
            }
        }
        for(ModelLineOptionVo vo : vos){
            if (vo.getStatus() != null && vo.getStatus().intValue() == 1){
                return vo;
            }
        }
        for(ModelLineOptionVo vo : vos){
            if (vo.getStatus() != null && vo.getStatus().intValue() == 2){
                return vo;
            }
        }
        return vos.get(0);
    }

}
