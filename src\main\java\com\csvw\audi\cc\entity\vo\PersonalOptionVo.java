package com.csvw.audi.cc.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PersonalOptionVo {

    @ApiModelProperty(value = "私人订制组合列表")
    private List<PersonalCompose> personalOptionComposes;

    @ApiModelProperty(value = "私人订制选装列表")
    private List<ModelLineOptionVo> personalOptions;

    @Data
    public static class PersonalCompose {
        public PersonalCompose(String composeName, List<ModelLineOptionVo> composePersonalOptions, List<String> typeIdsOfA, String seats){
            this.composeName = composeName;
            this.composePersonalOptions = composePersonalOptions;
            this.typeIdsOfA = typeIdsOfA;
            this.seats = seats;
            BigDecimal price = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(composePersonalOptions)){
                for (ModelLineOptionVo vo : composePersonalOptions){
                    if (vo.getPrice() == null){
                        this.composePrice = null;
                        break;
                    }else if (vo.getPrice() instanceof BigDecimal){
                        if (this.composePrice == null){
                            this.composePrice = BigDecimal.ZERO;
                        }
                        this.composePrice = ((BigDecimal)this.composePrice).add((BigDecimal) vo.getPrice());
                    }else if (vo.getPrice() instanceof String){
                        this.composePrice = vo.getPrice();
                        break;
                    }
                }
            }else {
                this.composePrice = BigDecimal.ZERO;
            }
        }
        private String composeName;
        private Object composePrice;
        private List<String> typeIdsOfA;
        private String seats;
        private List<ModelLineOptionVo> composePersonalOptions;
    }
}
