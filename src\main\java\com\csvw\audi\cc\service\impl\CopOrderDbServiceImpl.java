package com.csvw.audi.cc.service.impl;

import cn.hutool.db.handler.BeanHandler;
import cn.hutool.db.handler.BeanListHandler;
import com.alibaba.druid.pool.DruidDataSource;
import com.csvw.audi.cc.common.CopOrderSql;
import com.csvw.audi.cc.config.CopOrderDatasourceConfig;
import com.csvw.audi.cc.entity.dto.CcOrderInfo;
import com.csvw.audi.cc.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@Slf4j
public class CopOrderDbServiceImpl {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final DruidDataSource copOrderDataSource;

    public CopOrderDbServiceImpl(CopOrderDatasourceConfig config) throws SQLException {
        copOrderDataSource = new DruidDataSource();
        copOrderDataSource.setFilters("stat,config");
        copOrderDataSource.setInitialSize(config.getInitialSize());
        copOrderDataSource.setMinIdle(config.getMinIdle());
        copOrderDataSource.setMaxActive(config.getMaxActive());
        copOrderDataSource.setMaxWait(2000);
        copOrderDataSource.setMaxWaitThreadCount(200);
        copOrderDataSource.setQueryTimeout(6000);
        copOrderDataSource.setRemoveAbandonedTimeout(1800);
        copOrderDataSource.setTransactionQueryTimeout(6000);
        copOrderDataSource.setUrl(config.getUrl());
        copOrderDataSource.setUsername(config.getUsername());
        copOrderDataSource.setPassword(config.getPassword());
    }

    public CcOrderInfo listOrderByCcid(Long ccid) throws ServiceException {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet ccOrderResult = null;
        try {
            connection = copOrderDataSource.getConnection();
            preparedStatement = connection.prepareStatement(CopOrderSql.QUERY_ORDER_BY_CCID);
            preparedStatement.setString(1, String.valueOf(ccid));
            ccOrderResult = preparedStatement.executeQuery();
            BeanHandler<CcOrderInfo> beanListHandler = new BeanHandler<>(CcOrderInfo.class);
            CcOrderInfo ccOrderInfos = beanListHandler.handle(ccOrderResult);
            return ccOrderInfos;
        } catch (SQLException throwables) {
            log.error("查询ccid订单", throwables);
            throw new ServiceException("系统错误", "50001", null);
        } finally {
            closeConnection(ccOrderResult);
            closeConnection(preparedStatement);
            closeConnection(connection);
        }
    }



    private void closeConnection(AutoCloseable closeable){
        try {
            if (closeable != null) {
                closeable.close();
            }
        }catch (Exception e) {
            log.error("COP-COUPON查询关闭异常", e);
        }
    }

}
