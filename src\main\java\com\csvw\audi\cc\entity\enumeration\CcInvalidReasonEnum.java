package com.csvw.audi.cc.entity.enumeration;

import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Arrays;
import java.util.NoSuchElementException;

public enum CcInvalidReasonEnum {
    MODEL_LINE_OFF("modelLineOff", "当前配置车型已下架，配置单失效");
    private String value;
    @JsonValue
    private String description;
    CcInvalidReasonEnum(String value, String description){
        this.value = value;
        this.description = description;
    }
    public static CcInvalidReasonEnum parseValue(String v){
        try {
            return Arrays.stream(CcInvalidReasonEnum.values()).filter(i->i.value.equals(v)).findFirst().get();
        }catch (NoSuchElementException e){
            return null;
        }

    }
}
