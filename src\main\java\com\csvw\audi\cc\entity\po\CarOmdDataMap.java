package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * OMD数据映射
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarOmdDataMap对象", description="OMD数据映射")
public class CarOmdDataMap extends Model<CarOmdDataMap> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "omd库存车映射id")
      @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "车型编码")
    private String originModelCode;

    @ApiModelProperty(value = "配置线编码")
    private String originAccbTypeCode;

    @ApiModelProperty(value = "车型版本号")
    private String originModelVersion;

    @ApiModelProperty(value = "车型年款")
    private String originModelYear;

    @ApiModelProperty(value = "内饰code")
    private String originInteriorCode;

    @ApiModelProperty(value = "外饰code")
    private String originColorCode;

    @ApiModelProperty(value = "pr列表，逗号分隔")
    private String originPrList;

    @ApiModelProperty(value = "产品编码")
    private String originClassCode;

    @ApiModelProperty(value = "cc唯一值")
    private String originCcUniqueCode;

    @ApiModelProperty(value = "车型编码")
    private String modelCode;

    @ApiModelProperty(value = "配置线编码")
    private String accbTypeCode;

    @ApiModelProperty(value = "车型版本号")
    private String modelVersion;

    @ApiModelProperty(value = "车型年款")
    private String modelYear;

    @ApiModelProperty(value = "内饰code")
    private String interiorCode;

    @ApiModelProperty(value = "外饰code")
    private String colorCode;

    @ApiModelProperty(value = "pr列表，逗号分隔")
    private String prList;

    @ApiModelProperty(value = "产品编码")
    private String classCode;

    @ApiModelProperty(value = "cc唯一值")
    private String ccUniqueCode;

    @ApiModelProperty(value = "相应功能映射")
    private String api;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
