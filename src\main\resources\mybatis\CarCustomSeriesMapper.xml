<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarCustomSeriesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarCustomSeries">
        <id column="id" property="id" />
        <result column="custom_series_id" property="customSeriesId" />
        <result column="custom_series_code" property="customSeriesCode" />
        <result column="custom_series_name" property="customSeriesName" />
        <result column="series_id" property="seriesId" />
        <result column="series_code" property="seriesCode" />
        <result column="series_name" property="seriesName" />
        <result column="image_url" property="imageUrl" />
        <result column="classification" property="classification" />
        <result column="default_config" property="defaultConfig" />
        <result column="channel" property="channel" />
        <result column="weight" property="weight" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, custom_series_id, custom_series_code, custom_series_name, series_id, series_code, series_name, image_url, classification, default_config, channel, weight, create_time, update_time, del_flag
    </sql>

    <select id="listCustomSeries" parameterType="com.csvw.audi.cc.entity.po.CarCustomSeries" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
        from car_custom_series
        <where>
            <if test="channel == null or channel == '' or channel == 'master'">
                channel = 'master'
            </if>
            <if test="channel != null and channel != '' and channel != 'master'">
                channel in (#{channel}, 'master')
            </if>
            <if test="customSeriesCode != null and customSeriesCode != '' ">
                and custom_series_code = #{customSeriesCode}
            </if>
            <if test="customSeriesId != null and customSeriesId != ''">
                and custom_series_id = #{customSeriesId}
            </if>
            <if test="delFlag != null">
                and del_flag = #{delFlag}
            </if>
        </where>
        order by weight
    </select>

    <select id="getCustomSeriesDto" resultType="com.csvw.audi.cc.entity.dto.CustomSeriesDto">
        select cs.id, cs.custom_series_id, cs.custom_series_code, cs.custom_series_name, cs.series_id,
            cs.classification, cs.default_config,
            cs.series_code, cs.image_url, s.accb_model_code, s.series_name, s.accb_model_id, s.omd_series_code
        from car_custom_series cs left join car_series s on cs.series_id = s.series_id and s.channel = cs.channel
        <where>
            <if test="channel == null or channel == ''">
                cs.channel = 'master'
            </if>
            <if test="channel != null and channel != ''">
                cs.channel = #{channel}
            </if>
            <if test="customSeriesId != null and customSeriesId != ''">
                and cs.custom_series_id = #{customSeriesId}
            </if>
        </where>
        order by cs.weight
    </select>

    <select id="listCustomSeriesVo" parameterType="com.csvw.audi.cc.entity.dto.SeriesParamDto" resultType="com.csvw.audi.cc.entity.vo.CustomSeriesVo">
        select cs.id, cs.custom_series_id, cs.custom_series_code, cs.custom_series_name, cs.series_id,
        cs.classification, cs.default_config,
        cs.series_code, cs.image_url, s.accb_model_code, s.accb_model_id
        from car_custom_series cs left join car_series s on cs.series_id = s.series_id and cs.channel = s.channel
        <where>
            <if test="channel == null or channel == '' or channel == 'master'">
                cs.channel = 'master'
            </if>
            <if test="channel != null and channel != '' and channel != 'master'">
                cs.channel in (#{channel}, 'master')
            </if>
        </where>
        order by cs.weight
    </select>

</mapper>
