package com.csvw.audi.cc.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class CarShoppingCartParam {

    @ApiModelProperty(value = "购物车ID")
    private String shoppingCartId;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "ccid")
    private String ccid;

    @ApiModelProperty(value = "邀请码")
    private String invitationCode;

    @ApiModelProperty(value = "商品id")
    private String skuid;

    @ApiModelProperty(value = "自动保存（1:是 0:否）")
    private String autoSave;

}
