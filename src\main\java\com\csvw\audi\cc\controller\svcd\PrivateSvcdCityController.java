package com.csvw.audi.cc.controller.svcd;

import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.dto.SvcdCityDto;
import com.csvw.audi.cc.entity.vo.SvcdCityVo;
import com.csvw.audi.cc.service.ISvcdCityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "代理商-城市-feign")
@RestController
@RequestMapping("/private/api/v1/svcdCity")
public class PrivateSvcdCityController extends BaseController {

    @Autowired
    private ISvcdCityService svcdCityService;

    @ApiOperation("获取城市列表,默认查询存在经销商的")
    @GetMapping("/getCityList")
    public AjaxMessage<List<SvcdCityVo>> getProvinceList(SvcdCityDto cityDto) {
        List<SvcdCityVo> list =  svcdCityService.getCityList(cityDto);
        return new AjaxMessage<>("00", "成功", list);
    }
}
