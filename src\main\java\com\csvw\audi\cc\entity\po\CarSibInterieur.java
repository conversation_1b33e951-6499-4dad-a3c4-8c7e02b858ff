package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 内饰颜色面料表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarSibInterieur对象", description="内饰颜色面料表")
public class CarSibInterieur extends Model<CarSibInterieur> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "内饰颜色面料id")
    private String sibInterieurId;

    @ApiModelProperty(value = "内饰面料Code")
    private String sibInterieurCode;

    @ApiModelProperty(value = "内饰面料类别码")
    private String sibInterieurCategory;

    @ApiModelProperty(value = "面料id")
    private String sibOptionId;

    @ApiModelProperty(value = "面料编码")
    private String sibOptionCode;

    @ApiModelProperty(value = "面料类型")
    private String sibOptionCategory;

    @ApiModelProperty(value = "面料名称")
    private String sibName;

    @ApiModelProperty(value = "内饰id")
    private String interieurOptionId;

    @ApiModelProperty(value = "内饰编码")
    private String interieurOptionCode;

    @ApiModelProperty(value = "内饰类型")
    private String interieurOptionCategory;

    @ApiModelProperty(value = "内饰名称")
    private String interieurName;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "图片地址")
    private String imageUrl;

    @ApiModelProperty(value = "装备详情图片")
    private String imageUrlDetail;

    @ApiModelProperty(value = "装备列表图片")
    private String imageUrlList;

    @ApiModelProperty(value = "数据渠道")
    private String channel;

    @ApiModelProperty(value = "权重")
    private Integer weight;

    @ApiModelProperty(value = "默认内饰颜色面料")
    private Integer defaultConfig;

    @ApiModelProperty(value = "自定义车系id")
    private String customSeriesId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
