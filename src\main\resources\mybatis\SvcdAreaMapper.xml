<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.SvcdAreaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.SvcdArea">
        <id column="area_id" property="areaId" />
        <result column="name" property="name" />
        <result column="area_code" property="areaCode" />
        <result column="city_code" property="cityCode" />
        <result column="simple_name" property="simpleName" />
        <result column="quick_code" property="quickCode" />
        <result column="area_en" property="areaEn" />
        <result column="status" property="status" />
        <result column="zm_area_code" property="zmAreaCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        area_id, name, area_code, city_code, simple_name, quick_code, area_en, status, zm_area_code
    </sql>

</mapper>
