package com.csvw.audi.cc.application;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.bind.annotation.RestController;

@EnableFeignClients(basePackages = { "com.csvw.audi.cc.feign" })
//@EnableEurekaClient
@SpringBootApplication(exclude = {
        org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration.class
})
@RestController
@ComponentScan(basePackages = {"com.csvw.audi.cc.controller", "com.csvw.audi.cc.service", "com.csvw.audi.cc.config", "com.csvw.audi.cc.common.utils"})
@MapperScan(basePackages = {"com.csvw.audi.cc.mapper", "com.csvw.audi.cc.entity"})
public class TestAppRun {
    public static void main(String[] args) {
        SpringApplication.run(TestAppRun.class, args);
    }
}
