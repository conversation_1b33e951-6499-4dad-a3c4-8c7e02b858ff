package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.dto.CarMeasureMadeConfigDto;
import com.csvw.audi.cc.entity.dto.MeasureQueryDto;
import com.csvw.audi.cc.entity.po.CarMeasureMadeConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csvw.audi.cc.entity.po.CarMeasureMadeOriginConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 半订制化车辆配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
public interface CarMeasureMadeConfigMapper extends BaseMapper<CarMeasureMadeConfig> {

    List<CarMeasureMadeOriginConfig> listOriginConfigToConvert();

    List<CarMeasureMadeConfigDto> measureQuery(MeasureQueryDto dto);

    List<String> listDependSibInterieur(@Param("measureIds") List<Long> measureIds, @Param("optionCode") String optionCode);

    int measureCount(CarMeasureMadeOriginConfig originConfig);
}
