package com.csvw.audi.cc.entity.dto.omd;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OmdModelDto {
    @ApiModelProperty(value = "配置线代码, 不能为空", required = true)
    private String modelCode;
    @ApiModelProperty(value = "车型年, 不能为空", required = true)
    private String modelYear;
    @ApiModelProperty(value = "车型版本, 不能为空", required = true)
    private String modelVersion;
    @ApiModelProperty(value = "内饰, 不能为空", required = true)
    private String interiorCode;
    @ApiModelProperty("选装列表（包含选装包，选装件），逗号分开;可以为空")
    private String prList;
    @ApiModelProperty(value = "颜色代码, 不能为空", required = true)
    private String colorCode;
    @ApiModelProperty(value ="品牌，不能为空，C-SKODA/V-SVW/A-AUDI", required = true)
    private String classCode;
}
