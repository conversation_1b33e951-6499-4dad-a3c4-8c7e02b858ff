package com.csvw.audi.cc.component;

import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.common.SvcdMessage;
import com.csvw.audi.cc.component.kafkaService.SvcdKafkaService;
import com.csvw.audi.cc.constants.RedisConstant;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.exceptions.PersistenceException;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;

@Component
@Slf4j
public class SvcdKafkaListeners {

    @Autowired
    private SvcdSyncLogMapper svcdSyncLogMapper;

    @Autowired
    @Qualifier("svcdOrgKafkaService")
    private SvcdKafkaService svcdOrgKafkaService;

    @Autowired
    @Qualifier("svcdAfterSalesKafkaService")
    private SvcdKafkaService svcdAfterSalesKafkaService;

    @Autowired
    @Qualifier("svcdPositionKafkaService")
    private SvcdKafkaService svcdPositionKafkaService;

    @Autowired
    @Qualifier("svcdUserKafkaService")
    private SvcdKafkaService svcdUserKafkaService;

    @Autowired
    @Qualifier("svcdCityKafkaService")
    private SvcdKafkaService svcdCityKafkaService;

    @Autowired
    @Qualifier("svcdProvinceKafkaService")
    private SvcdKafkaService svcdProvinceKafkaService;

    @Autowired
    @Qualifier("svcdAreaKafkaService")
    private SvcdKafkaService svcdAreaKafkaService;

    @Autowired
    @Qualifier("svcdCompanyKafkaService")
    private SvcdKafkaService svcdCompanyKafkaService;

    @Autowired
    @Qualifier("svcdInvestorKafkaService")
    private SvcdKafkaService svcdInvestorKafkaService;

    @Autowired
    @Qualifier("svcdSalesAgentKafkaService")
    private SvcdKafkaService svcdSalesAgentKafkaService;

    @Autowired
    @Qualifier("svcdOrgRegionKafkaService")
    private SvcdKafkaService svcdOrgRegionKafkaService;

    @Autowired
    RedissonClient redissonClient;
    /**
     * 渠道商组织信息
     * @param record
     * @param ack
     */
    @KafkaListener(topics = "${spring.kafka.org-init-topic}", autoStartup = "${spring.kafka.initAutoStart}")
    public void initListenByOrg(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-init-渠道商组织信息:" + record.toString());
        businessProcessing(record,ack,svcdOrgKafkaService);
    }
    @KafkaListener(topics = "${spring.kafka.org-topic}")
    public void updateListenByOrg(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-update-渠道商组织信息:" + record.toString());
        businessProcessing(record,ack,svcdOrgKafkaService);
    }

    /**
     * 渠道商岗位信息
     * @param record
     * @param ack
     */
    @KafkaListener(topics = "${spring.kafka.position-init-topic}", autoStartup = "${spring.kafka.initAutoStart}")
    public void initListenByPosition(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-init-渠道商岗位信息:" + record.toString());
        businessProcessing(record,ack,svcdPositionKafkaService);
    }
    @KafkaListener(topics = "${spring.kafka.position-topic}")
    public void updateListenByPosition(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-update-渠道商岗位信息:" + record.toString());
        businessProcessing(record,ack,svcdPositionKafkaService);
    }

    /**
     * 渠道商人员信息
     * @param record
     * @param ack
     */
    @KafkaListener(topics = "${spring.kafka.user-init-topic}", autoStartup = "${spring.kafka.initAutoStart}")
    public void initListenByUser(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-init-渠道商人员信息:" + record.toString());
        RBucket<Object> bucket = redissonClient.getBucket(RedisConstant.KAFKA_USER_INIT_FLAG);
        if (bucket.get()!=null && (Boolean) bucket.get()){
            log.info("消费者-init-渠道商人员信息:{}",record.offset());
            businessProcessing(record,ack,svcdUserKafkaService);
        }
    }
    @KafkaListener(topics = "${spring.kafka.user-topic}")
    public void updateListenByUser(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-update-渠道商人员信息:" + record.toString());
        businessProcessing(record,ack,svcdUserKafkaService);
    }

    /**
     * 城市信息
     * @param record
     * @param ack
     */
    @KafkaListener(topics = "${spring.kafka.city-init-topic}", autoStartup = "${spring.kafka.initAutoStart}")
    public void initListenByCity(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-init-城市信息:" + record.toString());
        businessProcessing(record,ack,svcdCityKafkaService);
    }
    @KafkaListener(topics = "${spring.kafka.city-topic}")
    public void updateListenByCity(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-update-城市信息:" + record.toString());
        businessProcessing(record,ack,svcdCityKafkaService);
    }

    /**
     * 省份信息
     * @param record
     * @param ack
     */
    @KafkaListener(topics = "${spring.kafka.province-init-topic}", autoStartup = "${spring.kafka.initAutoStart}")
    public void initListenByProvince(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-init-省份信息:" + record.toString());
        businessProcessing(record,ack,svcdProvinceKafkaService);
    }
    @KafkaListener(topics = "${spring.kafka.province-topic}")
    public void updateListenByProvince(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-update-省份信息:" + record.toString());
        businessProcessing(record,ack,svcdProvinceKafkaService);
    }

    /**
     * 区域信息
     * @param record
     * @param ack
     */
    @KafkaListener(topics = "${spring.kafka.area-init-topic}", autoStartup = "${spring.kafka.initAutoStart}")
    public void initListenByArea(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-init-区域信息:" + record.toString());
        businessProcessing(record,ack,svcdAreaKafkaService);
    }
    @KafkaListener(topics = "${spring.kafka.area-topic}")
    public void updateListenByArea(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-update-区域信息:" + record.toString());
        businessProcessing(record,ack,svcdAreaKafkaService);
    }

    /**
     * 公司信息
     * @param record
     * @param ack
     */
    @KafkaListener(topics = "${spring.kafka.company-init-topic}", autoStartup = "${spring.kafka.initAutoStart}")
    public void initListenByCompany(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-init-公司信息:" + record.toString());
        businessProcessing(record,ack,svcdCompanyKafkaService);
    }
    @KafkaListener(topics = "${spring.kafka.company-topic}")
    public void updateListenByCompany(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-update-公司信息:" + record.toString());
        businessProcessing(record,ack,svcdCompanyKafkaService);
    }

    /**
     * 投资人信息
     * @param record
     * @param ack
     */
    @KafkaListener(topics = "${spring.kafka.investor-init-topic}", autoStartup = "${spring.kafka.initAutoStart}")
    public void initListenByInvestor(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-init-投资人信息:" + record.toString());
        businessProcessing(record,ack,svcdInvestorKafkaService);
    }
    @KafkaListener(topics = "${spring.kafka.investor-topic}")
    public void updateListenByInvestor(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-update-投资人信息:" + record.toString());
        businessProcessing(record,ack,svcdInvestorKafkaService);
    }

    /**
     * 售后服务商
     * @param record
     * @param ack
     */
    @KafkaListener(topics = "${spring.kafka.after-sales-init-topic}", autoStartup = "${spring.kafka.initAutoStart}")
    public void initListenByAfterSales(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-init-售后服务商:" + record.toString());
        businessProcessing(record,ack,svcdAfterSalesKafkaService);
    }
    @KafkaListener(topics = "${spring.kafka.after-sales-topic}")
    public void updateListenByAfterSales(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-update-售后服务商:" + record.toString());
        businessProcessing(record,ack,svcdAfterSalesKafkaService);
    }

    /**
     * 销售代理商
     * @param record
     * @param ack
     */
    @KafkaListener(topics = "${spring.kafka.sales-agent-init-topic}", autoStartup = "${spring.kafka.initAutoStart}")
    public void initListenBySalesAgent(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-init-销售代理商:" + record.toString());
        businessProcessing(record,ack,svcdSalesAgentKafkaService);
    }
    @KafkaListener(topics = "${spring.kafka.sales-agent-topic}")
    public void updateListenBySalesAgent(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-update-销售代理商:" + record.toString());
        businessProcessing(record,ack,svcdSalesAgentKafkaService);
    }

    /**
     * 组织区划信息
     * @param record
     * @param ack
     */
    @KafkaListener(topics = "${spring.kafka.org-region-init-topic}", autoStartup = "${spring.kafka.initAutoStart}")
    public void initListenByOrgRegion(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-init-组织区划信息:" + record.toString());
        businessProcessing(record,ack,svcdOrgRegionKafkaService);
    }
    @KafkaListener(topics = "${spring.kafka.org-region-topic}")
    public void updateListenByOrgRegion(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("消费者-update-组织区划信息:" + record.toString());
        businessProcessing(record,ack,svcdOrgRegionKafkaService);
    }

    private void businessProcessing(ConsumerRecord<String, String> record, Acknowledgment ack, SvcdKafkaService svcdKafkaService) {
        String consumerRecordValue = "";
        String logMsg = "";
        int status = 1;
        LocalDateTime startDate = LocalDateTime.now();
        SvcdSyncLog svcdSyncLog = new SvcdSyncLog();
        svcdSyncLog.setStatus(1);
        svcdSyncLog.setCreatedAt(startDate);
        svcdSyncLog.setUpdatedAt(startDate);
        svcdSyncLog.insert();

        try {
            svcdSyncLog.setKafkaOffset(String.valueOf(record.offset()));
            svcdSyncLog.setKafkaPartition(String.valueOf(record.partition()));
            svcdSyncLog.setKafkaTopic(record.topic());
            svcdSyncLog.setData(record.value());
            consumerRecordValue = record.value();

            //获取 json 结果集
            SvcdMessage<JSONObject> svcdMessage = JSONObject.parseObject(consumerRecordValue, SvcdMessage.class);
            JSONObject bodyJsonObject = svcdMessage.getBody();

            if("CREATE".equals(svcdMessage.getType())) {
                //持久化数据
                svcdKafkaService.insertData(bodyJsonObject,startDate);
            } else if("UPDATE".equals(svcdMessage.getType())) {
                //更新数据
                svcdKafkaService.updateData(bodyJsonObject,startDate);
            }

            if (StringUtils.isBlank(consumerRecordValue)) {
                log.info("SVCD kafka is null, topic = "+svcdSyncLog.getKafkaTopic());
            }

            status = 2;
        }
        catch (PersistenceException e){
            if(e.getCause() instanceof java.sql.SQLIntegrityConstraintViolationException) {
                logMsg = "主键重复"+e.getMessage();
                log.info("SVCD kafka 主键重复, topic = "+svcdSyncLog.getKafkaTopic());
            } else {
                logMsg = e.getMessage();
                status = 3;
                log.info("SVCD kafka 执行异常, topic = "+svcdSyncLog.getKafkaTopic(),e);
            }
        }
        catch (Exception e) {
            logMsg = e.getMessage();
            status = 3;
            log.info("SVCD kafka 执行异常, topic = "+svcdSyncLog.getKafkaTopic(),e);
        }

        svcdSyncLog.setStatus(status);
        svcdSyncLog.setMsg(logMsg);
        svcdSyncLog.setExecutionTime(Duration.between(startDate,LocalDateTime.now()).toMillis());
        svcdSyncLog.setUpdatedAt(LocalDateTime.now());
        svcdSyncLog.updateById();
        if (ack != null){
            ack.acknowledge();
        }
    }

}
