package com.csvw.audi.cc.common;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class SvcdMessage<T> {

    private String type;

    private String msg_version;

    private String version;

    private String time;

    private long timestamp = System.currentTimeMillis();

    private T body;

    public SvcdMessage(String type, String msg_version, String version, String time, T body){
        this.type = type;
        this.msg_version = msg_version;
        this.version = version;
        this.time = time;
        this.body = body;
    }
}
