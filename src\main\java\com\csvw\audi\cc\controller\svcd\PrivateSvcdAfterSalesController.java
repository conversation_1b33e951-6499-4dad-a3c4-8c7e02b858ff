package com.csvw.audi.cc.controller.svcd;

import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.dto.SvcdAfterSalesDto;
import com.csvw.audi.cc.entity.vo.SvcdAfterSalesVo;
import com.csvw.audi.cc.service.ISvcdAfterSalesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "售后服务商-feign")
@RestController
@RequestMapping("/private/api/v1/svcdAfterSales")
public class PrivateSvcdAfterSalesController extends BaseController {

    @Autowired
    private ISvcdAfterSalesService svcdAfterSalesService;

    @ApiOperation("获取售后服务商列表-feign")
    @GetMapping("/getAfterSalesList")
    public AjaxMessage<List<SvcdAfterSalesVo>> getAfterSalesList(SvcdAfterSalesDto afterSalesDto) {
        List<SvcdAfterSalesVo> list =  svcdAfterSalesService.getAfterSalesList(afterSalesDto);
        return new AjaxMessage<>("00", "成功", list);
    }

    @ApiOperation("获取代理商指定的服务商-feign")
    @GetMapping("/getAfterSalesByAgent")
    public AjaxMessage<SvcdAfterSalesVo> getAfterSalesByAgent(@RequestParam String agentCode) {
        return new AjaxMessage<>("00", "成功", svcdAfterSalesService.getAfterSalesByAgent(agentCode));
    }
}
