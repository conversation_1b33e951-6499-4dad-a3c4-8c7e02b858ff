<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarOmdBestRecommendMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarOmdBestRecommend">
        <id column="best_sell_recommend_model_id" property="bestSellRecommendModelId" />
        <result column="model_code" property="modelCode" />
        <result column="model_version" property="modelVersion" />
        <result column="model_year" property="modelYear" />
        <result column="interiorCode" property="interiorCode" />
        <result column="colorCode" property="colorCode" />
        <result column="prList" property="prList" />
        <result column="classCode" property="classCode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        best_sell_recommend_model_id, model_code, model_version, model_year, interiorCode, colorCode, prList, classCode, create_time, update_time, del_flag
    </sql>

</mapper>
