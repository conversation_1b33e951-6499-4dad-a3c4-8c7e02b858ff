package com.csvw.audi.cc.component.kafkaService.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csvw.audi.cc.component.kafkaService.SvcdKafkaService;
import com.csvw.audi.cc.entity.po.SvcdPositions;
import com.csvw.audi.cc.entity.po.SvcdRoles;
import com.csvw.audi.cc.entity.po.SvcdUser;
import com.csvw.audi.cc.service.ISvcdPositionsService;
import com.csvw.audi.cc.service.ISvcdRolesService;
import com.csvw.audi.cc.service.ISvcdUserImageAssistant;
import com.csvw.audi.cc.service.ISvcdUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service("svcdUserKafkaService")
public class SvcdUserKafkaServiceImpl implements SvcdKafkaService {
    @Autowired
    ISvcdUserService iSvcdUserService;
    @Autowired
    ISvcdPositionsService iSvcdPositionsService;
    @Autowired
    ISvcdRolesService iSvcdRolesService;
    @Autowired
    private ISvcdUserImageAssistant svcdUserImageAssistant;

    @Override
    public void insertData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdUser user = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdUser.class);
        List<Long> userIds = null;
        List<SvcdUser> list = iSvcdUserService.lambdaQuery().eq(SvcdUser::getUserUid, user.getUserUid()).list();
        if (CollectionUtil.isNotEmpty(list)){
            userIds = list.stream().map(a -> a.getUserId()).collect(Collectors.toList());
            iSvcdUserService.getBaseMapper().deleteBatchIds(userIds);
        }
        user.setCreatedAt(nowDate);
        user.setUpdatedAt(nowDate);
        user.insert();
        // 更新用户邀请好友封禁状态
        updateNoInvite(user);

        if (CollectionUtil.isNotEmpty(userIds)){
            iSvcdPositionsService.lambdaUpdate().in(SvcdPositions::getUserId,userIds).remove();
        }
        //员工岗位列表
        if(user.getPositions() != null) {
            for(SvcdPositions positions : user.getPositions()) {
                positions.setUserId(user.getUserId());
                positions.setCreatedAt(nowDate);
                positions.setUpdatedAt(nowDate);
                positions.insert();
            }
        }
        if (CollectionUtil.isNotEmpty(userIds)){
            iSvcdRolesService.lambdaUpdate().in(SvcdRoles::getUserId,userIds).remove();
        }
        //用户角色列表
        if(user.getRoles() != null) {
            for(SvcdRoles roles : user.getRoles()) {
                roles.setUserId(user.getUserId());
                roles.setCreatedAt(nowDate);
                roles.setUpdatedAt(nowDate);
                roles.insert();
            }
        }
        try {
            svcdUserImageAssistant.asyncFindAndStorageSvcdUserImage(Arrays.asList(user));
        } catch (Exception e) {
            log.info("网发推送来的消息文件上传oss异常");
            log.error("{}", e);
        }
    }

    @Override
    public void updateData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdUser user = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdUser.class);

        QueryWrapper<SvcdUser> userQusery = new QueryWrapper<>();
        userQusery.eq("user_uid",user.getUserUid());
        List<SvcdUser> list = user.selectList(userQusery);
        if(list == null || list.size() == 0) {
            user.setCreatedAt(nowDate);
            user.setUpdatedAt(nowDate);
            user.insert();
        } else {
            user.setUserId(list.get(0).getUserId());
            user.setUpdatedAt(nowDate);
            user.updateById();
        }
        // 更新用户邀请好友封禁状态
        updateNoInvite(user);
        if (StrUtil.isBlank(user.getPic())){
            iSvcdUserService.lambdaUpdate().eq(SvcdUser::getUserId,user.getUserId())
                    .set(SvcdUser::getPic,null).update();
        }
        //删除 员工岗位列表
        iSvcdPositionsService.lambdaUpdate().eq(SvcdPositions::getUserId,user.getUserId()).remove();
        //删除 用户角色列表
        iSvcdRolesService.lambdaUpdate().eq(SvcdRoles::getUserId,user.getUserId()).remove();

        //员工岗位列表
        if(user.getPositions() != null) {
            for(SvcdPositions positions : user.getPositions()) {
                positions.setUserId(user.getUserId());
                positions.setCreatedAt(nowDate);
                positions.setUpdatedAt(nowDate);
                positions.insert();
            }
        }
        //用户角色列表
        if(user.getRoles() != null) {
            for(SvcdRoles roles : user.getRoles()) {
                roles.setUserId(user.getUserId());
                roles.setCreatedAt(nowDate);
                roles.setUpdatedAt(nowDate);
                roles.insert();
            }
        }
        try {
            svcdUserImageAssistant.asyncFindAndStorageSvcdUserImage(Arrays.asList(user));
        } catch (Exception e) {
            log.info("网发推送来的消息文件上传oss异常");
            log.error("{}", e);
        }
    }

    /**
     * @description 更新用户邀请好友的封禁状态
     * 这里使用异步调用,提高一下效率
     */
    private void updateNoInvite(SvcdUser user) {
        iSvcdUserService.asyncUpdateNoInvite(user);
    }
}
