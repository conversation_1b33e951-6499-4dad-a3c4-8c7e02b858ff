package com.csvw.audi.cc.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.entity.dto.*;
import com.csvw.audi.cc.entity.dto.vwcc.CarCustomDetail;
import com.csvw.audi.cc.entity.dto.vwcc.ConfigDetail;
import com.csvw.audi.cc.entity.dto.vwcc.Option;
import com.csvw.audi.cc.entity.po.CarCustom;
import com.csvw.audi.cc.entity.po.CarCustomOption;
import com.csvw.audi.cc.entity.po.CarModelLineSpecialOption;
import com.csvw.audi.cc.entity.vo.ModelLineVo;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.feign.AudiOrderAdminFeign;
import com.csvw.audi.cc.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OmdCcCompareServiceImpl {

    @Autowired
    private AudiOrderAdminFeign audiOrderAdminFeign;

    @Autowired
    private CarRecommendCustomServiceImpl recommendCustomService;

    @Autowired
    private ICarCustomService carCustomService;

    @Autowired
    private ICarCustomOptionService carCustomOptionService;

    @Autowired
    private ICarOptionRelateService optionRelateService;

    @Autowired
    private ICarModelLineSpecialOptionService specialOptionService;

    @Autowired
    private ICarModelLineService modelLineService;

    public void omdOrderConfigValid(InputStream stream, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        params.setSheetNum(1);
        params.setNeedVerify(true);
        List<OmdCompareImportDto> importData = ExcelImportUtil.importExcel(stream, OmdCompareImportDto.class, params);
        List<OmdCompareExportDto> exportDtos = new ArrayList<>();
        OutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
            ExportParams exportParams = new ExportParams();
            exportParams.setSheetName("校验结果");
            for(OmdCompareImportDto i : importData){
                OmdCompareExportDto dto = new OmdCompareExportDto();
                try {
                    dto.setRes("正常");
                    BeanUtils.copyProperties(i, dto);
                    CopOrderDto orderDto = audiOrderAdminFeign.getOrderDetail(i.getOrderId()).getData();
                    if (orderDto == null) {
                        dto.setRes("异常");
                        dto.setResDesc("没有查到订单信息");
                        exportDtos.add(dto);
                        continue;
                    }
                    CarCustom carCustom = new CarCustom();
                    carCustom = carCustom.selectById(Long.valueOf(orderDto.getCarCustomId()));
                    if (carCustom == null) {
                        dto.setRes("异常");
                        dto.setResDesc("没有查到配置单信息");
                        exportDtos.add(dto);
                        continue;
                    }
                    QueryWrapper<CarCustomOption> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("ccid", carCustom.getCcid());
                    List<CarCustomOption> optionList = carCustomOptionService.list(queryWrapper);
                    OptionRelateParam optionRelateParam = new OptionRelateParam();
                    optionRelateParam.setModelLineId(carCustom.getModelLineId());
                    optionRelateParam.setRelateTypes(Arrays.asList("attach"));
                    List<OptionRelDto> relDtos = optionRelateService.listOptionRelDto(optionRelateParam);
                    Map<String, List<OptionRelDto>> oMap = new HashMap<>();
                    relDtos.forEach(r -> {
                        List<OptionRelDto> oRel = oMap.get(r.getOptionId());
                        if (oRel == null) {
                            oRel = new ArrayList<>();
                            oMap.put(r.getOptionId(), oRel);
                        }
                        oRel.add(r);
                    });
                    List<String> attachCodes = new ArrayList<>();
                    for (CarCustomOption o : optionList) {
                        if (oMap.get(o.getOptionId()) != null) {
                            for (OptionRelDto relDto : oMap.get(o.getOptionId())) {
                                if (relDto.getRelateType().equals("attach")) {
                                    attachCodes.add(relDto.getOptionRelateCode());
                                }
                            }
                        }
                    }
                    ModelParamDto modelParamDto = new ModelParamDto();
                    modelParamDto.setDelFlag(0);
                    modelParamDto.setModelLineId(carCustom.getModelLineId());
                    modelParamDto.setChannel(Constant.MASTER_CHANNEL);
                    List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
                    if (lines == null || lines.size() != 1) {
                        dto.setRes("异常");
                        dto.setResDesc("配置线异常");
                        exportDtos.add(dto);
                        continue;
                    }
                    ModelLineVo modelLine = lines.get(0);
                    if (modelLine.getSpecialLine().intValue() == 1) {
                        List<CarModelLineSpecialOption> specialOptions = specialOptionService.listModelLineSpecialOption(carCustom.getModelLineId());
                        if (org.apache.commons.collections4.CollectionUtils.isEmpty(specialOptions)) {
                            dto.setRes("异常");
                            dto.setResDesc("特殊配置线，配置单异常");
                            exportDtos.add(dto);
                            continue;
                        }
                        specialOptions.forEach(so -> {
                            attachCodes.add(so.getOptionCode());
                        });
                    }
                    dto.setOmdAttachCode(String.join(",", attachCodes));
                    CarCustomDetail customDetail = null;
                    try {
                        if (carCustom.getBestRecommendId() != null) {
                            customDetail = recommendCustomService.getCarConfigDetailOmd(carCustom);
                        } else {
                            customDetail = carCustomService.getCarConfigDetailOmd(carCustom);
                        }
                    } catch (Exception e) {
                        dto.setRes("异常");
                        dto.setResDesc("配置单信息查询失败");
                        continue;
                    }
                    ConfigDetail detail = customDetail.getConfigDetail();
                    dto.setCcModelLineCode(detail.getCarModel().getModelCode());
                    dto.setCcModelYear(detail.getCarModel().getModelYear());
                    dto.setCcModelVersion(detail.getCarModel().getOmdModelVersion());
                    dto.setCcInsideCode(detail.getInsideColor().getColorCode());
                    dto.setCcOutsideCode(detail.getOutsideColor().getColorCode());
                    if (StringUtils.isBlank(i.getPrCodes()) && CollectionUtils.isNotEmpty(detail.getOptionList())) {
                        dto.setRes("异常");
                        dto.setResDesc("omd和cc prList不一致");
                        String codes = detail.getOptionList().stream().map(Option::getOptionCode).collect(Collectors.joining(","));
                        dto.setCcPrCodes(codes);
                    }
                    if (StringUtils.isNotBlank(i.getPrCodes()) && CollectionUtils.isEmpty(detail.getOptionList())) {
                        dto.setRes("异常");
                        dto.setResDesc("omd和cc prList不一致");
                    }

                    if (StringUtils.isNotBlank(i.getPrCodes())) {
                        List<String> omdCodes = Arrays.asList(i.getPrCodes().split(","));
                        if (CollectionUtils.isNotEmpty(attachCodes) && !omdCodes.containsAll(attachCodes)) {
                            dto.setRes("异常");
                            dto.setRes("缺失附加编码: " + String.join(",", attachCodes));
                        }
                    }

                    if (StringUtils.isNotBlank(i.getPrCodes()) && CollectionUtils.isNotEmpty(detail.getOptionList())) {
                        List<String> omdCodes = Arrays.asList(i.getPrCodes().split(","));
                        List<String> codes = detail.getOptionList().stream().map(Option::getOptionCode).collect(Collectors.toList());
                        String ccCodes = detail.getOptionList().stream().map(Option::getOptionCode).collect(Collectors.joining(","));
                        dto.setCcPrCodes(ccCodes);
                        if (codes.containsAll(omdCodes) && omdCodes.containsAll(codes)) {

                        } else {
                            dto.setRes("异常");
                            dto.setResDesc("omd和cc prList不一致");
                        }

                    }

                    if (dto.getCcModelLineCode() != null && !dto.getCcModelLineCode().equals(dto.getModelLineCode())) {
                        dto.setRes("异常");
                        dto.setResDesc("omd和cc 配置线代码不一致");
                    }
                    if (dto.getInsideCode() != null && !dto.getInsideCode().equals(dto.getCcInsideCode())) {
                        dto.setRes("异常");
                        dto.setResDesc("omd和cc 内饰编码不一致");
                    }
                    if (dto.getOutsideCode() != null && !dto.getOutsideCode().equals(dto.getCcOutsideCode())) {
                        dto.setRes("异常");
                        dto.setResDesc("omd和cc 外饰编码不一致");
                    }
                }catch (Exception e){
                    dto.setRes("异常");
                    dto.setResDesc("程序异常：" +e.getMessage());
                    log.error("compare exception",e);
                }
                exportDtos.add(dto);
            }
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, OmdCompareExportDto.class, exportDtos);
            workbook.write(outputStream);
            workbook.close();
        }finally {
            if (outputStream != null){
                outputStream.flush();
                outputStream.close();
            }
        }

    }
}
