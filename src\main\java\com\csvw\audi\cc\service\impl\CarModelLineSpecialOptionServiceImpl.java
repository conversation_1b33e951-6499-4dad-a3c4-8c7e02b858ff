package com.csvw.audi.cc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.entity.dto.OptionParamDto;
import com.csvw.audi.cc.entity.po.CarModelLineSpecialOption;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.mapper.CarModelLineSpecialOptionMapper;
import com.csvw.audi.cc.service.ICarModelLineOptionService;
import com.csvw.audi.cc.service.ICarModelLineSpecialOptionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csvw.audi.cc.service.ICarOmdPriceTypeService;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 特定配置线配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
@Service
public class CarModelLineSpecialOptionServiceImpl extends ServiceImpl<CarModelLineSpecialOptionMapper, CarModelLineSpecialOption> implements ICarModelLineSpecialOptionService {

    @Autowired
    private ICarOmdPriceTypeService priceTypeService;

    @Autowired
    private ICarModelLineOptionService modelLineOptionService;

    @Override
    public BigDecimal totalOptionPrice(String modelLineId) throws ServiceException {
        List<CarModelLineSpecialOption> specialOptions = this.listModelLineSpecialOption(modelLineId);
        BigDecimal specialOptionsPrice = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(specialOptions)){
            return null;
        }else {
            for (CarModelLineSpecialOption specialOption : specialOptions){
                BigDecimal optionPrice = priceTypeService.optionPrice(modelLineId, specialOption.getOptionCode(), specialOption.getCategory());
                if (optionPrice == null){
                    return null;
                }
                specialOptionsPrice = specialOptionsPrice.add(optionPrice);
            }
        }
        return specialOptionsPrice;
    }

    @Override
    public List<CarModelLineSpecialOption> listModelLineSpecialOption(String modelLineId) {
        LambdaQueryWrapper<CarModelLineSpecialOption> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CarModelLineSpecialOption::getModelLineId, modelLineId)
                .eq(CarModelLineSpecialOption::getDelFlag, 0);
        return this.list(queryWrapper);
    }

    @Override
    public List<ModelLineOptionVo> listModelLineSpecialOptionVO(String modelLineId) {
        List<CarModelLineSpecialOption> specialOptions = this.listModelLineSpecialOption(modelLineId);
        if (CollectionUtils.isEmpty(specialOptions)){
            return new ArrayList<>();
        }
        OptionParamDto paramDto = new OptionParamDto();
        paramDto.setModelLineId(modelLineId);
        paramDto.setOptionIds(specialOptions.stream().map(i->i.getOptionId()).collect(Collectors.toList()));
        paramDto.setChannel(Constant.MASTER_CHANNEL);
        return modelLineOptionService.modelLineOptionQuery(paramDto);
    }
}
