package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 渠道商对应的金融机构
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="AdminOrgBank对象", description="渠道商对应的金融机构")
public class AdminOrgBank extends Model<AdminOrgBank> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "数据id")
      @TableId(value = "org_bank_id", type = IdType.ASSIGN_ID)
    private Long orgBankId;

    @ApiModelProperty(value = "渠道商编号")
    private String dealerCode;

    @ApiModelProperty(value = "渠道商名称")
    private String dealerName;

    @ApiModelProperty(value = "渠道商全称")
    private String dealerFullName;

    @ApiModelProperty(value = "金融机构代码")
    private String bankCode;

    @ApiModelProperty(value = "金融机构名称")
    private String bankName;

    @ApiModelProperty(value = "删除标识（1：删除，0：未删除）")
    private String delFlag;

    @ApiModelProperty(value = "创建人")
    private String createdUser;

    @ApiModelProperty(value = "更新人")
    private String updatedUser;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "金融方案确认文案")
    private String confirmText;

    @Override
    protected Serializable pkVal() {
        return this.orgBankId;
    }

}
