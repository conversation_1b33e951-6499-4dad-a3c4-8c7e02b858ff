package com.csvw.audi.cc.component.kafkaService.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csvw.audi.cc.component.kafkaService.SvcdKafkaService;
import com.csvw.audi.cc.entity.po.SvcdPosition;
import com.csvw.audi.cc.mapper.SvcdPositionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service("svcdPositionKafkaService")
public class SvcdPositionKafkaServiceImpl implements SvcdKafkaService {

    @Autowired
    private SvcdPositionMapper svcdPositionMapper;

    @Override
    public void insertData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdPosition position = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdPosition.class);
        position.setCreatedAt(nowDate);
        position.setUpdatedAt(nowDate);
        position.insert();
    }

    @Override
    public void updateData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdPosition position = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdPosition.class);

        QueryWrapper<SvcdPosition> positionQusery = new QueryWrapper<>();
        positionQusery.eq("code",position.getCode());
        List<SvcdPosition> list = position.selectList(positionQusery);
        if(list == null || list.size() == 0) {
            position.setCreatedAt(nowDate);
            position.setUpdatedAt(nowDate);
            position.insert();
        } else {
            position.setPositionId(list.get(0).getPositionId());
            position.setUpdatedAt(nowDate);
            position.updateById();
        }
    }
}
