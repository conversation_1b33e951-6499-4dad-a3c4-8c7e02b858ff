package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 区域信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SvcdArea对象", description="区域信息")
public class SvcdArea extends Model<SvcdArea> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "数据id")
      @TableId(value = "area_id", type = IdType.AUTO)
    private Long areaId;

    @ApiModelProperty(value = "区域名称")
    private String name;

    @ApiModelProperty(value = "区域代码")
    private Long areaCode;

    @ApiModelProperty(value = "城市代码")
    private Long cityCode;

    @ApiModelProperty(value = "简称")
    private String simpleName;

    @ApiModelProperty(value = "速查码")
    private String quickCode;

    @ApiModelProperty(value = "区域拼音")
    private String areaEn;

    @ApiModelProperty(value = "状态")
    private Long status;

    @ApiModelProperty(value = "招募区域代码")
    private Long zmAreaCode;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;


    @Override
    protected Serializable pkVal() {
        return this.areaId;
    }

}
