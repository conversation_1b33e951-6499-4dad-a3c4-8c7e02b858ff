package com.csvw.audi.cc.service;

import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.entity.dto.DealerDto;
import com.csvw.audi.cc.entity.po.SvcdChannelOrganization;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.vo.DealerVo;

import java.util.List;

/**
 * <p>
 * 渠道商组织信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
public interface ISvcdChannelOrganizationService extends IService<SvcdChannelOrganization> {

    public List<DealerVo> getAgentList(DealerDto dealerDto);

    public List<DealerVo> getNearestAgent(DealerDto dealerDto);

    public DealerVo getHeadquarters();

    public List<DealerVo> getDealerListByOfficialWebsite(DealerDto dealerDto);

    public List<DealerVo> getOrgList(DealerDto dealerDto);

    public JSONObject getDealerRequestParamByOfficialWebsite(DealerDto dealerDto);

    public List<DealerVo> getDealerListByAiChe(DealerDto dealerDto);

    List<DealerVo> getDealerListByCityWide(DealerDto dealerDto);

    public List<DealerVo> getDealerListByTestDrive(DealerDto dealerDto);

    List<SvcdChannelOrganization> selectSvcdChannelOrganizationByPCode(String pCode);
}
