package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.dto.DealerDto;
import com.csvw.audi.cc.entity.po.SvcdChannelOrganization;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csvw.audi.cc.entity.vo.DealerVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 渠道商组织信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
public interface SvcdChannelOrganizationMapper extends BaseMapper<SvcdChannelOrganization> {

    public List<DealerVo> getAgentList(DealerDto dealerDto);

    public List<DealerVo> getDealerListByOfficialWebsite(DealerDto dealerDto);

    public List<DealerVo> getOrgList(DealerDto dealerDto);

    List<SvcdChannelOrganization> selectSvcdChannelOrganizationByPCode(@Param("pCode") String pCode);
}
