<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarOmdStockRecommendLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarOmdStockRecommendLog">
        <id column="id" property="id" />
        <result column="stock_recommend_model_id" property="stockRecommendModelId" />
        <result column="model_code" property="modelCode" />
        <result column="model_version" property="modelVersion" />
        <result column="model_year" property="modelYear" />
        <result column="interior_code" property="interiorCode" />
        <result column="color_code" property="colorCode" />
        <result column="pr_list" property="prList" />
        <result column="class_code" property="classCode" />
        <result column="default_flag" property="defaultFlag" />
        <result column="dealer_net_code" property="dealerNetCode" />
        <result column="row_index" property="rowIndex" />
        <result column="operate_type" property="operateType" />
        <result column="operate_time" property="operateTime" />
        <result column="convert_recommend_car" property="convertRecommendCar" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, stock_recommend_model_id, model_code, model_version, model_year, interior_code, color_code, pr_list, class_code, default_flag, dealer_net_code, row_index, operate_type, operate_time, convert_recommend_car, create_time, update_time, del_flag
    </sql>

    <select id="listUnhandleRecommend" resultType="Long">
        select distinct `stock_recommend_model_id` from `car_omd_stock_recommend_log` where `convert_recommend_car` != 1
    </select>

</mapper>
