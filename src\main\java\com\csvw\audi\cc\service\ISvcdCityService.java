package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.dto.SvcdCityDto;
import com.csvw.audi.cc.entity.dto.SvcdProvinceCityListDto;
import com.csvw.audi.cc.entity.po.SvcdCity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.vo.SvcdCityVo;

import java.util.List;

/**
 * <p>
 * 城市信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
public interface ISvcdCityService extends IService<SvcdCity> {

    public List<SvcdCityVo> getCityList(SvcdCityDto cityDto);

    public List<SvcdCityVo> getCityListByOfficialWebsite(SvcdCityDto cityDto);

    List<SvcdProvinceCityListDto> getCityListForCityWide(SvcdCityDto cityDto);
}
