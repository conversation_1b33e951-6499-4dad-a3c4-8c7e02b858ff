package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.entity.dto.OptionRelDto;
import com.csvw.audi.cc.entity.dto.OptionRelateParam;
import com.csvw.audi.cc.entity.enumeration.OptionCategoryEnum;
import com.csvw.audi.cc.entity.po.CarOptionRelate;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.mapper.CarOptionMapper;
import com.csvw.audi.cc.mapper.CarOptionRelateMapper;
import com.csvw.audi.cc.service.ICarOptionRelateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 选装关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-16
 */
@Service
@Slf4j
public class CarOptionRelateServiceImpl extends ServiceImpl<CarOptionRelateMapper, CarOptionRelate> implements ICarOptionRelateService {

    @Autowired
    private CarOptionRelateMapper relateMapper;

    @Override
    public List<OptionRelDto> listOptionRelDto(OptionRelateParam optionRelateParam) {
        return relateMapper.listOptionRelDto(optionRelateParam);
    }

    @Override
    public List<OptionRelDto> listCombines(String modelLineId, String optionCode) {
        return relateMapper.listCombines(modelLineId, optionCode);
    }

    @Override
    public List<CarOptionRelate> listConflictOptionRelate(String modelLineId, Set<String> optionIds) {
        return relateMapper.listConflictOptionRelate(modelLineId, optionIds);
    }

    @Override
    public List<CarOptionRelate> listOptionConflict(String modelLineId, String optionId, Set<String> optionRelateIds) {
        return relateMapper.listOptionConflict(modelLineId, optionId, optionRelateIds);
    }

    @Override
    public List<CarOptionRelate> listOptionDepend(String modelLineId, String optionId, Set<String> optionRelateIds) {
        return relateMapper.listOptionDepend(modelLineId, optionId, optionRelateIds);
    }

    @Override
    public Boolean validDepends(String modelLineId, Set<String> optionIds) throws ServiceException {
        boolean hasOtherDepend = false;
        boolean depended = false;
        for (String optionId : optionIds){
            List<String> categories = relateMapper.listDependCategory(modelLineId, optionId);
            if (CollectionUtils.isNotEmpty(categories)){
                for (String category : categories){
                    List<CarOptionRelate> depends = relateMapper.listDependsForValid(modelLineId, category, optionId, optionIds);
                    if (depends != null && depends.size() != 1){
                        log.error("新增/修改配置存在依赖问题, modelLineId: {} , depends：{}", modelLineId, depends);
                        throw new ServiceException("400401", "参数异常：optionIds", "新增/修改配置存在依赖问题");
                    }
                }
            }
            List<String> groups = relateMapper.listDependGroup(modelLineId, optionId);
            if (CollectionUtils.isNotEmpty(groups)) {
                for (String group : groups) {
                    List<CarOptionRelate> depends = relateMapper.listDependsForGroupValid(modelLineId, group, optionId, optionIds);
                    if (depends != null && depends.size() != 1) {
                        log.error("新增/修改配置存在依赖问题, modelLineId: {} , depends：{}", modelLineId, depends);
                        throw new ServiceException("400401", "参数异常：optionIds", "新增/修改配置存在依赖问题");
                    }
                }
            }
        }
        if (hasOtherDepend && !depended){
            return false;
        }
        return true;
    }

    @Override
    public List<CarOptionRelate> listDependedByOptionId(String modelLineId, String optionId, Set<String> optionIds) {
        return relateMapper.listDependedByOptionId(modelLineId, optionId, optionIds);
    }

    @Override
    public List<CarOptionRelate> listSibInterieurDependedByOptionId(String modelLineId, String sibInterieurId, Set<String> optionIds) {
        return relateMapper.listSibInterieurIdDependedByOptionId(modelLineId, sibInterieurId, optionIds);
    }


}
