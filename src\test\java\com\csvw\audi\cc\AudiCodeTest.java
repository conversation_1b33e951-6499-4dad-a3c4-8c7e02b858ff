package com.csvw.audi.cc;

import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.utils.AccbRequestUtil;
import com.csvw.audi.cc.common.utils.accb.ACCBResponse;
import com.csvw.audi.cc.controller.AccbTestController;
import com.csvw.audi.cc.entity.CcDataDto;
import com.csvw.audi.cc.entity.PowerSetIterable;
import com.csvw.audi.cc.entity.dto.accb.AudiConfigDto;
import com.csvw.audi.cc.entity.dto.accb.OptionBriefDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

public class AudiCodeTest {

    @Test
    public void genAudiCode(){
        List<CcDataDto> dataDtoList = new ArrayList<>();
//        CcDataDto data1Dto = new CcDataDto("f48a0f2e-124c-5364-9601-a47bc80e85b1", "91593990-7257-5afc-ab43-63d3bc70f0d0", "TYPE:498B2Y", "S-line志远型流晶套装");
//        data1Dto.setExterieurCodes(Arrays.asList("2Y2Y 皓月白，金属漆", "B9B9 星河蓝，金属漆", "8C8C 赤霞朱，金属漆", "2T2T 旷夜黑，金属漆", "2L2L 和风灰，金属漆", "0D0D 珠光银，金属漆"));
//        data1Dto.setInterieurCodes(Arrays.asList("MP 静谧黑", "FF 悠远灰",  "FG 温情棕"));
//        data1Dto.setOptionCodes(Arrays.asList("53D 21吋5幅星钻式设计，RS竞速轮毂", "7TL 岩灰天然栓木饰条", "5MK 立体肌纹碳纤维饰板", "WA3 冬季舒适套装", "PS8 前排VIP行政商务套装", "PCY 奥迪Pro版增强型辅助驾驶系统套装", "KS1 wHUD平视显示系统", "9R1 夜视系统"));
//        CcDataDto data2Dto = new CcDataDto("f48a0f2e-124c-5364-9601-a47bc80e85b1", "1752b7cd-a6ef-5876-9302-2e6da1edc121", "TYPE:498B2Y-GPAHPAH", "S-line志远型曜黑套装");
//        data2Dto.setExterieurCodes(Arrays.asList("2T2T 旷夜黑，金属漆", "2YA2 皓月白带黑车顶，金属漆", "B9A2 星河蓝带黑车顶，金属漆", "8CA2 赤霞朱带黑车顶，金属漆", "2LA2 和风灰带黑车顶，金属漆", "0DA2 珠光银带黑车顶，金属漆"));
//        data2Dto.setInterieurCodes(Arrays.asList("MP 静谧黑", "FF 悠远灰",  "FG 温情棕"));
//        data2Dto.setOptionCodes(Arrays.asList("53D 21吋5幅星钻式设计，RS竞速轮毂", "7TL 岩灰天然栓木饰条", "WA3 冬季舒适套装", "PS8 前排VIP行政商务套装", "PCY 奥迪Pro版增强型辅助驾驶系统套装", "KS1 wHUD平视显示系统", "9R1 夜视系统"));
        //境远型流晶套装
        CcDataDto data3Dto = new CcDataDto("f48a0f2e-124c-5364-9601-a47bc80e85b1", "5a5c11d4-275c-56cd-967e-a6d5d2dc01aa", "498B2Y-MRADF15-GWCVWCV", "S-line境远型流晶套装");
        data3Dto.setExterieurCodes(Arrays.asList(
                "2Y2Y 皓月白，金属漆",
                "B9B9 星河蓝，金属漆",
                "8C8C 赤霞朱，金属漆",
                "2T2T 旷夜黑，金属漆",
                "2L2L 和风灰，金属漆",
                "0D0D 珠光银，金属漆"
        ));
        data3Dto.setInterieurCodes(Arrays.asList(
                "FG 温情棕",
                "FH 舒云白"
        ));
        data3Dto.setInterieurStyleCodes(Arrays.asList(
                "7TD 檀棕 天然桦木饰条",
                "7TL 岩灰 天然栓木饰条"
        ));
        data3Dto.setWheelCodes(Arrays.asList(
                "F15 20吋10幅 星芒轮毂",
                "42F 20吋5幅 星斗轮毂",
                "53D 21吋5幅 星钻式设计，RS竞速轮毂"
        ));
        data3Dto.setOptionCodes(Arrays.asList(
                "KS1 wHUD平视显示系统",
                "9R1 夜视系统",
                "9WP 后排娱乐系统",
                "9PF 自适应雨刮器",
                "6NQ 黑色车内顶棚"
        ));
        data3Dto.setOptionPackageCodes(Arrays.asList(
                "WA3 冬季舒适套装",
                "PCY 奥迪Pro版增强型辅助驾驶系统套装",
                "WAO 奥迪高级智能数字套装",
                "PT1 奥迪智能数字套装"
        ));

        //志远型
        CcDataDto zhiyuanDto = new CcDataDto("","","498B2Y","");
        zhiyuanDto.setExterieurCodes(Arrays.asList(
                "2Y2Y 皓月白，金属漆",
                "B9B9 星河蓝，金属漆",
                "8C8C 赤霞朱，金属漆",
                "2T2T 旷夜黑，金属漆",
                "2L2L 和风灰，金属漆",
                "0D0D 珠光银，金属漆"
        ));
        zhiyuanDto.setInterieurCodes(Arrays.asList(
                "FG 温情棕",
                "FF 悠远灰",
                "MP 静谧黑"
        ));
        zhiyuanDto.setInterieurStyleCodes(Arrays.asList(
                "7TD 檀棕 天然桦木饰条",
                "7TL 岩灰 天然栓木饰条"
        ));
        zhiyuanDto.setWheelCodes(Arrays.asList(
                "F15 20吋10幅 星芒轮毂",
                "42F 20吋5幅 星斗轮毂",
                "53D 21吋5幅 星钻式设计，RS竞速轮毂",
                "F13 20吋双5幅 星束轮毂"
        ));
        zhiyuanDto.setOptionCodes(Arrays.asList(
                "KS1 wHUD平视显示系统",
                "9R1 夜视系统",
                "9VS Bang＆Olufsen 3D环绕音响系统",
                "9WP 后排娱乐系统",
                "8IZ 智能激光大灯",
                "9PF 自适应雨刮器",
                "6NQ 黑色车内顶棚"
        ));
        zhiyuanDto.setOptionPackageCodes(Arrays.asList(
                "WA3 冬季舒适套装",
                "PT1 奥迪智能数字套装",
                "PGC Intelligent Electric Tailgate package 智能电动尾门套装",
                "PDW+WA6+2V3+GZ2+9PF 尊享行政座舱",
                "WAO Audi Advanced Smart Digital package 奥迪高级智能数字套装",
                "PS8 VIP Executive Seat package front seats 前排行政套装",
                "WAB+PS8 行政尊享全套装",
                "PS1 Sport Seat Business package 后排行政套装",
                "PCY 奥迪Pro版增强型辅助驾驶系统套装"
        ));

        //境远耀黑型
        CcDataDto jinyuanYaoheiDto = new CcDataDto("","","498B2Y-MRADF15-GPAHPAH-GWCVWCV","");
        jinyuanYaoheiDto.setExterieurCodes(Arrays.asList(
                "2YA2 皓月白带黑车顶，金属漆",
                "B9A2 星河蓝带黑车顶，金属漆",
                "8CA2 赤霞朱带黑车顶，金属漆",
                "2T2T 旷夜黑，金属漆",
                "2LA2 和风灰带黑车顶，金属漆",
                "0DA2 珠光银带黑车顶，金属漆"
        ));
        jinyuanYaoheiDto.setInterieurCodes(Arrays.asList(
                "FG 温情棕",
                "FH 舒云白"
        ));
        jinyuanYaoheiDto.setInterieurStyleCodes(Arrays.asList(
                "7TD 檀棕 天然桦木饰条",
                "7TL 岩灰 天然栓木饰条"
        ));
        jinyuanYaoheiDto.setWheelCodes(Arrays.asList(
                "F15 20吋10幅 星芒轮毂",
                "42F 20吋5幅 星斗轮毂",
                "53D 21吋5幅 星钻式设计，RS竞速轮毂"
        ));
        jinyuanYaoheiDto.setOptionCodes(Arrays.asList(
                "KS1 wHUD平视显示系统",
                "9R1 夜视系统",
                "9WP 后排娱乐系统",
                "9PF 自适应雨刮器"
        ));
        jinyuanYaoheiDto.setOptionPackageCodes(Arrays.asList(
                "WA3 冬季舒适套装",
                "PCY 奥迪Pro版增强型辅助驾驶系统套装",
                "WAO 奥迪高级智能数字套装",
                "PT1 奥迪智能数字套装"
        ));

        //志远耀黑型
        CcDataDto zhiyuanYaoheiDto = new CcDataDto("","","498B2Y-GPAHPAH","");
        zhiyuanYaoheiDto.setExterieurCodes(Arrays.asList(
                "2YA2 皓月白带黑车顶，金属漆",
                "B9A2 星河蓝带黑车顶，金属漆",
                "8CA2 赤霞朱带黑车顶，金属漆",
                "2T2T 旷夜黑，金属漆",
                "2LA2 和风灰带黑车顶，金属漆",
                "0DA2 珠光银带黑车顶，金属漆"
        ));
        zhiyuanYaoheiDto.setInterieurCodes(Arrays.asList(
                "FG 温情棕",
                "FF 悠远灰",
                "MP 静谧黑"
        ));
        zhiyuanYaoheiDto.setInterieurStyleCodes(Arrays.asList(
                "7TD 檀棕 天然桦木饰条",
                "7TL 岩灰 天然栓木饰条"
        ));
        zhiyuanYaoheiDto.setWheelCodes(Arrays.asList(
                "F15 20吋10幅 星芒轮毂",
                "42F 20吋5幅 星斗轮毂",
                "53D 21吋5幅 星钻式设计，RS竞速轮毂",
                "F13 20吋双5幅 星束轮毂"
        ));
        zhiyuanYaoheiDto.setOptionCodes(Arrays.asList(
                "KS1 wHUD平视显示系统",
                "9R1 夜视系统",
                "9VS Bang＆Olufsen 3D环绕音响系统",
                "9WP 后排娱乐系统",
                "8IZ 智能激光大灯",
                "9PF 自适应雨刮器"
        ));
        zhiyuanYaoheiDto.setOptionPackageCodes(Arrays.asList(
                "WA3 冬季舒适套装",
                "PT1 奥迪智能数字套装",
                "PGC Intelligent Electric Tailgate package 智能电动尾门套装",
                "PDW+WA6+2V3+GZ2+9PF 尊享行政座舱",
                "WAO Audi Advanced Smart Digital package 奥迪高级智能数字套装",
                "PS8 VIP Executive Seat package front seats 前排行政套装",
                "WAB+PS8 行政尊享全套装",
                "PS1 Sport Seat Business package 后排行政套装",
                "PCY 奥迪Pro版增强型辅助驾驶系统套装"
        ));

        //498B2Y-GPAHPAH-GPDWPDW-GPF3PF3-GPS6PS6-GWA6WA6-MEIH5MD-MRAD53D-MBBO6FJ-MHSW8IZ-MLSE9VS


//        CcDataDto data4Dto = new CcDataDto("f48a0f2e-124c-5364-9601-a47bc80e85b1", "3bf59329-04cf-5d92-bf85-6fb3bef3a4bf", "TYPE:498B2Y-MRADF15-GPAHPAH-GWCVWCV", "S-line境远型曜黑套装");
//        data4Dto.setExterieurCodes(Arrays.asList("2T2T 旷夜黑，金属漆", "2YA2 皓月白带黑车顶，金属漆", "B9A2 星河蓝带黑车顶，金属漆", "8CA2 赤霞朱带黑车顶，金属漆", "2LA2 和风灰带黑车顶，金属漆", "0DA2 珠光银带黑车顶，金属漆"));
//        data4Dto.setInterieurCodes(Arrays.asList("FG 温情棕", "舒云白 FH"));
//        data4Dto.setOptionCodes(Arrays.asList("WA3 冬季舒适套装", "PCY 奥迪Pro版增强型辅助驾驶系统套装",  "WAO 奥迪高级智能数字套装", "KS1 wHUD平视显示系统", "9R1 夜视系统"));
//        CcDataDto data5Dto = new CcDataDto("f48a0f2e-124c-5364-9601-a47bc80e85b1", "55473782-583e-5d95-a8fc-3d69acffc7e3", "TYPE:498B2Y-GPAHPAH-GPDWPDW-GPF3PF3-GPS6PS6-GWA6WA6-MEIH5MD-MRAD53D-MBBO6FJ-MHSW8IZ-MLSE9VS", "edition one");
//        data5Dto.setExterieurCodes(Arrays.asList("B9B9 星河蓝，金属漆", "3ZA2 青山黛带黑车顶"));
//        data5Dto.setInterieurCodes(Arrays.asList("RI  高定琥珀棕"));
//        data5Dto.setOptionCodes(Arrays.asList());
        dataDtoList.add(zhiyuanDto);
        List<List<String>> outputs = new ArrayList<>();
        dataDtoList.forEach(m-> {
            List<String> output = new ArrayList<>();
            output.add(m.getTypeCode());
            for (String exterieurCode : m.getExterieurCodes()) {
                List<String> copyOutput6 = new ArrayList<>(output);
                copyOutput6.add(extractCode(exterieurCode));
                for (String interieurCode : m.getInterieurCodes()) {
                    List<String> copyOutput5 = new ArrayList<>(copyOutput6);
                    copyOutput5.add(extractCode(interieurCode));
                    for (String interieurStyleCode : m.getInterieurStyleCodes()) {
                        List<String> copyOutput4 = new ArrayList<>(copyOutput5);
                        copyOutput4.add(extractCode(interieurStyleCode));
                        for (String wheelCode : m.getWheelCodes()) {
                            List<String> copyOutput3 = new ArrayList<>(copyOutput4);
                            copyOutput3.add(extractCode(wheelCode));
                            //从这里开始可以多选
                            List<String> optionCodesSwitch = switchCodes(extractCodes(m.getOptionCodes()));
                            List<String> optionPackageCodesSwitch = switchCodes(extractCodes(m.getOptionPackageCodes()));
                            for (String optionCode : optionCodesSwitch) {
                                List<String> copyOutput2 = new ArrayList<>(copyOutput3);
                                copyOutput2.add(extractCode(optionCode));
                                for (String optionPackageCode : optionPackageCodesSwitch) {
                                    List<String> copyOutput1 = new ArrayList<>(copyOutput2);//safe for immuatable String list
                                    copyOutput1.add(extractCode(optionPackageCode));
                                    outputs.add(copyOutput1);
                                }
                            }
                        }
                    }
                }
            }
        });
        checkRules(outputs);
        manualRules(outputs);
        writeFile(outputs);
    }

    private void writeFile(List<List<String>> outputs){
        try {
            File file = new File("d:\\tmp\\outputs.txt");

            // if file doesnt exists, then create it
            if (!file.exists()) {
                file.createNewFile();
            }
            FileWriter fw = new FileWriter(file.getAbsoluteFile());
            BufferedWriter bw = new BufferedWriter(fw);
            for (List<String> output : outputs) {
                bw.write(String.join(",", output));
                bw.write("\r\n");
            }
            bw.close();
        }catch (Exception e){

        }
    }

    private String extractCode(String code){
        String[] codeArray = code.split(" ");
        return codeArray[0];
    }

    private List<String> extractCodes(List<String> codes){
        List<String> ret = new ArrayList<>();
        for (String code : codes) {
            String[] codeArray = code.split(" ");
            ret.add(codeArray[0]);
        }
        return ret;
    }

    private void manualRules(List<List<String>> outputs){
        for (List<String> output : outputs) {
            String toCheck = String.join(",", output);
            if(toCheck.contains("498B2Y-GPAHPAH,") || toCheck.contains("498B2Y,")) {
                //System.out.println("Check Rule #4, #5");
                if (toCheck.contains("PS8") || toCheck.contains("WAB+PS8")) {//选装前排VIP行政商务套装
                    if (toCheck.contains("MP")) { //把MP换成FH
                        for (int i = 0; i < output.size(); i++) {
                            if (output.get(i).equals("MP")) {
                                output.set(i, "FH");
                            }
                        }
                    }
                    if (!toCheck.contains("WAB+PS8")) {
                        //rule5 只有单独选中PS8选装包时，需要额外补加一个装备4D8
                        output.add("4D8");
                    }
                }
            }

            if(toCheck.contains("498B2Y-GPAHPAH,") || toCheck.contains("498B2Y-MRADF15-GPAHPAH-GWCVWCV,")){
                //System.out.println("Check Rule #3");
                //rule3
                if(!toCheck.contains("2T2T,")){
                    output.add("6FJ");
                }
            }
        }
    }

    private boolean checkRules(List<List<String>> outputs){
        return outputs.removeIf(output -> !isValid(output));
    }

    private boolean isValid(List<String> output){
        String toCheck = String.join(",", output);
        //rule1 PT1和WAO只能二选一
        //System.out.println("check rule #1");
        if(toCheck.contains("PT1") && toCheck.contains("WAO")){
            return false;
        }

        //rule6 6NQ黑色车内顶棚与舒云白和悠远灰内饰色不可同时选择
        if(toCheck.contains("6NQ,") && (toCheck.contains("FF,") || toCheck.contains("FH,"))){
            return false;
        }

        if(toCheck.contains("498B2Y-GPAHPAH,") || toCheck.contains("498B2Y,")) {
            //System.out.println("Check Rule #2");
            //rule2 PS1、PS8和WAB+PS8只能三选一
            if (toCheck.contains("PS1") && toCheck.contains("PS8")) {
                return false;
            } else if (toCheck.contains("PS1") && toCheck.contains("WAB+PS8")) {
                return false;
            } else if (toCheck.contains("PS8") && toCheck.contains("WAB+PS8")) {
                return false;
            }

            //System.out.println("Check Rule #4");
            //rule4  选装前排VIP行政商务套装后的内饰配色：温情棕、舒云白
            if((toCheck.contains("PS8") || toCheck.contains("WAB+PS8")) && (toCheck.contains("FF")) ){//选装前排VIP行政商务套装
                // || toCheck.contains("MP")  把FF作为false，把MP换成舒云白
                return false;
            }
        }
        return true;
    }

    private List<String> switchCodes(List<String> codes){
        //放开选择的，C1,C2,C3,...,Cn
        int nCnt = codes.size();
        int nBit = (0xFFFFFFFF >>> (32 - nCnt));

        List<String> ret = new ArrayList<>();
        for (int i = 1; i <= nBit; i++) {
            List<String> iter = new ArrayList<>();
            for (int j = 0; j < nCnt; j++) {
                if ((i << (31 - j)) >> 31 == -1) {
                    iter.add(codes.get(j));
                }
            }
            ret.add(String.join(",", iter));
        }
        return ret;
    }
}
