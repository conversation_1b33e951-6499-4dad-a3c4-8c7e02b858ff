package com.csvw.audi.cc.common.utils.accb;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Signer {
    public static final String LINE_SEPARATOR = "\n";

    public static final String SDK_SIGNING_ALGORITHM = "SDK-HMAC-SHA256";

    public static final String X_SDK_DATE = "X-Sdk-Date";

    public static final String X_SDK_CONTENT_SHA256 = "x-sdk-content-sha256";

    public static final String AUTHORIZATION = "Authorization";

    public static final SimpleDateFormat TIME_FORMATTER = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");

    private static final Pattern AUTHORIZATION_PATTERN = Pattern.compile("SDK-HMAC-SHA256\\s+Access=([^,]+),\\s?SignedHeaders=([^,]+),\\s?Signature=(\\w+)");

    private static final String LINUX_NEW_LINE = "\n";

    public static final String HOST = "Host";

    static {
        TIME_FORMATTER.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    public void sign(Request request) {
        String singerDate = getHeader(request, "X-Sdk-Date");
        if (singerDate == null) {
            singerDate = TIME_FORMATTER.format(new Date());
            request.addHeader("X-Sdk-Date", singerDate);
        }
        addHostHeader(request);
        String contentSha256 = calculateContentHash(request);
        String[] signedHeaders = getSignedHeaders(request);
        String canonicalRequest = createCanonicalRequest(request, signedHeaders, contentSha256);
        String stringToSign = createStringToSign(canonicalRequest, singerDate);
        byte[] signingKey = deriveSigningKey(request.getSecrect());
        byte[] signature = computeSignature(stringToSign, signingKey);
        request.addHeader("Authorization",
                buildAuthorizationHeader(signedHeaders, signature, request.getKey()));
    }

    protected String getCanonicalizedResourcePath(String resourcePath) {
        if (resourcePath == null || resourcePath.isEmpty())
            return "/";
        try {
            resourcePath = (new URI(resourcePath)).getPath();
        } catch (URISyntaxException e) {
            return resourcePath;
        }
        String value = HttpUtils.urlEncode(resourcePath, true);
        if (!value.startsWith("/"))
            value = "/".concat(value);
        if (!value.endsWith("/"))
            value = value.concat("/");
        return value;
    }

    protected String getCanonicalizedQueryString(Map<String, List<String>> parameters) {
        SortedMap<String, List<String>> sorted = new TreeMap<>();
        for (Map.Entry<String, List<String>> entry : parameters.entrySet()) {
            String encodedParamName = HttpUtils.urlEncode(entry
                    .getKey(), false);
            List<String> paramValues = entry.getValue();
            List<String> encodedValues = new ArrayList<>(paramValues.size());
            for (String value : paramValues)
                encodedValues.add(HttpUtils.urlEncode(value, false));
            Collections.sort(encodedValues);
            sorted.put(encodedParamName, encodedValues);
        }
        StringBuilder result = new StringBuilder();
        for (Map.Entry<String, List<String>> entry : sorted.entrySet()) {
            for (String value : entry.getValue()) {
                if (result.length() > 0)
                    result.append("&");
                result.append(entry.getKey())
                        .append("=")
                        .append(value);
            }
        }
        return result.toString();
    }

    protected String createCanonicalRequest(Request request, String[] signedHeaders, String contentSha256) {
        StringBuilder canonicalRequestBuilder = new StringBuilder(request.getMethod().toString());
        canonicalRequestBuilder.append("\n")
                .append(getCanonicalizedResourcePath(request.getPath())).append("\n")
                .append(getCanonicalizedQueryString(request.getQueryStringParams())).append("\n")
                .append(getCanonicalizedHeaderString(request, signedHeaders)).append("\n")
                .append(getSignedHeadersString(signedHeaders)).append("\n").append(contentSha256);
        String canonicalRequest = canonicalRequestBuilder.toString();
        return canonicalRequest;
    }

    protected String createStringToSign(String canonicalRequest, String singerDate) {
        StringBuilder stringToSignBuilder = new StringBuilder("SDK-HMAC-SHA256");
        stringToSignBuilder.append("\n").append(singerDate)
                .append("\n").append(BinaryUtils.toHex(hash(canonicalRequest)));
        String stringToSign = stringToSignBuilder.toString();
        return stringToSign;
    }

    private final byte[] deriveSigningKey(String secret) {
        return secret.getBytes(StandardCharsets.UTF_8);
    }

    protected byte[] sign(byte[] data, byte[] key, SigningAlgorithm algorithm) {
        try {
            Mac mac = Mac.getInstance(algorithm.toString());
            mac.init(new SecretKeySpec(key, algorithm.toString()));
            return mac.doFinal(data);
        } catch (NoSuchAlgorithmException|java.security.InvalidKeyException e) {
            return null;
        }
    }

    protected final byte[] computeSignature(String stringToSign, byte[] signingKey) {
        return sign(stringToSign.getBytes(StandardCharsets.UTF_8), signingKey, SigningAlgorithm.HmacSHA256);
    }

    private String buildAuthorizationHeader(String[] signedHeaders, byte[] signature, String accessKey) {
        String credential = "Access=" + accessKey;
        String signerHeaders = "SignedHeaders=" + getSignedHeadersString(signedHeaders);
        String signatureHeader = "Signature=" + BinaryUtils.toHex(signature);
        StringBuilder authHeaderBuilder = new StringBuilder();
        authHeaderBuilder.append("SDK-HMAC-SHA256").append(" ").append(credential).append(", ")
                .append(signerHeaders).append(", ").append(signatureHeader);
        return authHeaderBuilder.toString();
    }

    protected String[] getSignedHeaders(Request request) {
        String[] signedHeaders = (String[])request.getHeaders().keySet().toArray((Object[])new String[0]);
        Arrays.sort(signedHeaders, String.CASE_INSENSITIVE_ORDER);
        return signedHeaders;
    }

    protected String getCanonicalizedHeaderString(Request request, String[] signedHeaders) {
        Map<String, String> requestHeaders = request.getHeaders();
        StringBuilder buffer = new StringBuilder();
        for (String header : signedHeaders) {
            String key = header.toLowerCase();
            String value = requestHeaders.get(header);
            buffer.append(key).append(":");
            if (value != null)
                buffer.append(value.trim());
            buffer.append("\n");
        }
        return buffer.toString();
    }

    protected String getSignedHeadersString(String[] signedHeaders) {
        StringBuilder buffer = new StringBuilder();
        for (String header : signedHeaders) {
            if (buffer.length() > 0)
                buffer.append(";");
            buffer.append(header.toLowerCase());
        }
        return buffer.toString();
    }

    protected void addHostHeader(Request request) {
        boolean haveHostHeader = false;
        for (String key : request.getHeaders().keySet()) {
            if ("Host".equalsIgnoreCase(key)) {
                haveHostHeader = true;
                break;
            }
        }
        if (!haveHostHeader)
            request.addHeader("Host", request.getHost());
    }

    protected String getHeader(Request request, String header) {
        if (header == null)
            return null;
        Map<String, String> headers = request.getHeaders();
        for (String key : headers.keySet()) {
            if (header.equalsIgnoreCase(key))
                return headers.get(key);
        }
        return null;
    }

    public boolean verify(Request request) {
        String singerDate = getHeader(request, "X-Sdk-Date");
        String authorization = getHeader(request, "Authorization");
        Matcher m = AUTHORIZATION_PATTERN.matcher(authorization);
        if (!m.find())
            return false;
        String[] signedHeaders = m.group(2).split(";");
        String contentSha256 = calculateContentHash(request);
        String canonicalRequest = createCanonicalRequest(request, signedHeaders, contentSha256);
        String stringToSign = createStringToSign(canonicalRequest, singerDate);
        byte[] signingKey = deriveSigningKey(request.getSecrect());
        byte[] signature = computeSignature(stringToSign, signingKey);
        String signatureResult = buildAuthorizationHeader(signedHeaders, signature, request.getKey());
        return signatureResult.equals(authorization);
    }

    protected String calculateContentHash(Request request) {
        String content_sha256 = getHeader(request, "x-sdk-content-sha256");
        if (content_sha256 != null)
            return content_sha256;
        return BinaryUtils.toHex(hash(request.getBody()));
    }

    public byte[] hash(String text) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update(text.getBytes(StandardCharsets.UTF_8));
            return md.digest();
        } catch (NoSuchAlgorithmException e) {
            return null;
        }
    }
}
