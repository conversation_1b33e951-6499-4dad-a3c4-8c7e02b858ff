package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 车辆ABC类配置选装
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarModelLineTypeOption对象", description="车辆ABC类配置选装")
public class CarModelLineTypeOption extends Model<CarModelLineTypeOption> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "车辆ABC类配置id")
    private Long modelLineTypeId;

    @ApiModelProperty(value = "选装编码")
    private String code;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
