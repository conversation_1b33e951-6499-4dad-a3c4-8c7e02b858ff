package com.csvw.audi.cc.controller.svcd;

import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.dto.SvcdAfterSalesDto;
import com.csvw.audi.cc.entity.vo.SvcdAfterSalesVo;
import com.csvw.audi.cc.service.ISvcdAfterSalesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "售后服务商")
@RestController
@RequestMapping("/api/v1/svcdAfterSales")
public class SvcdAfterSalesController extends BaseController {

    @Autowired
    private ISvcdAfterSalesService svcdAfterSalesService;

    @ApiOperation("获取售后服务商列表")
    @GetMapping("/getAfterSalesList")
    public AjaxMessage<List<SvcdAfterSalesVo>> getAfterSalesList(SvcdAfterSalesDto afterSalesDto) {
        List<SvcdAfterSalesVo> list =  svcdAfterSalesService.getAfterSalesList(afterSalesDto);
        return new AjaxMessage<>("00", "成功", list);
    }

    @ApiOperation("获取售后服务商列表(服务预约和取送车)")
    @GetMapping("/getAfterSalesListForReservation")
    public AjaxMessage<List<SvcdAfterSalesVo>> getAfterSalesListForReservation(@RequestHeader(value = "X-User-Id") String userId, SvcdAfterSalesDto afterSalesDto) {
//        if(afterSalesDto.getLongitude() == null) {
//            afterSalesDto.setLongitude(Double.parseDouble("0"));
//        }
//        if(afterSalesDto.getLatitude() == null) {
//            afterSalesDto.setLatitude(Double.parseDouble("0"));
//        }
        List<SvcdAfterSalesVo> list =  svcdAfterSalesService.getAfterSalesListForReservation(userId,afterSalesDto);
        return new AjaxMessage<>("00", "成功", list);
    }
}
