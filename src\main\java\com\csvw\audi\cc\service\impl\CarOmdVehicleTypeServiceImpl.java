package com.csvw.audi.cc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.csvw.audi.cc.entity.po.CarModelLineType;
import com.csvw.audi.cc.entity.po.CarOmdVehicleType;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.mapper.CarOmdVehicleTypeMapper;
import com.csvw.audi.cc.service.ICarModelLineTypeOptionService;
import com.csvw.audi.cc.service.ICarModelLineTypeService;
import com.csvw.audi.cc.service.ICarOmdVehicleTypeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * omd同步ABC类配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Service
public class CarOmdVehicleTypeServiceImpl extends ServiceImpl<CarOmdVehicleTypeMapper, CarOmdVehicleType> implements ICarOmdVehicleTypeService {

    @Autowired
    private OmdServiceImpl omdService;

    @Autowired
    private ICarModelLineTypeService modelLineTypeService;

    @Autowired
    private ICarOmdVehicleTypeService omdVehicleTypeService;

    @Autowired
    private RedissonClient redissonClient;

    private final String OMD_ABC_LOCK = "saic_audi:applock:audi_car_config:receive_omd_abc";

    @Override
    public void receiveOmdAbc() throws Exception {
        RLock lock = redissonClient.getLock(OMD_ABC_LOCK);
        try {
            lock.lock();
            LambdaQueryWrapper<CarModelLineType> tQ = new LambdaQueryWrapper<>();
            tQ.orderByDesc(CarModelLineType::getCreateTime).last("limit 1");
            CarModelLineType carModelLineType = modelLineTypeService.getOne(tQ);
            LambdaQueryWrapper<CarOmdVehicleType> vehicleTypeQ = new LambdaQueryWrapper<>();
            vehicleTypeQ.orderByDesc(CarOmdVehicleType::getCreateTime).last("limit 1");
            CarOmdVehicleType vehicleType = omdVehicleTypeService.getOne(vehicleTypeQ);
            Integer classifyVersion = vehicleType != null ? vehicleType.getClassifyVersion()+1: 0;
            LambdaUpdateWrapper<CarOmdVehicleType> vehicleUpdateQ = new LambdaUpdateWrapper<>();
            vehicleUpdateQ.set(CarOmdVehicleType::getDelFlag, 1);

            omdVehicleTypeService.update(vehicleUpdateQ);
            omdService.receiveOmdAbcData(i->{
                i.setCreateTime(LocalDateTime.now());
                i.setClassifyVersion(classifyVersion);
                omdVehicleTypeService.save(i);
            });
            modelLineTypeService.convertOmdAbcData();
            modelLineTypeService.updateModelLineTypeFlagByABC(classifyVersion);
            modelLineTypeService.exchangeOmdAbcData(carModelLineType==null?null:carModelLineType.getCreateTime());
        }finally {
            lock.unlock();
        }

    }
}
