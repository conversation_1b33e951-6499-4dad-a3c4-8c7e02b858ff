<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarModelLineMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarModelLine">
        <id column="id" property="id" />
        <result column="model_line_id" property="modelLineId" />
        <result column="model_line_name" property="modelLineName" />
        <result column="model_line_code" property="modelLineCode" />
        <result column="accb_type_code" property="accbTypeCode" />
        <result column="accb_type_id" property="accbTypeId" />
        <result column="model_id" property="modelId" />
        <result column="custom_series_id" property="customSeriesId" />
        <result column="image_url" property="imageUrl" />
        <result column="channel" property="channel" />
        <result column="version" property="version" />
        <result column="engine" property="engine" />
        <result column="front_status" property="front_status" />
        <result column="default_config" property="defaultConfig" />
        <result column="special_line" property="specialLine" />
        <result column="measure" property="measure" />
        <result column="weight" property="weight" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, model_line_id, model_line_name, model_line_code, accb_type_code, accb_type_id, model_id, custom_series_id, image_url, channel, version, default_config, special_line, measure, weight, create_time, update_time, del_flag
    </sql>

    <select id="listModelLine" resultType="com.csvw.audi.cc.entity.vo.ModelLineVo" parameterType="modelParamDto">
        select ml.id, ml.model_line_id, ml.model_line_code, ml.model_line_name, ml.accb_type_code,
            ml.accb_type_id, ml.image_url, ml.model_id, ml.custom_series_id, ml.version, ml.default_config, ml.special_line, ml.measure, ml.personal,
            m.model_code, m.model_year, ml.channel, ml.front_status, ml.engine, ml.suit, ml.suit_image_url, ml.type_flag, ml.classify_version, ml.weight,
            ml.price_add_color, ml.image_url1,ml.omd_model_unicode,ml.omd_model_status,
            ccs.custom_series_code, ccs.`custom_series_name`, m.from_omd
        from car_model_line ml left join car_custom_series ccs on ml.custom_series_id = ccs.custom_series_id and ml.channel = ccs.channel
        left join car_model m on m.model_id = ml.model_id and m.channel = ml.channel
        <where>
            <if test="customSeriesId != null and customSeriesId != ''">
                ml.custom_series_id = #{customSeriesId}
            </if>
            <if test="channel == null or channel == '' or channel == 'master'">
                and ml.channel = 'master'
            </if>
            <if test="channel != null and channel != '' and channel != 'master'">
                and ml.channel in (#{channel}, 'master')
            </if>
            <if test="modelLineId != null and modelLineId != ''">
                and ml.model_line_id = #{modelLineId}
            </if>
            <if test="accbTypeCode != null and accbTypeCode != ''">
                and ml.accb_type_code = #{accbTypeCode}
            </if>
            <if test="measure != null">
                and (ml.measure is null or ml.measure = #{measure})
            </if>
            <if test="delFlag != null">
                and ml.del_flag = #{delFlag}
            </if>
        </where>
        order by ccs.weight, ml.weight
    </select>

    <select id="listModelLineAstro" resultType="com.csvw.audi.cc.entity.vo.ModelLineBriefVo" parameterType="modelParamDto">
        select ml.id, ml.model_line_id, ml.model_line_code, ml.model_line_name, ml.accb_type_code,
        ml.accb_type_id, ml.image_url, ml.model_id, ml.custom_series_id, ml.version, ml.default_config,
        m.model_code, m.model_year, ml.channel, ccs.custom_series_code, ccs.`custom_series_name`
        from car_model_line ml left join car_custom_series ccs on ml.custom_series_id = ccs.custom_series_id and ml.channel = ccs.channel
        left join car_model m on m.model_id = ml.model_id and m.channel = ml.channel
        <where>
            <if test="customSeriesId != null and customSeriesId != ''">
                ml.custom_series_id = #{customSeriesId}
            </if>
            <if test="channel == null or channel == '' or channel == 'master'">
                and ml.channel = 'master'
            </if>
            <if test="channel != null and channel != '' and channel != 'master'">
                and ml.channel in (#{channel}, 'master')
            </if>
            <if test="modelLineId != null and modelLineId != ''">
                and ml.model_line_id = #{modelLineId}
            </if>
            <if test="accbTypeCode != null and accbTypeCode != ''">
                and ml.accb_type_code = #{accbTypeCode}
            </if>
            <if test="delFlag != null">
                and ml.del_flag = #{delFlag}
            </if>
        </where>
        order by  ccs.`weight`, ml.`weight`
    </select>


    <select id="listPriceCondition" resultType="com.csvw.audi.cc.entity.dto.PriceCondition">
        SELECT ml.accb_type_code, ml.version model_version, m.model_code, m.model_year
        FROM car_model_line ml left join car_model m on ml.model_id = m.model_id and ml.channel = m.channel
        where ml.channel = 'master' and ml.model_line_id = #{modelLineId}
    </select>

    <select id="listAccbPriceCondition" parameterType="com.csvw.audi.cc.entity.dto.TypePriceParam"
            resultType="com.csvw.audi.cc.entity.dto.PriceCondition">
        SELECT distinct ml.accb_type_code, ml.version model_version, m.model_code, m.model_year
        FROM car_model_line ml left join car_model m on ml.model_id = m.model_id and ml.channel = m.channel
        where ml.channel = 'master' and ml.accb_type_code=#{accbTypeCode} and ml.version=#{version} and m.model_year=#{modelYear}
    </select>

    <select id="listModelLineByOmdData" parameterType="com.csvw.audi.cc.entity.dto.OmdModelParam"
            resultType="com.csvw.audi.cc.entity.vo.ModelLineVo">
        select ml.id, ml.model_line_id, ml.model_line_code, ml.model_line_name, ml.accb_type_code,
            ml.accb_type_id, ml.image_url, ml.model_id, ml.custom_series_id, ml.version, ml.default_config, ml.special_line,
            m.model_code, m.model_year, ml.channel, ccs.custom_series_code, ccs.custom_series_name,
            ml.omd_model_unicode,ml.omd_model_status, m.from_omd
        from car_model_line ml left join car_custom_series ccs on ml.custom_series_id = ccs.custom_series_id and ml.channel = ccs.channel
        left join car_model m on m.model_id = ml.model_id and m.channel = ml.channel
        where ml.accb_type_code=#{accbTypeCode} and ml.version=#{version} and m.model_year=#{modelYear}
        and ml.channel = 'master'
    </select>

    <select id="listAdminModels" resultType="com.csvw.audi.cc.entity.vo.AdminModel">
        select replace(accb_type_code, "TYPE:", "") accb_type_code, model_line_name
        from car_model_line
        where channel = 'master'
        <if test="customSeriesId != null and customSeriesId != ''">
            and custom_series_id = #{customSeriesId}
        </if>
        group by accb_type_code order by weight
    </select>

    <select id="listBlips" resultType="String">
        select sell_blips from car_model_line_blips where model_line_id = #{modelLineId} order by weight
    </select>

</mapper>
