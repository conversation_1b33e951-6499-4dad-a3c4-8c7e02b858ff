package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.common.utils.CcUtils;
import com.csvw.audi.cc.entity.dto.omd.OmdModelDto;
import com.csvw.audi.cc.entity.po.CarOmdDataMap;
import com.csvw.audi.cc.mapper.CarOmdDataMapMapper;
import com.csvw.audi.cc.service.ICarOmdDataMapService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * OMD数据映射 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
@Service
@Slf4j
public class CarOmdDataMapServiceImpl extends ServiceImpl<CarOmdDataMapMapper, CarOmdDataMap> implements ICarOmdDataMapService {

    @Override
    public OmdModelDto getOmdDataFromMap(OmdModelDto omdModelDto, String api) {
        OmdModelDto mappedDto = null;
        List<CarOmdDataMap> mapList = lambdaQuery().isNull(CarOmdDataMap::getCcUniqueCode).list();
        if (CollectionUtils.isNotEmpty(mapList)){
            mapList.forEach(i-> lambdaUpdate().eq(CarOmdDataMap::getId, i.getId()).set(CarOmdDataMap::getOriginCcUniqueCode, CcUtils.getCcUniqueCode(
                    i.getOriginModelCode(), i.getOriginModelYear(), i.getOriginModelVersion(), i.getOriginColorCode(), i.getOriginInteriorCode(), i.getOriginPrList()
                    ))
                    .set(CarOmdDataMap::getCcUniqueCode, CcUtils.getCcUniqueCode(
                            i.getModelCode(), i.getModelYear(), i.getModelVersion(), i.getColorCode(), i.getInteriorCode(), i.getPrList()
                    )).update());

        }
        String ccUniqueCode = CcUtils.getCcUniqueCode(omdModelDto.getModelCode().substring(0,6), omdModelDto.getModelYear(), omdModelDto.getModelVersion(),
                omdModelDto.getColorCode(), omdModelDto.getInteriorCode(), omdModelDto.getPrList());
        CarOmdDataMap dataMap = lambdaQuery().eq(CarOmdDataMap::getOriginCcUniqueCode, ccUniqueCode).eq(CarOmdDataMap::getApi, api)
                .eq(CarOmdDataMap::getDelFlag, 0).one();
        if (dataMap != null){
            mappedDto = new OmdModelDto();
            BeanUtils.copyProperties(dataMap, mappedDto);
            mappedDto.setModelCode(dataMap.getAccbTypeCode());
        }
        log.debug("mapped omd Data {}", mappedDto);
        return mappedDto;
    }
}
