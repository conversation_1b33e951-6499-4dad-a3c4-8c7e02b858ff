<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarModelLineTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarModelLineType">
        <id column="id" property="id" />
        <result column="omd_vehicle_type_id" property="omdVehicleTypeId" />
        <result column="accb_type_code" property="accbTypeCode" />
        <result column="model_year" property="modelYear" />
        <result column="model_version" property="modelVersion" />
        <result column="exterieur_color_code" property="exterieurColorCode" />
        <result column="rad_code" property="radCode" />
        <result column="interieur_color_code" property="interieurColorCode" />
        <result column="sib_code" property="sibCode" />
        <result column="eih_code" property="eihCode" />
        <result column="seats" property="seats" />
        <result column="pr_list" property="prList" />
        <result column="pr_num" property="prNum" />
        <result column="classify" property="classify" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, omd_vehicle_type_id, accb_type_code, model_year, model_version, exterieur_color_code, rad_code, interieur_color_code, sib_code, eih_code, seats, pr_list, pr_num, classify, status, create_time, del_flag
    </sql>

    <select id="findBeforeCheckType" parameterType="modelLineTypeDto" resultType="string">
        <if test="prCodes == null">
            select classify from car_model_line_type where accb_type_code = #{accbTypeCode}
            and model_year = #{modelYear} and model_version = #{modelVersion}
            <if test="extCode != null">
                and exterieur_color_code = #{extCode}
            </if>
            <if test="radCode != null">
                and rad_code = #{radCode}
            </if>
            <if test="inCode != null">
                and interieur_color_code = #{inCode}
            </if>
            <if test="sibCode != null">
                and sib_code = #{sibCode}
            </if>
            <if test="eihCode != null">
                and eih_code = #{eihCode}
            </if>
            <if test="seat != null">
                and seats = #{seat}
            </if>
            and status = 1
            order by classify limit 1
        </if>
        <if test="prCodes != null">
            select classify from car_model_line_type t left join car_model_line_type_option o
            on t.id = o.model_line_type_id where t.accb_type_code = #{accbTypeCode}
            and t.model_year = #{modelYear} and t.model_version = #{modelVersion}
            <if test="extCode != null">
                and t.exterieur_color_code = #{extCode}
            </if>
            <if test="radCode != null">
                and t.rad_code = #{radCode}
            </if>
            <if test="inCode != null">
                and t.interieur_color_code = #{inCode}
            </if>
            <if test="sibCode != null">
                and t.sib_code = #{sibCode}
            </if>
            <if test="eihCode != null">
                and t.eih_code = #{eihCode}
            </if>
            <if test="seat != null">
                and t.seats = #{seat}
            </if>
            and o.code in
            <foreach collection="prCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and t.status = 1
            group by t.id having count(*) = #{prCodesNum}
            order by classify limit 1
        </if>
    </select>

    <select id="findType" parameterType="modelLineTypeDto" resultType="modelTypeResultDto">
        <if test="prCodes == null">
            select classify, omd_vehicle_type_id from car_model_line_type where accb_type_code = #{accbTypeCode}
            and model_year = #{modelYear} and model_version = #{modelVersion}
            <if test="extCode != null">
                and exterieur_color_code = #{extCode}
            </if>
            <if test="radCode != null">
                and rad_code = #{radCode}
            </if>
            <if test="inCode != null">
                and interieur_color_code = #{inCode}
            </if>
            <if test="sibCode != null">
                and sib_code = #{sibCode}
            </if>
            <if test="eihCode != null">
                and eih_code = #{eihCode}
            </if>
            <if test="seat != null">
                and seats = #{seat}
            </if>
            and pr_list is null
            and status = 1
            order by classify limit 1
        </if>
        <if test="prCodes != null">
            select classify, omd_vehicle_type_id from car_model_line_type t left join car_model_line_type_option o
            on t.id = o.model_line_type_id where t.accb_type_code = #{accbTypeCode}
            and t.model_year = #{modelYear} and t.model_version = #{modelVersion}
            <if test="extCode != null">
                and t.exterieur_color_code = #{extCode}
            </if>
            <if test="radCode != null">
                and t.rad_code = #{radCode}
            </if>
            <if test="inCode != null">
                and t.interieur_color_code = #{inCode}
            </if>
            <if test="sibCode != null">
                and t.sib_code = #{sibCode}
            </if>
            <if test="eihCode != null">
                and t.eih_code = #{eihCode}
            </if>
            <if test="seat != null">
                and t.seats = #{seat}
            </if>
            and o.code in
            <foreach collection="prCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and t.status = 1
            group by t.id, t.pr_num having count(*) = t.pr_num and count(*) = #{prCodesNum}
            order by classify limit 1
        </if>
    </select>


    <select id="queryTypePrs" resultType="typePrs" parameterType="typePrListQueryDto">
        select pr_list, seats from car_model_line_type
        where accb_type_code = #{accbTypeCode} and model_year = #{modelYear} and model_version = #{modelVersion} and status = 1
        and classify != 'C'
        <if test="seats != null">
            and seats = #{seats}
        </if>
        group by pr_list, seats
    </select>

    <select id="queryComposeTypeIdsOfA" resultType="String" parameterType="typePrListQueryDto">
        select id from car_model_line_type
        where accb_type_code = #{accbTypeCode} and model_year = #{modelYear}
        and model_version = #{modelVersion}
        <if test="prList != null" >
            and pr_list = #{prList}
        </if>
        <if test="prList == null">
            and pr_list is null
        </if>
        and classify = "A"
        and status = 1
    </select>

    <select id="queryTypeIdsOfA" parameterType="modelLineTypeDto" resultType="String">
        select id from car_model_line_type where accb_type_code = #{accbTypeCode}
        and model_year = #{modelYear} and model_version = #{modelVersion} and classify = "A"
        <if test="extCode != null">
            and exterieur_color_code = #{extCode}
        </if>
        <if test="radCode != null">
            and rad_code = #{radCode}
        </if>
        <if test="inCode != null">
            and interieur_color_code = #{inCode}
        </if>
        <if test="sibCode != null">
            and sib_code = #{sibCode}
        </if>
        <if test="eihCode != null">
            and eih_code = #{eihCode}
        </if>
        <if test="seat != null">
            and seats = #{seat}
        </if>
        <if test="extCodes != null">
            and exterieur_color_code in
            <foreach collection="extCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and status = 1
    </select>

    <select id="queryModelClassify" resultType="modelClassify">
        select concat("TYPE:", `accb_type_code`) `accb_type_code`  , `model_year` ,
        `model_version` , GROUP_CONCAT( distinct(`classify`)) modelClassify from `car_model_line_type`
        where status = 0
        GROUP BY `accb_type_code` , `model_year` , `model_version`
    </select>


    <select id="queryModelClassifyByParam" resultType="modelClassify" parameterType="modelLineTypeDto">
        select concat("TYPE:", `accb_type_code`) `accb_type_code`  , `model_year` ,
        `model_version` , GROUP_CONCAT( distinct(`classify`)) modelClassify from `car_model_line_type`
        where accb_type_code = #{accbTypeCode}
        and model_year = #{modelYear} and model_version = #{modelVersion} and status = 0 and
        (exterieur_color_code in
        <foreach collection="extCodes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        or exterieur_color_code is null)
        GROUP BY `accb_type_code` , `model_year` , `model_version`
    </select>

    <delete id="offlineTypeOption">
        DELETE FROM `car_model_line_type_option` WHERE `model_line_type_id` in (select id from `car_model_line_type` WHERE create_time &lt;= #{lastTime})
    </delete>

    <delete id="offlineType">
        delete from `car_model_line_type` WHERE create_time &lt;= #{lastTime}
    </delete>

    <delete id="flushErrorTypeOptionData">
        DELETE FROM `car_model_line_type_option` WHERE `model_line_type_id` in (select id from `car_model_line_type` WHERE `status` = 0)
    </delete>

    <delete id="flushErrorTypeData">
        delete from `car_model_line_type` WHERE `status` = 0
    </delete>

    <select id="listSeats" parameterType="modelLineTypeDto" resultType="String">
        select distinct seats from car_model_line_type where accb_type_code = #{accbTypeCode}
        and model_year = #{modelYear} and model_version = #{modelVersion} and classify = "A" and status = 1
        <if test="extCodes != null">
            and exterieur_color_code in
            <foreach collection="extCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

    </select>
</mapper>
