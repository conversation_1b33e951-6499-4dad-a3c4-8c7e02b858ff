package com.csvw.audi.cc.entity.dto.vwcc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

@Data
public class ColorDetail {
    @ApiModelProperty("颜色编码")
    private String colorCode;
    private String colorDesc;
    private String colorFlag;
    @ApiModelProperty("颜色名字")
    private String colorNameCn;
    private String colorNameEn;
    private String colorPainting;
    private String colorWeight;
    private String customColorCode;
    private String customColorId;
    private String customModelId;
    @ApiModelProperty("图片地址")
    private String imageUrl;
    private Object price;
    @ApiModelProperty("优惠金额")
    private Object discount;
}
