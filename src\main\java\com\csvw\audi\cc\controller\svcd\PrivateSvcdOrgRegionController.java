package com.csvw.audi.cc.controller.svcd;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.po.SvcdChannelOrganization;
import com.csvw.audi.cc.entity.po.SvcdOrgRegion;
import com.csvw.audi.cc.service.ISvcdChannelOrganizationService;
import com.csvw.audi.cc.service.ISvcdOrgRegionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Api(tags = "组织区划信息-feign")
@RestController
@RequestMapping("/private/api/v1/orgRegion")
public class PrivateSvcdOrgRegionController extends BaseController {

    @Autowired
    private ISvcdOrgRegionService svcdOrgRegionService;

    @Autowired
    private ISvcdChannelOrganizationService svcdChannelOrganizationService;

    @ApiOperation("获取渠道商的组织区划信息")
    @PostMapping("/getOrgRegionListByDealerCode")
    public AjaxMessage<List<SvcdOrgRegion>> getOrgRegionListByDealerCode(@RequestBody Set<String> dealerCodes) {
        QueryWrapper<SvcdChannelOrganization> orgQuery = new QueryWrapper();
        orgQuery.in("dealer_code",new ArrayList(dealerCodes));
        List<SvcdChannelOrganization> orgList =  svcdChannelOrganizationService.list(orgQuery);
        Set<String> regionCodes = orgList.stream().map(SvcdChannelOrganization::getRegionCode).collect(Collectors.toSet());

        QueryWrapper<SvcdOrgRegion> queryWrapper = new QueryWrapper();
        queryWrapper.in("region_code",new ArrayList(regionCodes));
        List<SvcdOrgRegion> list =  svcdOrgRegionService.list(queryWrapper);
        return new AjaxMessage<>("00", "成功", list);
    }

}
