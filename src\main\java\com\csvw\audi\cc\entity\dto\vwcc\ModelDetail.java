package com.csvw.audi.cc.entity.dto.vwcc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ModelDetail {
    @ApiModelProperty("自定义车型代码")
    private String customModelCode;
    private String customModelId;
    private String defaultInteriorColor;
    private String displacement;
    private String emissionLevel;
    private String energyType;
    private String engineComment;
    private String engineDesc;
    private String engineId;
    private String engineName;
    @ApiModelProperty("车型代码")
    private String modelCode;
    @ApiModelProperty("车型名称")
    private String modelNameCn;
    private String modelNameEn;
    private List<ParamDetail> modelParamList;
    private Object modelPrice;
    private String modelYear;
    private String modellineDesc;
    @ApiModelProperty("配置线id")
    private String modellineId;
    private String modellineName;
    @ApiModelProperty("车型版本")
    private String omdModelVersion;
    private String promotionActivities;
    private String seriesCode;
    private String imageUrl;
    @ApiModelProperty("车头图")
    private String headImageUrl;
    @ApiModelProperty("预计交付日期")
    private String deliveryTime;
    @ApiModelProperty("配置线代码")
    private String modelLineCode;
}
