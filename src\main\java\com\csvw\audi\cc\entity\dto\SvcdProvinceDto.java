package com.csvw.audi.cc.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 省份信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Data
public class SvcdProvinceDto {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "省份名称")
    private String name;

    @ApiModelProperty(value = "省份代码")
    private String provinceCode;

    @ApiModelProperty(value = "是否显示全部数据（1:是, 2:存在渠道商的, 3:存在代理商的, 4:存在服务商的）")
    private Integer searchAll;

    @ApiModelProperty(value = "检索类型（0:全部 1:服务商 2:代理商）,此参数仅供官网渠道商列表使用")
    private Integer searchType;

}
