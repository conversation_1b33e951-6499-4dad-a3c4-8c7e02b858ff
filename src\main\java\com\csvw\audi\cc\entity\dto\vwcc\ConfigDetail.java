package com.csvw.audi.cc.entity.dto.vwcc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ConfigDetail {

    private List<Option> attachmentList;
    @ApiModelProperty("车型")
    private ModelDetail carModel;
    @ApiModelProperty("车系")
    private SeriesDetail carSeries;
    private List<GroupDetail> groups;
    @ApiModelProperty("内饰颜色")
    private ColorDetail insideColor;
    private ModellineDetail modellineDetail;
    @ApiModelProperty("选装")
    private List<Option> optionList;
    @ApiModelProperty("外饰颜色")
    private ColorDetail outsideColor;
    @ApiModelProperty("总价")
    private Object totalPrice;
    @ApiModelProperty("优惠金额")
    private Object discount;
}
