package com.csvw.audi.cc.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class LoanVo {
    @ApiModelProperty(value = "贷款金额")
    private BigDecimal price;
    @ApiModelProperty(value = "首付")
    private BigDecimal payment;
    @ApiModelProperty(value = "首付比例")
    private BigDecimal paymentRatio;
    @ApiModelProperty(value = "贷款比例")
    private BigDecimal loanRatio;
    @ApiModelProperty(value = "贷款金额")
    private BigDecimal loan;
    @ApiModelProperty(value = "贷款月期")
    private String month;
    @ApiModelProperty(value = "贷款利率")
    private String interestRate;
    @ApiModelProperty(value = "月供")
    private BigDecimal monthPay;
}
