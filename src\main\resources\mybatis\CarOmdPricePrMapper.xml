<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarOmdPricePrMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarOmdPricePr">
        <id column="id" property="id" />
        <result column="accb_type_code" property="accbTypeCode" />
        <result column="price_version" property="priceVersion" />
        <result column="model_code" property="modelCode" />
        <result column="model_version" property="modelVersion" />
        <result column="price" property="price" />
        <result column="price_type" property="priceType" />
        <result column="discount_amount" property="discountAmount" />
        <result column="pr_code" property="prCode" />
        <result column="expire_date" property="expireDate" />
        <result column="model_year" property="modelYear" />
        <result column="effective_date" property="effectiveDate" />
        <result column="brand_code" property="brandCode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, accb_type_code, price_version, model_code, model_version, price, price_type, discount_amount, pr_code, expire_date, model_year, effective_date, brand_code, create_time, update_time, del_flag
    </sql>

    <select id="getPrice" parameterType="com.csvw.audi.cc.entity.dto.PriceCondition" resultType="java.math.BigDecimal">
        select price from car_omd_price_pr
        where accb_type_code = #{accbTypeCode} and model_code = #{modelCode}
        and model_year = #{modelYear} and model_version = #{modelVersion}
        and price_type = #{priceType} and brand_code = #{brand} and pr_code = #{optionCode}
        and current_date between effective_date and ifnull(expire_date, current_date);
    </select>

</mapper>
