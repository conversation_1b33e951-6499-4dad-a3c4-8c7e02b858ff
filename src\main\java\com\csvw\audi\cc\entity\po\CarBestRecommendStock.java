package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * OMD推荐车库存表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarBestRecommendStock对象", description="OMD推荐车库存表")
public class CarBestRecommendStock extends Model<CarBestRecommendStock> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "推荐车库存记录id")
      @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    private Long bestRecommendId;

    @ApiModelProperty(value = "omd推荐车id")
    private Long recommendModelId;

    @ApiModelProperty(value = "经销商网络代码")
    private String dealerNetCode;

    @ApiModelProperty(value = "同步的库存数")
    private Long syncStockNum;

    @ApiModelProperty(value = "库存量")
    private Long stockNum;

    @ApiModelProperty(value = "类型（总部推荐车:hq, 经销商推荐车:dealer)")
    private String type;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
