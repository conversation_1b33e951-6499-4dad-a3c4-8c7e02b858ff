package com.csvw.audi.cc.exception;

import lombok.Data;

@Data
public class ServiceException extends Exception {

    private Integer status;

    private String code;

    private Object data;

    private String errors;

    private String errorLog;

    public ServiceException(String code, String errors, Object data) {
        super();
        this.code = code;
        this.data = data;
        this.errors = errors;
    }

    public ServiceException(String code, String message) {
        super(message);
        this.code = code;
    }

    public ServiceException(Integer status, String code, String message) {
        super(message);
        this.status = status;
        this.code = code;
    }

    /**
     *
     * @param code 异常code
     * @param message 异常消息
     * @param errorLog 日常日志
     */
    public ServiceException(String code, String message, String errorLog) {
        super(message);
        this.code = code;
        this.errorLog = errorLog;
    }

    public ServiceException(String message, String code, String errors, Object data) {
        super(message);
        this.code = code;
        this.data = data;
        this.errors = errors;
    }

    public ServiceException(String message, String code, String errors, Object data, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.data = data;
        this.errors = errors;
    }

}
