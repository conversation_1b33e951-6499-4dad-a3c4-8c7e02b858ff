<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarStockLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarStockLog">
        <id column="id" property="id" />
        <result column="best_recommend_id" property="bestRecommendId" />
        <result column="ccid" property="ccid" />
        <result column="operate" property="operate" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, best_recommend_id, ccid, operate, create_time, update_time, del_flag
    </sql>
    
    <select id="findLogToSync" resultType="com.csvw.audi.cc.entity.po.CarStockLog">
        SELECT sl.* FROM `car_stock_log` sl left join car_best_recommend_custom_sync rcs on sl.`ccid` = rcs.ccid
        where sl.`recommend_model_id` = #{recommendModelId} and sl.`type` = #{type} and sl.operate=1 and rcs.ccid is null;
    </select>

</mapper>
