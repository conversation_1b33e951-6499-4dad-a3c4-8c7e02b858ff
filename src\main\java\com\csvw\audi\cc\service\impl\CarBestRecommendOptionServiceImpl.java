package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.entity.po.CarBestRecommendOption;
import com.csvw.audi.cc.mapper.CarBestRecommendOptionMapper;
import com.csvw.audi.cc.service.ICarBestRecommendOptionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 畅销推荐车配置项 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-08
 */
@Service
public class CarBestRecommendOptionServiceImpl extends ServiceImpl<CarBestRecommendOptionMapper, CarBestRecommendOption> implements ICarBestRecommendOptionService {

    @Autowired
    CarBestRecommendOptionMapper optionMapper;

    @Override
    public List<String> listRecommendOptionIds(Long bestRecommendId) {
        return optionMapper.listRecommendOptionIds(bestRecommendId);
    }
}
