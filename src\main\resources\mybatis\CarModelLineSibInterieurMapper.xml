<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarModelLineSibInterieurMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarModelLineSibInterieur">
        <id column="id" property="id" />
        <result column="sib_interieur_id" property="sibInterieurId" />
        <result column="weight" property="weight" />
        <result column="model_line_id" property="modelLineId" />
        <result column="default_config" property="defaultConfig" />
        <result column="status" property="status" />
        <result column="channel" property="channel" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sib_interieur_id, weight, model_line_id, default_config, status, channel, create_time, update_time, del_flag
    </sql>
    
    <select id="listModelLineSibInterieur"
            parameterType="com.csvw.audi.cc.entity.vo.ModelLineOptionVo"
            resultType="com.csvw.audi.cc.entity.vo.ModelLineSibInterieurVo">
        select si.sib_interieur_id, si.sib_interieur_code, si.sib_interieur_category, si.sib_option_code, si.sib_option_category, si.sib_option_id, si.sib_name,
            si.interieur_name, si.interieur_option_code,  si.interieur_option_category, si.interieur_option_id, si.image_url, si.image_url_detail, si.image_url_list,
            si.channel,  IFNULl(mlsi.default_config, si.default_config), ifnull(mlsi.description, si.description) description, mlsi.status, mlsi.default_config
        from car_sib_interieur si
        left join car_model_line_sib_interieur mlsi on si.sib_interieur_id = mlsi.sib_interieur_id
        <where>
            <if test="channel == null or channel == '' or channel == 'master'">
                and si.channel = 'master'
            </if>
            <if test="channel != null and channel != '' and channel != 'master'">
                and si.channel in (#{channel}, 'master')
            </if>
            <if test="modelLineId != null and modelLineId != ''">
                and mlsi.model_line_id = #{modelLineId}
            </if>
            <if test="sibInterieurId != null and sibInterieurId != ''">
                and si.sib_interieur_id = #{sibInterieurId}
            </if>
            <if test="sibOptionCode != null and sibOptionCode != ''">
                and si.sib_option_code = #{sibOptionCode}
            </if>
            <if test="interieurOptionCode != null and interieurOptionCode != ''">
                and si.interieur_option_code = #{interieurOptionCode}
            </if>
            <if test="status != null">
                and mlsi.status = #{status}
            </if>
            <if test="delFlag != null">
                and si.del_flag = #{delFlag}
            </if>
        </where>
        order by mlsi.weight;
    </select>

    <select id="queryModelLineSibInterieur" parameterType="com.csvw.audi.cc.entity.dto.SibInterieurQueryDto"
            resultType="com.csvw.audi.cc.entity.vo.ModelLineSibInterieurVo">
        select si.sib_interieur_id, si.sib_interieur_code, si.sib_interieur_category, si.sib_option_code, si.sib_option_category, si.sib_option_id, si.sib_name,
        si.interieur_name, si.interieur_option_code,  si.interieur_option_category, si.interieur_option_id, si.image_url, si.image_url_detail, si.image_url_list,
        si.channel,  IFNULl(mlsi.default_config, si.default_config), ifnull(mlsi.description, si.description) description, mlsi.status, mlsi.default_config
        from car_sib_interieur si
        left join car_model_line_sib_interieur mlsi on si.sib_interieur_id = mlsi.sib_interieur_id
        <where>
            <if test="channel == null or channel == '' or channel == 'master'">
                and si.channel = 'master'
            </if>
            <if test="channel != null and channel != '' and channel != 'master'">
                and si.channel in (#{channel}, 'master')
            </if>
            <if test="modelLineId != null and modelLineId != ''">
                and mlsi.model_line_id = #{modelLineId}
            </if>
            <if test="sibInterieurId != null and sibInterieurId != ''">
                and si.sib_interieur_id = #{sibInterieurId}
            </if>
            <if test="sibInterieurIds != null ">
                and si.sib_interieur_id in
                <foreach collection="sibInterieurIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="sibOptionCode != null and sibOptionCode != ''">
                and si.sib_option_code = #{sibOptionCode}
            </if>
            <if test="interieurOptionCode != null and interieurOptionCode != ''">
                and si.interieur_option_code = #{interieurOptionCode}
            </if>
            <if test="status != null">
                and mlsi.status = #{status}
            </if>
            <if test="delFlag != null">
                and si.del_flag = #{delFlag}
            </if>
        </where>
        order by mlsi.weight;
    </select>

</mapper>
