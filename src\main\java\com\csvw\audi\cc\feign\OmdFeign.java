package com.csvw.audi.cc.feign;

import com.csvw.audi.cc.component.InfraInterceptorConfig;
import com.csvw.audi.cc.entity.dto.omd.*;
import com.csvw.audi.cc.entity.po.CarMeasureMadeOriginConfig;
import com.csvw.audi.cc.entity.po.CarOmdVehicleType;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "omdFeign", url = "${audi-car-config.infra.url}", configuration = InfraInterceptorConfig.class)
public interface OmdFeign {

    @PostMapping(value = "/omd/stdPrice/getCurrentTypePrice", consumes = MediaType.APPLICATION_JSON_VALUE)
    OmdRes<TypePriceRes> getCurrentTypePrice(@RequestHeader("X-App-Id")String appId, @RequestHeader("X-Sequence-No")String sequenceNo, @RequestHeader("X-Timestamp")String timestamp, @RequestHeader("X-Signature")String sign, @RequestBody String json);

    @PostMapping(value = "/omd/stdPrice/getCurrentTypeColorPrice", consumes = MediaType.APPLICATION_JSON_VALUE)
    OmdRes<ColorPriceRes> getCurrentTypeColorPrice(@RequestHeader("X-App-Id")String appId, @RequestHeader("X-Sequence-No")String sequenceNo, @RequestHeader("X-Timestamp")String timestamp, @RequestHeader("X-Signature")String sign, @RequestBody String json);

    @PostMapping(value = "/omd/stdPrice/getCurrentTypePrPrice", consumes = MediaType.APPLICATION_JSON_VALUE)
    OmdRes<PrPriceRes> getCurrentTypePrPrice(@RequestHeader("X-App-Id")String appId, @RequestHeader("X-Sequence-No")String sequenceNo, @RequestHeader("X-Timestamp")String timestamp, @RequestHeader("X-Signature")String sign, @RequestBody String json);

    @PostMapping(value = "/omd/stdPrice/getCurrentTypePrpkgPrice", consumes = MediaType.APPLICATION_JSON_VALUE)
    OmdRes<PrPkgPriceRes> getCurrentTypePrpkgPrice(@RequestHeader("X-App-Id")String appId, @RequestHeader("X-Sequence-No")String sequenceNo, @RequestHeader("X-Timestamp")String timestamp, @RequestHeader("X-Signature")String sign, @RequestBody String json);

    @PostMapping(value = "/omd/stdPrice/getCurrentTypePrice", consumes = MediaType.APPLICATION_JSON_VALUE)
    Response getCurrentTypePriceTest(@RequestHeader("X-App-Id")String appId, @RequestHeader("X-Sequence-No")String sequenceNo, @RequestHeader("X-Timestamp")String timestamp, @RequestHeader("X-Signature")String sign, @RequestBody String json);

    /**
     * 畅销推荐车
     * @param omdParam
     * @return
     */
    @PostMapping(value = "/omd/recommendModel/bestRecommendModelQuery")
    OmdRes<BestRecommendRes> bestRecommendModelQuery(@RequestBody OmdParam<BaseRequestBody> omdParam);

    /**
     * 畅销车库存查询
     * @param param
     * @return
     */
    @PostMapping(value = "/omd/vehicle/queryAudiHqVehicle")
    OmdNormalRes<HqQueryResult> queryAudiHqVehicle(@RequestBody OmdParam<HqVehicleBody> param);

    /**
     * 库存推荐车
     */
    @PostMapping(value = "/omd/recommendModel/stockRecommendModelQuery")
    OmdRes<StockRecommendRes> stockRecommendModelQuery(@RequestBody OmdParam<StockRecommendBody> param);

    /**
     * 库存车库存查询
     * @param param
     * @return
     */
    @PostMapping(value = "/omd/vehicle/queryAudiDealerVehicle")
    OmdNormalRes<HqQueryResult> queryAudiDealerVehicle(@RequestBody OmdParam<DealerVehicleBody> param);

    /**
     * 交付时间查询
     * @param param
     * @return
     */
    @PostMapping(value = "/omd/estDelivery/estimateDeliveryQuery")
    OmdObjectRes<EstimateDeliveryRes> estimateDeliveryQuery(@RequestBody OmdParam<EstimateDeliveryBody> param);

    /**
     * 半订制同步
     * @param omdParam
     * @return
     */
    @PostMapping(value = "/omd/semiCustomizationModel/query")
    OmdRes<CarMeasureMadeOriginConfig> semiCustomizationModel(@RequestBody OmdParam<BaseRequestBody> omdParam);

    /**
     * 车辆模糊查询同步
     * @param omdParam
     * @return
     */
    @PostMapping(value = "/omd/vehicle/vehicleFuzzyQuery")
    OmdRes<ModelQueryRes> modelQuery(@RequestBody OmdParam<ModelQueryBody> omdParam);

    /**
     * ABC类清单同步
     * @param omdParam
     * @return
     */
    @PostMapping(value = "/omd/modelgroup/accbAbcModelGroupQuery")
    OmdRes<CarOmdVehicleType> abcClassisfModelDetailQuery(OmdParam<BaseRequestBody> omdParam);
}
