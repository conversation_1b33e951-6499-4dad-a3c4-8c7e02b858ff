package com.csvw.audi.cc.component.kafkaService.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.csvw.audi.cc.component.kafkaService.SvcdKafkaService;
import com.csvw.audi.cc.entity.po.SvcdCity;
import com.csvw.audi.cc.mapper.SvcdCityMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service("svcdCityKafkaService")
public class SvcdCityKafkaServiceImpl implements SvcdKafkaService {

    @Autowired
    private SvcdCityMapper svcdCityMapper;

    @Override
    public void insertData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdCity city = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdCity.class);
        city.setCreatedAt(nowDate);
        city.setUpdatedAt(nowDate);
        city.insert();
    }

    @Override
    public void updateData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdCity city = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdCity.class);
        Long cityCode = city.getCityCode();

        QueryWrapper<SvcdCity> cityQusery = new QueryWrapper<>();
        cityQusery.eq("city_code",cityCode);
        List<SvcdCity> list = city.selectList(cityQusery);
        if(list == null || list.size() == 0) {
            city.setCreatedAt(nowDate);
            city.setUpdatedAt(nowDate);
            city.insert();
        } else {
            city.setUpdatedAt(nowDate);

            UpdateWrapper<SvcdCity> cityUpdateWrapper = new UpdateWrapper<>();
            cityUpdateWrapper.eq("city_code",cityCode);
            city.setCityCode(null);//主键置空，防止数据库更新报错
            svcdCityMapper.update(city,cityUpdateWrapper);
        }
    }
}
