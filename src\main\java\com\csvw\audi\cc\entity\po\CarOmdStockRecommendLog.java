package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * OMD经销商库存车日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarOmdStockRecommendLog对象", description="OMD经销商库存车日志表")
public class CarOmdStockRecommendLog extends Model<CarOmdStockRecommendLog> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "omd库存车记录id")
    private Long stockRecommendModelId;

    @ApiModelProperty(value = "车型编码")
    private String modelCode;

    @ApiModelProperty(value = "车型版本号")
    private String modelVersion;

    @ApiModelProperty(value = "车型年款")
    private String modelYear;

    @ApiModelProperty(value = "内饰code")
    private String interiorCode;

    @ApiModelProperty(value = "外饰code")
    private String colorCode;

    @ApiModelProperty(value = "pr列表，逗号分隔")
    private String prList;

    @ApiModelProperty(value = "产品编码")
    private String classCode;

    @ApiModelProperty(value = "缺省标志：1-缺省，0-非缺省，暂时不用")
    private String defaultFlag;

    @ApiModelProperty(value = "经销商网络代码")
    private String dealerNetCode;

    @ApiModelProperty(value = "记录行号")
    private Long rowIndex;

    @ApiModelProperty(value = "操作类型:A-新增,M-修改,D-删除")
    private String operateType;

    @ApiModelProperty(value = "操作时间: YYYY-MM-DD hh:mm:ss")
    private LocalDateTime operateTime;

    @ApiModelProperty(value = "转换标识，0:未处理，1:处理成功，2:异常")
    private Integer convertRecommendCar;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.stockRecommendModelId;
    }

}
