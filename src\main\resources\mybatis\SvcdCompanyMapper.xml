<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.SvcdCompanyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.SvcdCompany">
        <id column="company_id" property="companyId" />
        <result column="name" property="name" />
        <result column="company_code" property="companyCode" />
        <result column="start_time" property="startTime" />
        <result column="registered_capital" property="registeredCapital" />
        <result column="address" property="address" />
        <result column="chairman" property="chairman" />
        <result column="chairman_phone" property="chairmanPhone" />
        <result column="legal_representative" property="legalRepresentative" />
        <result column="legal_representative_phone" property="legalRepresentativePhone" />
        <result column="contact_person" property="contactPerson" />
        <result column="contact_person_mobile" property="contactPersonMobile" />
        <result column="contact_person_email" property="contactPersonEmail" />
        <result column="company_nature" property="companyNature" />
        <result column="related_industry" property="relatedIndustry" />
        <result column="business_license_url" property="businessLicenseUrl" />
        <result column="promise" property="promise" />
        <result column="stock_right_pic_url" property="stockRightPicUrl" />
        <result column="registration_report_url" property="registrationReportUrl" />
        <result column="company_regulation_url" property="companyRegulationUrl" />
        <result column="other_support_doc_url" property="otherSupportDocUrl" />
        <result column="finance_year" property="financeYear" />
        <result column="finance_roe" property="financeRoe" />
        <result column="finance_margin_rate" property="financeMarginRate" />
        <result column="finance_pretax_margin" property="financePretaxMargin" />
        <result column="finance_debt" property="financeDebt" />
        <result column="audi_start_year" property="audiStartYear" />
        <result column="audi_ebt_ratio" property="audiEbtRatio" />
        <result column="bank_credit" property="bankCredit" />
        <result column="sap_code" property="sapCode" />
        <result column="tax_no" property="taxNo" />
        <result column="tax_tel" property="taxTel" />
        <result column="bank_name" property="bankName" />
        <result column="bank_account_number" property="bankAccountNumber" />
        <result column="deleted" property="deleted" />
        <result column="investor_code" property="investorCode" />
        <result column="spare1" property="spare1" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        company_id, name, company_code, start_time, registered_capital, address, chairman, chairman_phone, legal_representative, legal_representative_phone, contact_person, contact_person_mobile, contact_person_email, company_nature, related_industry, business_license_url, promise, stock_right_pic_url, registration_report_url, company_regulation_url, other_support_doc_url, finance_year, finance_roe, finance_margin_rate, finance_pretax_margin, finance_debt, audi_start_year, audi_ebt_ratio, bank_credit, sap_code, tax_no, tax_tel, bank_name, bank_account_number, deleted, investor_code, spare1
    </sql>

</mapper>
