package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.dto.CarCustomQueryDto;
import com.csvw.audi.cc.entity.po.CarCustom;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csvw.audi.cc.entity.vo.CarCustomDetailVo;
import com.csvw.audi.cc.entity.vo.CarCustomVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
public interface CarCustomMapper extends BaseMapper<CarCustom> {

    public CarCustomVo a7OrQ5Byccid(@Param("ccid")String ccid);

    List<CarCustomDetailVo> listCustomDetail(CarCustomQueryDto queryDto);
}
