package com.csvw.audi.cc.entity.vo;

import com.csvw.audi.cc.entity.dto.OptionRelDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
public class ModelLineOptionVo implements Serializable {

    @ApiModelProperty(value = "配置项id")
    private String optionId;

    @ApiModelProperty(value = "配置项名称")
    private String optionName;

    @ApiModelProperty(value = "配置编码")
    private String optionCode;

    @ApiModelProperty(value = "配置种类编码")
    private String category;

    @ApiModelProperty(value = "配置项类型")
    private String optionType;

    @ApiModelProperty(value = "配置项类型")
    private String optionTypeName;

    @ApiModelProperty(value = "装备图片")
    private String imageUrl;

    @ApiModelProperty(value = "装备详情图片")
    private String imageUrlDetail;

    @ApiModelProperty(value = "装备列表图片")
    private String imageUrlList;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "默认配置项")
    private Integer defaultConfig;

    @ApiModelProperty(value = "配置线id")
    @JsonIgnore
    private String modelLineId;

    @ApiModelProperty(value = "配置线配置项状态（0:无，1:标准装备，2:可选装备）")
    private Integer status;

    @ApiModelProperty(value = "配置线配置项标准状态（0:无，1:标准装备，2:可选装备）")
    private Integer condition;

    @JsonIgnore
    private String channel;

    @JsonIgnore
    private String mloChannel;

    @JsonIgnore
    private Integer delFlag;

    @JsonIgnore
    private String packageId;

    @JsonIgnore
    private String hasCode;

    @ApiModelProperty(value = "价格")
    private Object price;

    private Integer detailPageHidden;

    private Set<String> typeIdsOfA = new HashSet<>();

    @ApiModelProperty(value = "配置项详情")
    private List<CarOptionDetailVo> optionDetailList;

    @ApiModelProperty(value = "选装包PR列表")
    private List<ModelLineOptionVo> packetItems;

    @ApiModelProperty
    private List<OptionRelDto> optionRelates;

    @ApiModelProperty("标签")
    private List<CarTagVo> tags;

    private String equipmentRights;

}
