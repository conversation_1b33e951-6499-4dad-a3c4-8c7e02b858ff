package com.csvw.audi.cc.feign;


import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.entity.vo.ReviceTaskVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "audi-async-task-adapter")
public interface AudiAsyncTaskAdapterService {

    @PostMapping("/api/messageCenter/reviceTask")
    AjaxMessage reviceTask(@RequestBody ReviceTaskVo reviceTask);
}
