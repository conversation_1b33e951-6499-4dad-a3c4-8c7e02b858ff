spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      filters: stat,config
      initial-size: '8'
      max-active: '10'
      min-idle: '5'
      query-timeout: '6000'
      remove-abandoned-timeout: '1800'
      transaction-query-timeout: '6000'
#      url: ********************************************************************************************************************************************************
#      username: audi_pre
#      password: hj6Q_I5v
#      url: ********************************************************************************************************************************************************
#      username: audi_dev
#      password: p7e_dN3W
      type: com.alibaba.druid.pool.DruidDataSource
      url: ****************************************************************************************************************
      username: root
      password: 1234qwer
  redis:
#    password: fontre
#    database: 5
#    port: 2017
#    host: **************
    host: localhost
    timeout: 50s
    lettuce:
      pool:
        max-active: 2
  kafka:
    initAutoStart: false
    bootstrap-servers: **************:9092
    groupId: audi-kmsp-dealer-sync-action-group-2
    setAutoStartup: false
    org-topic: AUDI-SVCD-OPERATION-ORG-TOPIC
    org-init-topic: AUDI-SVCD-OPERATION-ORG-INIT-TOPIC
    position-topic: AUDI-SVCD-OPERATION-POSITION-TOPIC
    position-init-topic: AUDI-SVCD-OPERATION-POSITION-INIT-TOPIC
    user-topic: AUDI-SVCD-OPERATION-USER-TOPIC
    user-init-topic: AUDI-SVCD-OPERATION-USER-INIT-TOPIC
    city-topic: AUDI-SVCD-OPERATION-CITY-TOPIC
    city-init-topic: AUDI-SVCD-OPERATION-CITY-INIT-TOPIC
    province-topic: AUDI-SVCD-OPERATION-PROVINCE-TOPIC
    province-init-topic: AUDI-SVCD-OPERATION-PROVINCE-INIT-TOPIC
    area-topic: AUDI-SVCD-OPERATION-AREA-TOPIC
    area-init-topic: AUDI-SVCD-OPERATION-AREA-INIT-TOPIC
    company-topic: AUDI-SVCD-OPERATION-COMPANY-TOPIC
    company-init-topic: AUDI-SVCD-OPERATION-COMPANY-INIT-TOPIC
    investor-topic: AUDI-SVCD-OPERATION-INVESTOR-TOPIC
    investor-init-topic: AUDI-SVCD-OPERATION-INVESTOR-INIT-TOPIC
    after-sales-topic: AUDI-SVCD-OPERATION-AFTER-SALES-TOPIC
    after-sales-init-topic: AUDI-SVCD-OPERATION-AFTER-SALES-INIT-TOPIC
    sales-agent-topic: AUDI-SVCD-OPERATION-SALES-AGENT-TOPIC
    sales-agent-init-topic: AUDI-SVCD-OPERATION-SALES-AGENT-INIT-TOPIC
    org-region-topic: AUDI-SVCD-OPERATION-ORG-REGION-TOPIC
    org-region-init-topic: AUDI-SVCD-OPERATION-ORG-REGION-INIT-TOPIC
event-kafka:
  bootstrap-servers: **************:9092
  event-topic: input-audi-AP-dev
mybatis-plus:
  configuration:
    mapUnderscoreToCamelCase: 'true'
  mapperLocations: classpath:mybatis/*.xml
  typeAliasesPackage: com.csvw.audi.cc.entity
audi-car-config:
  accb:
    url: https://accb-api.audibrand.cn
    host: accb-api.audibrand.cn
    secret: bc5adda0388a496d91a88fd6fbb4511e
    key: 16d9980fa26345e88c8b371303fdda72

#    host: pre-accb-api.audibrand.cn
#    url: https://pre-accb-api.audibrand.cn
#    key: e0f97ac39ba64668838ccc8921d68550
#    secret: f50123af146c40049c83802d8f90be8b
    lang: en
    product: CHINA_SAIC
  infra:
    appId: Vu3rx4iuPqZD
    appSecret: dnQAVhjD5qbHkI1Yn9LoWggitDc1pUEd
    url: http://*************:60066/audi_noprod/infra/test
    amsAesKey: "]Pym-j~D5=]QUyS1"
    amsAesIv: audi-ams-app-tst
  cc:
   delivery: 2022年第一季度起
   q5eDelivery: 2022年第二季度起
  hqDealerCode: 76600019
  bestRecommend:
    filterEnable: true
    modelLineIds:
      - "5dd76400-eef9-4c37-a7a2-df6e4432448a"
    customSeriesIds:
      - "ca060a3b-6822-44e3-9956-4d33ae290018"
  es:
    host: http://localhost:9200
# http://**************:9200,http://**************:9200,http://**************:9200

api:
  swagger:
    enabled: true
    basePackage: com.csvw.audi.cc.controller
    contact: <EMAIL>
    description: 车辆配置器服务
    title: audi-car-config
    version: '2.0'
logging:
  level:
    com.csvw.audi: debug

svcd:
  dealer-image-url: https://ndmc-test.saic-audi.cn/file/
cop-order:
  datasource:
    url: *******************************************************************************************************************************************************
    username: audi_dev
    password: p7e_dN3W
    initialSize: 3
    maxActive: 30
    minIdle: 6
server:
  port: 8081

oss:
  aliyun:
    accessKeyId: LTAI4GByWpVvHk147c7a7MMb
    accessKeySecret: ******************************
    bucketName: sx-audi
    endpoint: http://oss-cn-shanghai.aliyuncs.com
    segmentationUrl: https://sx-audi.oss-cn-shanghai.aliyuncs.com/
    dealerImagePath: dealerImage

fan-task-2023:
  topic: 2023-fan-task-pre
  endTime: 2023-09-30 00:00:00

audi:
  purple-int-scq: http://**************:32560