<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarCustomMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarCustom">
        <id column="ccid" property="ccid" />
        <result column="audi_code" property="audiCode" />
        <result column="accb_model_id" property="accbModelId" />
        <result column="accb_model_code" property="accbModelCode" />
        <result column="model_desc" property="modelDesc" />
        <result column="accb_type_id" property="accbTypeId" />
        <result column="accb_type_code" property="accbTypeCode" />
        <result column="accb_type_desc" property="accbTypeDesc" />
        <result column="model_year" property="modelYear" />
        <result column="sib_interieur_id" property="sibInterieurId" />
        <result column="user_id" property="userId" />
        <result column="user_mobile" property="userMobile" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
        <result column="model_line_id" property="modelLineId" />
        <result column="entry_point" property="entryPoint" />
        <result column="deposit_type" property="depositType" />
        <result column="snapshot_id" property="snapshot_id" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ccid, audi_code, accb_model_id, accb_model_code, model_desc, accb_type_id, accb_type_code, accb_type_desc, model_year, sib_interieur_id, user_id, user_mobile, create_user, create_time, update_user, update_time, remark, model_line_id, entry_point, deposit_type, snapshot_id
    </sql>

    <select id="a7OrQ5Byccid" resultType="com.csvw.audi.cc.entity.vo.CarCustomVo" parameterType="java.lang.String">
        SELECT
            custom_series.custom_series_code, model_line.model_line_id
        FROM
            (SELECT
                model_line_id, custom_series_id
            FROM
                car_model_line
            WHERE
                channel = 'master'
                AND model_line_id = (SELECT model_line_id FROM car_custom WHERE ccid = #{ccid,jdbcType=VARCHAR})) model_line
        LEFT JOIN
            (SELECT DISTINCT
                custom_series_code, custom_series_id
            FROM
                car_custom_series
            WHERE
                channel = 'master'
                AND custom_series_code IS NOT NULL) custom_series
        ON custom_series.custom_series_id = model_line.custom_series_id
    </select>

    <select id="listCustomDetail" resultType="com.csvw.audi.cc.entity.vo.CarCustomDetailVo" parameterType="com.csvw.audi.cc.entity.dto.CarCustomQueryDto">
        select cc.*, cs.`custom_series_id` , cs.`custom_series_code` , cs.`custom_series_name`  from `car_custom` cc
        LEFT JOIN `car_model_line` ml on cc.`model_line_id`  = ml.`model_line_id`
        LEFT JOIN `car_custom_series` cs on ml.`custom_series_id`  = cs.`custom_series_id`
        where `ccid` in
        <foreach collection="ccids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and ml.`channel` = 'master' and cs.`channel` = 'master'
    </select>

</mapper>
