package com.csvw.audi.cc.entity.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class Pageable {
    public Pageable(Pageable pageable){
        if (pageable == null){
            return;
        }
        this.pageNum = pageable.getPageNum() != null?pageable.getPageNum():1;
        this.pageSize = pageable.getPageSize() != null?pageable.getPageSize():10;
    }
    private Integer pageNum=1;
    private Integer pageSize=10;
}
