package com.csvw.audi.cc.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

@Data
@Accessors(chain = true)
@ApiModel(value = "接收消息V0")
public class ReviceTaskVo {

    @ApiModelProperty(value = "topic名称")
    private String topic;

    @ApiModelProperty(value = "服务名")
    private String serviceName;

    @ApiModelProperty(value = "服务类型 区分服务不同topic")
    private String tag;

    @ApiModelProperty(value = "消息body json 格式")
    private String data;

    @ApiModelProperty(value = "key:参数名,value:参数值")
    private Map<String, String> params;


}
