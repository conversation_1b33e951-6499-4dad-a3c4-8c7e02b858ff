package com.csvw.audi.cc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csvw.audi.cc.entity.po.CarSibInterieur;
import com.csvw.audi.cc.mapper.CarSibInterieurMapper;
import com.csvw.audi.cc.service.ICarSibInterieurService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 内饰颜色面料表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-19
 */
@Service
public class CarSibInterieurServiceImpl extends ServiceImpl<CarSibInterieurMapper, CarSibInterieur> implements ICarSibInterieurService {

    @Override
    public CarSibInterieur getSibInterieur(String sibInterieurId, String channel) {
        LambdaQueryWrapper<CarSibInterieur> sibInterQ = new LambdaQueryWrapper<>();
        sibInterQ.eq(CarSibInterieur::getSibInterieurId, sibInterieurId)
                .eq(CarSibInterieur::getChannel, channel)
                .eq(CarSibInterieur::getDelFlag, 0);
        return this.getOne(sibInterQ);
    }


}
