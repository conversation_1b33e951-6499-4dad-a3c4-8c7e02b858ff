package com.csvw.audi.cc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.common.utils.CcUtils;
import com.csvw.audi.cc.config.AppConfig;
import com.csvw.audi.cc.entity.dto.*;
import com.csvw.audi.cc.entity.dto.accb.AudiConfigDto;
import com.csvw.audi.cc.entity.dto.accb.OptionBriefDto;
import com.csvw.audi.cc.entity.dto.omd.*;
import com.csvw.audi.cc.entity.enumeration.*;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.entity.vo.*;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.mapper.CarRecommendMapper;
import com.csvw.audi.cc.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 推荐车 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
@Service
@Slf4j
public class CarRecommendServiceImpl extends ServiceImpl<CarRecommendMapper, CarRecommend> implements ICarRecommendService {

    @Autowired
    private IACCBService accbService;

    @Autowired
    private ICarModelLineService modelLineService;

    @Autowired
    private ICarOptionService optionService;

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private ICarSibInterieurService sibInterieurService;

    @Autowired
    private ICarModelLineSibInterieurService modelLineSibInterieurService;

    @Autowired
    private ICarConfigImageService configImageService;

    @Autowired
    private OmdServiceImpl omdService;

    @Autowired
    private ICarModelLineOptionService modelLineOptionService;

    @Autowired
    private ICarOptionRelateService optionRelateService;

    @Autowired
    private ICarOmdBestRecommendService omdBestRecommendService;

    @Autowired
    private ICarBestRecommendService bestRecommendService;

    @Autowired
    private ICarBestRecommendStockService bestRecommendStockService;

    @Autowired
    private ICarBestRecommendOptionService bestRecommendOptionService;

    @Autowired
    private ICarCustomService customService;

    @Autowired
    private ICarStockLogService stockLogService;

    @Autowired
    private ICarOmdStockRecommendLogService stockRecommendLogService;

    @Autowired
    private ICarOmdStockRecommendService stockRecommendService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private DataSourceTransactionManager transactionManager;

    @Autowired
    private TransactionDefinition transactionDefinition;

    @Autowired
    private ICarBestRecommendCustomSyncService customSyncService;

    @Autowired
    private ICarOmdStockMapService stockMapService;

    private final String BEST_STOCK_LOCK_PREFIX = "saic_audi:applock:audi_car_config:best_recommend_stock_lock:";

    @Override
    public List<RecommendCarVo> recommendCar(String channel, String customSeriesId) {
        LambdaQueryWrapper<CarRecommend> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(customSeriesId)) {
            queryWrapper.eq(CarRecommend::getCustomSeriesId, customSeriesId);
        }
        if (StringUtils.isNotBlank(channel)) {
            queryWrapper.eq(CarRecommend::getChannel, channel);
        }
        queryWrapper.eq(CarRecommend::getDelFlag, 0).orderByAsc(CarRecommend::getWeight);
        List<CarRecommend> recommends = this.list(queryWrapper);
        List<RecommendCarVo> res = new ArrayList<>();
        if (recommends != null) {
            res = recommends.stream().map(i -> {
                RecommendCarVo vo = new RecommendCarVo();
                AudiConfigDto configDto;
                if (StringUtils.isNotBlank(i.getAudiCodeContent())) {
                    configDto = JSONObject.parseObject(i.getAudiCodeContent(), AudiConfigDto.class);
                } else {
                    configDto = accbService.audiConfig(i.getAudiCode());
                    i.setAudiCodeContent(JSONObject.toJSONString(configDto));
                    i.updateById();
                }
                vo.setAudiCode(i.getAudiCode());
                vo.setAccbTypeCode(configDto.getTypeCode());
                vo.setAccbTypeId(configDto.getTypeId());
                if (configDto.getOptions() != null) {
                    vo.setOptionCodes(configDto.getOptions().stream().map(OptionBriefDto::getCode).collect(Collectors.toList()));
                }
                try {
                    vo.setOptions(optionService.optionQueryByOptionCodes(Constant.MASTER_CHANNEL, i.getCustomSeriesId(), i.getModelLineId(), vo.getOptionCodes().stream().map(c -> {
                        String[] splits = c.split(":");
                        if (splits.length > 1) {
                            return splits[1];
                        } else {
                            return splits[0];
                        }
                    }).collect(Collectors.toList())));
                    Iterator<ModelLineOptionVo> optionIt = vo.getOptions().iterator();
                    while (optionIt.hasNext()) {
                        String optionId = optionIt.next().getOptionId();
                        try {
                            List<ModelLineOptionVo> a = modelLineService.optionQueryByOptionId(Constant.MASTER_CHANNEL, i.getCustomSeriesId(), i.getModelLineId(), optionId, true);
                            if (CollectionUtils.isEmpty(a) || a.get(0).getStatus().intValue() == 0) {
                                optionIt.remove();
                            }
                        } catch (Exception e) {
                            log.error("drm 推荐车配置项异常", e);
                            optionIt.remove();
                        }

                    }
                } catch (NoSuchFieldException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
                ModelParamDto modelParamDto = new ModelParamDto();
                modelParamDto.setDelFlag(0);
                modelParamDto.setModelLineId(i.getModelLineId());
                modelParamDto.setChannel(Constant.MASTER_CHANNEL);
                List<ModelLineVo> lines = null;
                try {
                    lines = modelLineService.listModelLine(modelParamDto);
                    if (CollectionUtils.isEmpty(lines)) {
                        log.error("配置线参数异常：{}", modelParamDto);
                    }
                } catch (Exception e) {
                    log.error("DRM 推荐车异常", e);
                }
                vo.setModelLine(lines.get(0));
                vo.setOptions(vo.getOptions().stream().filter(co -> !OptionCategoryEnum.INCOLOR.getValue().equals(co.getCategory())).collect(Collectors.toList()));
                return vo;

            }).collect(Collectors.toList());
        }
        return res;
    }

    @Override
    @Cacheable(cacheNames = {"cc_cache"}, keyGenerator = "springCacheKeyGenerator")
    public List<RecommendCarSphereVo> recommendCarSphere(String channel, String customSeriesId) {
        LambdaQueryWrapper<CarRecommend> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(customSeriesId)) {
            queryWrapper.eq(CarRecommend::getCustomSeriesId, customSeriesId);
        }
        if (StringUtils.isNotBlank(channel)) {
            queryWrapper.eq(CarRecommend::getChannel, channel);
        }
        queryWrapper.eq(CarRecommend::getDelFlag, 0).orderByAsc(CarRecommend::getWeight);
        List<CarRecommend> recommends = this.list(queryWrapper);
        List<RecommendCarSphereVo> res = new ArrayList<>();
        if (recommends != null) {
            res = recommends.stream().map(i -> {
                RecommendCarSphereVo vo = new RecommendCarSphereVo();
                AudiConfigDto configDto;
                if (StringUtils.isNotBlank(i.getAudiCodeContent())) {
                    log.info("audi code cc cache：{}", i.getAudiCodeContent());
                    configDto = JSONObject.parseObject(i.getAudiCodeContent(), AudiConfigDto.class);
                } else {
                    configDto = accbService.audiConfig(i.getAudiCode());
                    if (configDto == null) {
                        log.error("audi code error");
                        return null;
                    }
                    i.setAudiCodeContent(JSONObject.toJSONString(configDto));
                    i.updateById();
                }
                log.info("audi code configDto: {}", configDto);
                ModelParamDto modelParamDto = new ModelParamDto();
                modelParamDto.setChannel(channel);
                modelParamDto.setDelFlag(0);
                modelParamDto.setModelLineId(i.getModelLineId());
                modelParamDto.setCustomSeriesId(customSeriesId);
                List<ModelLineVo> modelLineVos;
                try {
                    modelLineVos = modelLineService.listModelLine(modelParamDto);
                } catch (Exception e) {
                    log.error("查询配置线失败", e);
                    return null;
                }
                if (CollectionUtils.isEmpty(modelLineVos) || modelLineVos.size() > 1) {
                    log.error("未查到配置线");
                    return null;
                }
                ModelLineVo modelLineVo = modelLineVos.get(0);
                vo.setModelLineVo(modelLineVo);
                vo.setTypeFlag(modelLineVo.getTypeFlag());
                vo.setAudiCode(i.getAudiCode());
                vo.setAccbTypeCode(configDto.getTypeCode());
                vo.setAccbTypeId(configDto.getTypeId());
                vo.setModelLineCode(modelLineVo.getModelLineCode());
                vo.setModelYear(modelLineVo.getModelYear());
                vo.setCustomSeriesCode(modelLineVo.getCustomSeriesCode());
                vo.setModelLineId(modelLineVo.getModelLineId());
                vo.setModelLineName(modelLineVo.getModelLineName());
                vo.setImageUrl(modelLineVo.getImageUrl());
                vo.setCustomSeriesId(modelLineVo.getCustomSeriesId());
                vo.setDeliverTime(appConfig.getCc().getDelivery());
                vo.setTags(modelLineVo.getTags());
                vo.setMeasure(modelLineVo.getMeasure());
                vo.setPersonal(modelLineVo.getPersonal());
                vo.setSuit(modelLineVo.getSuit());
                vo.setSuitImageUrl(modelLineVo.getSuitImageUrl());
                vo.setSellBlips(modelLineService.listBlips(vo.getModelLineId()));
                vo.setOmdModelStatus(modelLineVo.getOmdModelStatus());
                if (modelLineVo.getCustomSeriesCode().equals("49")) {
                    vo.setDeliverTime(appConfig.getCc().getDelivery());
                } else if (modelLineVo.getCustomSeriesCode().equals("G4")) {
                    vo.setDeliverTime(appConfig.getCc().getQ5eDelivery());
                }
                if (configDto.getOptions() != null) {
                    List<String> personalOptionIds = new ArrayList<>();
                    vo.setPersonalOptionIds(personalOptionIds);
                    configDto.getOptions().forEach(o -> {
                        String[] os = o.getCode().split(",");
                        if (os.length > 0) {
                            for (String code : os) {
                                codeToOption(code, channel, vo);
                            }
                        }
                    });

                    PriceComputeParam priceComputeParam = recommendVoToPriceComputeParam(vo);
                    try {
                        String category = OptionCategoryEnum.PACKET.getValue();
                        List<ModelLineOptionVo> optionVos = new ArrayList<>();
                        List<ModelLineOptionVo> packetEquities = modelLineService.modelLineOption(channel, i.getModelLineId(), category).stream().filter(o-> OptionTypeEnum.PACKET_EQUITY.getValue().equals(o.getOptionType()) && o.getStatus() != 0).collect(Collectors.toList());
//                        List<ModelLineOptionVo> exterieurs = modelLineService.modelLineOption(channel, i.getModelLineId(), "COLOR_EXTERIEUR");
                        optionVos.addAll(packetEquities);
//                        optionVos.addAll(exterieurs);
                        vo.setPacketEquity(packetEquities);
                        log.debug("推荐车权益包，{}", packetEquities);
                        if (CollectionUtils.isNotEmpty(optionVos)){
                           List<ModelLineOptionVo> defaultVos = optionVos.stream().filter(e->e.getDefaultConfig() != null && e.getDefaultConfig().intValue() == 1).collect(Collectors.toList());
                           if(CollectionUtils.isNotEmpty(defaultVos)){
                               if(priceComputeParam.getOptionIds() == null) {
                                   priceComputeParam.setOptionIds(new ArrayList<>());
                               }
                               priceComputeParam.getOptionIds().add(defaultVos.get(0).getOptionId());
                           }
                        }
                        vo.setPrice(modelLineService.computePrice(priceComputeParam));
                    } catch (ServiceException e) {
                        log.error("推荐车价格计算错误", e);
                        return null;
                    } catch (Exception e) {
                        log.error("推荐车处理错误", e);
                    }
                }
                return vo;
            }).filter(i ->{
                if(i.getOmdModelStatus() == 0 && (i.getMeasure() == null || i.getMeasure() != 1) && Constant.NEED_TYPE_FLAG.contains(channel)){
                    if (StringUtils.isBlank(i.getTypeFlag())){
                        return false;
                    }
                }
                return i != null &&
                        modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId());
                }
            ).collect(Collectors.toList());
        }
        return res;
    }

    @Override
    @Cacheable(cacheNames = {"cc_cache_level2_best_recommend"}, keyGenerator = "springCacheKeyGenerator")
    public List<BestRecommendCarVo> omdBestRecommendCar(String channel, String dealerCode, String customSeriesId) {
        List<CarBestRecommendDto> bestRecommend = bestRecommendService.listBestRecommendByDealerCode(dealerCode, customSeriesId);
        if (CollectionUtils.isNotEmpty(bestRecommend)) {
            List<BestRecommendCarVo> vos = bestRecommend.stream().map(i -> {
                BestRecommendCarVo recommendCarVo = new BestRecommendCarVo();
                recommendCarVo.setBestRecommendId(String.valueOf(i.getBestRecommendId()));
                recommendCarVo.setRecommendModelId(i.getRecommendModelId());
                try {
                    ModelParamDto paramDto = new ModelParamDto();
                    paramDto.setModelLineId(i.getModelLineId());
                    paramDto.setChannel(channel);
                    List<ModelLineVo> modelLineVo = modelLineService.listModelLine(paramDto);
                    if (CollectionUtils.isEmpty(modelLineVo)) {
                        return null;
                    }
                    recommendCarVo.setModelLine(modelLineVo.get(0));
                    ModelLineSibInterieurVo param = new ModelLineSibInterieurVo();
                    param.setModelLineId(i.getModelLineId());
                    param.setSibInterieurId(i.getSibInterieurId());
                    param.setDelFlag(0);
                    param.setChannel(channel);
                    List<ModelLineSibInterieurVo> sibInterieurVos = modelLineSibInterieurService.modelLineSibInterieur(param);
                    if (CollectionUtils.isNotEmpty(sibInterieurVos)) {
                        recommendCarVo.setModelLineSibInterieurVo(sibInterieurVos.get(0));
                    }
                    List<String> optionIds = bestRecommendOptionService.listRecommendOptionIds(i.getBestRecommendId());
                    List<ModelLineOptionVo> prVos = modelLineService.optionQueryByOptionIds(channel, recommendCarVo.getModelLine().getCustomSeriesId(), i.getModelLineId(), optionIds);
                    prVos = prVos == null ? new ArrayList<>() : prVos;
                    recommendCarVo.setOptions(prVos);
                    Set<String> categories = recommendCarVo.getOptions().stream().map(ModelLineOptionVo::getCategory).collect(Collectors.toSet());
                    if (!categories.contains(OptionCategoryEnum.SEET.getValue())) {
                        String vosCode = null;
                        ModelLineOptionVo vosOption = null;
                        for (ModelLineOptionVo o : recommendCarVo.getOptions()) {
                            if (o.getCategory() != null && o.getCategory().equals(OptionCategoryEnum.PACKET.getValue())) {
                                OptionParamDto vosParamDto = new OptionParamDto();
                                vosParamDto.setOptionId(o.getOptionId());
                                vosParamDto.setModelLineId(recommendCarVo.getModelLine().getModelLineId());
                                vosParamDto.setChannel(channel);
                                vosParamDto.setCategory(OptionCategoryEnum.SEET.getValue());
                                vosParamDto.setDelFlag(0);
                                List<ModelLineOptionVo> vosItems = modelLineOptionService.listModelLinePacketItem(vosParamDto);
                                if (CollectionUtils.isNotEmpty(vosItems)) {
                                    vosCode = vosItems.get(0).getOptionCode();
                                    vosOption = vosItems.get(0);
                                }
                            }
                        }
                        if (vosOption != null) {
                            recommendCarVo.getOptions().add(vosOption);
                        }
                        if (vosCode == null) {
                            String category = OptionCategoryEnum.SEET.getValue();
                            List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, recommendCarVo.getModelLine().getModelLineId(), category);
                            for (ModelLineOptionVo o : optionVos) {
                                if (o.getStatus() != null && o.getStatus().intValue() == 1) {
                                    recommendCarVo.getOptions().add(o);
                                    break;
                                }
                            }
                        }
                    }
                    if (!categories.contains(OptionCategoryEnum.WHEEL.getValue())) {
                        String category = OptionCategoryEnum.WHEEL.getValue();
                        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, recommendCarVo.getModelLine().getModelLineId(), category);
                        for (ModelLineOptionVo o : optionVos) {
                            if (o.getStatus() != null && o.getStatus().intValue() == 1) {
                                recommendCarVo.getOptions().add(o);
                                break;
                            }
                        }
                    }
                    if (!categories.contains(OptionCategoryEnum.EIH.getValue())) {
                        String category = OptionCategoryEnum.EIH.getValue();
                        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, recommendCarVo.getModelLine().getModelLineId(), category);
                        for (ModelLineOptionVo o : optionVos) {
                            if (o.getStatus() != null && o.getStatus().intValue() == 1) {
                                recommendCarVo.getOptions().add(o);
                                break;
                            }
                        }
                    }
                    String category = "RAD";
                    List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, recommendCarVo.getModelLine().getModelLineId(), category);
                    optionVos.forEach(rad -> {
                        if (rad.getStatus() != null && rad.getStatus().intValue() == 1) {
                            recommendCarVo.setStandardRad(rad);
                            return;
                        }
                    });
                    PriceComputeParam priceComputeParam = new PriceComputeParam();
                    priceComputeParam.setOptionIds(optionIds);
                    priceComputeParam.setCustomSeriesId(recommendCarVo.getModelLine().getCustomSeriesId());
                    priceComputeParam.setModelLineId(recommendCarVo.getModelLine().getModelLineId());
                    recommendCarVo.setTotalPrice(modelLineService.computePrice(priceComputeParam));
                } catch (Exception e) {
                    log.error("查询推荐车异常", e);
                    return null;
                }

                return recommendCarVo;
            }).filter(i -> i != null && validDealerRecommend(i.getRecommendModelId())).collect(Collectors.toList());
            return vos;
        }
        return Lists.newArrayList();
    }

    @Override
    @Cacheable(cacheNames = {"cc_cache_level2_best_recommend"}, keyGenerator = "springCacheKeyGenerator")
    public List<BestRecommendCarVo> omdBestRecommendCarFix(String channel, String dealerCode, String customSeriesId) {
        List<CarBestRecommendDto> bestRecommend = bestRecommendService.listBestRecommendByDealerCodeFix(dealerCode, customSeriesId);
        if (CollectionUtils.isNotEmpty(bestRecommend)) {
            List<BestRecommendCarVo> vos = bestRecommend.stream().map(i -> {
                BestRecommendCarVo recommendCarVo = new BestRecommendCarVo();
                recommendCarVo.setBestRecommendId(String.valueOf(i.getBestRecommendId()));
                recommendCarVo.setRecommendModelId(i.getRecommendModelId());
                try {
                    ModelParamDto paramDto = new ModelParamDto();
                    paramDto.setModelLineId(i.getModelLineId());
                    paramDto.setChannel(channel);
                    List<ModelLineVo> modelLineVo = modelLineService.listModelLine(paramDto);
                    if (CollectionUtils.isEmpty(modelLineVo)) {
                        return null;
                    }
                    recommendCarVo.setModelLine(modelLineVo.get(0));
                    ModelLineSibInterieurVo param = new ModelLineSibInterieurVo();
                    param.setModelLineId(i.getModelLineId());
                    param.setSibInterieurId(i.getSibInterieurId());
                    param.setDelFlag(0);
                    param.setChannel(channel);
                    List<ModelLineSibInterieurVo> sibInterieurVos = modelLineSibInterieurService.modelLineSibInterieur(param);
                    if (CollectionUtils.isNotEmpty(sibInterieurVos)) {
                        recommendCarVo.setModelLineSibInterieurVo(sibInterieurVos.get(0));
                    }
                    List<String> optionIds = bestRecommendOptionService.listRecommendOptionIds(i.getBestRecommendId());
                    List<ModelLineOptionVo> prVos = modelLineService.optionQueryByOptionIds(channel, recommendCarVo.getModelLine().getCustomSeriesId(), i.getModelLineId(), optionIds);
                    prVos = prVos == null ? new ArrayList<>() : prVos;
                    recommendCarVo.setOptions(prVos);
                    Set<String> categories = recommendCarVo.getOptions().stream().map(ModelLineOptionVo::getCategory).collect(Collectors.toSet());
                    if (!categories.contains(OptionCategoryEnum.SEET.getValue())) {
                        String vosCode = null;
                        ModelLineOptionVo vosOption = null;
                        for (ModelLineOptionVo o : recommendCarVo.getOptions()) {
                            if (o.getCategory() != null && o.getCategory().equals(OptionCategoryEnum.PACKET.getValue())) {
                                OptionParamDto vosParamDto = new OptionParamDto();
                                vosParamDto.setOptionId(o.getOptionId());
                                vosParamDto.setModelLineId(recommendCarVo.getModelLine().getModelLineId());
                                vosParamDto.setChannel(channel);
                                vosParamDto.setCategory(OptionCategoryEnum.SEET.getValue());
                                vosParamDto.setDelFlag(0);
                                List<ModelLineOptionVo> vosItems = modelLineOptionService.listModelLinePacketItem(vosParamDto);
                                if (CollectionUtils.isNotEmpty(vosItems)) {
                                    vosCode = vosItems.get(0).getOptionCode();
                                    vosOption = vosItems.get(0);
                                }
                            }
                        }
                        if (vosOption != null) {
                            recommendCarVo.getOptions().add(vosOption);
                        }
                        if (vosCode == null) {
                            String category = OptionCategoryEnum.SEET.getValue();
                            List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, recommendCarVo.getModelLine().getModelLineId(), category);
                            for (ModelLineOptionVo o : optionVos) {
                                if (o.getStatus() != null && o.getStatus().intValue() == 1) {
                                    recommendCarVo.getOptions().add(o);
                                    break;
                                }
                            }
                        }
                    }
                    if (!categories.contains(OptionCategoryEnum.WHEEL.getValue())) {
                        String category = OptionCategoryEnum.WHEEL.getValue();
                        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, recommendCarVo.getModelLine().getModelLineId(), category);
                        for (ModelLineOptionVo o : optionVos) {
                            if (o.getStatus() != null && o.getStatus().intValue() == 1) {
                                recommendCarVo.getOptions().add(o);
                                break;
                            }
                        }
                    }
                    if (!categories.contains(OptionCategoryEnum.EIH.getValue())) {
                        String category = OptionCategoryEnum.EIH.getValue();
                        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, recommendCarVo.getModelLine().getModelLineId(), category);
                        for (ModelLineOptionVo o : optionVos) {
                            if (o.getStatus() != null && o.getStatus().intValue() == 1) {
                                recommendCarVo.getOptions().add(o);
                                break;
                            }
                        }
                    }
                    String category = "RAD";
                    List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, recommendCarVo.getModelLine().getModelLineId(), category);
                    optionVos.forEach(rad -> {
                        if (rad.getStatus() != null && rad.getStatus().intValue() == 1) {
                            recommendCarVo.setStandardRad(rad);
                            return;
                        }
                    });
                    PriceComputeParam priceComputeParam = new PriceComputeParam();
                    priceComputeParam.setOptionIds(optionIds);
                    priceComputeParam.setCustomSeriesId(recommendCarVo.getModelLine().getCustomSeriesId());
                    priceComputeParam.setModelLineId(recommendCarVo.getModelLine().getModelLineId());
                    recommendCarVo.setTotalPrice(modelLineService.computePrice(priceComputeParam));
                } catch (Exception e) {
                    log.error("查询推荐车异常", e);
                    return null;
                }

                return recommendCarVo;
            }).filter(i -> i != null && validDealerRecommend(i.getRecommendModelId())).collect(Collectors.toList());
            return vos;
        }
        return Lists.newArrayList();
    }

    private boolean validDealerRecommend(Long recommendModelId) {
        CarOmdStockRecommend stockRecommend = stockRecommendService.getById(recommendModelId);
        if (stockRecommend == null || stockRecommend.getDelFlag().intValue() == 0) {
            return true;
        }
        return false;
    }

    @Override
    public void convertStockRecommendCarStockMap() {
        List<Long> stockRecommendIds = stockRecommendLogService.listUnhandleRecommend();

        // convert
        stockRecommendIds.forEach(i -> {
            TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);
            try {
                LambdaQueryWrapper<CarOmdStockRecommendLog> q = new LambdaQueryWrapper<>();
                q.eq(CarOmdStockRecommendLog::getStockRecommendModelId, i)
                        .orderByDesc(CarOmdStockRecommendLog::getOperateTime).last("limit 1");
                CarOmdStockRecommendLog stockRecommendLog = stockRecommendLogService.getOne(q);
                if (stockRecommendLog == null || stockRecommendLog.getConvertRecommendCar().intValue() != 3) {
                    return;
                }
                String ccUniqueCode = CcUtils.getCcUniqueCode(stockRecommendLog.getModelCode().split("-")[0],
                        stockRecommendLog.getModelYear(), stockRecommendLog.getModelVersion(),
                        stockRecommendLog.getColorCode(), stockRecommendLog.getInteriorCode(), stockRecommendLog.getPrList());
                LambdaQueryWrapper<CarOmdStockMap> stockMapQ = new LambdaQueryWrapper<>();
                stockMapQ.eq(CarOmdStockMap::getOriginCcUniqueCode, ccUniqueCode)
                        .eq(CarOmdStockMap::getOriginAccbTypeCode, stockRecommendLog.getModelCode())
                        .eq(CarOmdStockMap::getDelFlag, 0);
                CarOmdStockMap stockMap = stockMapService.getOne(stockMapQ);
                if (stockMap == null){
                    return ;
                }
                LambdaQueryWrapper<CarOmdBestRecommend> bestQ = new LambdaQueryWrapper<>();
                bestQ.eq(CarOmdBestRecommend::getModelCode, stockRecommendLog.getModelCode())
                        .eq(CarOmdBestRecommend::getModelYear, stockRecommendLog.getModelYear())
                        .eq(CarOmdBestRecommend::getModelVersion, stockRecommendLog.getModelVersion())
                        .eq(CarOmdBestRecommend::getInteriorCode, stockRecommendLog.getInteriorCode())
                        .eq(CarOmdBestRecommend::getColorCode, stockRecommendLog.getColorCode())
                        .eq(CarOmdBestRecommend::getPrList, stockRecommendLog.getPrList());
                List<CarOmdBestRecommend> bestRecommends = omdBestRecommendService.list(bestQ);
                LambdaQueryWrapper<CarOmdStockRecommend> stocksQ = new LambdaQueryWrapper<>();
                stocksQ.eq(CarOmdStockRecommend::getModelCode, stockRecommendLog.getModelCode())
                        .eq(CarOmdStockRecommend::getModelYear, stockRecommendLog.getModelYear())
                        .eq(CarOmdStockRecommend::getModelVersion, stockRecommendLog.getModelVersion())
                        .eq(CarOmdStockRecommend::getInteriorCode, stockRecommendLog.getInteriorCode())
                        .eq(CarOmdStockRecommend::getColorCode, stockRecommendLog.getColorCode())
                        .eq(CarOmdStockRecommend::getPrList, stockRecommendLog.getPrList());
                List<CarOmdStockRecommend> stockRecommends = stockRecommendService.list(stocksQ);
                LambdaQueryWrapper<CarOmdStockRecommend> sq = new LambdaQueryWrapper<>();
                sq.eq(CarOmdStockRecommend::getCcUniqueCode, stockMap.getCcUniqueCode())
                        .eq(CarOmdStockRecommend::getDealerNetCode, stockRecommendLog.getDealerNetCode())
                        .eq(CarOmdStockRecommend::getModelCode, stockMap.getAccbTypeCode())
                        .eq(CarOmdStockRecommend::getDelFlag, 0);
                List<CarOmdStockRecommend> mapStockRecommends = stockRecommendService.list(sq);
                if (CollectionUtils.isNotEmpty(mapStockRecommends)) {
                    // 逻辑删除多余的mapStock
                    if (mapStockRecommends.size() > 1) {
                        for (CarOmdStockRecommend mapStock : mapStockRecommends) {
                            if (mapStock.getStockMapRecommend() != null || mapStock.getStockMapRecommend().intValue() == 1) {
                                LambdaUpdateWrapper<CarBestRecommendStock> stockU = new LambdaUpdateWrapper<>();
                                stockU.eq(CarBestRecommendStock::getRecommendModelId, mapStock.getStockRecommendModelId())
                                        .eq(CarBestRecommendStock::getType, StockTypeEnum.DEALER.getValue());
                                bestRecommendStockService.update(stockU);
                                LambdaUpdateWrapper<CarOmdStockRecommend> mapStockU = new LambdaUpdateWrapper<>();
                                mapStockU.set(CarOmdStockRecommend::getDelFlag, 1)
                                        .eq(CarOmdStockRecommend::getStockRecommendModelId, mapStock.getStockRecommendModelId());
                                stockRecommendService.update(mapStockU);
                            }
                        }
                    }
                    log.info("经销商库存已存在，{}", mapStockRecommends);
                    stockRecommendLog.setConvertRecommendCar(1);
                    stockRecommendLog.setUpdateTime(LocalDateTime.now());
                    stockRecommendLog.updateById();
                    return;
                }

                CarOmdStockRecommend mapStockRecommend = new CarOmdStockRecommend();
                BeanUtils.copyProperties(stockMap, mapStockRecommend);
                // 配置线代码
                mapStockRecommend.setModelCode(stockMap.getAccbTypeCode());
                mapStockRecommend.setCreateTime(LocalDateTime.now());
                mapStockRecommend.setStockMapRecommend(1);
                mapStockRecommend.setDealerNetCode(stockRecommendLog.getDealerNetCode());
                mapStockRecommend.insert();
                // 关联已存在的库存车
                if (CollectionUtils.isNotEmpty(bestRecommends)) {
                    for (CarOmdBestRecommend bestRecommend : bestRecommends) {
                        LambdaQueryWrapper<CarBestRecommendStock> stockQ = new LambdaQueryWrapper<>();
                        stockQ.eq(CarBestRecommendStock::getRecommendModelId, bestRecommend.getBestSellRecommendModelId())
                                .eq(CarBestRecommendStock::getType, StockTypeEnum.HQ.getValue());
                        List<CarBestRecommendStock> stocks = bestRecommendStockService.list(stockQ);
                        if (CollectionUtils.isNotEmpty(stocks)) {
                            CarBestRecommendStock oldStock = stocks.get(0);
                            CarBestRecommendStock stock = new CarBestRecommendStock();
                            stock.setStockNum(0l);
                            stock.setBestRecommendId(oldStock.getBestRecommendId());
                            stock.setDealerNetCode(stockRecommendLog.getDealerNetCode());
                            stock.setRecommendModelId(mapStockRecommend.getStockRecommendModelId());
                            stock.setType(StockTypeEnum.DEALER.getValue());
                            stock.setCreateTime(LocalDateTime.now());
                            stock.insert();
                            stockRecommendLog.setConvertRecommendCar(1);
                            stockRecommendLog.setUpdateTime(LocalDateTime.now());
                            stockRecommendLog.updateById();
                            return;
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(stockRecommends)) {
                    for (CarOmdStockRecommend stockRecommend1 : stockRecommends) {
                        LambdaQueryWrapper<CarBestRecommendStock> stockQ = new LambdaQueryWrapper<>();
                        stockQ.eq(CarBestRecommendStock::getRecommendModelId, stockRecommend1.getStockRecommendModelId())
                                .eq(CarBestRecommendStock::getType, StockTypeEnum.DEALER.getValue());
                        List<CarBestRecommendStock> stocks = bestRecommendStockService.list(stockQ);
                        if (CollectionUtils.isNotEmpty(stocks)) {
                            CarBestRecommendStock oldStock = stocks.get(0);
                            CarBestRecommendStock stock = new CarBestRecommendStock();
                            stock.setStockNum(0l);
                            stock.setBestRecommendId(oldStock.getBestRecommendId());
                            stock.setDealerNetCode(stockRecommendLog.getDealerNetCode());
                            stock.setRecommendModelId(mapStockRecommend.getStockRecommendModelId());
                            stock.setType(StockTypeEnum.DEALER.getValue());
                            stock.setCreateTime(LocalDateTime.now());
                            stock.insert();
                            stockRecommendLog.setConvertRecommendCar(1);
                            stockRecommendLog.setUpdateTime(LocalDateTime.now());
                            stockRecommendLog.updateById();
                            return;
                        }
                    }
                }

                // 转换bestRecommend
                OmdModelDto omdModelDto = new OmdModelDto();
                BeanUtils.copyProperties(mapStockRecommend, omdModelDto);

                BestRecommendCarVo carVo = null;
                try {
                    carVo = bestRecommendToVo(omdModelDto, Constant.MASTER_CHANNEL);
                } catch (Exception e) {
                    transactionManager.rollback(transactionStatus);
                    log.error("库存车转换失败", e);
                }
                if (carVo != null) {
                    CarBestRecommend newBestRecommend = new CarBestRecommend();
                    newBestRecommend.setBestSellRecommendModelId(null);
                    newBestRecommend.setModelLineId(carVo.getModelLine().getModelLineId());
                    newBestRecommend.setSibInterieurId(carVo.getModelLineSibInterieurVo().getSibInterieurId());
                    newBestRecommend.setCreateTime(LocalDateTime.now());
                    newBestRecommend.insert();

                    for (ModelLineOptionVo option : carVo.getOptions()) {
                        CarBestRecommendOption bestRecommendOption = new CarBestRecommendOption();
                        bestRecommendOption.setBestRecommendId(newBestRecommend.getBestRecommendId());
                        bestRecommendOption.setOptionId(option.getOptionId());
                        bestRecommendOption.setOptionCode(option.getOptionCode());
                        bestRecommendOption.setCreateTime(LocalDateTime.now());
                        bestRecommendOptionService.save(bestRecommendOption);
                    }
                    CarBestRecommendStock stock = new CarBestRecommendStock();
                    stock.setStockNum(0l);
                    stock.setBestRecommendId(newBestRecommend.getBestRecommendId());
                    stock.setDealerNetCode(stockRecommendLog.getDealerNetCode());
                    stock.setRecommendModelId(mapStockRecommend.getStockRecommendModelId());
                    stock.setType(StockTypeEnum.DEALER.getValue());
                    stock.setCreateTime(LocalDateTime.now());
                    stock.insert();
                    stockRecommendLog.setConvertRecommendCar(1);
                    stockRecommendLog.setUpdateTime(LocalDateTime.now());
                    stockRecommendLog.updateById();
                } else {
                    transactionManager.rollback(transactionStatus);
                    log.error("库存车转换失败");
                }
            } catch (Exception e) {
                log.error("经销商库存车转换异常", e);
                transactionManager.rollback(transactionStatus);
                throw e;
            } finally {
                if (!transactionStatus.isCompleted()) {
                    transactionManager.commit(transactionStatus);
                }
            }
        });
    }

    @Override
    public void convertStockRecommendCar() {
        List<Long> stockRecommendIds = stockRecommendLogService.listUnhandleRecommend();

        // convert
        stockRecommendIds.forEach(i -> {
            TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);
            try {
                boolean convert = false;
                LambdaQueryWrapper<CarOmdStockRecommendLog> q = new LambdaQueryWrapper<>();
                q.eq(CarOmdStockRecommendLog::getStockRecommendModelId, i)
                        .orderByDesc(CarOmdStockRecommendLog::getOperateTime).last("limit 1");
                CarOmdStockRecommendLog stockRecommendLog = stockRecommendLogService.getOne(q);
                if (stockRecommendLog == null || stockRecommendLog.getConvertRecommendCar().intValue() == 1) {
                    return;
                }
                LambdaQueryWrapper<CarOmdStockRecommend> sq = new LambdaQueryWrapper<>();
                sq.eq(CarOmdStockRecommend::getStockRecommendModelId, stockRecommendLog.getStockRecommendModelId());
                CarOmdStockRecommend stockRecommend = stockRecommendService.getOne(sq);
                if (stockRecommendLog.getOperateType().equals("D")) {
                    if (stockRecommend == null) {
                        stockRecommendLog.setConvertRecommendCar(1);
                        stockRecommendLog.setUpdateTime(LocalDateTime.now());
                        stockRecommendLog.updateById();
                        log.error("经销商库存不存在，{}", stockRecommendLog);
                        return;
                    }
                    stockRecommend.setDelFlag(1);
                    stockRecommend.setUpdateTime(LocalDateTime.now());
                    stockRecommend.updateById();

                    LambdaUpdateWrapper<CarBestRecommendStock> stockU = new LambdaUpdateWrapper<>();
                    stockU.eq(CarBestRecommendStock::getRecommendModelId, stockRecommendLog.getStockRecommendModelId())
                            .eq(CarBestRecommendStock::getDealerNetCode, stockRecommendLog.getDealerNetCode())
                            .set(CarBestRecommendStock::getDelFlag, 1);
                    bestRecommendStockService.update(stockU);
                    stockRecommendLog.setConvertRecommendCar(1);
                    stockRecommendLog.setUpdateTime(LocalDateTime.now());
                    stockRecommendLog.updateById();
                }
                // 去掉相应的权益包
                if(stockRecommendLog.getPrList() != null){
                    if ("G6".equals(stockRecommendLog.getClassCode())) {
                        if (stockRecommendLog.getPrList().startsWith("YEA")){
                            stockRecommendLog.setPrList(stockRecommendLog.getPrList().length() == 3 ? null : stockRecommendLog.getPrList().substring(4));
                        }else {
                            stockRecommendLog.setPrList(stockRecommendLog.getPrList().replace(",YEA", ""));
                        }
                    } else if ("49".equals(stockRecommendLog.getClassCode())) {
                        if (stockRecommendLog.getPrList().startsWith("YEG")){
                            stockRecommendLog.setPrList(stockRecommendLog.getPrList().length() == 3 ? null : stockRecommendLog.getPrList().substring(4));
                        }else {
                            stockRecommendLog.setPrList(stockRecommendLog.getPrList().replace(",YEG", ""));
                        }
                    }
                }
                LambdaQueryWrapper<CarOmdBestRecommend> bestQ = new LambdaQueryWrapper<>();
                bestQ.eq(CarOmdBestRecommend::getModelCode, stockRecommendLog.getModelCode())
                        .eq(CarOmdBestRecommend::getModelYear, stockRecommendLog.getModelYear())
                        .eq(CarOmdBestRecommend::getModelVersion, stockRecommendLog.getModelVersion())
                        .eq(CarOmdBestRecommend::getInteriorCode, stockRecommendLog.getInteriorCode())
                        .eq(CarOmdBestRecommend::getColorCode, stockRecommendLog.getColorCode())
                        .eq(CarOmdBestRecommend::getPrList, stockRecommendLog.getPrList());
                List<CarOmdBestRecommend> bestRecommends = omdBestRecommendService.list(bestQ);
                LambdaQueryWrapper<CarOmdStockRecommend> stocksQ = new LambdaQueryWrapper<>();
                stocksQ.eq(CarOmdStockRecommend::getModelCode, stockRecommendLog.getModelCode())
                        .eq(CarOmdStockRecommend::getModelYear, stockRecommendLog.getModelYear())
                        .eq(CarOmdStockRecommend::getModelVersion, stockRecommendLog.getModelVersion())
                        .eq(CarOmdStockRecommend::getInteriorCode, stockRecommendLog.getInteriorCode())
                        .eq(CarOmdStockRecommend::getColorCode, stockRecommendLog.getColorCode())
                        .eq(CarOmdStockRecommend::getPrList, stockRecommendLog.getPrList());
                List<CarOmdStockRecommend> stockRecommends = stockRecommendService.list(stocksQ);
                if (stockRecommendLog.getOperateType().equals("A")) {
                    if (stockRecommend != null) {
                        stockRecommendLog.setConvertRecommendCar(3);
                        stockRecommendLog.setUpdateTime(LocalDateTime.now());
                        stockRecommendLog.updateById();
                        log.error("经销商库存已存在，{}", stockRecommendLog);
                        return;
                    }
                    stockRecommend = new CarOmdStockRecommend();
                    BeanUtils.copyProperties(stockRecommendLog, stockRecommend);
                    stockRecommend.insert();
                    // 关联已存在的库存车
                    if (CollectionUtils.isNotEmpty(bestRecommends)) {
                        for (CarOmdBestRecommend bestRecommend : bestRecommends) {
                            LambdaQueryWrapper<CarBestRecommendStock> stockQ = new LambdaQueryWrapper<>();
                            stockQ.eq(CarBestRecommendStock::getRecommendModelId, bestRecommend.getBestSellRecommendModelId())
                                    .eq(CarBestRecommendStock::getType, StockTypeEnum.HQ.getValue());
                            List<CarBestRecommendStock> stocks = bestRecommendStockService.list(stockQ);
                            if (CollectionUtils.isNotEmpty(stocks)) {
                                CarBestRecommendStock oldStock = stocks.get(0);
                                CarBestRecommendStock stock = new CarBestRecommendStock();
                                stock.setStockNum(0l);
                                stock.setBestRecommendId(oldStock.getBestRecommendId());
                                stock.setDealerNetCode(stockRecommendLog.getDealerNetCode());
                                stock.setRecommendModelId(stockRecommendLog.getStockRecommendModelId());
                                stock.setType(StockTypeEnum.DEALER.getValue());
                                stock.setCreateTime(LocalDateTime.now());
                                stock.insert();
                                stockRecommendLog.setConvertRecommendCar(1);
                                stockRecommendLog.setUpdateTime(LocalDateTime.now());
                                stockRecommendLog.updateById();
                                return;
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(stockRecommends)) {
                        for (CarOmdStockRecommend stockRecommend1 : stockRecommends) {
                            LambdaQueryWrapper<CarBestRecommendStock> stockQ = new LambdaQueryWrapper<>();
                            stockQ.eq(CarBestRecommendStock::getRecommendModelId, stockRecommend1.getStockRecommendModelId())
                                    .eq(CarBestRecommendStock::getType, StockTypeEnum.DEALER.getValue());
                            List<CarBestRecommendStock> stocks = bestRecommendStockService.list(stockQ);
                            if (CollectionUtils.isNotEmpty(stocks)) {
                                CarBestRecommendStock oldStock = stocks.get(0);
                                CarBestRecommendStock stock = new CarBestRecommendStock();
                                stock.setStockNum(0l);
                                stock.setBestRecommendId(oldStock.getBestRecommendId());
                                stock.setDealerNetCode(stockRecommendLog.getDealerNetCode());
                                stock.setRecommendModelId(stockRecommendLog.getStockRecommendModelId());
                                stock.setType(StockTypeEnum.DEALER.getValue());
                                stock.setCreateTime(LocalDateTime.now());
                                stock.insert();
                                stockRecommendLog.setConvertRecommendCar(1);
                                stockRecommendLog.setUpdateTime(LocalDateTime.now());
                                stockRecommendLog.updateById();
                                return;
                            }
                        }
                    }
                    convert = true;
                }

                if (stockRecommendLog.getOperateType().equals("M")) {
                    stockRecommend = new CarOmdStockRecommend();
                    BeanUtils.copyProperties(stockRecommendLog, stockRecommend);
                    StringBuilder ccUniqueCode = new StringBuilder();
                    ccUniqueCode.append(stockRecommend.getModelCode().split("-")[0]).append("/").append(stockRecommend.getModelYear()).append("/")
                            .append(stockRecommend.getModelVersion()).append("/").append(stockRecommend.getColorCode()).append("/")
                            .append(stockRecommend.getInteriorCode()).append("/");
                    if (stockRecommend.getPrList() != null){
                        String[] prs = stockRecommend.getPrList().split(",");
                        ArrayList<String> prList = new ArrayList<>(Arrays.asList(prs));
                        prList.sort(String::compareTo);
                        ccUniqueCode.append(Strings.join(prList, ','));
                    }
                    stockRecommend.setCcUniqueCode(ccUniqueCode.toString());
                    stockRecommend.insertOrUpdate();
                    // 删掉当前的stock关联
                    LambdaQueryWrapper<CarBestRecommendStock> stockDeleteQuery = new LambdaQueryWrapper<>();
                    stockDeleteQuery.eq(CarBestRecommendStock::getRecommendModelId, stockRecommendLog.getStockRecommendModelId())
                            .eq(CarBestRecommendStock::getType, StockTypeEnum.DEALER.getValue());
                    bestRecommendStockService.remove(stockDeleteQuery);

                    // 关联已存在的库存车
                    if (CollectionUtils.isNotEmpty(bestRecommends)) {
                        for (CarOmdBestRecommend bestRecommend : bestRecommends) {
                            LambdaQueryWrapper<CarBestRecommendStock> stockQ = new LambdaQueryWrapper<>();
                            stockQ.eq(CarBestRecommendStock::getRecommendModelId, bestRecommend.getBestSellRecommendModelId())
                                    .eq(CarBestRecommendStock::getType, StockTypeEnum.HQ.getValue());
                            List<CarBestRecommendStock> stocks = bestRecommendStockService.list(stockQ);
                            if (CollectionUtils.isNotEmpty(stocks)) {
                                CarBestRecommendStock oldStock = stocks.get(0);
                                CarBestRecommendStock stock = new CarBestRecommendStock();
                                stock.setStockNum(0l);
                                stock.setBestRecommendId(oldStock.getBestRecommendId());
                                stock.setDealerNetCode(stockRecommendLog.getDealerNetCode());
                                stock.setRecommendModelId(stockRecommendLog.getStockRecommendModelId());
                                stock.setType(StockTypeEnum.DEALER.getValue());
                                stock.setCreateTime(LocalDateTime.now());
                                stock.insert();
                            }
                            stockRecommendLog.setConvertRecommendCar(1);
                            stockRecommendLog.setUpdateTime(LocalDateTime.now());
                            stockRecommendLog.updateById();
                            return;
                        }
                    }
                    if (CollectionUtils.isNotEmpty(stockRecommends)) {
                        for (CarOmdStockRecommend stockRecommend1 : stockRecommends) {
                            LambdaQueryWrapper<CarBestRecommendStock> stockQ = new LambdaQueryWrapper<>();
                            stockQ.eq(CarBestRecommendStock::getRecommendModelId, stockRecommend1.getStockRecommendModelId())
                                    .eq(CarBestRecommendStock::getType, StockTypeEnum.DEALER.getValue());
                            List<CarBestRecommendStock> stocks = bestRecommendStockService.list(stockQ);
                            if (CollectionUtils.isNotEmpty(stocks)) {
                                CarBestRecommendStock oldStock = stocks.get(0);
                                CarBestRecommendStock stock = new CarBestRecommendStock();
                                stock.setStockNum(0l);
                                stock.setBestRecommendId(oldStock.getBestRecommendId());
                                stock.setDealerNetCode(stockRecommendLog.getDealerNetCode());
                                stock.setRecommendModelId(stockRecommendLog.getStockRecommendModelId());
                                stock.setType(StockTypeEnum.DEALER.getValue());
                                stock.setCreateTime(LocalDateTime.now());
                                stock.insert();
                            }
                            stockRecommendLog.setConvertRecommendCar(1);
                            stockRecommendLog.setUpdateTime(LocalDateTime.now());
                            stockRecommendLog.updateById();
                            return;
                        }
                    }
                    convert = true;
                }

                if (convert) {
                    OmdModelDto omdModelDto = new OmdModelDto();
                    BeanUtils.copyProperties(stockRecommendLog, omdModelDto);
                    BestRecommendCarVo carVo = null;
                    try {
                        carVo = bestRecommendToVo(omdModelDto, Constant.MASTER_CHANNEL);
                    } catch (Exception e) {
                        transactionManager.rollback(transactionStatus);
                        log.error("库存车转换失败", e);
                    }
                    if (carVo != null) {
                        CarBestRecommend newBestRecommend = new CarBestRecommend();
                        newBestRecommend.setBestSellRecommendModelId(null);
                        newBestRecommend.setModelLineId(carVo.getModelLine().getModelLineId());
                        newBestRecommend.setSibInterieurId(carVo.getModelLineSibInterieurVo().getSibInterieurId());
                        newBestRecommend.setCreateTime(LocalDateTime.now());
                        newBestRecommend.insert();

                        for (ModelLineOptionVo option : carVo.getOptions()) {
                            CarBestRecommendOption bestRecommendOption = new CarBestRecommendOption();
                            bestRecommendOption.setBestRecommendId(newBestRecommend.getBestRecommendId());
                            bestRecommendOption.setOptionId(option.getOptionId());
                            bestRecommendOption.setOptionCode(option.getOptionCode());
                            bestRecommendOption.setCreateTime(LocalDateTime.now());
                            bestRecommendOptionService.save(bestRecommendOption);
                        }
                        CarBestRecommendStock stock = new CarBestRecommendStock();
                        stock.setStockNum(0l);
                        stock.setBestRecommendId(newBestRecommend.getBestRecommendId());
                        stock.setDealerNetCode(stockRecommendLog.getDealerNetCode());
                        stock.setRecommendModelId(stockRecommendLog.getStockRecommendModelId());
                        stock.setType(StockTypeEnum.DEALER.getValue());
                        stock.setCreateTime(LocalDateTime.now());
                        stock.insert();
                        stockRecommendLog.setConvertRecommendCar(1);
                    } else {
                        transactionManager.rollback(transactionStatus);
                        stockRecommendLog.setConvertRecommendCar(3);
                    }
                    stockRecommendLog.setUpdateTime(LocalDateTime.now());
                    stockRecommendLog.updateById();
                }
            } catch (Exception e) {
                log.error("经销商库存车转换异常", e);
                transactionManager.rollback(transactionStatus);
                throw e;
            } finally {
                if (!transactionStatus.isCompleted()) {
                    transactionManager.commit(transactionStatus);
                }
            }
        });


    }

    @Override
    @Transactional
    public void syncOmdBestRecommendCar() {
        List<BestRecommendRes> bestRecommendRes = omdService.bestRecommendRes();
        log.info("OMD畅销推荐车数据： {}", bestRecommendRes);
        LambdaUpdateWrapper<CarOmdBestRecommend> omdRecommendUpdate = new LambdaUpdateWrapper<>();
        omdRecommendUpdate.set(CarOmdBestRecommend::getDelFlag, 1).eq(CarOmdBestRecommend::getDelFlag, 0);
        omdBestRecommendService.update(omdRecommendUpdate);
        // update carRecommendStock
        LambdaUpdateWrapper<CarBestRecommendStock> bestRecommendUpdate = new LambdaUpdateWrapper<>();
        bestRecommendUpdate.set(CarBestRecommendStock::getDelFlag, 1)
                .eq(CarBestRecommendStock::getType, StockTypeEnum.HQ.getValue())
                .eq(CarBestRecommendStock::getDelFlag, 0);
        bestRecommendStockService.update(bestRecommendUpdate);
        if (CollectionUtils.isNotEmpty(bestRecommendRes)) {
            handleBestRecommendRes(bestRecommendRes);
        }
    }

    /**
     * 新增是否是库存转换以及ccUniqueCode
     * 查询匹配的库存映射，要求原始的配置转换失败的
     * 查询是否已存在转换的内容，多个的情况删除映射的转换。保留omd转换
     * 没有转换内容，新增转换
     */
    @Override
    @Transactional
    public void syncOmdBestRecommendCarStockMap(List<CarOmdStockMap> stockMaps) {
        List<BestRecommendRes> bestRecommendRes = new ArrayList<>();
        stockMaps.forEach(i->{
            LambdaQueryWrapper<CarOmdBestRecommend> recommendQ = new LambdaQueryWrapper<>();
            recommendQ.eq(CarOmdBestRecommend::getCcUniqueCode, i.getCcUniqueCode())
                    .eq(CarOmdBestRecommend::getModelCode, i.getAccbTypeCode());
            List<CarOmdBestRecommend> omdBestRecommends = omdBestRecommendService.list(recommendQ);
            CarOmdBestRecommend omdBestRecommend = null;
            if (omdBestRecommends.size()>1){
                for(CarOmdBestRecommend r : omdBestRecommends){
                    if (r.getStockMapRecommend() == null || r.getStockMapRecommend().intValue() == 0){
                        omdBestRecommend = r;
                    }
                }
                if (omdBestRecommend == null){
                    omdBestRecommend = omdBestRecommends.get(0);
                }
            }else if (omdBestRecommends.size() == 1){
                omdBestRecommend = omdBestRecommends.get(0);
            }
            if (omdBestRecommend == null){
                omdBestRecommend = new CarOmdBestRecommend();
                BeanUtils.copyProperties(i, omdBestRecommend);
                omdBestRecommend.setStockMapRecommend(1);
                omdBestRecommend.setType(BestRecommendTypeEnum.HQ_RECOMMEND.getValue());
                omdBestRecommend.setCreateTime(LocalDateTime.now());
                omdBestRecommend.insert();
            }else {
                LambdaUpdateWrapper<CarOmdBestRecommend> omdRecommendOn = new LambdaUpdateWrapper<>();
                omdRecommendOn.set(CarOmdBestRecommend::getDelFlag, 0)
                        .eq(CarOmdBestRecommend::getBestSellRecommendModelId, omdBestRecommend.getBestSellRecommendModelId());
                omdBestRecommendService.update(omdRecommendOn);
            }
            BestRecommendRes res = new BestRecommendRes();
            BeanUtils.copyProperties(omdBestRecommend, res);
            bestRecommendRes.add(res);
        });
        log.info("OMD畅销推荐车数据： {}", bestRecommendRes);
        if (CollectionUtils.isNotEmpty(bestRecommendRes)) {
            handleBestRecommendRes(bestRecommendRes);
        }
    }

    private void handleBestRecommendRes(List<BestRecommendRes> bestRecommendRes){
        bestRecommendRes.forEach(i -> {
            CarOmdBestRecommend omdBestRecommend = omdBestRecommendService.getById(i.getBestSellRecommendModelId());
            if (omdBestRecommend != null) {
                LambdaUpdateWrapper<CarOmdBestRecommend> omdRecommendOn = new LambdaUpdateWrapper<>();
                omdRecommendOn.set(CarOmdBestRecommend::getDelFlag, 0).eq(CarOmdBestRecommend::getBestSellRecommendModelId, i.getBestSellRecommendModelId());
                omdBestRecommendService.update(omdRecommendOn);
            } else {
                omdBestRecommend = new CarOmdBestRecommend();
                BeanUtils.copyProperties(i, omdBestRecommend);
                omdBestRecommend.setType(BestRecommendTypeEnum.HQ_RECOMMEND.getValue());
                omdBestRecommend.setCreateTime(LocalDateTime.now());
                omdBestRecommendService.save(omdBestRecommend);
            }
        });
        bestRecommendRes.stream().forEach(i -> {
            try {
                // 关联 carRecommendStock
                LambdaQueryWrapper<CarBestRecommendStock> bestRecommendStockQuery = new LambdaQueryWrapper<>();
                bestRecommendStockQuery.eq(CarBestRecommendStock::getRecommendModelId, i.getBestSellRecommendModelId())
                        .eq(CarBestRecommendStock::getType, StockTypeEnum.HQ.getValue());
                CarBestRecommendStock bestRecommendStock = bestRecommendStockService.getOne(bestRecommendStockQuery);

                LambdaQueryWrapper<CarBestRecommend> bestRecommendQuery = new LambdaQueryWrapper<>();
                bestRecommendQuery.eq(CarBestRecommend::getBestSellRecommendModelId, i.getBestSellRecommendModelId());
                CarBestRecommend bestRecommend = bestRecommendService.getOne(bestRecommendQuery);
                if (bestRecommendStock != null) {
                    bestRecommendStock.setDelFlag(0);
                    bestRecommendStock.updateById();
                } else {
                    // 查询经销商是否有同样的配置，进行关联
                    LambdaQueryWrapper<CarOmdStockRecommend> stocksQ = new LambdaQueryWrapper<>();
                    stocksQ.eq(CarOmdStockRecommend::getModelCode, i.getModelCode())
                            .eq(CarOmdStockRecommend::getModelYear, i.getModelYear())
                            .eq(CarOmdStockRecommend::getModelVersion, i.getModelVersion())
                            .eq(CarOmdStockRecommend::getInteriorCode, i.getInteriorCode())
                            .eq(CarOmdStockRecommend::getColorCode, i.getColorCode())
                            .eq(CarOmdStockRecommend::getPrList, i.getPrList());
                    List<CarOmdStockRecommend> stockRecommends = stockRecommendService.list(stocksQ);
                    if (CollectionUtils.isNotEmpty(stockRecommends)) {
                        for (CarOmdStockRecommend stockRecommend1 : stockRecommends) {
                            LambdaQueryWrapper<CarBestRecommendStock> stockQ = new LambdaQueryWrapper<>();
                            stockQ.eq(CarBestRecommendStock::getRecommendModelId, stockRecommend1.getStockRecommendModelId())
                                    .eq(CarBestRecommendStock::getType, StockTypeEnum.DEALER.getValue());
                            List<CarBestRecommendStock> stocks = bestRecommendStockService.list(stockQ);
                            if (CollectionUtils.isNotEmpty(stocks)) {
                                CarBestRecommendStock oldStock = stocks.get(0);
                                CarBestRecommendStock stock = new CarBestRecommendStock();
                                stock.setStockNum(0l);
                                stock.setBestRecommendId(oldStock.getBestRecommendId());
                                stock.setRecommendModelId(i.getBestSellRecommendModelId());
                                stock.setType(StockTypeEnum.HQ.getValue());
                                stock.setCreateTime(LocalDateTime.now());
                                stock.insert();
                                return;
                            }
                        }
                    }
                    if (bestRecommend == null) {
                        BestRecommendCarVo carVo = bestRecommendToVo(i, Constant.MASTER_CHANNEL);
                        if (carVo != null) {
                            CarBestRecommend newBestRecommend = new CarBestRecommend();
                            newBestRecommend.setBestSellRecommendModelId(i.getBestSellRecommendModelId());
                            newBestRecommend.setModelLineId(carVo.getModelLine().getModelLineId());
                            newBestRecommend.setSibInterieurId(carVo.getModelLineSibInterieurVo().getSibInterieurId());
                            newBestRecommend.setCreateTime(LocalDateTime.now());
                            newBestRecommend.insert();

                            for (ModelLineOptionVo option : carVo.getOptions()) {
                                CarBestRecommendOption bestRecommendOption = new CarBestRecommendOption();
                                bestRecommendOption.setBestRecommendId(newBestRecommend.getBestRecommendId());
                                bestRecommendOption.setOptionId(option.getOptionId());
                                bestRecommendOption.setOptionCode(option.getOptionCode());
                                bestRecommendOption.setCreateTime(LocalDateTime.now());
                                bestRecommendOptionService.save(bestRecommendOption);
                            }
                            CarBestRecommendStock stock = new CarBestRecommendStock();
                            stock.setStockNum(0l);
                            stock.setBestRecommendId(newBestRecommend.getBestRecommendId());
                            stock.setRecommendModelId(newBestRecommend.getBestSellRecommendModelId());
                            stock.setType(StockTypeEnum.HQ.getValue());
                            stock.setCreateTime(LocalDateTime.now());
                            stock.insert();
                        }
                    } else {
                        CarBestRecommendStock stock = new CarBestRecommendStock();
                        stock.setStockNum(0l);
                        stock.setBestRecommendId(bestRecommend.getBestRecommendId());
                        stock.setRecommendModelId(bestRecommend.getBestSellRecommendModelId());
                        stock.setType(StockTypeEnum.HQ.getValue());
                        stock.setCreateTime(LocalDateTime.now());
                        stock.insert();
                    }
                }
            } catch (Exception e) {
                log.error("畅销推荐车数据转化异常", e);
            }
        });
    }

    @Override
    public void syncOmdBestRecommendCarHqVehicle() {
        LambdaQueryWrapper<CarBestRecommendStock> stockQuery = new LambdaQueryWrapper<>();
        stockQuery.eq(CarBestRecommendStock::getType, StockTypeEnum.HQ.getValue()).eq(CarBestRecommendStock::getDelFlag, 0);
        List<CarBestRecommendStock> bestRecommendStock = bestRecommendStockService.list(stockQuery);
        log.debug("bestRecommendStock: {}", bestRecommendStock);
        if (CollectionUtils.isNotEmpty(bestRecommendStock)) {
            bestRecommendStock.forEach(i -> {
                String key = BEST_STOCK_LOCK_PREFIX + String.valueOf(i.getRecommendModelId());
                RLock lock = redissonClient.getLock(key);
                try {
                    lock.lock(2, TimeUnit.MINUTES);
                    CarOmdBestRecommend omdBestRecommend = omdBestRecommendService.getById(i.getRecommendModelId());
                    log.debug("omdStockRecommend: {}", omdBestRecommend);
                    if (omdBestRecommend != null) {
                        HqVehicleBody vehicleBody = new HqVehicleBody();
                        vehicleBody.setBrandCode("A");
                        if (StringUtils.isNotBlank(omdBestRecommend.getModelCode())) {
                            vehicleBody.setModelCode(omdBestRecommend.getModelCode().split("-")[0]);
                        }
                        vehicleBody.setModelYear(omdBestRecommend.getModelYear());
                        vehicleBody.setModelVersion(omdBestRecommend.getModelVersion());
                        vehicleBody.setInteriorCode(omdBestRecommend.getInteriorCode());
                        vehicleBody.setColorCode(omdBestRecommend.getColorCode());
                        vehicleBody.setPrList(omdBestRecommend.getPrList());
                        vehicleBody.setAccbTypeCode(omdBestRecommend.getModelCode());
                        Long stockNum = omdService.queryAudiHqVehicle(vehicleBody);
                        Long appendNum = stockMapService.getStockNumHqOrigin(omdBestRecommend);
                        if (appendNum != null){
                            stockNum += appendNum;
                        }
                        if (stockNum != null) {
                            i.setSyncStockNum(stockNum);
                            i.setUpdateTime(LocalDateTime.now());

                            customSyncService.syncRecommendCustom(i.getRecommendModelId(), i.getType());
                            LambdaQueryWrapper<CarBestRecommendCustomSync> customSyncQuery = new LambdaQueryWrapper<>();
                            customSyncQuery.eq(CarBestRecommendCustomSync::getRecommendModelId, i.getRecommendModelId())
                                    .eq(CarBestRecommendCustomSync::getType, i.getType())
                                    .ne(CarBestRecommendCustomSync::getStatus, 1);
                            Integer notSyncNum = customSyncService.count(customSyncQuery);
                            log.info("sync stock bestRecommendId:{}, recommendModelId:{} syncStock:{}, notSyncNum:{}", i.getBestRecommendId(), i.getRecommendModelId(), stockNum, notSyncNum);
                            i.setStockNum((long) (stockNum.intValue() - notSyncNum.intValue()));
                            i.updateById();
                        }
                    }
                } finally {
                    lock.unlock();
                }
            });
        }
    }

    @Override
    public void syncOmdBestRecommendCarStockVehicle() {
        LambdaQueryWrapper<CarBestRecommendStock> stockQuery = new LambdaQueryWrapper<>();
        stockQuery.eq(CarBestRecommendStock::getType, StockTypeEnum.DEALER.getValue()).eq(CarBestRecommendStock::getDelFlag, 0);
        List<CarBestRecommendStock> bestRecommendStock = bestRecommendStockService.list(stockQuery);
        if (CollectionUtils.isNotEmpty(bestRecommendStock)) {
            bestRecommendStock.forEach(i -> {
                String key = BEST_STOCK_LOCK_PREFIX + String.valueOf(i.getRecommendModelId());
                RLock lock = redissonClient.getLock(key);
                try {
                    lock.lock(2, TimeUnit.MINUTES);
                    CarOmdStockRecommend omdStockRecommend = stockRecommendService.getById(i.getRecommendModelId());
                    if (omdStockRecommend != null) {
                        DealerVehicleBody vehicleBody = new DealerVehicleBody();
                        vehicleBody.setBrandCode("A");
                        if (StringUtils.isNotBlank(omdStockRecommend.getModelCode())) {
                            vehicleBody.setModelCode(omdStockRecommend.getModelCode().split("-")[0]);
                        }
                        vehicleBody.setModelYear(omdStockRecommend.getModelYear());
                        vehicleBody.setModelVersion(omdStockRecommend.getModelVersion());
                        vehicleBody.setInteriorCode(omdStockRecommend.getInteriorCode());
                        vehicleBody.setColorCode(omdStockRecommend.getColorCode());
                        vehicleBody.setPrList(omdStockRecommend.getPrList());
                        vehicleBody.setAccbTypeCode(omdStockRecommend.getModelCode());
                        vehicleBody.setDealerNetCode(omdStockRecommend.getDealerNetCode());
                        Long stockNum = omdService.queryAudiDealerVehicle(vehicleBody);
                        Long appendNum = stockMapService.getStockNumDealerOrigin(omdStockRecommend);
                        if (appendNum != null){
                            stockNum += appendNum;
                        }
                        if (stockNum != null) {
                            i.setUpdateTime(LocalDateTime.now());
                            i.setSyncStockNum(stockNum);

                            customSyncService.syncRecommendCustom(i.getRecommendModelId(), i.getType());
                            LambdaQueryWrapper<CarBestRecommendCustomSync> customSyncQuery = new LambdaQueryWrapper<>();
                            customSyncQuery.eq(CarBestRecommendCustomSync::getRecommendModelId, i.getRecommendModelId())
                                    .eq(CarBestRecommendCustomSync::getType, i.getType())
                                    .ne(CarBestRecommendCustomSync::getStatus, 1);
                            Integer notSyncNum = customSyncService.count(customSyncQuery);
                            log.info("sync stock bestRecommendId:{}, recommendModelId:{} syncStock:{}, notSyncNum:{}", i.getBestRecommendId(), i.getRecommendModelId(), stockNum, notSyncNum);
                            i.setStockNum((long) (stockNum.intValue() - notSyncNum.intValue()));
                            i.updateById();
                        }
                    }
                } finally {
                    lock.unlock();
                }
            });

        }
    }

    @Override
    @Transactional
    public void lockBestRecommendStock(Long ccid, String dealerCode) throws ServiceException {
        boolean hqRecommend = false;
        if (dealerCode == null || dealerCode.equals(appConfig.getHqDealerCode())) {
            dealerCode = appConfig.getHqDealerCode();
            hqRecommend = true;
        }
        CarCustom carCustom = customService.getById(ccid);
        if (carCustom.getBestRecommendId() != null) {
            RLock lock = null;
            try {
                LambdaQueryWrapper<CarBestRecommendStock> bestRecommendQuery = new LambdaQueryWrapper<>();
                bestRecommendQuery.eq(CarBestRecommendStock::getBestRecommendId, carCustom.getBestRecommendId())
                        .eq(CarBestRecommendStock::getDelFlag, 0);
                if (hqRecommend) {
                    bestRecommendQuery.eq(CarBestRecommendStock::getType, StockTypeEnum.HQ.getValue());
                } else {
                    bestRecommendQuery.eq(CarBestRecommendStock::getType, StockTypeEnum.DEALER.getValue())
                            .eq(CarBestRecommendStock::getDealerNetCode, dealerCode);
                }
                bestRecommendQuery.last("for update");
                CarBestRecommendStock bestRecommend = null;
                List<CarBestRecommendStock> bestRecommends = bestRecommendStockService.list(bestRecommendQuery);
                if (CollectionUtils.isNotEmpty(bestRecommends)) {
                    bestRecommend = bestRecommends.get(0);
                }
                //  非总部库存，没有查到库存。去总部库存查
                if (!hqRecommend &&
                        (bestRecommend == null || bestRecommend.getStockNum() == null || bestRecommend.getStockNum() < 1)) {
                    bestRecommendQuery = new LambdaQueryWrapper<>();
                    bestRecommendQuery.eq(CarBestRecommendStock::getBestRecommendId, carCustom.getBestRecommendId())
                            .eq(CarBestRecommendStock::getType, StockTypeEnum.HQ.getValue())
                            .last("for update");
                    bestRecommend = bestRecommendStockService.getOne(bestRecommendQuery);
                }
                // 锁库存
                if (bestRecommend != null) {
                    // 分布式锁精确到modelId
                    String key = BEST_STOCK_LOCK_PREFIX + String.valueOf(bestRecommend.getRecommendModelId());
                    lock = redissonClient.getLock(key);
                    lock.lock(2, TimeUnit.MINUTES);
                    if (bestRecommend.getStockNum() != null && bestRecommend.getStockNum() > 0) {
                        LambdaQueryWrapper<CarStockLog> stockLogQ = new LambdaQueryWrapper<>();
                        stockLogQ.eq(CarStockLog::getCcid, ccid).orderByDesc(CarStockLog::getCreateTime).last("for update");
                        List<CarStockLog> logs = stockLogService.list(stockLogQ);
                        if (CollectionUtils.isNotEmpty(logs)) {
                            if (logs.get(0).getOperate().intValue() == 1) {
                                throw new ServiceException("50000", "当前ccid已存在库存锁定", "推荐车库存异常");
                            }
                        }

                        CarStockLog stockLog = new CarStockLog();
                        stockLog.setCcid(ccid);
                        stockLog.setBestRecommendId(carCustom.getBestRecommendId());
                        stockLog.setCreateTime(LocalDateTime.now());
                        stockLog.setType(bestRecommend.getType());
                        stockLog.setRecommendModelId(bestRecommend.getRecommendModelId());
                        stockLog.setOperate(1);
                        stockLog.insert();

                        bestRecommendStockService.plusStockNum(bestRecommend.getId(), -1);


                    } else {
                        throw new ServiceException("50000", "库存不足", "推荐车库存异常");
                    }
                } else {
                    throw new ServiceException("400401", "没有库存", "推荐车库存异常");
                }
            } finally {
                if (lock != null) {
                    lock.unlock();
                }
            }
        } else {
            throw new ServiceException("400401", "非推荐车不用锁库存", "推荐车库存异常");
        }
    }

    @Override
    @Transactional
    public void unlockBestRecommendStock(Long ccid) throws ServiceException {
        CarCustom carCustom = customService.getById(ccid);
        if (carCustom.getBestRecommendId() != null) {
            RLock lock = null;
            try {

                LambdaQueryWrapper<CarStockLog> stockLogQ = new LambdaQueryWrapper<>();
                stockLogQ.eq(CarStockLog::getCcid, ccid).orderByDesc(CarStockLog::getCreateTime).last("for update");
                List<CarStockLog> logs = stockLogService.list(stockLogQ);
                CarStockLog lastLog = null;
                if (CollectionUtils.isNotEmpty(logs)) {
                    lastLog = logs.get(0);
                    if (lastLog.getOperate().intValue() == 2) {
                        // 已解锁库存
                        return;
                    }
                } else {
                    throw new ServiceException("50000", "未上锁", "推荐车库存异常");
                }
                String key = BEST_STOCK_LOCK_PREFIX + String.valueOf(lastLog.getRecommendModelId());
                lock = redissonClient.getLock(key);
                lock.lock(2, TimeUnit.MINUTES);
                LambdaQueryWrapper<CarBestRecommendStock> bestRecommendQuery = new LambdaQueryWrapper<>();
                bestRecommendQuery.eq(CarBestRecommendStock::getBestRecommendId, carCustom.getBestRecommendId())
                        .eq(CarBestRecommendStock::getRecommendModelId, lastLog.getRecommendModelId())
                        .last("for update");
                CarBestRecommendStock bestRecommend = bestRecommendStockService.getOne(bestRecommendQuery);
                if (bestRecommend == null) {
                    throw new ServiceException("50000", "业务异常", "业务异常，未找到库存车");
                }
                bestRecommendStockService.plusStockNum(bestRecommend.getId(), 1);

                CarStockLog stockLog = new CarStockLog();
                stockLog.setCcid(ccid);
                stockLog.setBestRecommendId(carCustom.getBestRecommendId());
                stockLog.setCreateTime(LocalDateTime.now());
                stockLog.setOperate(2);
                stockLog.setType(bestRecommend.getType());
                stockLog.setRecommendModelId(bestRecommend.getRecommendModelId());
                stockLog.insert();
            } finally {
                if (lock != null) {
                    lock.unlock();
                }
            }
        } else {
            throw new ServiceException("400401", "非推荐车不用锁库存", "推荐车库存异常");
        }
    }

    @Override
    public BestRecommendCarVo bestRecommendToVo(OmdModelDto omdModelDto, String channel) throws Exception {
        omdModelDto = ruleToMapModel(omdModelDto);
        BestRecommendCarVo bestRecommendCarVo = new BestRecommendCarVo();
        List<ModelLineOptionVo> options = new ArrayList<>();
        bestRecommendCarVo.setOptions(options);
        OmdModelParam omdModelParam = new OmdModelParam();
        omdModelParam.setModelYear(omdModelDto.getModelYear());
        omdModelParam.setVersion(omdModelDto.getModelVersion());
        omdModelParam.setColorCode(omdModelDto.getColorCode());
        String modelCode = omdModelDto.getModelCode();
        if (StringUtils.isNotBlank(modelCode) && !modelCode.startsWith("TYPE:")) {
            modelCode = "TYPE:" + modelCode;
        }
        omdModelParam.setAccbTypeCode(modelCode);
        omdModelParam.setChannel(channel);
        ModelLineVo modelLineVo = modelLineService.getModelLineByOmdData(omdModelParam);
        if (modelLineVo == null) {
            log.error("畅销推荐车配置线异常: {}", omdModelParam);
            return null;
        }
        bestRecommendCarVo.setModelLine(modelLineVo);

        String exterieurCode = omdModelDto.getColorCode();
        String category = "COLOR_EXTERIEUR";
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, modelLineVo.getModelLineId(), category);
        log.debug("外饰数据: {}", optionVos);
        List<ModelLineOptionVo> es = optionVos.stream().filter(i -> i.getOptionCode() != null && i.getOptionCode().equals(exterieurCode)).collect(Collectors.toList());
        if (es.size() > 0) {
            es = es.stream().filter(i -> i.getCondition() != null && i.getCondition().intValue() != 0).collect(Collectors.toList());
        }
        if (es.size() != 1) {
            log.error("畅销推荐车定位外饰异常: {}", exterieurCode);
            return null;
        }
        options.add(es.get(0));
        String interieurCode = omdModelDto.getInteriorCode();
        category = "COLOR_INTERIEUR";
        optionVos = modelLineService.modelLineOption(channel, modelLineVo.getModelLineId(), category);
        List<ModelLineOptionVo> inters = optionVos.stream().filter(i -> i.getOptionCode() != null && i.getOptionCode().equals(interieurCode)).collect(Collectors.toList());
        if (inters != null && inters.size() != 1) {
            log.error("畅销推荐车定位内饰异常: {}", interieurCode);
            return null;
        }
        ModelLineOptionVo inter = inters.get(0);
        ModelLineOptionVo sib = null;
        ModelLineOptionVo vos = null;
        ModelLineOptionVo eih = null;
        if (StringUtils.isNotBlank(omdModelDto.getPrList())) {
            String[] prCodes = omdModelDto.getPrList().split(",");
            List<String> modelPrCodes = getPrCodesFromModelCode(modelCode);
            List<String> filteredCodes = new ArrayList<>();
            if (prCodes.length > 0) {
                for (String filterCode : prCodes) {
                    if (!modelPrCodes.contains(filterCode)) {
                        filteredCodes.add(filterCode);
                    }
                }
            }
            prCodes = filteredCodes.toArray(new String[filteredCodes.size()]);
            if (prCodes.length > 0) {
                List<String> notMatchCode = new ArrayList(Arrays.asList(prCodes));
                List<String> prList = new ArrayList(Arrays.asList(prCodes));
                OptionRelateParam optionRelateParam = new OptionRelateParam();
                optionRelateParam.setModelLineId(modelLineVo.getModelLineId());
                optionRelateParam.setOptionRelateCodes(prList);
                optionRelateParam.setRelateTypes(Arrays.asList(OptionRelateEnum.ATTACH.getValue()));
                List<OptionRelDto> relDtos = optionRelateService.listOptionRelDto(optionRelateParam);
                OptionRelateParam CombineRelateParam = new OptionRelateParam();
                CombineRelateParam.setCustomSeriesId(modelLineVo.getCustomSeriesId());
                CombineRelateParam.setOptionRelateCodes(prList);
                CombineRelateParam.setRelateTypes(Arrays.asList(OptionRelateEnum.COMBINE.getValue()));
                relDtos.addAll(optionRelateService.listOptionRelDto(CombineRelateParam));
                Map<String, List<OptionRelDto>> combineMap = new HashMap<>();
                for (OptionRelDto relDto : relDtos) {
                    if (relDto.getRelateType().equals(OptionRelateEnum.COMBINE.getValue())) {
                        if (combineMap.get(relDto.getOptionId()) == null) {
                            combineMap.put(relDto.getOptionId(), new ArrayList<>());
                        }
                        combineMap.get(relDto.getOptionId()).add(relDto);
                    } else if (relDto.getRelateType().equals(OptionRelateEnum.ATTACH.getValue())) {
                        notMatchCode.remove(relDto.getOptionRelateCode());
                        continue;
                    }
                }
                // 按照combine匹配数量排序
                List<String> sortedCombineIds = combineMap.values().stream().filter(i -> CollectionUtils.isNotEmpty(i))
                        .sorted((c, b) -> Integer.valueOf(b.size()).compareTo(c.size()))
                        .map(i -> i.get(0).getOptionId()).collect(Collectors.toList());
                log.debug("combine map: {}", combineMap);
                log.debug("sorted combine ids: {}", sortedCombineIds);
                int sortedCombineIdIndex = 0;
                for (String optionId : sortedCombineIds) {
                    sortedCombineIdIndex++;
                    OptionRelateParam relateParam = new OptionRelateParam();
                    relateParam.setCustomSeriesId(modelLineVo.getCustomSeriesId());
                    relateParam.setOptionId(optionId);
                    relateParam.setRelateTypes(Arrays.asList(OptionRelateEnum.COMBINE.getValue()));
                    List<OptionRelDto> combineRlts = optionRelateService.listOptionRelDto(relateParam);
                    List<String> matchCombineCodes = combineMap.get(optionId).stream().map(i -> i.getOptionRelateCode()).collect(Collectors.toList());
                    log.debug("match combine codes: {}", matchCombineCodes);
                    boolean notMatch = false;
                    for (OptionRelDto relDto : combineRlts) {
                        // 校验匹配的组合关系一致，以及未匹配存在
                        if (!matchCombineCodes.contains(relDto.getOptionRelateCode())
                                || !notMatchCode.contains(relDto.getOptionRelateCode())) {
                            notMatch = true;
                            continue;
                        }
                    }
                    if (notMatch) {
                        continue;
                    }
                    List<ModelLineOptionVo> combinePacket = modelLineService.modelLineOptionQuery(channel, modelLineVo.getModelLineId(), Arrays.asList(optionId));
                    log.debug("combine packet option data: {}", combinePacket);
                    if (CollectionUtils.isEmpty(combinePacket) && sortedCombineIdIndex != sortedCombineIds.size()) {
                        continue;
                    }
                    if (combinePacket.size() != 1) {
                        log.error("畅销推荐车prList异常，没有找到相应pr notMatchCodes: {}", prList);
                        return null;
                    }
                    notMatchCode.removeAll(matchCombineCodes);
                    options.add(combinePacket.get(0));
                }
                if (notMatchCode.size() != 0) {
                    List<ModelLineOptionVo> prVos = modelLineService.modelLineOptionQueryByOptionCodes(channel, modelLineVo.getCustomSeriesId(), modelLineVo.getModelLineId(), notMatchCode);
                    if (modelLineVo.getFromOmd() != null && modelLineVo.getFromOmd() == 1){
                        prVos = prVos.stream().filter(codeVo->StringUtils.isNotBlank(codeVo.getModelLineId())).collect(Collectors.toList());
                    }
                    if (CollectionUtils.isEmpty(prVos)) {
                        log.error("畅销推荐车prList异常，没有找到相应pr prList: {}", prList);
                        return null;
                    } else {
                        Map<String, List<ModelLineOptionVo>> prMap = new HashMap<>();
                        prVos.forEach(i -> {
                            if (prMap.get(i.getOptionCode()) == null) {
                                prMap.put(i.getOptionCode(), new ArrayList<>());
                            }
                            prMap.get(i.getOptionCode()).add(i);
                        });
                        Iterator<String> it = notMatchCode.iterator();
                        while (it.hasNext()) {
                            String code = it.next();
                            List<ModelLineOptionVo> prs = prMap.get(code);
                            ModelLineOptionVo pr = this.validModelLineVo(prs);
                            if (CollectionUtils.isEmpty(prs)) {
                                log.error("畅销推荐车prList异常，没有找到相应pr prList: {}, code: {}", prList, code);
                                return null;
                            }
                            if (StringUtils.isNotBlank(pr.getCategory()) && pr.getCategory().equals("SIB")) {
                                sib = pr;
                            }
                            if (OptionCategoryEnum.SEET.getValue().equals(pr.getCategory())) {
                                vos = pr;
                            }
                            if (OptionCategoryEnum.EIH.getValue().equals(pr.getCategory())) {
                                eih = pr;
                            }
                            if (pr != null) {
                                it.remove();
                                options.add(pr);
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(notMatchCode)) {
                        log.error("畅销推荐车prList异常，没有找到相应pr notMatchCodes: {}", notMatchCode);
                        return null;
                    }
                }
            }
        } else {
            category = "SIB";
            optionVos = modelLineService.modelLineOption(channel, modelLineVo.getModelLineId(), category);
            List<ModelLineOptionVo> sibs = optionVos.stream().filter(i -> i.getOptionCode() != null && i.getStatus().intValue() == 1).collect(Collectors.toList());
            if (sibs != null && sibs.size() != 1) {
                log.error("畅销推荐车定位面料异常");
                return null;
            }
            sib = sibs.get(0);


            optionVos = modelLineService.modelLineOption(channel, modelLineVo.getModelLineId(), OptionCategoryEnum.EIH.getValue());
            List<ModelLineOptionVo> eihs = optionVos.stream().filter(i -> i.getOptionCode() != null && i.getStatus().intValue() == 1).collect(Collectors.toList());
            if (eihs != null && eihs.size() != 1) {
                log.error("畅销推荐车定位饰板异常");
                return null;
            }
            eih = eihs.get(0);

            category = OptionCategoryEnum.SEET.getValue();
            optionVos = modelLineService.modelLineOption(channel, modelLineVo.getModelLineId(), category);
            List<ModelLineOptionVo> seat = optionVos.stream().filter(i -> i.getOptionCode() != null && i.getStatus().intValue() == 1).collect(Collectors.toList());
            // q5e没有座椅，校验降级
            /*if (seat != null && seat.size() != 1){
                log.error("畅销推荐车定位座椅异常");
                return null;
            }*/
            if (CollectionUtils.isNotEmpty(seat)) {
                vos = seat.get(0);
            }

        }

        // 查询选装包里的SIB_CODE/VOS_CODE/EIH_CODE
        String sibCode = null, vosCode = null, eihCode=null;
        for (ModelLineOptionVo o : options) {
            if (o.getCategory() != null && o.getCategory().equals(OptionCategoryEnum.PACKET.getValue())) {
                OptionParamDto paramDto = new OptionParamDto();
                paramDto.setOptionId(o.getOptionId());
                paramDto.setModelLineId(bestRecommendCarVo.getModelLine().getModelLineId());
                paramDto.setChannel(Constant.MASTER_CHANNEL);
                paramDto.setCategory(OptionCategoryEnum.SIB.getValue());
                paramDto.setDelFlag(0);
                List<ModelLineOptionVo> sibItems = modelLineOptionService.listModelLinePacketItem(paramDto);
                if (CollectionUtils.isNotEmpty(sibItems)) {
                    sibCode = sibItems.stream().filter(packetSib->packetSib.getStatus() != 0).collect(Collectors.toList()).get(0).getOptionCode();
                }
                paramDto = new OptionParamDto();
                paramDto.setOptionId(o.getOptionId());
                paramDto.setModelLineId(bestRecommendCarVo.getModelLine().getModelLineId());
                paramDto.setChannel(Constant.MASTER_CHANNEL);
                paramDto.setCategory(OptionCategoryEnum.SEET.getValue());
                paramDto.setDelFlag(0);
                List<ModelLineOptionVo> vosItems = modelLineOptionService.listModelLinePacketItem(paramDto);
                if (CollectionUtils.isNotEmpty(vosItems)) {
                    vosCode = vosItems.stream().filter(packetSeat->packetSeat.getStatus() != 0).collect(Collectors.toList()).get(0).getOptionCode();
                }
                paramDto = new OptionParamDto();
                paramDto.setOptionId(o.getOptionId());
                paramDto.setModelLineId(bestRecommendCarVo.getModelLine().getModelLineId());
                paramDto.setChannel(Constant.MASTER_CHANNEL);
                paramDto.setCategory(OptionCategoryEnum.EIH.getValue());
                paramDto.setDelFlag(0);
                List<ModelLineOptionVo> eihItems = modelLineOptionService.listModelLinePacketItem(paramDto);
                if (CollectionUtils.isNotEmpty(eihItems)) {
                    eihCode = eihItems.stream().filter(packetSeat->packetSeat.getStatus() != 0).collect(Collectors.toList()).get(0).getOptionCode();
                }
            }
        }
        category = "SIB";
        optionVos = modelLineService.modelLineOptionWithOutPriceFilter(channel, modelLineVo.getModelLineId(), category);
        if (sibCode != null) {
            for (ModelLineOptionVo sibOption : optionVos) {
                if (sibOption.getOptionCode().equals(sibCode)) {
                    sib = sibOption;
                    break;
                }
            }
        } else if (sib == null){
            List<ModelLineOptionVo> sibs = optionVos.stream().filter(i -> i.getOptionCode() != null && i.getStatus().intValue() == 1).collect(Collectors.toList());
            if (sibs != null && sibs.size() != 1) {
                log.error("畅销推荐车定位默认面料异常");
                return null;
            }
            sib = sibs.get(0);
        }
        optionVos = modelLineService.modelLineOptionWithOutPriceFilter(channel, modelLineVo.getModelLineId(), OptionCategoryEnum.EIH.getValue());
        if (eihCode != null) {
            for (ModelLineOptionVo eihOption : optionVos) {
                if (eihOption.getOptionCode().equals(eihCode)) {
                    eih = eihOption;
                    break;
                }
            }
        }

        category = OptionCategoryEnum.SEET.getValue();
        optionVos = modelLineService.modelLineOptionWithOutPriceFilter(channel, modelLineVo.getModelLineId(), category);
        if (vosCode != null) {
            for (ModelLineOptionVo vosOption : optionVos) {
                if (vosOption.getOptionCode().equals(vosCode)) {
                    vos = vosOption;
                    break;
                }
            }
        } else {
            List<ModelLineOptionVo> seat = optionVos.stream().filter(i -> i.getOptionCode() != null && i.getCondition().intValue() == 1).collect(Collectors.toList());
            /*if (seat != null && seat.size() != 1) {
                log.error("畅销推荐车定位面料异常");
                return null;
            }*/
            if (CollectionUtils.isNotEmpty(seat)) {
                vos = seat.get(0);
            }
        }

        if(eih != null){
            options.add(eih);
        }
        ModelLineSibInterieurVo param = new ModelLineSibInterieurVo();
        param.setModelLineId(modelLineVo.getModelLineId());
        param.setSibOptionCode(sib.getOptionCode());
        param.setInterieurOptionCode(inter.getOptionCode());
        param.setDelFlag(0);
        param.setChannel(channel);
        List<ModelLineSibInterieurVo> sibInterieurVos = modelLineSibInterieurService.modelLineSibInterieur(param);
        if (sibInterieurVos != null && sibInterieurVos.size() != 1) {
            log.error("畅销推荐车定位内饰面料异常: {}", param);
            return null;
        }
        bestRecommendCarVo.setSibOption(sib);
        bestRecommendCarVo.setVosOption(vos);
        bestRecommendCarVo.setModelLineSibInterieurVo(sibInterieurVos.get(0));
        return bestRecommendCarVo;
    }

    // 按规则处理相应车型
    private OmdModelDto ruleToMapModel(OmdModelDto omdModelDto) {

        // a7l 45黑影特殊规则，omd切换配置线加选装包
        if(omdModelDto.getModelCode().equals("498BZY-MESTGZ2-GPGCPGC-GPT1PT1-GWAEWAE-GYEIYEI-MLSS2C7-MHSW8IZ")
                && omdModelDto.getModelYear().equals("2022") &&omdModelDto.getModelVersion().equals("0")){
            if (StringUtils.isBlank(omdModelDto.getPrList())){
                omdModelDto.setPrList("2C7,8IZ,GZ2,PGC,PT1,WAE,YEI");
            }else if (!Arrays.asList(omdModelDto.getPrList().split(",")).contains("YEI")){
                omdModelDto.setPrList(omdModelDto.getPrList() + ",2C7,8IZ,GZ2,PGC,PT1,WAE,YEI");
            }
            omdModelDto.setModelCode("498BZY-GPGCPGC-GWAEWAE-GYEHYEH");
        }
        if(omdModelDto.getModelCode().equals("498BZY-GPCYPCY-GPGCPGC-GPT1PT1-GWAEWAE-GYEJYEJ-MFRI2V3-MLSE9VS")
                && omdModelDto.getModelYear().equals("2022") &&omdModelDto.getModelVersion().equals("0")){
            if (StringUtils.isBlank(omdModelDto.getPrList())){
                omdModelDto.setPrList("2V3,9VS,PCY,PGC,PT1,WAE,YEJ");
            }else if (!Arrays.asList(omdModelDto.getPrList().split(",")).contains("YEJ")){
                omdModelDto.setPrList(omdModelDto.getPrList() + ",2V3,9VS,PCY,PGC,PT1,WAE,YEJ");
            }
            omdModelDto.setModelCode("498BZY-GPGCPGC-GWAEWAE-GYEHYEH");
        }

        // a7l 2024款 45 黑影规则
        if(omdModelDto.getModelCode().equals("498BZY-MREII56-GPAHPAH-GPCYPCY-GPDWPDW-GPS6PS6-GWA6WA6-GYEBYEB-MEIH5MK-MRAD53D-MLSE9VS")
                && omdModelDto.getModelYear().equals("2024") &&omdModelDto.getModelVersion().equals("0")){
            if (StringUtils.isBlank(omdModelDto.getPrList())){
                omdModelDto.setPrList("5MK,PCY,9VS,PDW,WA6,YEB");
            }else if (!Arrays.asList(omdModelDto.getPrList().split(",")).contains("YEB")){
                omdModelDto.setPrList(omdModelDto.getPrList() + ",5MK,PCY,9VS,PDW,WA6,YEB");
            }
            omdModelDto.setModelCode("498BZY-MREII56-GPAHPAH-GPS6PS6-GYEAYEA-MLRA2PF-MRAD53D");
        }
        return omdModelDto;
    }

    @Override
    public boolean validHqType(BestRecommendCarVo i, Integer type) {
        LambdaQueryWrapper<CarOmdBestRecommend> omdBestRecommendQ = new LambdaQueryWrapper<>();
        omdBestRecommendQ.eq(CarOmdBestRecommend::getBestSellRecommendModelId, i.getRecommendModelId())
                .eq(CarOmdBestRecommend::getType, type);
        return omdBestRecommendService.getOne(omdBestRecommendQ) != null;
    }

    @Override
    public Long bestRecommendStockNum(Long ccid, String dealerCode) throws ServiceException {
        boolean hqRecommend = false;
        if (dealerCode == null || dealerCode.equals(appConfig.getHqDealerCode())) {
            dealerCode = appConfig.getHqDealerCode();
            hqRecommend = true;
        }
        CarCustom carCustom = customService.getById(ccid);
        if (carCustom.getBestRecommendId() != null) {
            LambdaQueryWrapper<CarBestRecommendStock> bestRecommendQuery = new LambdaQueryWrapper<>();
            bestRecommendQuery.eq(CarBestRecommendStock::getBestRecommendId, carCustom.getBestRecommendId())
                    .eq(CarBestRecommendStock::getDelFlag, 0);
            if (hqRecommend) {
                bestRecommendQuery.eq(CarBestRecommendStock::getType, StockTypeEnum.HQ.getValue());
            } else {
                bestRecommendQuery.eq(CarBestRecommendStock::getType, StockTypeEnum.DEALER.getValue())
                        .eq(CarBestRecommendStock::getDealerNetCode, dealerCode);
            }
            CarBestRecommendStock bestRecommend = null;
            List<CarBestRecommendStock> bestRecommends = bestRecommendStockService.list(bestRecommendQuery);
            if (CollectionUtils.isNotEmpty(bestRecommends)) {
                bestRecommend = bestRecommends.get(0);
            }
            //  非总部库存，没有查到库存。去总部库存查
            if (!hqRecommend &&
                    (bestRecommend == null || bestRecommend.getStockNum() == null || bestRecommend.getStockNum() < 1)) {
                bestRecommendQuery = new LambdaQueryWrapper<>();
                bestRecommendQuery.eq(CarBestRecommendStock::getBestRecommendId, carCustom.getBestRecommendId())
                        .eq(CarBestRecommendStock::getType, StockTypeEnum.HQ.getValue())
                        .last("for update");
                bestRecommend = bestRecommendStockService.getOne(bestRecommendQuery);
            }
            if (bestRecommend != null && bestRecommend.getStockNum() != null) {
                return bestRecommend.getStockNum();
            }
        }
        return 0l;
    }

    @Override
    public Long findHqRecommendId(Long bestSellRecommendModelId) {
        CarBestRecommendStock recommendStock = bestRecommendStockService.getOne(Wrappers.<CarBestRecommendStock>lambdaQuery()
                .eq(CarBestRecommendStock::getRecommendModelId, bestSellRecommendModelId)
                .eq(CarBestRecommendStock::getType, StockTypeEnum.HQ.getValue())
                .eq(CarBestRecommendStock::getDelFlag, 0).last("limit 1"));
        if(recommendStock != null){
            return recommendStock.getBestRecommendId();
        }
        return null;
    }

    @Override
    public Long findDealerRecommendId(Long stockRecommendModelId) {
        CarBestRecommendStock recommendStock = bestRecommendStockService.getOne(Wrappers.<CarBestRecommendStock>lambdaQuery()
                .eq(CarBestRecommendStock::getRecommendModelId, stockRecommendModelId)
                .eq(CarBestRecommendStock::getType, StockTypeEnum.DEALER.getValue())
                .eq(CarBestRecommendStock::getDelFlag, 0).last("limit 1"));
        if(recommendStock != null){
            return recommendStock.getBestRecommendId();
        }
        return null;
    }

    private List<String> getPrCodesFromModelCode(String modelCode) {
        List<String> modelPrCodes = new ArrayList<>();
        String[] codes = modelCode.split("-");
        if (codes.length > 1){
            for (int i=1; i<codes.length; i++){
                if (codes[i].length() > 3) {
                    modelPrCodes.add(codes[i].substring(codes[i].length() - 3));
                }
            }
        }
        return modelPrCodes;
    }

    private PriceComputeParam recommendVoToPriceComputeParam(RecommendCarSphereVo vo){
        PriceComputeParam priceComputeParam  = new PriceComputeParam();
        priceComputeParam.setModelLineId(vo.getModelLineId());
        List<String> optionIds = new ArrayList<>();
        optionIds.addAll(vo.getPersonalOptionIds());
        if (StringUtils.isNotBlank(vo.getExterieurOptionId())){
            optionIds.add(vo.getExterieurOptionId());
        }
        if (StringUtils.isNotBlank(vo.getInterieurOptionId())){
            optionIds.add(vo.getInterieurOptionId());
        }
        if (StringUtils.isNotBlank(vo.getSibOptionId())){
            optionIds.add(vo.getSibOptionId());
        }
        if (StringUtils.isNotBlank(vo.getVosOptionId())){
            optionIds.add(vo.getVosOptionId());
        }
        if (StringUtils.isNotBlank(vo.getRadOptionId())){
            optionIds.add(vo.getRadOptionId());
        }

        return priceComputeParam;
    }

    private void codeToOption( String code, String channel, RecommendCarSphereVo vo){
        vo.setPersonalOptionCodes(new ArrayList<>());
        String[] codes = code.split(":");
        LambdaQueryWrapper<CarOption> oq = new LambdaQueryWrapper<>();
        if (codes.length == 1) {
            oq.eq(CarOption::getOptionCode, codes[0]);
        }else if (codes.length == 2){
            oq.eq(CarOption::getOptionCode, codes[1]);
        }
        oq.eq(CarOption::getDelFlag, 0)
                .eq(CarOption::getCustomSeriesId, vo.getCustomSeriesId())
                .eq(CarOption::getChannel, Constant.MASTER_CHANNEL)
                .ne(CarOption::getOptionType, OptionTypeEnum.PACKETITEM.getValue());
        List<CarOption> carOptions = optionService.list(oq);
        if (carOptions != null && carOptions.size()>0){
            CarOption carOption = carOptions.get(0);
            if (carOptions.size()>1){
                List<CarOption> filteredOption = carOptions.stream().filter(o->{
                    CarModelLineOption mlo = modelLineOptionService.lambdaQuery().eq(CarModelLineOption::getOptionId, o.getOptionId())
                            .eq(CarModelLineOption::getModelLineId, vo.getModelLineId()).ne(CarModelLineOption::getStatus, 0)
                            .eq(CarModelLineOption::getChannel, Constant.MASTER_CHANNEL).one();
                    return mlo != null;
                }).collect(Collectors.toList());
                if (filteredOption != null && filteredOption.size()>0){
                    carOption = filteredOption.get(0);
                }
            }
            switch (carOption.getCategory()){
                case "COLOR_EXTERIEUR":
                    vo.setExterieurOptionId(carOption.getOptionId());
                    vo.setExterieurOptionCode(carOption.getOptionCode());
                    vo.setHeadImageUrl(configImageService.getCcHeadImageUrl(channel, vo.getModelLineId(), carOption.getOptionCode()));
                    break;
                case "COLOR_INTERIEUR":
                    vo.setInterieurOptionId(carOption.getOptionId());
                    vo.setInterieurOptionCode(carOption.getOptionCode());
                    break;
                case "RAD":
                    vo.setRadOptionId(carOption.getOptionId());
                    vo.setRadOptionCode(carOption.getOptionCode());
                    break;
                case "VOS":
                    vo.setVosOptionId(carOption.getOptionId());
                    vo.setVosOptionCode(carOption.getOptionCode());
                    break;
                case "SIB":
                    vo.setSibOptionId(carOption.getOptionId());
                    vo.setSibOptionCode(carOption.getOptionCode());
                    break;
                default:
                    vo.getPersonalOptionIds().add(carOption.getOptionId());
                    vo.getPersonalOptionCodes().add(carOption.getOptionCode());
            }
        }
        if (StringUtils.isNotBlank(vo.getInterieurOptionId()) && StringUtils.isNotBlank(vo.getSibOptionId())){
            LambdaQueryWrapper<CarSibInterieur> siQ = new LambdaQueryWrapper<>();
            siQ.eq(CarSibInterieur::getInterieurOptionId, vo.getInterieurOptionId())
                    .eq(CarSibInterieur::getSibOptionId, vo.getSibOptionId())
                    .eq(CarSibInterieur::getDelFlag, 0)
                    .eq(CarSibInterieur::getChannel, Constant.MASTER_CHANNEL);
            List<CarSibInterieur> sibInterieurs = sibInterieurService.list(siQ);
            if (sibInterieurs != null && sibInterieurs.size()>0){
                vo.setSibInterieurOptionId(sibInterieurs.get(0).getSibInterieurId());
                vo.setSibInterieurOptionCode(sibInterieurs.get(0).getSibInterieurCode());
            }
        }
    }

    private ModelLineOptionVo validModelLineVo(List<ModelLineOptionVo> vos){
        if(CollectionUtils.isEmpty(vos)){
            return null;
        }
        for(ModelLineOptionVo vo : vos){
            if ("personal".equals(vo.getOptionType())){
                return vo;
            }
        }
        for(ModelLineOptionVo vo : vos){
            if (vo.getStatus() != null && vo.getStatus().intValue() == 1){
                return vo;
            }
        }
        for(ModelLineOptionVo vo : vos){
            if (vo.getStatus() != null && vo.getStatus().intValue() == 2){
                return vo;
            }
        }
        return vos.get(0);
    }

}
