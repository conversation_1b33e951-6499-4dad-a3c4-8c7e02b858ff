<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarEnergySystemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarEnergySystem">
        <id column="id" property="id" />
        <result column="energy_system" property="energySystem" />
        <result column="image_url" property="imageUrl" />
        <result column="custom_series_id" property="customSeriesId" />
        <result column="channel" property="channel" />
        <result column="engine" property="engine" />
        <result column="weight" property="weight" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, energy_system, image_url, custom_series_id, channel, engine, weight, create_time, update_time, del_flag
    </sql>

</mapper>
