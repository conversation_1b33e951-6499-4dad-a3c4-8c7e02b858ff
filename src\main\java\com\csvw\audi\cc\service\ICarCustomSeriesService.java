package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.dto.CustomSeriesDto;
import com.csvw.audi.cc.entity.dto.CustomSeriesParam;
import com.csvw.audi.cc.entity.dto.SeriesParamDto;
import com.csvw.audi.cc.entity.po.CarCustomSeries;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.vo.CustomSeriesVo;
import com.csvw.audi.cc.entity.vo.SeriesOptionVo;

import java.util.List;

/**
 * <p>
 * 自定义车系 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface ICarCustomSeriesService extends IService<CarCustomSeries> {

    List<CarCustomSeries> listCustomSeries(String channel, CustomSeriesParam customSeriesCode) throws Exception;

    CustomSeriesDto getCustomSeriesDto(String channel, String customSeriesId);

    List<CustomSeriesVo> listCustomSeriesVo(SeriesParamDto paramDto) throws Exception;

    List<SeriesOptionVo> seriesOptions(String channel, String customSeriesId);
}
