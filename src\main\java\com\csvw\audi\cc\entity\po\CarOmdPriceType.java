package com.csvw.audi.cc.entity.po;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 配置线价格
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarOmdPriceType对象", description="配置线价格")
public class CarOmdPriceType extends Model<CarOmdPriceType> {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "accb配置线编码")
    private String accbTypeCode;

    @ApiModelProperty(value = "价格版本号")
    private String priceVersion;

    @ApiModelProperty(value = "车型编码")
    private String modelCode;

    @ApiModelProperty(value = "车型版本号")
    private String modelVersion;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "价格类型")
    private String priceType;

    @ApiModelProperty(value = "折扣")
    private BigDecimal discountAmount;

    @ApiModelProperty(value = "过期时间")
    private LocalDate expireDate;

    @ApiModelProperty(value = "车型年款")
    private String modelYear;

    @ApiModelProperty(value = "有效时间")
    private LocalDate effectiveDate;

    @ApiModelProperty(value = "权重")
    private String brandCode;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
