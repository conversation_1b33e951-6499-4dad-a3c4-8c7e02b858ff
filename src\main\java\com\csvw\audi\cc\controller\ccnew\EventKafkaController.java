package com.csvw.audi.cc.controller.ccnew;


import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.common.utils.CcEncryptUtils;
import com.csvw.audi.cc.config.AppConfig;
import com.csvw.audi.cc.entity.dto.AmsQueryDto;
import com.csvw.audi.cc.entity.dto.ModelParamDto;
import com.csvw.audi.cc.entity.dto.OptionRelDto;
import com.csvw.audi.cc.entity.dto.OptionRelateParam;
import com.csvw.audi.cc.entity.dto.omd.AmsModelQueryRecordParam;
import com.csvw.audi.cc.entity.dto.omd.ModelQueryBody;
import com.csvw.audi.cc.entity.dto.omd.ModelQueryRes;
import com.csvw.audi.cc.entity.enumeration.OptionCategoryEnum;
import com.csvw.audi.cc.entity.po.CarModelLineSpecialOption;
import com.csvw.audi.cc.entity.vo.AmsModelQueryVo;
import com.csvw.audi.cc.entity.vo.AmsQueryVo;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.entity.vo.ModelLineVo;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.feign.AudiServiceAdapter;
import com.csvw.audi.cc.service.ICarConfigImageService;
import com.csvw.audi.cc.service.ICarModelLineService;
import com.csvw.audi.cc.service.ICarModelLineSpecialOptionService;
import com.csvw.audi.cc.service.ICarOptionRelateService;
import com.csvw.audi.cc.service.impl.OmdServiceImpl;
import feign.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


@RestController
@RequestMapping( value = {"/api/v1/cc/public/event"})
@Slf4j
@Api(tags = "埋点事件")
public class EventKafkaController extends BaseController {

    @Autowired
    @Qualifier(value = "eventKafkaTemplate")
    private KafkaTemplate<String, String> eventKafkaTemplate;

    @Value("${event-kafka.event-topic}")
    private String eventTopic;

    ExecutorService executorService = Executors.newFixedThreadPool(5);

    @PostMapping(value = {"/push"})
    @ApiOperation("事件推送")
    public AjaxMessage<Object> pushEvent(@Validated @RequestBody JSONObject jsonObject) throws Exception {
        log.debug("事件推送：{}", jsonObject);
        eventKafkaTemplate.send(eventTopic, jsonObject.toJSONString());
        return new AjaxMessage<>("00", "成功", null);
    }



}

