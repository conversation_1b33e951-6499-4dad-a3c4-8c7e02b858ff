package com.csvw.audi.cc.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CustomSeriesDto {

    private String id;

    @ApiModelProperty(value = "自定义车系id")
    private String customSeriesId;

    @ApiModelProperty(value = "车系code")
    private String customSeriesCode;

    @ApiModelProperty(value = "车系code")
    private String customSeriesName;

    @ApiModelProperty(value = "车系id")
    private String seriesId;

    @ApiModelProperty(value = "车系code")
    private String seriesCode;

    @ApiModelProperty(value = "车系名称")
    private String seriesName;

    @ApiModelProperty(value = "车系图片")
    private String imageUrl;

    @ApiModelProperty(value = "车系分类，sedan: 轿车，suv：越野")
    private String classification;

    @ApiModelProperty(value = "默认配置")
    private String defaultConfig;

    @ApiModelProperty(value = "accb车系code")
    private String accbModelCode;

    @ApiModelProperty(value = "accb车型ID")
    private String accbModelId;

    private String omdSeriesCode;

}
