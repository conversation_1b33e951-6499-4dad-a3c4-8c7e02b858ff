package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 上汽大众奥迪SVCD消息同步日志
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SvcdSyncLog对象", description="上汽大众奥迪SVCD消息同步日志")
public class SvcdSyncLog extends Model<SvcdSyncLog> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "数据id")
      @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "接收的json信息")
    private String data;

    @ApiModelProperty(value = "处理状态（1:未处理,2:处理成功,3:处理失败）")
    private Integer status;

    @ApiModelProperty(value = "其他信息")
    private String msg;

    @ApiModelProperty(value = "kafka offset")
    private String kafkaOffset;

    @ApiModelProperty(value = "kafka partition")
    private String kafkaPartition;

    @ApiModelProperty(value = "kafka topic")
    private String kafkaTopic;

    @ApiModelProperty(value = "执行时间（毫秒）")
    private Long executionTime;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
