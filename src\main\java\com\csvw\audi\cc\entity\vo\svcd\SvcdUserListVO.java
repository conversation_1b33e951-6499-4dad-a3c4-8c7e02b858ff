package com.csvw.audi.cc.entity.vo.svcd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("代理商人员查询请求模型")
public class SvcdUserListVO {
    @ApiModelProperty("工作状态 1：在岗，0：无岗，2：离职")
    private Integer workStatus;
    @ApiModelProperty("人员状态 0: 停用 1：启用")
    private Integer status;
    @ApiModelProperty("所属渠道商 网络代码")
    private String dealerCode;
    @ApiModelProperty("岗位编码")
    private String positionCode;
    @ApiModelProperty("是否为正式岗位 1：是 0：否")
    private Integer checkStatus;

    private List<Integer> workStatusList;
}
