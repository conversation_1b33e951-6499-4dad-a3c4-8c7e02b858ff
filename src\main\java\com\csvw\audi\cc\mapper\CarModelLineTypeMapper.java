package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.dto.*;
import com.csvw.audi.cc.entity.po.CarModelLineType;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csvw.audi.cc.entity.vo.ModelLineVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 车辆ABC类配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
public interface CarModelLineTypeMapper extends BaseMapper<CarModelLineType> {

    String findBeforeCheckType(ModelLineTypeDto modelLineTypeDto);

    ModelTypeResultDto findType(ModelLineTypeDto modelLineTypeDto);

    List<TypePrs> queryTypePrs(TypePrListQueryDto queryDto);

    Set<String> queryTypeIdsOfA(ModelLineTypeDto typeParam);

    List<String> queryComposeTypeIdsOfA(TypePrListQueryDto queryDto);

    List<ModelClassify> queryModelClassify();

    List<ModelClassify> queryModelClassifyByParam(ModelLineTypeDto typeParam);

    void offlineTypeOption(@Param("lastTime") LocalDateTime lastTime);

    void offlineType(@Param("lastTime") LocalDateTime lastTime);

    void flushErrorTypeOptionData();

    void flushErrorTypeData();

    List<String> listSeats(ModelLineTypeDto typeDto);
}
