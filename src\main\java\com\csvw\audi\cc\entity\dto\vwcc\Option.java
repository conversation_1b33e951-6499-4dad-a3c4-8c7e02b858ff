package com.csvw.audi.cc.entity.dto.vwcc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import java.math.BigDecimal;
import java.util.List;

@Data
@Alias("ccOption")
public class Option {
    private String customOptionId;
    private String optionType;
    private String optionSubType;
    private Integer optionWeight;
    private String isNecessary;
    @ApiModelProperty("选装名称")
    private String optionNameCn;
    private String optionNameEn;
    private String optionDesc;
    @ApiModelProperty("选装编码")
    private String optionCode;
    private Object optionPrice;
    @ApiModelProperty("优惠金额")
    private Object discount;
    private String priceFlag;
    private String priceLabel;
    private String customOptionCode;
    @ApiModelProperty("选装图片地址")
    private String imageUrl;
    private String optionGroup;
    @ApiModelProperty("选装类型编码")
    private String optionClassification;
    private String optionClassificationName;
    private String optionClassification2nd;
    private String optionClassification2ndName;
    private String disStatus;
    private String reportType;
    private List<OptionDetail> optionDetailVOs;
}
