package com.csvw.audi.cc.entity.po;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarCustomOptionSnapshot对象", description="")
public class CarCustomOptionSnapshot extends Model<CarCustomOptionSnapshot> {

    private static final long serialVersionUID=1L;

      @TableId(value = "custom_option_id", type = IdType.ASSIGN_ID)
    private Long customOptionId;

    private Long snapshotId;

    private Long ccid;

    @ApiModelProperty(value = "配置项编码")
    private String code;

    @ApiModelProperty(value = "配置项类型")
    private String category;

    @ApiModelProperty(value = "配置项描述")
    private String description;

    @ApiModelProperty(value = "配置项id")
    private String optionId;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;


    @Override
    protected Serializable pkVal() {
        return this.customOptionId;
    }

}
