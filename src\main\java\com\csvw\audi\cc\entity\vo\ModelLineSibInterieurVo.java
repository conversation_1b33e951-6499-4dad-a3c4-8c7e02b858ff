package com.csvw.audi.cc.entity.vo;

import com.csvw.audi.cc.entity.dto.OptionRelDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
public class ModelLineSibInterieurVo implements Serializable {

    @ApiModelProperty(value = "内饰颜色面料id")
    private String sibInterieurId;

    @ApiModelProperty(value = "内饰面料Code")
    private String sibInterieurCode;

    @ApiModelProperty(value = "内饰面料类别码")
    private String sibInterieurCategory;

    @ApiModelProperty(value = "面料id")
    private String sibOptionId;

    @ApiModelProperty(value = "面料编码")
    private String sibOptionCode;

    @ApiModelProperty(value = "面料类型")
    private String sibOptionCategory;

    @ApiModelProperty(value = "面料名称")
    private String sibName;

    @ApiModelProperty(value = "内饰id")
    private String interieurOptionId;

    @ApiModelProperty(value = "内饰编码")
    private String interieurOptionCode;

    @ApiModelProperty(value = "内饰类型")
    private String interieurOptionCategory;

    @ApiModelProperty(value = "内饰名称")
    private String interieurName;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "装备图片")
    private String imageUrl;

    @ApiModelProperty(value = "装备详情图片")
    private String imageUrlDetail;

    @ApiModelProperty(value = "装备列表图片")
    private String imageUrlList;

    @ApiModelProperty(value = "默认配置项")
    private Integer defaultConfig;

    @ApiModelProperty(value = "配置线id")
    @JsonIgnore
    private String modelLineId;

    @ApiModelProperty(value = "配置线配置项状态（0:无，1:标准装备，2:可选装备, 3:条件可选）")
    private Integer status;

    @JsonIgnore
    private String channel;

    @JsonIgnore
    private Integer DelFlag;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    private Set<String> typeIdsOfA = new HashSet<>();

    @ApiModelProperty
    private List<OptionRelDto> sibInterieurRelates;


}
