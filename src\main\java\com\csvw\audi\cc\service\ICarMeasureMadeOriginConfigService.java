package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.po.CarMeasureMadeOriginConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.exception.ServiceException;

/**
 * <p>
 * 半订制化车辆原始配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
public interface ICarMeasureMadeOriginConfigService extends IService<CarMeasureMadeOriginConfig> {

    void convertMeasureMade() throws Exception;

    void syncOmdMeasureSemiCustomizationModel();

    void handleMeasureCcUniqueCode();

    String ccMatchMeasure(Long ccid) throws Exception;

    void normalConfigToMeasure(String userId, String key) throws Exception;

    void handleMeasureZhuMengYef();

    CarMeasureMadeOriginConfig measureOriginConfigByMeasureId(Long measureId);
}
