package com.csvw.audi.cc.entity.dto.okapi;

import lombok.Data;

import java.util.List;

@Data
public class LabelConfigItemResDto {
    private String brandCode;
    private String subBrandCode;
    private String modelUnicode;
    private String modelUnicodeShort;
    private List<LabelResDto> children;

    @Data
    public static class LabelResDto {
        private String familyCode;
        private String featureCode;
        private String externalFeatureNameZh;
        private String externalFeatureNameEn;
        private String featurePrice;
        private Integer featureWeight;
        private List<MaterialInfo> materialList;
    }

    @Data
    public static class MaterialInfo{
        private String materialName;
        private String materialType;
        private String materialUrl;
        private String materialDesc;
        private String materialRange;
        private String materialSize;
        private String materialHeight;
        private String materialWidth;
    }

}
