package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.common.utils.ChannelDataUtils;
import com.csvw.audi.cc.entity.dto.OptionParamDto;
import com.csvw.audi.cc.entity.dto.SibInterieurQueryDto;
import com.csvw.audi.cc.entity.po.CarModelLineSibInterieur;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.entity.vo.ModelLineSibInterieurVo;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.mapper.CarModelLineSibInterieurMapper;
import com.csvw.audi.cc.service.ICarModelLineOptionService;
import com.csvw.audi.cc.service.ICarModelLineSibInterieurService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csvw.audi.cc.service.ICarOmdPriceTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 配置线内饰面料关联 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-19
 */
@Service
public class CarModelLineSibInterieurServiceImpl extends ServiceImpl<CarModelLineSibInterieurMapper, CarModelLineSibInterieur> implements ICarModelLineSibInterieurService {

    @Autowired
    private CarModelLineSibInterieurMapper sibInterieurMapper;

    @Autowired
    private ICarOmdPriceTypeService priceTypeService;

    @Autowired
    private ICarModelLineOptionService modelLineOptionService;

    @Override
    public List<ModelLineSibInterieurVo> modelLineSibInterieur(String channel, String modelLineId, String sibInterieurId) throws Exception {
        SibInterieurQueryDto param = new SibInterieurQueryDto();
        param.setModelLineId(modelLineId);
        param.setSibInterieurId(sibInterieurId);
        param.setDelFlag(0);
        param.setChannel(channel);
        List<ModelLineSibInterieurVo> sibInterieurVos = sibInterieurMapper.queryModelLineSibInterieur(param);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            sibInterieurVos = ChannelDataUtils.channelData(sibInterieurVos, ModelLineSibInterieurVo.class, channel, "sibInterieurId", false);
        }

        /*sibInterieurVos = sibInterieurVos.stream().map(i->{
            try {
                i.setPrice(priceTypeService.optionPrice(modelLineId, i.getSibOptionCode(), "SIB"));
            } catch (ServiceException e) {
                i.setPrice(null);
            }
            return i;
        }).filter(i->priceFilter(i)).collect(Collectors.toList());*/

        sibInterieurVos.forEach(i-> {
            try {
                i.setPrice(priceTypeService.optionPrice(modelLineId, i.getSibOptionCode(), "SIB"));
            } catch (ServiceException e) {
                i.setPrice(null);
            }
        });
        return sibInterieurVos;
    }


    @Override
    public List<ModelLineSibInterieurVo> modelLineSibInterieur(ModelLineSibInterieurVo param) throws NoSuchFieldException, IllegalAccessException {
        String channel = param.getChannel();
        List<ModelLineSibInterieurVo> sibInterieurVos = sibInterieurMapper.listModelLineSibInterieur(param);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            sibInterieurVos = ChannelDataUtils.channelData(sibInterieurVos, ModelLineSibInterieurVo.class, channel, "sibInterieurId", false);
        }
        sibInterieurVos.forEach(i-> {
            try {
                i.setPrice(priceTypeService.optionPrice(param.getModelLineId(), i.getSibOptionCode(), "SIB"));
            } catch (ServiceException e) {
                i.setPrice(null);
            }
        });
        return sibInterieurVos;
    }

    @Override
    public List<ModelLineSibInterieurVo> modelLineSibInterieur(SibInterieurQueryDto param) throws NoSuchFieldException, IllegalAccessException {
        String channel = param.getChannel();
        List<ModelLineSibInterieurVo> sibInterieurVos = sibInterieurMapper.queryModelLineSibInterieur(param);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            sibInterieurVos = ChannelDataUtils.channelData(sibInterieurVos, ModelLineSibInterieurVo.class, channel, "sibInterieurId", false);
        }
        sibInterieurVos.forEach(i-> {
            try {
                i.setPrice(priceTypeService.optionPrice(param.getModelLineId(), i.getSibOptionCode(), "SIB"));
            } catch (ServiceException e) {
                i.setPrice(null);
            }
        });
        return sibInterieurVos;
    }

    private boolean priceFilter(ModelLineSibInterieurVo sibInterieur){
        // 标装不算价格
        OptionParamDto optionParamDto = new OptionParamDto();
        optionParamDto.setModelLineId(sibInterieur.getModelLineId());
        optionParamDto.setOptionId(sibInterieur.getSibOptionId());
        optionParamDto.setChannel(Constant.MASTER_CHANNEL);
        optionParamDto.setDelFlag(0);
        List<ModelLineOptionVo> optionVos = modelLineOptionService.modelLineOptionQuery(optionParamDto);
        if (optionVos == null || optionVos.size() != 1){
           return false;
        }
        ModelLineOptionVo optionVo = optionVos.get(0);
        if(optionVo.getStatus() != null && optionVo.getStatus().intValue() == 1) {
            sibInterieur.setPrice(null);
            return true;
        } else if(sibInterieur.getPrice() == null){
            return false;
        }
        return true;
    }
}
