package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 半订制化车辆配置选装
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarMeasureMadeConfigOption对象", description="半订制化车辆配置选装")
public class CarMeasureMadeConfigOption extends Model<CarMeasureMadeConfigOption> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "半订制车辆id")
      @TableId(value = "measure_option_id", type = IdType.ASSIGN_ID)
    private Long measureOptionId;

    @ApiModelProperty(value = "半订制车辆id")
    private Long measureId;

    @ApiModelProperty(value = "选装id")
    private String optionId;

    @ApiModelProperty(value = "选装id")
    private String optionCode;


    @Override
    protected Serializable pkVal() {
        return this.measureOptionId;
    }

}
