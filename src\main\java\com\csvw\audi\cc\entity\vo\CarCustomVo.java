package com.csvw.audi.cc.entity.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.csvw.audi.cc.entity.enumeration.CcInvalidReasonEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CarCustomVo {
    private String ccid;
    private String customSeriesId;
    private Object modelLinePrice;
    private String modelLineId;
    private String sibInterieurId;
    private Object totalPrice;
    private BigDecimal sibInterieurPrice;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime configTime;
    private List<CarCustomOptionVo> customOptionVoList;

    @ApiModelProperty(value = "车系code")
    private String customSeriesCode;
    @ApiModelProperty(value = "配置单是否有效，0:失效，1:有效")
    private Integer valid;
    private String invalidReason;
    @ApiModelProperty(value = "配置单更新标识，0:无更新，1:有更新")
    private Integer updateFlag;

    @ApiModelProperty(value = "配置单更新内容")
    private String updateContent;

    @ApiModelProperty(value = "车辆清单类型")
    private String classify;

    @ApiModelProperty(value = "预计交付周期")
    private String estimateDelivery;
}
