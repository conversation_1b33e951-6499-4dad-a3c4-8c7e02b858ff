package com.csvw.audi.cc.controller;


import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.entity.dto.AudiUserResp;
import com.csvw.audi.cc.entity.dto.CustomSeriesDto;
import com.csvw.audi.cc.entity.dto.ModelParamDto;
import com.csvw.audi.cc.entity.dto.vwcc.CarCustomDetail;
import com.csvw.audi.cc.entity.po.CarCustom;
import com.csvw.audi.cc.entity.po.CarShoppingCart;
import com.csvw.audi.cc.entity.vo.CarCustomVo;
import com.csvw.audi.cc.entity.vo.CarShoppingCartParam;
import com.csvw.audi.cc.entity.vo.CarShoppingCartVo;
import com.csvw.audi.cc.entity.vo.ModelLineVo;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.feign.AudiBBSFeignService;
import com.csvw.audi.cc.feign.AudiTaskFeign;
import com.csvw.audi.cc.feign.CopProdQueryFeign;
import com.csvw.audi.cc.service.IAudiAsyncTaskAdapterServiceHelper;
import com.csvw.audi.cc.service.ICarCustomService;
import com.csvw.audi.cc.service.ICarShoppingCartService;
import com.csvw.sx.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 购物车 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Slf4j
@Api(tags = "大订-购物车")
@RestController
@RequestMapping("/api/v1/carShoppingCart")
public class CarShoppingCartController extends BaseController {

    @Autowired
    private ICarCustomService carCustomService;

    @Autowired
    private ICarShoppingCartService carShoppingCartService;

    @Autowired
    private CopProdQueryFeign copProdQueryFeign;

    @Autowired
    private IAudiAsyncTaskAdapterServiceHelper audiAsyncTaskAdapterServiceHelper;

    @Autowired
    private AudiBBSFeignService audiBBSFeignService;

    @Autowired
    private AudiTaskFeign audiTaskFeign;


    @ApiOperation("获取购物车列表")
    @GetMapping("/getCarShoppingCartList")
    public AjaxMessage<List<CarShoppingCartVo>> getCarShoppingCartList(@RequestHeader(value = "X-User-Id", required = false) String userId) {
        List<CarShoppingCartVo> list = carShoppingCartService.getCarShoppingCartList(userId);
        return new AjaxMessage<>("00", "成功", list);
    }

    @ApiOperation("获取购物车列表")
    @GetMapping("/getCarShoppingCartListV2")
    public AjaxMessage<List<CarShoppingCartVo>> getCarShoppingCartListV2(@RequestHeader(value = "X-User-Id", required = false) String userId) {
        List<CarShoppingCartVo> list = carShoppingCartService.getCarShoppingCartList(userId);
        return new AjaxMessage<>("00", "成功", list);
    }

    @ApiOperation("添加至购物车配置")
    @PostMapping("/addCarShoppingCart")
    public AjaxMessage<CarShoppingCart> addCarShoppingCart(
            @RequestHeader(value = "X-User-Id") String userId,
            @RequestParam String ccid, String skuid, String invitationCode, String shoppingCartId) {
        log.info("添加至购物车 userId:" + userId + ", ccid:" + ccid + ", invitationCode:" + invitationCode + ", shoppingCartId:" + shoppingCartId);
        CarCustom carCustom = carCustomService.getById(ccid);
        if (carCustom == null) {
            return new AjaxMessage<>("40004", "配置不存在：ccid", null);
        }

        if (skuid == null || "".equals(skuid)) {
            CarCustomVo carCustomVo = carCustomService.a7OrQ5Byccid(ccid);
            if (carCustomVo == null || StringUtil.isEmpty(carCustomVo.getCustomSeriesCode())) {
                log.info("添加至购物车配置，获取 skuid 失败，无 customSeriesCode，ccid:" + ccid);
            }
            JSONObject resultJson = copProdQueryFeign.selectProdCar(carCustomVo.getCustomSeriesCode(), 1);
            JSONObject dataJson = resultJson.getJSONObject("data");
            log.info("添加至购物车配置，获取 skuid " + resultJson);
            if (dataJson == null) {
                log.info("添加至购物车配置，获取 skuid，无数据");
            } else {
                skuid = dataJson.getString("prodSkuId");
            }
        }

        CarShoppingCartParam carShoppingCartParam = new CarShoppingCartParam();
        carShoppingCartParam.setUserId(userId);
        carShoppingCartParam.setCcid(ccid);
        carShoppingCartParam.setSkuid(skuid);
        carShoppingCartParam.setInvitationCode(invitationCode);
        carShoppingCartParam.setShoppingCartId(shoppingCartId);
        carShoppingCartParam.setAutoSave("0");

        //保存到购物车
        CarShoppingCart carShoppingCart = carShoppingCartService.saveCarShoppingCart(carShoppingCartParam);
        AudiUserResp userResp = audiBBSFeignService.findByUserId(Long.valueOf(userId));
        if (Objects.nonNull(userResp)) {
            //宠粉节
            audiAsyncTaskAdapterServiceHelper.finishCarShoppingCart(
                    userResp.getUserIdIdp(), carCustom.getAccbTypeCode()
            );
        }
        //保存车型配置发汇星
        CustomSeriesDto customSeries = null;
        try {
            customSeries = carCustomService.getCustomSeriesInfo(Constant.MASTER_CHANNEL, carCustom);

        } catch (Exception e) {
            log.error("查询车系配置错误:",e);
            e.printStackTrace();
        }
        /**删除首次配置车型发汇星逻辑
        if(Objects.nonNull(userResp) && customSeries!=null){
            String seriesCode = customSeries.getSeriesCode();
            Map<String, String> params = new HashMap<>();
            params.put("userId", userId);
            params.put("idpId", userResp.getUserIdIdp());
            params.put("key1", userId);
            params.put("key2", seriesCode);
            audiTaskFeign.shareCarConfigLine(params);
        }**/
        return new AjaxMessage<>("00", "成功", carShoppingCart);
    }

    @ApiOperation("自动保存购物车配置")
    @PostMapping("/autoSaveCarShoppingCart")
    public AjaxMessage<CarShoppingCart> autoSaveCarShoppingCart(@RequestHeader(value = "X-User-Id") String userId,
                                                                @RequestHeader(value = "X-User-Mobile", required = false) String mobile,
                                                                @RequestHeader(value = "channel") String channel,
                                                                @RequestHeader(value="X-Member-Id", required = false) String memberId,
                                                                @RequestBody CarShoppingCartVo carShoppingCartVo) throws Exception {
        return new AjaxMessage<>("00", "成功", carShoppingCartService.autoSaveCarShoppingCart(memberId, userId, mobile, channel, carShoppingCartVo));
    }

    @ApiOperation("获取最近的一个购物车")
    @GetMapping("/getLatestCarShoppingCart")
    public AjaxMessage<CarShoppingCartVo> getLatestCarShoppingCartList(@RequestHeader(value = "X-User-Id") String userId) {
        CarShoppingCartVo list = carShoppingCartService.getLatestCarShoppingCartList(userId);
        return new AjaxMessage<>("00", "成功", list);
    }

    @ApiOperation("获取购物车总数量")
    @GetMapping("/getCarShoppingCartListCount")
    public AjaxMessage<Integer> getCarShoppingCartListCount(@RequestHeader(value = "X-User-Id", required = false) String userId) {
        List<CarShoppingCartVo> list = carShoppingCartService.getCarShoppingCartList(userId);
        return new AjaxMessage<>("00", "成功", list.size());
    }

    @ApiOperation("删除购物车")
    @PostMapping("/delCarShoppingCart")
    public AjaxMessage<String> delCarShoppingCart(@RequestHeader(value = "X-User-Id") String userId,
                                                  @RequestBody CarShoppingCartVo carShoppingCartVo) {
        if (carShoppingCartVo.getShoppingCartIds() == null) {
            return new AjaxMessage<>("01", "失败", "数据ID为空");
        }
        carShoppingCartVo.setUserId(userId);
        List<String> errorIds = carShoppingCartService.delCarShoppingCartByIds(carShoppingCartVo);
        if (errorIds.size() > 0) {
            return new AjaxMessage<>("00", "失败,userId : " + userId, errorIds.toString());
        }
        return new AjaxMessage<>("00", "成功", "");
    }

    @ApiOperation("获取购物车信息")
    @GetMapping("/getCarShoppingCartDetail")
    public AjaxMessage<CarShoppingCartVo> getCarShoppingCartDetail(@RequestParam String shoppingCartId) {
        CarShoppingCartVo detail = carShoppingCartService.getCarShoppingCartDetail(shoppingCartId);
        return new AjaxMessage<>("00", "成功", detail);
    }

}

