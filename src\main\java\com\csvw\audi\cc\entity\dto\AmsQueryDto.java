package com.csvw.audi.cc.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class AmsQueryDto {
    @NotBlank
    @ApiModelProperty("配置线id")
    private String modelLineId;
    @NotEmpty
    @ApiModelProperty("选项ids，颜色id必传")
    private List<String> optionIds;
    @NotBlank
    @ApiModelProperty("小程序密文")
    private String amsParam;
}
