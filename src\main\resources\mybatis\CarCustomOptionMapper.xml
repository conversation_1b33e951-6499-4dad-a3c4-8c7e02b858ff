<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarCustomOptionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarCustomOption">
        <id column="custom_option_id" property="customOptionId" />
        <result column="ccid" property="ccid" />
        <result column="code" property="code" />
        <result column="category" property="category" />
        <result column="description" property="description" />
        <result column="option_id" property="optionId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        custom_option_id, ccid, code, category, description, option_id
    </sql>

</mapper>
