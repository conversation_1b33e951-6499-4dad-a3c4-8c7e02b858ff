package com.csvw.audi.cc.entity.dto.okapi;

import lombok.Data;

import java.util.List;

@Data
public class LabelConfigResDto {
    private String brandCode;
    private String subBrandCode;
    private String seriesCode;
    private String modelUnicode;
    private String modelUnicodeShort;
    private String checkState;
    private String mstMgrpId;
    private String errMsg;
    private List<LabelResDto> children;
    private List<AlterationInfo> alteration;
    private List<ModelGroupLab> modelGroupLab;
    private List<ConfigPrompt> prompt;

    @Data
    public static class ConfigPrompt {
        private String labelCode;
        private String familyCode;
        private String featureCode;
        private String externalFeatureNameZh;
        private String alterationType;
        private String groupIdentifier;
    }

    @Data
    public static class LabelResDto {
        private String labelType;
        private String labelCode;
        private String labelNameZh;
        private String labelNameEh;
        private String parLabelCode;
        private String familyCode;
        private String featureCode;
        private String externalFeatureNameZh;
        private String externalFeatureNameEn;
        private String featureStatus;
        private String featureStatusCode;
        private String featurePrice;
        private Integer featureWeight;
        private String equipmentRights;
        private List<MaterialInfo> materialList;
        private List<ModelGroupLab> modelGroupLab;
    }

    @Data
    public static class MaterialInfo{
        private String materialName;
        private String materialType;
        private String materialUrl;
        private String materialDesc;
        private String materialRange;
        private String materialSize;
        private String materialHeight;
        private String materialWidth;
    }

    @Data
    public static class AlterationInfo {
        private String labelCode;
        private String familyCode;
        private String featureCode;
        private String alterationType;
    }

    @Data
    public static class ModelGroupLab {
        private String mstLab;
        private String mstLabDesc;
        private String mstLabCategory;
        private String mstLabCategoryDesc;
    }
}
