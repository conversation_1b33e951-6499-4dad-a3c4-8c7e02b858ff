package com.csvw.audi.cc.controller.ccnew;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.config.AppConfig;
import com.csvw.audi.cc.entity.dto.*;
import com.csvw.audi.cc.entity.dto.omd.EstimateDeliveryRes;
import com.csvw.audi.cc.entity.dto.omd.OmdModelDto;
import com.csvw.audi.cc.entity.dto.vwcc.*;
import com.csvw.audi.cc.entity.enumeration.BestRecommendTypeEnum;
import com.csvw.audi.cc.entity.enumeration.DepositTypeEnum;
import com.csvw.audi.cc.entity.enumeration.OptionTypeEnum;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.entity.vo.*;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.feign.AudiTaskFeign;
import com.csvw.audi.cc.service.*;
import com.csvw.audi.cc.service.impl.CarConfigSnapshotService;
import com.csvw.audi.cc.service.impl.CarRecommendCustomServiceImpl;
import com.csvw.audi.cc.service.impl.OmdServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.math.raw.Mod;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1/cc")
@Api(tags = "自配版车辆配置")
@Slf4j
public class CarConfigApiController extends BaseController {

    @Autowired
    private ICarCustomSeriesService customSeriesService;

    @Autowired
    private ICarModelLineService modelLineService;

    @Autowired
    private ICarModelLineOptionService modelLineOptionService;

    @Autowired
    private ICarOptionService optionService;

    @Autowired
    private ICarModelLineParameterService modelLineParameterService;

    @Autowired
    private ICarCustomService carCustomService;

    @Autowired
    private ICarCustomOptionService customOptionService;

    @Autowired
    private ICarOptionRelateService optionRelateService;

    @Autowired
    private ICarRecommendService recommendService;

    @Autowired
    private ICarModelLineSibInterieurService sibInterieurService;

    @Autowired
    private AudiTaskFeign audiTaskFeign;

    @Autowired
    private CarRecommendCustomServiceImpl recommendCustomService;

    @Autowired
    private IModelLineLoanRuleService modelLineLoanRuleService;

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private ICarBestRecommendStockService bestRecommendStockService;

    @Autowired
    private ICarCustomEstimateService customEstimateService;

    @Autowired
    private CarConfigSnapshotService carConfigSnapshot;

    @Autowired
    private ICarModelLineTypeService modelLineTypeService;

    @Autowired
    private OmdServiceImpl omdService;

    @GetMapping(value = {"/modelLine/configs", "/public/modelLine/configs"})
    @ApiOperation("配置线全配置")
    public AjaxMessage<ModelLineConfigVo> modelLineConfigs(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                           @RequestParam String modelLineId,
                                                           @RequestParam(value = "filter", defaultValue = "STANDARD_EQUIPMENT", required = false) String filter) throws Exception {
        Integer status = null;
        if (filter != null){
            switch (filter) {
                case "STANDARD_EQUIPMENT":
                    status = 1;
                    break;
                case "OPTIONAL_EQUIPMENT":
                    status = 2;
                    break;
                default:
                    status = null;
            }
        }
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        ModelLineConfigVo configVo = modelLineService.modelLineConfig(channel, modelLineId, status);
        return successMessage(configVo);
    }

    @GetMapping(value = {"/public/modelLine/sphere/seriesConfigs"})
    @ApiOperation("sphere车系配置线配置")
    public AjaxMessage<SeriesConfigsVo> seriesConfigs(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                           @RequestParam String customSeriesId) throws Exception {
        SeriesConfigsVo seriesConfigsVo = modelLineService.seriesConfigs(channel, customSeriesId);
        return successMessage(seriesConfigsVo);
    }

    @GetMapping(value = {"/public/series/seriesOptions"})
    @ApiOperation("sphere车系配置线配置")
    public AjaxMessage<List<SeriesOptionVo>> seriesOptions(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                      @RequestParam String customSeriesId) throws Exception {
        List<SeriesOptionVo> optionVos = customSeriesService.seriesOptions(channel, customSeriesId);
        return successMessage(optionVos);
    }

    @GetMapping(value = {"/modelLine/configs/compare"})
    @ApiOperation("车辆对比数据")
    public AjaxMessage<ModelLineConfigCompareVo> modelLineConfigsCompare(@RequestParam(required = false) String modelLineId, @RequestParam(required = false) String ccid) throws Exception {
        if (StringUtils.isBlank(modelLineId) && StringUtils.isBlank(ccid)){
            throw new ServiceException("400401", "参数异常：modelLineId和ccid需要其中一个", "");
        }

        List<CarCustomOption> options = new ArrayList<>();
        if (StringUtils.isNotBlank(ccid)){
            CarCustom cc = carCustomService.getById(ccid);
            if (cc == null || (StringUtils.isNotBlank(modelLineId) && !cc.getModelLineId().equals(modelLineId))){
                return new AjaxMessage<>("400401","参数异常：ccid", null);
            }
            if (StringUtils.isBlank(modelLineId)){
                modelLineId = cc.getModelLineId();
            }
            LambdaQueryWrapper<CarCustomOption> optionQuery = new LambdaQueryWrapper<>();
            optionQuery.eq(CarCustomOption::getCcid, ccid);
            options = customOptionService.list(optionQuery);
        }
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        ModelLineConfigCompareVo configVo = modelLineService.modelLineConfigCompare(modelLineId);
        if (configVo != null){
            configVo.setModelLineVo(lines.get(0));
        }else {
            return successMessage(null);
        }
        ModelLineOptionTagParam tagParam = new ModelLineOptionTagParam();
        tagParam.setModelLineId(modelLineId);
        tagParam.setChannel(Constant.MASTER_CHANNEL);
        tagParam.setTagCode("PUBLIC_PRODUCT_SELECTED");
        List<ModelLineOptionVo> productSelected = modelLineService.modelLineTagOption(tagParam);
        Map<String, ModelLineOptionVo> productSelectMap = productSelected.stream().collect(Collectors.toMap(ModelLineOptionVo::getOptionId, i->i, (o, n)->n));
        if (CollectionUtils.isNotEmpty(options)){
            Map<String, CarCustomOption> optionMap = options.stream().collect(Collectors.toMap(CarCustomOption::getOptionId, i->i, (o, n)->n));
            if (configVo.getModelLineOption() != null){
                configVo.getModelLineOption().forEach(i->{
                    CarCustomOption selectedOption = optionMap.get(i.getOptionId());
                    if (selectedOption != null && i.getStatus().intValue() != 1 && !i.getOptionType().equals(OptionTypeEnum.PACKETITEM.getValue())){
                        i.setSelected(1);
                    }
                    if (productSelectMap != null && productSelectMap.get(i.getOptionId()) != null){
                        i.setProductSelected(1);
                    }
                });

            }
        }
        return successMessage(configVo);
    }

    @GetMapping(value = {"/modelLine/configs/personalOption", "/public/modelLine/configs/personalOption"})
    @ApiOperation("私人定制")
    public AjaxMessage<List<ModelLineOptionVo>> modelLinePersonalOption(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                             @RequestParam String modelLineId) throws Exception {
        List<String> notInCategory = Arrays.asList("RAD", "COLOR_EXTERIEUR", "COLOR_INTERIEUR", "SIB", "EIH");
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        List<ModelLineOptionVo> optionVos = modelLineService.modelLinePersonalOption(channel, modelLineId, notInCategory);
        OptionRelateParam optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLineId);
        optionRelateParam.setRelateTypes(Arrays.asList("conflict", "depend"));
//        optionRelateParam.setNotInRelateCategory(Arrays.asList("COLOR_INTERIEUR", "COLOR_EXTERIEUR", "SIB"));
        List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        Map<String, List<OptionRelDto>> oMap = new HashMap<>();
        relDtos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel == null) {
                oRel = new ArrayList<>();
                oMap.put(i.getOptionId(), oRel);
            }
            oRel.add(i);
        });
        optionVos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel != null){
                i.setOptionRelates(oRel);
            }
        });
        return successMessage(optionVos);
    }

    @GetMapping(value = {"/modelLine/configs/personalOptionPlus", "/public/modelLine/configs/personalOptionPlus"})
    @ApiOperation("私人定制+组合列表")
    public AjaxMessage<PersonalOptionVo> modelLinePersonalOptionPlus(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                                        @RequestParam String modelLineId, String seats) throws Exception {
        List<String> notInCategory = Arrays.asList("RAD", "COLOR_EXTERIEUR", "COLOR_INTERIEUR", "SIB", "EIH");
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        ModelLineVo line = lines.get(0);
        List<ModelLineOptionVo> optionVos = modelLineService.modelLinePersonalOption(channel, modelLineId, notInCategory);
        OptionRelateParam optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLineId);
        optionRelateParam.setRelateTypes(Arrays.asList("conflict", "depend"));
//        optionRelateParam.setNotInRelateCategory(Arrays.asList("COLOR_INTERIEUR", "COLOR_EXTERIEUR", "SIB"));
        List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        Map<String, List<OptionRelDto>> oMap = new HashMap<>();
        relDtos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel == null) {
                oRel = new ArrayList<>();
                oMap.put(i.getOptionId(), oRel);
            }
            oRel.add(i);
        });
        optionVos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel != null){
                i.setOptionRelates(oRel);
            }
        });
        PersonalOptionVo vo = new PersonalOptionVo();
        if (CollectionUtils.isNotEmpty(optionVos)) {
            if ("G4".equals(line.getCustomSeriesCode()) && "7".equals(seats)) {
                List<String> q5e6Seat = Arrays.asList("WE8", "8I6+WE8", "4D3+WE8");
                optionVos = optionVos.stream().filter(o -> !q5e6Seat.contains(o.getOptionCode())).collect(Collectors.toList());
            } else if ("G6".equals(line.getCustomSeriesCode()) && "7".equals(seats)) {
                List<String> q6Seat = Arrays.asList("PS1");
                optionVos = optionVos.stream().filter(o -> !q6Seat.contains(o.getOptionCode())).collect(Collectors.toList());
            }
        }
        vo.setPersonalOptions(optionVos);
        TypePrListQueryDto queryDto = new TypePrListQueryDto(line.getAccbTypeCode(), line.getModelYear(), line.getVersion(), optionVos);
        if (StringUtils.isNotBlank(seats)) {
            queryDto.setSeats(seats);
        }
        vo.setPersonalOptionComposes(modelLineTypeService.queryPersonalComposes(queryDto));
        return successMessage(vo);
    }

    @GetMapping(value = {"/modelLine/configs/packetItem", "/public/modelLine/configs/packetItem"})
    @ApiOperation("配置线选装包详细选装件")
    public AjaxMessage<List<ModelLineOptionVo>> packetItem(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                           @RequestParam String modelLineId, String optionId) throws Exception {
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        OptionParamDto optionParamDto = new OptionParamDto();
        optionParamDto.setDelFlag(0);
        optionParamDto.setModelLineId(modelLineId);
        optionParamDto.setChannel(channel);
        optionParamDto.setOptionId(optionId);
        List<ModelLineOptionVo> optionVos = modelLineService.modelLinePacketItem(optionParamDto);
        return successMessage(optionVos);
    }

    @GetMapping(value = {"/modelLine/configs/rad", "/public/modelLine/configs/rad"})
    @ApiOperation("配置线轮毂")
    public AjaxMessage<List<ModelLineOptionVo>> modelLineRad(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, @RequestParam String modelLineId, String seats) throws Exception {
        String category = "RAD";
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        ModelLineVo line = lines.get(0);
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, modelLineId, category);
        if (StringUtils.isNotBlank(line.getTypeFlag()) && !line.getTypeFlag().contains("C")) {
            TypePrListQueryDto queryDto = new TypePrListQueryDto(line.getAccbTypeCode(), line.getModelYear(), line.getVersion());
            optionVos = optionVos.stream().filter(o->{
                queryDto.setOptionVo(o);
                queryDto.setModelLineVo(line);
                queryDto.setSeats(seats);
                return modelLineTypeService.filterOption(queryDto);
            }).collect(Collectors.toList());
        }
        optionVos = modelLineService.handleVirtualRad(line, seats, optionVos);
        return successMessage(optionVos);
    }

    @GetMapping({"/modelLine/configs/color_exterieur", "/public/modelLine/configs/color_exterieur"})
    @ApiOperation("配置线外饰")
    public AjaxMessage<List<ModelLineOptionVo>> modelLineExterieur(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, @RequestParam String modelLineId, String seats) throws Exception {
        String category = "COLOR_EXTERIEUR";
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        ModelLineVo line = lines.get(0);
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, modelLineId, category);
        if (StringUtils.isNotBlank(line.getTypeFlag()) && !line.getTypeFlag().contains("C")) {
            TypePrListQueryDto queryDto = new TypePrListQueryDto(line.getAccbTypeCode(), line.getModelYear(), line.getVersion());
            optionVos = optionVos.stream().filter(o->{
                queryDto.setOptionVo(o);
                queryDto.setModelLineVo(line);
                queryDto.setSeats(seats);
                return modelLineTypeService.filterOption(queryDto);
            }).collect(Collectors.toList());
        }
        return successMessage(optionVos);
    }


    @GetMapping(value = {"/modelLine/configs/color_interieur", "/public/modelLine/configs/color_interieur"})
    @ApiOperation("配置线内饰")
    public AjaxMessage<List<ModelLineOptionVo>> modelLineInterieur(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,@RequestParam String modelLineId) throws Exception {
        String category = "COLOR_INTERIEUR";
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, modelLineId, category);
        return successMessage(optionVos);
    }

    @GetMapping(value = {"/modelLine/configs/sib_color_interieur", "/public/modelLine/configs/sib_color_interieur"})
    @ApiOperation("配置线内饰面料整合")
    public AjaxMessage<List<ModelLineSibInterieurVo>> modelLineSibInterieur(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                                            @RequestParam String modelLineId, String sibInterieurId, String seats) throws Exception {
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        ModelLineVo line = lines.get(0);
        List<ModelLineSibInterieurVo> sibInterieurVos = sibInterieurService.modelLineSibInterieur(channel, modelLineId, sibInterieurId);
        OptionRelateParam optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLineId);
        optionRelateParam.setRelateTypes(Arrays.asList("conflict", "depend"));
//        optionRelateParam.setNotInRelateCategory(Arrays.asList("COLOR_INTERIEUR", "COLOR_EXTERIEUR", "SIB"));
        List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        Map<String, List<OptionRelDto>> oMap = new HashMap<>();
        relDtos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel == null) {
                oRel = new ArrayList<>();
                oMap.put(i.getOptionId(), oRel);
            }
            oRel.add(i);
        });
        sibInterieurVos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getSibInterieurId());
            if (oRel != null){
                i.setSibInterieurRelates(oRel);
            }
        });
        if (StringUtils.isNotBlank(line.getTypeFlag()) && !line.getTypeFlag().contains("C")) {
            TypePrListQueryDto queryDto = new TypePrListQueryDto(line.getAccbTypeCode(), line.getModelYear(), line.getVersion());
            sibInterieurVos = sibInterieurVos.stream().filter(o->{
                queryDto.setSibInterieurVo(o);
                queryDto.setModelLineVo(line);
                queryDto.setSeats(seats);
                return modelLineTypeService.filterInterOption(queryDto);
            }).collect(Collectors.toList());
        }
        return successMessage(sibInterieurVos);
    }

    @GetMapping(value = {"/modelLine/configs/vos", "/public/modelLine/configs/vos"})
    @ApiOperation("配置线座椅")
    public AjaxMessage<List<ModelLineOptionVo>> modelLineVos(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                             @RequestParam String modelLineId) throws Exception {
        String category = "VOS";
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, modelLineId, category);
        return successMessage(optionVos);
    }

    @GetMapping(value = {"/modelLine/configs/sib", "/public/modelLine/configs/sib"})
    @ApiOperation("配置线面料")
    public AjaxMessage<List<ModelLineOptionVo>> modelLineSib(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                             @RequestParam String modelLineId) throws Exception {
        String category = "SIB";
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, modelLineId, category);
        return successMessage(optionVos);
    }

    @GetMapping(value = {"/modelLine/configs/eih", "/public/modelLine/configs/eih"})
    @ApiOperation("配置线饰条")
    public AjaxMessage<List<ModelLineOptionVo>> modelLineEid(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                             @RequestParam String modelLineId, String seats) throws Exception {
        String category = "EIH";
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        ModelLineVo line = lines.get(0);
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, modelLineId, category);
        if (StringUtils.isNotBlank(line.getTypeFlag()) && !line.getTypeFlag().contains("C")) {
            TypePrListQueryDto queryDto = new TypePrListQueryDto(line.getAccbTypeCode(), line.getModelYear(), line.getVersion());
            optionVos = optionVos.stream().filter(o->{
                queryDto.setOptionVo(o);
                queryDto.setModelLineVo(line);
                queryDto.setSeats(seats);
                return modelLineTypeService.filterOption(queryDto);
            }).collect(Collectors.toList());
        }
        return successMessage(optionVos);
    }

    @GetMapping(value = {"/modelLine/configs/options", "/public/modelLine/configs/options"})
    @ApiOperation("配置线装备项查询")
    public AjaxMessage<List<ModelLineOptionVo>> modelLineQuery(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                               @RequestParam String modelLineId, String[] optionIds) throws Exception {
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOptionQuery(channel, modelLineId, Arrays.asList(optionIds));
        return successMessage(optionVos);
    }

//    @GetMapping("/modelLine/configs/optional_equipment")
//    @ApiOperation("配置线可选装备")
    public AjaxMessage<List<ModelLineOptionVo>> modelLineEquipment(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                                   @RequestParam String modelLineId) throws Exception {
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOptionalEquipment(channel, modelLineId);
        return successMessage(optionVos);
    }

    @GetMapping(value = {"/modelLine/parameter", "/public/modelLine/parameter"})
    @ApiOperation("配置线参数")
    public AjaxMessage<List<ModelLineParameterVo>> parameter(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                             @RequestParam String modelLineId) throws Exception {
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        List<ModelLineParameterVo> data = modelLineParameterService.listModelLineParameter(modelLineId);
        return successMessage(data);
    }

    @GetMapping(value = {"/modelLine/groupList", "/public/modelLine/groupList"})
    @ApiOperation("配置线(车型)分组列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "配置线类型，1：高定，2：半定，3：高定半定混合", required = true, paramType = "query")
    })
    public AjaxMessage<List<ModelLineGroupVo>> modelLineGroupList(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                    @RequestParam String customSeriesId, @RequestParam(defaultValue = "1") int type) throws Exception {
        CustomSeriesParam seriesParam = new CustomSeriesParam();
        seriesParam.setCustomSeriesId(customSeriesId);
        List<CarCustomSeries> carCustomSeries = customSeriesService.listCustomSeries(channel, seriesParam);
        if (CollectionUtils.isEmpty(carCustomSeries)){
            throw new ServiceException("参数异常：customSeriesId", "400401", "");
        }
        ModelParamDto paramDto = new ModelParamDto();
        paramDto.setCustomSeriesId(customSeriesId);
        paramDto.setDelFlag(0);
        paramDto.setChannel(channel);
        List<ModelLineVo> data = modelLineService.listModelLine(paramDto);
        data = data.stream().filter(i-> {
            if(i.getOmdModelStatus() == 0 && (i.getMeasure() == null || i.getMeasure() != 1) && Constant.NEED_TYPE_FLAG.contains(channel)){
                if (StringUtils.isBlank(i.getTypeFlag())){
                    return false;
                }
            }
            List<ModelLineOptionVo> optionVos;
            try {
                optionVos = modelLineService.modelLinePersonalOptionDefault(i.getModelLineId());
                if(i.getPriceAddColor() != null && i.getPriceAddColor().intValue() == 1) {
                    String category = "COLOR_EXTERIEUR";
                    List<ModelLineOptionVo> exterieurs = modelLineService.modelLineOption(channel, i.getModelLineId(), category).stream().filter(e -> e.getDefaultConfig() == 1).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(exterieurs)) {
                        optionVos.add(exterieurs.get(0));
                    }
                }
            } catch (Exception e) {
                log.error("查询配置线默认私人定制异常", e);
                return false;
            }
            if (CollectionUtils.isNotEmpty(optionVos)){
                List<String> optionIds = optionVos.stream().map(ModelLineOptionVo::getOptionId).collect(Collectors.toList());
                PriceComputeParam computeParam = new PriceComputeParam();
                computeParam.setOptionIds(optionIds);
                computeParam.setModelLineId(i.getModelLineId());
                try {
                    i.setPrice(modelLineService.computePrice(computeParam));
                } catch (ServiceException e) {
                    log.error("查询配置线价格计算异常", e);
                    return false;
                }
            }
            if (i.getFrontStatus() == null || i.getFrontStatus().intValue() == 0) {
                return false;
            }
            switch (type){
                case 1:
                    return i.getPersonal() != null && i.getPersonal().intValue() == 1;
                case 2:
                    return i.getMeasure() != null && i.getMeasure().intValue() == 1 && modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId());
                case 3:
                    return (i.getMeasure() != null && i.getMeasure().intValue() == 1 && modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId())) ||
                            (i.getPersonal() != null && i.getPersonal().intValue()  == 1);
            }
            return false;
        }).collect(Collectors.toList());
        Map<String, ModelLineGroupVo> map = new HashMap<>();
        List<ModelLineGroupVo> res = new ArrayList<>();
        ModelLineGroupVo noEngineVo = new ModelLineGroupVo();
        res.add(noEngineVo);
        for (ModelLineVo v : data){
            if (StringUtils.isBlank(v.getEngine())){
                if (noEngineVo.getModelLines() == null){
                    noEngineVo.setModelLines(new ArrayList<>());
                }
                noEngineVo.getModelLines().add(v);
                continue;
            }
            ModelLineGroupVo vo = map.get(v.getEngine());
            if (vo == null){
                vo = new ModelLineGroupVo();
                map.put(v.getEngine(), vo);
                res.add(vo);
                vo.setModelLines(new ArrayList<>());
                vo.setEngine(v.getEngine());
                vo.getModelLines().add(v);
            }else {
                vo.getModelLines().add(v);
            }
        }
        if(res.get(0).getModelLines() == null){
            res.remove(0);
        }
        return successMessage(res);
    }

    @GetMapping(value = {"/modelLine", "/public/modelLine"})
    @ApiOperation("配置线(车型)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "配置线类型，1：高定，2：半定，3：高定半定混合", required = true, paramType = "query")
    })
    public AjaxMessage<List<ModelLineVo>> modelLine(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                    @RequestParam String customSeriesId,  @RequestParam( defaultValue = "1") int type) throws Exception {
        CustomSeriesParam seriesParam = new CustomSeriesParam();
        seriesParam.setCustomSeriesId(customSeriesId);
        List<CarCustomSeries> carCustomSeries = customSeriesService.listCustomSeries(channel, seriesParam);
        if (CollectionUtils.isEmpty(carCustomSeries)){
            throw new ServiceException("参数异常：customSeriesId", "400401", "");
        }
        ModelParamDto paramDto = new ModelParamDto();
        paramDto.setCustomSeriesId(customSeriesId);
        paramDto.setDelFlag(0);
        paramDto.setChannel(channel);
        List<ModelLineVo> data = modelLineService.listModelLine(paramDto);
        data = data.stream()
                /*.sorted((a,b)->{
                        BigDecimal aPrice=null, bPrice=null;
                        if (a.getPrice() != null && a.getPrice() instanceof BigDecimal){
                            aPrice = (BigDecimal) a.getPrice();
                        }
                        if (b.getPrice() != null && b.getPrice() instanceof BigDecimal){
                            bPrice = (BigDecimal) b.getPrice();
                        }
                        if (aPrice==null && bPrice== null){
                            return 0;
                        }
                        if (aPrice == null){
                            return 1;
                        }
                        if (bPrice == null){
                            return -1;
                        }
                        return bPrice.compareTo(aPrice);
                    })*/
                .filter(i-> {
                    if(i.getOmdModelStatus() == 0 && (i.getMeasure() == null || i.getMeasure() != 1) && Constant.NEED_TYPE_FLAG.contains(channel)){
                        if (StringUtils.isBlank(i.getTypeFlag())){
                            return false;
                        }
                    }
                    List<ModelLineOptionVo> optionVos;
                    try {
                        optionVos = modelLineService.modelLinePersonalOptionDefault(i.getModelLineId());
                        if(i.getPriceAddColor() != null && i.getPriceAddColor().intValue() == 1) {
                            String category = "COLOR_EXTERIEUR";
                            List<ModelLineOptionVo> exterieurs = modelLineService.modelLineOption(channel, i.getModelLineId(), category).stream().filter(e -> e.getDefaultConfig() == 1).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(exterieurs)) {
                                optionVos.add(exterieurs.get(0));
                            }
                        }
                    } catch (Exception e) {
                        log.error("查询配置线默认私人定制异常", e);
                        return false;
                    }
                    if (CollectionUtils.isNotEmpty(optionVos)){
                        List<String> optionIds = optionVos.stream().map(ModelLineOptionVo::getOptionId).collect(Collectors.toList());
                        PriceComputeParam computeParam = new PriceComputeParam();
                        computeParam.setOptionIds(optionIds);
                        computeParam.setModelLineId(i.getModelLineId());
                        try {
                            i.setPrice(modelLineService.computePrice(computeParam));
                        } catch (ServiceException e) {
                            log.error("查询配置线价格计算异常", e);
                            return false;
                        }
                    }
                    if (i.getFrontStatus() == null || i.getFrontStatus().intValue() == 0) {
                        return false;
                    }
                    switch (type){
                        case 1:
                            return i.getPersonal() != null && i.getPersonal().intValue() == 1;
                        case 2:
                            return i.getMeasure() != null && i.getMeasure().intValue() == 1 && modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId());
                        case 3:
                            return (i.getMeasure() != null && i.getMeasure().intValue() == 1 && modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId())) ||
                                    (i.getPersonal() != null && i.getPersonal().intValue()  == 1);
                    }
                    return false;
                }).collect(Collectors.toList());
        return successMessage(data);
    }

    @GetMapping(value = {"/switch/modelLine"})
    @ApiOperation("小订切换配置线(车型)列表")
    public AjaxMessage<List<ModelLineVo>> modelLineSwitch(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                    @RequestParam String customSeriesId) throws Exception {
        CustomSeriesParam seriesParam = new CustomSeriesParam();
        seriesParam.setCustomSeriesId(customSeriesId);
        List<CarCustomSeries> carCustomSeries = customSeriesService.listCustomSeries(channel, seriesParam);
        if (CollectionUtils.isEmpty(carCustomSeries)){
            throw new ServiceException("参数异常：customSeriesId", "400401", "");
        }
        ModelParamDto paramDto = new ModelParamDto();
        paramDto.setCustomSeriesId(customSeriesId);
        paramDto.setDelFlag(0);
        paramDto.setChannel(channel);
        List<ModelLineVo> data = modelLineService.listModelLine(paramDto).stream().filter(i->{
                    if(i.getOmdModelStatus() == 0 && Constant.NEED_TYPE_FLAG.contains(channel)){
                        if (StringUtils.isBlank(i.getTypeFlag())){
                            return false;
                        }
                    }
                    List<ModelLineOptionVo> optionVos;
                    try {
                        optionVos = modelLineService.modelLinePersonalOptionDefault(i.getModelLineId());
                        if(i.getPriceAddColor() != null && i.getPriceAddColor().intValue() == 1) {
                            String category = "COLOR_EXTERIEUR";
                            List<ModelLineOptionVo> exterieurs = modelLineService.modelLineOption(channel, i.getModelLineId(), category).stream().filter(e -> e.getDefaultConfig() == 1).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(exterieurs)) {
                                optionVos.add(exterieurs.get(0));
                            }
                        }
                    } catch (Exception e) {
                        log.error("查询配置线默认私人定制异常", e);
                        return false;
                    }
                    if (CollectionUtils.isNotEmpty(optionVos)){
                        List<String> optionIds = optionVos.stream().map(ModelLineOptionVo::getOptionId).collect(Collectors.toList());
                        PriceComputeParam computeParam = new PriceComputeParam();
                        computeParam.setOptionIds(optionIds);
                        computeParam.setModelLineId(i.getModelLineId());
                        try {
                            i.setPrice(modelLineService.computePrice(computeParam));
                        } catch (ServiceException e) {
                            log.error("查询配置线价格计算异常", e);
                            return false;
                        }
                    }
                    return true;
                })
                .sorted((a,b)->{
                        BigDecimal aPrice=null, bPrice=null;
                        if (a.getPrice() != null && a.getPrice() instanceof BigDecimal){
                            aPrice = (BigDecimal) a.getPrice();
                        }
                        if (b.getPrice() != null && b.getPrice() instanceof BigDecimal){
                            bPrice = (BigDecimal) b.getPrice();
                        }
                        if (aPrice==null && bPrice== null){
                            return 0;
                        }
                        if (aPrice == null){
                            return 1;
                        }
                        if (bPrice == null){
                            return -1;
                        }
                        return bPrice.compareTo(aPrice);
                    })
                .collect(Collectors.toList());
        return successMessage(data);
    }

    @GetMapping(value = {"/modelLineQuery", "/public/modelLineQuery"})
    @ApiOperation("配置线(车型)查询，查询所有配置线")
    public AjaxMessage<List<ModelLineVo>> modelLineQuery(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                    @RequestParam String modelLineId) throws Exception {

        ModelParamDto paramDto = new ModelParamDto();
        paramDto.setModelLineId(modelLineId);
        paramDto.setDelFlag(0);
        paramDto.setChannel(channel);
        List<ModelLineVo> data = modelLineService.listModelLine(paramDto);
        data = data.stream().filter(line ->{
            List<ModelLineOptionVo> optionVos;
            try {
                optionVos = modelLineService.modelLinePersonalOptionDefault(line.getModelLineId());
                if(line.getPriceAddColor() != null && line.getPriceAddColor().intValue() == 1) {
                    String category = "COLOR_EXTERIEUR";
                    List<ModelLineOptionVo> exterieurs = modelLineService.modelLineOption(channel, line.getModelLineId(), category).stream().filter(e -> e.getDefaultConfig() == 1).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(exterieurs)) {
                        optionVos.add(exterieurs.get(0));
                    }
                }
            } catch (Exception e) {
                log.error("查询配置线默认私人定制异常", e);
                return false;
            }
            if (CollectionUtils.isNotEmpty(optionVos)){
                List<String> optionIds = optionVos.stream().map(ModelLineOptionVo::getOptionId).collect(Collectors.toList());
                PriceComputeParam computeParam = new PriceComputeParam();
                computeParam.setOptionIds(optionIds);
                computeParam.setModelLineId(line.getModelLineId());
                try {
                    line.setPrice(modelLineService.computePrice(computeParam));
                } catch (ServiceException e) {
                    log.error("查询配置线价格计算异常", e);
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
        return successMessage(data);
    }

    @GetMapping(value = {"/customSeries", "/public/customSeries"})
    @ApiOperation("自定义车系(车系)")
    public AjaxMessage<List<CustomSeriesVo>> customSeries(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, CustomSeriesParam seriesParam) throws Exception {
        List<CarCustomSeries> data = customSeriesService.listCustomSeries(channel, seriesParam);
        List<CustomSeriesVo> vos = new ArrayList<>();
        if (data != null){
            vos = data.stream().map(i->{
                CustomSeriesVo vo = new CustomSeriesVo();
                BeanUtils.copyProperties(i, vo);
                return vo;
            }).collect(Collectors.toList());
        }
        return successMessage(vos);
    }

    @PostMapping()
    @ApiOperation("配置器配车（ccid）")
    public AjaxMessage<CarCustomDetail> addcarconfig(@RequestHeader(value = "X-User-Id", required = false) String userId,
                                                     @RequestHeader(value = "X-User-Mobile", required = false) String userMobile,
                                                     @RequestHeader(value="X-Member-Id", required = false) String memberId,
                                                     @RequestHeader(value = "channel") String channel,
                                                     @RequestBody CarCustomDto carCustomDto) throws Exception {
        carCustomDto.setDepositType(DepositTypeEnum.SCHEDULE.getType());
        CarCustom carCustom = carCustomService.addCarCustomConfig(memberId, userId,userMobile, channel, carCustomDto);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        return new AjaxMessage<>("00", "成功", detail);
    }

    @PostMapping("/okapi")
    @ApiOperation("OKAPIData配置器配车（ccid）")
    public AjaxMessage<CarCustomDetail> addcarconfigOkapi(@RequestHeader(value = "X-User-Id", required = false) String userId,
                                                     @RequestHeader(value = "X-User-Mobile", required = false) String userMobile,
                                                     @RequestHeader(value="X-Member-Id", required = false) String memberId,
                                                     @RequestHeader(value = "channel") String channel,
                                                     @RequestBody OkapiCustomDto okapiCustomDto) throws Exception {
        OmdModelDto omdModelDto = new OmdModelDto();
        BeanUtils.copyProperties(okapiCustomDto, omdModelDto);
        CarModelDto carModelDto = omdService.convertOriginData(omdModelDto);
        CarCustomDto carCustomDto = new CarCustomDto();
        BeanUtils.copyProperties(okapiCustomDto, carCustomDto);
        carCustomDto.setSibInterieurId(carModelDto.getSibInterieur().getSibInterieurId());
        carCustomDto.setOptionIds(carModelDto.getOptions().stream().map(ModelLineOptionVo::getOptionId).collect(Collectors.toSet()));
        carCustomDto.getOptionIds().add(carModelDto.getSibInterieur().getInterieurOptionId());
        carCustomDto.getOptionIds().add(carModelDto.getSibInterieur().getSibOptionId());
        carCustomDto.setDepositType(DepositTypeEnum.SCHEDULE.getType());
        carCustomDto.setModelLineId(carModelDto.getModelLineVo().getModelLineId());
        CarCustom carCustom = carCustomService.addCarCustomConfig(memberId, userId,userMobile, channel, carCustomDto);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        return new AjaxMessage<>("00", "成功", detail);
    }

    @PutMapping("/okapi")
    @ApiOperation("OKAPIData修改配置器配车（ccid）")
    public AjaxMessage<CarCustomDetail> updatecarconfigOkapi(@RequestHeader(value = "X-User-Id", required = false) String userId,
                                                          @RequestHeader(value = "X-User-Mobile", required = false) String userMobile,
                                                          @RequestHeader(value="X-Member-Id", required = false) String memberId,
                                                          @RequestHeader(value = "channel") String channel,
                                                          @RequestBody OkapiCustomDto okapiCustomDto) throws Exception {
        OmdModelDto omdModelDto = new OmdModelDto();
        BeanUtils.copyProperties(okapiCustomDto, omdModelDto);
        CarModelDto carModelDto = omdService.convertOriginData(omdModelDto);
        CarCustomDto carCustomDto = new CarCustomDto();
        BeanUtils.copyProperties(okapiCustomDto, carCustomDto);
        carCustomDto.setSibInterieurId(carModelDto.getSibInterieur().getSibInterieurId());
        carCustomDto.setOptionIds(carModelDto.getOptions().stream().map(ModelLineOptionVo::getOptionId).collect(Collectors.toSet()));
        carCustomDto.getOptionIds().add(carModelDto.getSibInterieur().getInterieurOptionId());
        carCustomDto.getOptionIds().add(carModelDto.getSibInterieur().getSibOptionId());
        carCustomDto.setDepositType(DepositTypeEnum.SCHEDULE.getType());
        carCustomDto.setModelLineId(carModelDto.getModelLineVo().getModelLineId());
        CarCustom carCustom = carCustomService.updateCarCustomConfig(memberId, userId,userMobile, carCustomDto, okapiCustomDto.getCcid());
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        return new AjaxMessage<>("00", "成功", detail);
    }

    @PutMapping("/updateNew")
    @ApiOperation("有更新状态配置单，完成更新")
    public AjaxMessage<String> updateNew(@RequestParam("ccid") String ccid) throws Exception {
        CarCustom carCustom = carCustomService.updateNew(ccid);
        return new AjaxMessage<>("00", "成功", null);
    }

    @PostMapping("estimateConfig")
    @ApiOperation("通过相似车配车（ccid）")
    public AjaxMessage<CarCustomDetail> addEstimateCarconfig(@RequestHeader(value = "X-User-Id", required = false) String userId,
                                                     @RequestHeader(value = "X-User-Mobile", required = false) String userMobile,
                                                             @RequestHeader(value="X-Member-Id", required = false) String memberId,
                                                             @RequestHeader(value = "channel") String channel,
                                                     @RequestBody CarEstimateCustomDto carCustomDto, BindingResult bindingResult) throws Exception {
        super.validParam(bindingResult);
        carCustomDto.setDepositType(DepositTypeEnum.STOCK.getType());
        CarCustom carCustom = carCustomService.addCarCustomConfig(memberId, userId,userMobile, channel, carCustomDto);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        CarCustomEstimate carCustomEstimate = new CarCustomEstimate();
        carCustomEstimate.setCcid(carCustom.getCcid());
        carCustomEstimate.setFromCcid(carCustomDto.getFromCcid());
        carCustomEstimate.setUniqueCode(carCustomDto.getUniqueCode());
        carCustomEstimate.setCreateTime(LocalDateTime.now());
        customEstimateService.save(carCustomEstimate);
        carCustomService.validCcidWithUniqueCode(carCustom.getCcid(), carCustomDto.getUniqueCode());
        return new AjaxMessage<>("00", "成功", detail);
    }

    @PostMapping(value = {"/bestRecommendConfig", "/public/bestRecommendConfig"} )
    @ApiOperation("配置器畅销推荐车配车（ccid）")
    public AjaxMessage<CarCustomDetail> addcarconfigByBestRecommend(@RequestHeader(value = "X-User-Id", required = false) String userId,
                                                                    @RequestHeader(value = "X-User-Mobile", required = false) String userMobile,
                                                                    @RequestHeader(value="X-Member-Id", required = false) String memberId,
                                                                    @RequestHeader(value = "channel") String channel,
                                                                    @RequestParam Long bestRecommendId, @RequestParam String entryPoint, String sourceId) throws Exception {
        CarCustom carCustom = carCustomService.addCarCustomConfigByBestRecommend(userId,memberId, userMobile,bestRecommendId, sourceId, channel, entryPoint);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        return new AjaxMessage<>("00", "成功", detail);
    }

    @PostMapping("/public")
    @ApiOperation("匿名配置器配车（ccid）")
    public AjaxMessage<CarCustomDetail> addCarConfigPublic(@RequestHeader(value = "channel") String channel,@RequestBody CarCustomDto carCustomDto) throws Exception {
        carCustomDto.setDepositType(DepositTypeEnum.SCHEDULE.getType());
        CarCustom carCustom = carCustomService.addCarCustomConfig(null,null,null, channel, carCustomDto);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        return new AjaxMessage<>("00", "成功", detail);
    }

    @PostMapping(value = "bind")
    @ApiOperation("匿名ccid绑定用户")
    public AjaxMessage bindCc(@RequestHeader(value = "X-User-Id") String userId,
                                                     @RequestHeader(value = "X-User-Mobile") String userMobile,
                                                     @RequestParam String ccid) throws Exception {
        carCustomService.bindCc(userId,userMobile,ccid);
        return new AjaxMessage<>("00", "成功", null);
    }

    @PutMapping("/{ccid}")
    @ApiOperation("高定修改配置器配车（ccid）")
    public AjaxMessage<CarCustomDetail> updatecarconfig(@RequestHeader(value = "X-User-Id", required = false) String userId,
                                                        @RequestHeader(value = "X-User-Mobile", required = false) String userMobile,
                                                        @RequestHeader(value="X-Member-Id", required = false) String memberId,
                                                        @RequestBody CarCustomDto carCustomDto, @PathVariable("ccid") String ccid) throws Exception {
        CarCustom cc = carCustomService.getById(ccid);
        if (cc == null || (cc.getUserId() != null && !cc.getUserId().equals(userId))) {
            return new AjaxMessage<>("40004", "配置单不可修改", null);
        }
        CarCustom carCustom = carCustomService.updateCarCustomConfig(memberId,userId,userMobile,carCustomDto, ccid);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        return new AjaxMessage<>("00", "成功", detail);
    }

    @GetMapping(value = {"/recommendCar", "/public/recommendCar"})
    @ApiOperation("推荐车")
    public AjaxMessage<List<RecommendCarSphereVo>> recommendCar(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, String customSeriesId){
        List<RecommendCarSphereVo> recommendCarVos = recommendService.recommendCarSphere(channel, customSeriesId);
        return successMessage(recommendCarVos);
    }

    @GetMapping(value = {"/bestRecommendCar", "/public/bestRecommendCar"})
    @ApiOperation("总部推荐车-当季热销")
    public AjaxMessage<List<BestRecommendCarVo>> bestRecommendCar(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, String customSeriesId){
        List<BestRecommendCarVo> recommendCarVos = recommendService.omdBestRecommendCar(channel, appConfig.getHqDealerCode(),customSeriesId);
        if (CollectionUtils.isEmpty(recommendCarVos)){
            return successMessage(recommendCarVos);
        }
        recommendCarVos = recommendCarVos.stream().filter(i->{
            // 过滤先行版
            if(appConfig.getBestRecommend() != null
                    && appConfig.getBestRecommend().getFilterEnable()
                    && CollectionUtils.isNotEmpty(appConfig.getBestRecommend().getModelLineIds())
                    && appConfig.getBestRecommend().getModelLineIds().contains(i.getModelLine().getModelLineId())){
                return false;
            }

            // 总部推荐类型过滤 库存过滤
            return recommendService.validHqType(i, BestRecommendTypeEnum.HQ_RECOMMEND.getValue()) && bestRecommendStockService.validRecommendStock(i, appConfig.getHqDealerCode());
        }).collect(Collectors.toList());
        recommendCarVos.sort(Comparator.comparing(BestRecommendCarVo::getStockNum).reversed());
        recommendCarVos = recommendCarVos.subList(0, recommendCarVos.size()>8?8:recommendCarVos.size());
        recommendCarVos = recommendCarVos.stream().sorted((a,b)->{
            BigDecimal aPrice=null, bPrice=null;
            if (a.getTotalPrice() != null && a.getTotalPrice() instanceof BigDecimal){
                aPrice = (BigDecimal) a.getTotalPrice();
            }
            if (b.getTotalPrice() != null && b.getTotalPrice() instanceof BigDecimal){
                bPrice = (BigDecimal) b.getTotalPrice();
            }
            if (aPrice==null && bPrice== null){
                return 0;
            }
            if (aPrice == null){
                return 1;
            }
            if (bPrice == null){
                return -1;
            }
            return bPrice.compareTo(aPrice);
        }).collect(Collectors.toList());
        return successMessage(recommendCarVos);
    }

    @GetMapping(value = {"/bestRecommendCarTiger", "/public/bestRecommendCarTiger"})
    @ApiOperation("虎年限定")
    public AjaxMessage<List<BestRecommendCarVo>> bestRecommendCarTiger(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, String customSeriesId, Boolean sortedByStockNum){
        List<BestRecommendCarVo> recommendCarVos = recommendService.omdBestRecommendCar(channel, appConfig.getHqDealerCode(),customSeriesId);
        recommendCarVos = recommendCarVos.stream().sorted((a,b)->{
            BigDecimal aPrice=null, bPrice=null;
            if (a.getTotalPrice() != null && a.getTotalPrice() instanceof BigDecimal){
                aPrice = (BigDecimal) a.getTotalPrice();
            }
            if (b.getTotalPrice() != null && b.getTotalPrice() instanceof BigDecimal){
                bPrice = (BigDecimal) b.getTotalPrice();
            }
            if (aPrice==null && bPrice== null){
                return 0;
            }
            if (aPrice == null){
                return 1;
            }
            if (bPrice == null){
                return -1;
            }
            return bPrice.compareTo(aPrice);
        }).filter(i->{
            // 过滤先行版
            if(appConfig.getBestRecommend() != null
                    && appConfig.getBestRecommend().getFilterEnable()
                    && CollectionUtils.isNotEmpty(appConfig.getBestRecommend().getModelLineIds())
                    && appConfig.getBestRecommend().getModelLineIds().contains(i.getModelLine().getModelLineId())){
                return false;
            }
            // 过滤Q5E
            if(appConfig.getBestRecommend() != null
                    && appConfig.getBestRecommend().getFilterEnable()
                    && CollectionUtils.isNotEmpty(appConfig.getBestRecommend().getCustomSeriesIds())
                    && appConfig.getBestRecommend().getCustomSeriesIds().contains(i.getModelLine().getCustomSeriesId())){
                return false;
            }

            // 总部推荐类型过滤 库存过滤
            return recommendService.validHqType(i, BestRecommendTypeEnum.TIGER.getValue()) && bestRecommendStockService.validRecommendStock(i, appConfig.getHqDealerCode());
        }).collect(Collectors.toList());
        if (sortedByStockNum != null && sortedByStockNum){
            recommendCarVos.sort(Comparator.comparing(BestRecommendCarVo::getStockNum).reversed());
        }
        return successMessage(recommendCarVos);
    }

    @GetMapping(value = {"/bestRecommendCarFix", "/public/bestRecommendCarFix"})
    @ApiOperation("OMD混合库存车（混合库存车）")
    public AjaxMessage<List<BestRecommendCarVo>> bestRecommendCarFix(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, String dealerCode, String customSeriesId, Boolean sortedByStockNum){
        List<BestRecommendCarVo> recommendCarVos = recommendService.omdBestRecommendCarFix(channel, dealerCode, customSeriesId);
        recommendCarVos = recommendCarVos.stream().sorted((a,b)->{
            BigDecimal aPrice=null, bPrice=null;
            if (a.getTotalPrice() != null && a.getTotalPrice() instanceof BigDecimal){
                aPrice = (BigDecimal) a.getTotalPrice();
            }
            if (b.getTotalPrice() != null && b.getTotalPrice() instanceof BigDecimal){
                bPrice = (BigDecimal) b.getTotalPrice();
            }
            if (aPrice==null && bPrice== null){
                return 0;
            }
            if (aPrice == null){
                return 1;
            }
            if (bPrice == null){
                return -1;
            }
            return bPrice.compareTo(aPrice);
        }).filter(i->{
            // 过滤先行版
            if(appConfig.getBestRecommend() != null
                    && appConfig.getBestRecommend().getFilterEnable()
                    && CollectionUtils.isNotEmpty(appConfig.getBestRecommend().getModelLineIds())
                    && appConfig.getBestRecommend().getModelLineIds().contains(i.getModelLine().getModelLineId())){
                return false;
            }
            // 库存过滤
            return bestRecommendStockService.validRecommendFixStock(i, dealerCode);
        }).collect(Collectors.toList());
        if (sortedByStockNum){
            recommendCarVos.sort(Comparator.comparing(BestRecommendCarVo::getStockNum).reversed());
        }
        return successMessage(recommendCarVos);
    }


    @GetMapping(value = {"/bestRecommendCarAgent", "/public/bestRecommendCarAgent"})
    @ApiOperation("OMD经销商推荐车（经销商库存车）")
    public AjaxMessage<List<BestRecommendCarVo>> bestRecommendCarAgent(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, String dealerCode, String customSeriesId, Boolean sortedByStockNum){
        List<BestRecommendCarVo> recommendCarVos = recommendService.omdBestRecommendCar(channel, dealerCode, customSeriesId);
        recommendCarVos = recommendCarVos.stream().sorted((a,b)->{
            BigDecimal aPrice=null, bPrice=null;
            if (a.getTotalPrice() != null && a.getTotalPrice() instanceof BigDecimal){
                aPrice = (BigDecimal) a.getTotalPrice();
            }
            if (b.getTotalPrice() != null && b.getTotalPrice() instanceof BigDecimal){
                bPrice = (BigDecimal) b.getTotalPrice();
            }
            if (aPrice==null && bPrice== null){
                return 0;
            }
            if (aPrice == null){
                return 1;
            }
            if (bPrice == null){
                return -1;
            }
            return bPrice.compareTo(aPrice);
        }).filter(i->{
            // 过滤先行版
            if(appConfig.getBestRecommend() != null
                    && appConfig.getBestRecommend().getFilterEnable()
                    && CollectionUtils.isNotEmpty(appConfig.getBestRecommend().getModelLineIds())
                    && appConfig.getBestRecommend().getModelLineIds().contains(i.getModelLine().getModelLineId())){
                return false;
            }
            // 过滤Q5E
            if(appConfig.getBestRecommend() != null
                    && appConfig.getBestRecommend().getFilterEnable()
                    && CollectionUtils.isNotEmpty(appConfig.getBestRecommend().getCustomSeriesIds())
                    && appConfig.getBestRecommend().getCustomSeriesIds().contains(i.getModelLine().getCustomSeriesId())){
                return false;
            }

            // 特定经销商过滤特定渠道特定配置，N5V-FF。处理错误车辆特选车销售
            if(!channel.equals(Constant.ONEAPP_CHANNEL)){
                if (Arrays.asList("76646059", "76623019").contains(dealerCode)
                        && i.getModelLine().getAccbTypeCode().equals("TYPE:498B2Y-GPAHPAH")
                        && i.getModelLineSibInterieurVo().getSibInterieurCode().equals("N5V-FF")){
                    return false;
                }
            }

            // 库存过滤
            return bestRecommendStockService.validRecommendStock(i, dealerCode);
        }).collect(Collectors.toList());
        if (sortedByStockNum != null && sortedByStockNum){
            recommendCarVos.sort(Comparator.comparing(BestRecommendCarVo::getStockNum).reversed());
        }
        return successMessage(recommendCarVos);
    }

    @PostMapping(value = {"/modelLine/configs/priceCompute", "/public/modelLine/configs/priceCompute"})
    @ApiOperation("价格计算")
    public AjaxMessage<Object> priceCompute(@RequestHeader(value="X-Member-Id", required = false) String memberId, @RequestBody @Validated PriceComputeParam priceComputeParam, BindingResult bindingResult) throws ServiceException {
        super.validParam(bindingResult);
        log.info("=========price compute memberId: {}", memberId);
        Object price = modelLineService.computePrice(priceComputeParam);
        return successMessage(price);
    }

    @PostMapping(value = {"/modelLine/configs/measurePriceCompute", "/public/modelLine/configs/measurePriceCompute"})
    @ApiOperation("半订制价格计算")
    public AjaxMessage<Object> measurePriceCompute(@RequestBody @Validated PriceComputeParam priceComputeParam, BindingResult bindingResult) throws ServiceException {
        super.validParam(bindingResult);
        Object price = modelLineService.measurePriceCompute(priceComputeParam);
        return successMessage(price);
    }

    @PostMapping(value = {"/modelLine/configs/ccEstimate", "/public/modelLine/configs/ccEstimate"})
    @ApiOperation("CC预计交付时间")
    public AjaxMessage<CcEstimateVo> ccEstimate(@RequestBody @Validated CcEstimateDeliveryParam estimateDeliveryParam, BindingResult bindingResult) throws Exception {
        super.validParam(bindingResult);
        CcEstimateVo res = modelLineService.ccEstimate(estimateDeliveryParam);
        return successMessage(res);
    }

    @PostMapping(value = {"/modelLine/configs/estimateQuery", "/public/modelLine/configs/estimateQuery"})
    @ApiOperation("预计交付时间")
    public AjaxMessage<EstimateDeliveryRes> estimateQuery(@RequestBody @Validated EstimateDeliveryParam estimateDeliveryParam, BindingResult bindingResult) throws Exception {
        super.validParam(bindingResult);
        EstimateDeliveryRes res = modelLineService.estimateQuery(estimateDeliveryParam);
        if (res != null){
            Integer num = null;
            if (StringUtils.isNotBlank(res.getEstimateOfflineCycle())){
                num = Integer.valueOf(res.getEstimateOfflineCycle());
            }
            if (StringUtils.isNotBlank(res.getLgsCycle())){
                if (num == null){
                    num = 0;
                }
                num = num + Integer.valueOf(res.getLgsCycle());
            }
            if (num != null){
                res.setEstimateDate(LocalDate.now().plusDays(num));
            }
        }
        return successMessage(res);
    }

    @GetMapping(value = {"/modelLine/configs/estimateQueryByCc"})
    @ApiOperation("配置单预计交付时间")
    public AjaxMessage<EstimateDeliveryRes> estimateQueryByCc(@RequestParam String ccid, String dealerNetCode) throws Exception {
        EstimateDeliveryRes res = modelLineService.estimateQueryByCc(ccid, dealerNetCode);
        if (res != null){
            Integer num = null;
            if (StringUtils.isNotBlank(res.getEstimateOfflineCycle())){
                num = Integer.valueOf(res.getEstimateOfflineCycle());
            }
            if (StringUtils.isNotBlank(res.getLgsCycle())){
                if (num == null){
                    num = 0;
                }
                num = num + Integer.valueOf(res.getLgsCycle());
            }
            if (num != null){
                res.setEstimateDate(LocalDate.now().plusDays(num));
            }
        }
        return successMessage(res);
    }

    @GetMapping(value = {"/modelLine/configs/similarityModel"})
    @ApiOperation("配置单相似车")
    public AjaxMessage<EstimateDeliveryRes> similarityModel(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, @RequestParam String ccid,@RequestParam String dealerNetCode) throws Exception {
        EstimateDeliveryRes res = modelLineService.similarityModel(ccid, dealerNetCode, channel);
        return successMessage(res);
    }

    @PostMapping("/public/source")
    @ApiOperation("配置来源")
    @Transactional
    public AjaxMessage<CustomSourceVo> source(@Validated @RequestBody CustomSourceDto sourceDto, BindingResult bindingResult) throws Exception {
        validParam(bindingResult);
        LambdaQueryWrapper<CarCustomSource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
//                .eq(CarCustomSource::getName, sourceDto.getName())
                .eq(CarCustomSource::getApp, sourceDto.getApp())
                .eq(CarCustomSource::getDealerCode, sourceDto.getDealerCode())
//                .eq(CarCustomSource::getDealerName, sourceDto.getDealerName())
                ;
        CarCustomSource source = new CarCustomSource();
        CustomSourceVo vo = new CustomSourceVo();
        List<CarCustomSource> exists = source.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(exists)){
            CarCustomSource carCustomSource = exists.get(0);
            BeanUtils.copyProperties(carCustomSource, vo);
            vo.setSourceId(String.valueOf(carCustomSource.getSourceId()));
        }else {
            BeanUtils.copyProperties(sourceDto, source);
            source.setCreateTime(LocalDateTime.now());
            source.setType("2");
            source.insert();
            BeanUtils.copyProperties(source, vo);
            vo.setSourceId(String.valueOf(source.getSourceId()));
        }
        return successMessage(vo);
    }

    @GetMapping("/detail")
    @ApiOperation("获取配置单")
    public AjaxMessage<CarCustomDetail> carconfigDetail(@RequestParam String ccid) throws Exception {
        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        if (carCustom == null){
            return failureMessage(null);
        }
        String channel = Constant.ONEAPP_CHANNEL;
        CarCustomDetail customDetail = carConfigSnapshot.carConfigDetail(channel, carCustom);
        return new AjaxMessage<>("00", "成功", customDetail);
    }

    @GetMapping("/detailV2")
    @ApiOperation("获取配置单")
    public AjaxMessage<CarCustomDetail> carconfigDetailV2(@RequestHeader(value = "X-User-Id",required = false) String userId,
                                                          @RequestParam String ccid) throws Exception {
        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        if (carCustom == null || !carCustom.getUserId().equals(userId)){
            return failureMessage(null);
        }
        String channel = Constant.ONEAPP_CHANNEL;
        CarCustomDetail customDetail = carConfigSnapshot.carConfigDetail(channel, carCustom);
        return new AjaxMessage<>("00", "成功", customDetail);
    }

    @GetMapping("/{ccid}")
    @ApiOperation("获取配置单")
    public AjaxMessage<CarCustomVo> carconfig(@PathVariable("ccid") String ccid) throws Exception {
        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        if (carCustom == null){
            return failureMessage(null);
        }
        CarCustomVo customVo = carConfigSnapshot.getCarCustomVo(carCustom);
        return new AjaxMessage<>("00", "成功", customVo);

    }

    @GetMapping("/refresh")
    @ApiOperation("刷新配置单id")
    public AjaxMessage<String> refreshCc(@RequestParam String ccid) throws Exception {
        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        if (carCustom == null){
            return failureMessage(null);
        }
        String newCcid = carCustomService.refreshCcid(carCustom);
        return new AjaxMessage<>("00", "成功", newCcid);

    }

    @GetMapping("/share/{ccid}")
    @ApiOperation("分享配置单")
    public AjaxMessage<Boolean> shareCarConfig(
            @RequestHeader(value = "X-User-Id", required = false) String userId,
            @RequestHeader(value = "X-User-idpId", required = false) String idpId,
            @PathVariable("ccid") String ccid) throws Exception {
        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        if (carCustom == null){
            return failureMessage(null);
        }
        CarCustomDetail customDetail = carCustomService.getCarConfigDetail(Constant.MASTER_CHANNEL, carCustom);
        String seriesCode = customDetail.getConfigDetail().getCarSeries().getSeriesCode();
        Map<String, String> params = new HashMap<>();
        params.put("userId", userId);
        params.put("idpId", idpId);
        params.put("key1", userId);
        params.put("key2", seriesCode);
        audiTaskFeign.shareCarConfigLine(params);

        return new AjaxMessage<>("00", "成功", true);
    }

    @GetMapping(value = {"/loanCalculator/modelLine", "/public/loanCalculator/modelLine"})
    @ApiOperation("贷款计算器配置线(车型)")
    public AjaxMessage<List<ModelLineVo>> loanCalculatorModelLine(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                    @RequestParam(required = false) String customSeriesId, @RequestParam(required = false, defaultValue = "3") Integer type) throws Exception {
        ModelParamDto paramDto = new ModelParamDto();
        if (StringUtils.isNotBlank(customSeriesId)) {
            CustomSeriesParam seriesParam = new CustomSeriesParam();
            seriesParam.setCustomSeriesId(customSeriesId);
            List<CarCustomSeries> carCustomSeries = customSeriesService.listCustomSeries(channel, seriesParam);
            if (CollectionUtils.isEmpty(carCustomSeries)) {
                throw new ServiceException("参数异常：customSeriesId", "400401", "");
            }
            paramDto.setCustomSeriesId(customSeriesId);
        }
        paramDto.setDelFlag(0);
        paramDto.setChannel(channel);
        List<ModelLineVo> data = modelLineService.listModelLine(paramDto);
        data = data.stream().filter(i-> {
            if(i.getOmdModelStatus() == 0 && (i.getMeasure() == null || i.getMeasure() != 1) && Constant.NEED_TYPE_FLAG.contains(channel)){
                if (StringUtils.isBlank(i.getTypeFlag())){
                    return false;
                }
            }
            List<ModelLineOptionVo> optionVos;
            try {
                optionVos = modelLineService.modelLinePersonalOptionDefault(i.getModelLineId());
                if(i.getPriceAddColor() != null && i.getPriceAddColor().intValue() == 1) {
                    String category = "COLOR_EXTERIEUR";
                    List<ModelLineOptionVo> exterieurs = modelLineService.modelLineOption(channel, i.getModelLineId(), category).stream().filter(e -> e.getDefaultConfig() == 1).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(exterieurs)) {
                        optionVos.add(exterieurs.get(0));
                    }
                }
            } catch (Exception e) {
                log.error("查询配置线默认私人定制异常", e);
                return false;
            }
            if (CollectionUtils.isNotEmpty(optionVos)){
                List<String> optionIds = optionVos.stream().map(ModelLineOptionVo::getOptionId).collect(Collectors.toList());
                PriceComputeParam computeParam = new PriceComputeParam();
                computeParam.setOptionIds(optionIds);
                computeParam.setModelLineId(i.getModelLineId());
                try {
                    i.setPrice(modelLineService.computePrice(computeParam));
                } catch (ServiceException e) {
                    log.error("查询配置线价格计算异常", e);
                    return false;
                }
            }
            if (i.getFrontStatus() == null || i.getFrontStatus().intValue() == 0) {
                return false;
            }
            switch (type){
                case 1:
                    return i.getPersonal() != null && i.getPersonal().intValue() == 1;
                case 2:
                    return i.getMeasure() != null && i.getMeasure().intValue() == 1 && modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId());
                case 3:
                    return (i.getMeasure() != null && i.getMeasure().intValue() == 1 && modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId())) ||
                            (i.getPersonal() != null && i.getPersonal().intValue()  == 1);
            }
            return false;
        }).sorted((a,b)->{
            BigDecimal aPrice=null, bPrice=null;
            if (a.getPrice() != null && a.getPrice() instanceof BigDecimal){
                aPrice = (BigDecimal) a.getPrice();
            }
            if (b.getPrice() != null && b.getPrice() instanceof BigDecimal){
                bPrice = (BigDecimal) b.getPrice();
            }
            if (aPrice==null && bPrice== null){
                return 0;
            }
            if (aPrice == null){
                return 1;
            }
            if (bPrice == null){
                return -1;
            }
            return aPrice.compareTo(bPrice);
        }).collect(Collectors.toList());
        List<String> modelLineIds = modelLineLoanRuleService.loanRuleModelLineIds(customSeriesId);
        data = data.stream().filter(i->modelLineIds.contains(i.getModelLineId())).collect(Collectors.toList());
        return successMessage(data);
    }

    @PostMapping(value = {"/modelLine/configs/frontOptions", "/public/modelLine/configs/frontOptions"})
    @ApiOperation("配置线前端配置")
    public AjaxMessage<FrontOptions> modelLineConfigsValidation(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                           @RequestBody ConfigValidationParam param, BindingResult bindingResult) throws Exception {
        super.validParam(bindingResult);
        FrontOptions frontOptions = modelLineService.listFrontOptions(param, channel);
        return successMessage(frontOptions);
    }

    @PostMapping(value = {"/modelLine/minip2D", "/public/modelLine/minip2D"})
    @ApiOperation("小程序2D配车，车型列表")
    public AjaxMessage<List<ModelLineVo>> minip2D(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                             @RequestParam String customSeriesId) throws Exception {
        CustomSeriesParam seriesParam = new CustomSeriesParam();
        seriesParam.setCustomSeriesId(customSeriesId);
        List<CarCustomSeries> carCustomSeries = customSeriesService.listCustomSeries(channel, seriesParam);
        if (CollectionUtils.isEmpty(carCustomSeries)){
            throw new ServiceException("参数异常：customSeriesId", "400401", "");
        }
        ModelParamDto paramDto = new ModelParamDto();
        paramDto.setCustomSeriesId(customSeriesId);
        paramDto.setDelFlag(0);
        paramDto.setChannel(channel);
        List<ModelLineVo> data = modelLineService.listModelLine(paramDto);
        data = data.stream().filter(i->{
            if (i.getFrontStatus() == null || i.getFrontStatus().intValue() == 0) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        List<ModelLineVo> res = new ArrayList<>();
        // 正常配置线加上，推荐车配置线
        List<RecommendCarSphereVo> recommends = recommendService.recommendCarSphere(channel, customSeriesId);
        if (recommends != null) {
            res.addAll(recommends.stream().map(RecommendCarSphereVo::getModelLineVo).collect(Collectors.toList()));
        }
        res.addAll(data);
        res = res.stream().filter(i-> {
                    List<ModelLineOptionVo> optionVos;
                    try {
                        optionVos = modelLineService.modelLinePersonalOptionDefault(i.getModelLineId());
                        if(i.getPriceAddColor() != null && i.getPriceAddColor().intValue() == 1) {
                            String category = "COLOR_EXTERIEUR";
                            List<ModelLineOptionVo> exterieurs = modelLineService.modelLineOption(channel, i.getModelLineId(), category).stream().filter(e -> e.getDefaultConfig() == 1).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(exterieurs)) {
                                optionVos.add(exterieurs.get(0));
                            }
                        }
                    } catch (Exception e) {
                        log.error("查询配置线默认私人定制异常", e);
                        return false;
                    }
                    if (CollectionUtils.isNotEmpty(optionVos)){
                        List<String> optionIds = optionVos.stream().map(ModelLineOptionVo::getOptionId).collect(Collectors.toList());
                        PriceComputeParam computeParam = new PriceComputeParam();
                        computeParam.setOptionIds(optionIds);
                        computeParam.setModelLineId(i.getModelLineId());
                        try {
                            i.setPrice(modelLineService.computePrice(computeParam));
                        } catch (ServiceException e) {
                            log.error("查询配置线价格计算异常", e);
                            return false;
                        }
                    }
                    return (i.getMeasure() != null && i.getMeasure().intValue() == 1 && modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId())) ||
                            (i.getPersonal() != null && i.getPersonal().intValue()  == 1);
                }).collect(Collectors.toList());

        return successMessage(res);
    }

}

