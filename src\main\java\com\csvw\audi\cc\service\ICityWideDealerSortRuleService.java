package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.vo.DealerVo;
import com.csvw.audi.common.model.activity.Activity4IntraCity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/2 17:19
 * @description 同城经销商列表排序
 */
public interface ICityWideDealerSortRuleService {

    List<DealerVo> sortByCityWideRule(List<DealerVo> dealerList, List<String> hopOrAudiCityDealerList,
        Map<String, List<Activity4IntraCity>> activity4IntraCityMap);
}
