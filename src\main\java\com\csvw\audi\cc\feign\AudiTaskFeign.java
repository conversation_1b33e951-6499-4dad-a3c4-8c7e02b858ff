package com.csvw.audi.cc.feign;

import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.common.AjaxMessage;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@FeignClient(name = "audi-task")
//@FeignClient(value = "audi-task", url = "http://127.0.0.1:8082")
public interface AudiTaskFeign {

    @PostMapping("/private/task/shareCarConfigLine")
    JSONObject shareCarConfigLine(@RequestBody Map<String, String> map);

    @PostMapping(value = "/private/task/finishReserveInvitation", consumes = MediaType.APPLICATION_JSON_VALUE)
    JSONObject finishReserveInvitation(@RequestBody String jsonObject);

}
