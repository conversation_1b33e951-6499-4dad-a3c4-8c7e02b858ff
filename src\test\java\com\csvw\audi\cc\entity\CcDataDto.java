package com.csvw.audi.cc.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class CcDataDto {
    public CcDataDto(String modelId, String typeId, String typeCode, String typeName){
        this.modelId = modelId;
        this.typeId = typeId;
        this.typeName = typeName;
        this.typeCode = typeCode;
    }
    private String modelId;
    private String typeId;
    private String typeName;
    private String typeCode;
    private List<String> exterieurCodes;
    private List<String> interieurCodes;
    private List<String> wheelCodes;
    private List<String> interieurStyleCodes;
    private List<String> optionCodes;
    private List<String> optionPackageCodes;
}