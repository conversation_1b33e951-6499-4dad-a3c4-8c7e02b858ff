package com.csvw.audi.cc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csvw.audi.cc.config.OssConfig;
import com.csvw.audi.cc.entity.dto.DealerDto;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.entity.vo.DealerVo;
import com.csvw.audi.cc.feign.AudiActivityServiceFeign;
import com.csvw.audi.cc.mapper.SvcdChannelOrganizationFileMapper;
import com.csvw.audi.cc.mapper.SvcdChannelOrganizationMapper;
import com.csvw.audi.cc.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csvw.audi.cc.common.utils.StringUtil;
import com.csvw.audi.common.model.activity.Activity4IntraCity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 渠道商组织信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Service
public class SvcdChannelOrganizationServiceImpl extends ServiceImpl<SvcdChannelOrganizationMapper, SvcdChannelOrganization> implements ISvcdChannelOrganizationService {

//    @Value("${svcd.dealer-image-url}")
//    private String svcdDealerImageUrl;

    @Autowired
    private OssConfig ossConfig;

    //总部图片 封面图
    static String dealerHeadquartersImageUrl = "test/2021/06/02/a7l/dealer-headquarters2.png";
    //总部图片 略缩图
    static String dealerHeadquartersThumbnailUrl = "test/2021/06/02/a7l/dealer-headquarters2-thumbnai.png";
    //代理商默认图片 封面图
    static String dealerDefaultImageUrl = "test/2021/06/02/a7l/dealer-default.png";
    //代理商默认图片 略缩图
    static String dealerDefaultThumbnailUrl = "test/2021/06/02/a7l/dealer-default-thumbnai.png";
    //服务商默认图片 封面图
    static String afterSalesDefaultImageUrl = "test/2021/06/02/a7l/afterSales-default.png";
    //服务商默认图片 略缩图
    static String afterSalesDefaultThumbnailUrl = "test/2021/06/02/a7l/afterSales-default-thumbnai.png";

    @Autowired
    private SvcdChannelOrganizationMapper svcdChannelOrganizationMapper;

    @Autowired
    private SvcdChannelOrganizationFileMapper svcdChannelOrganizationFileMapper;

    @Autowired
    private ISvcdOrgRegionService svcdOrgRegionService;

    @Autowired
    private ISvcdCityService svcdCityService;
    @Autowired
    private AudiActivityServiceFeign audiActivityServiceFeign;
    @Autowired
    private ICityWideDealerSortRuleService cityWideDealerSortRuleService;

    @Override
    public List<DealerVo> getAgentList(DealerDto dealerDto) {
        //默认返回总部
        if(dealerDto.getDefaultHeadquarters() == null) {
            dealerDto.setDefaultHeadquarters(1);
        }

        //默认门店图片
        if(dealerDto.getHaveImage() == null) {
            dealerDto.setHaveImage(1);
        }

        List<DealerVo> list = svcdChannelOrganizationMapper.getAgentList(dealerDto);
        if(list == null) {
            list = new ArrayList<>();
            if(dealerDto.getDefaultHeadquarters().intValue() == 1) {
                list.add(getHeadquarters());//取总部信息
            }
        } else if(dealerDto.getDefaultHeadquarters().intValue() == 1) {
            // 没有总部加总部
            Set<String> dealerCodes = list.stream().map(i->i.getDealerCode()).collect(Collectors.toSet());
            if (!dealerCodes.contains("76600019")) {
                list.add(getHeadquarters());//取总部信息
            }
        }

        //设置代理商图片
        if(dealerDto.getHaveImage() == 1 && list.size() > 0) {
            setDealerImage(list);
        }

        //门店平分保留1位小数
        for(DealerVo vo : list) {
            if(StringUtil.isEmpty(vo.getEvaluateScore())) {
                continue;
            }
            try {
                vo.setEvaluateScore(new BigDecimal(vo.getEvaluateScore()).setScale(1, RoundingMode.HALF_UP).toString());
            } catch (Exception e) {
                vo.setEvaluateScore("0.0");
                continue;
            }
        }

        return list;
    }

    @Override
    public List<DealerVo> getNearestAgent(DealerDto dealerDto) {
        //默认返回总部
        if(dealerDto.getDefaultHeadquarters() == null) {
            dealerDto.setDefaultHeadquarters(0);
        }

        //默认不返回城市所在大区门店
        if(dealerDto.getIfRegionCodeByCity() == null) {
            dealerDto.setIfRegionCodeByCity(0);
        }

        List<DealerVo> list = svcdChannelOrganizationMapper.getAgentList(dealerDto);
        //如果该城市没有代理商，则取该城市所在大区的所有代理商
        if((list == null || list.size() == 0) && StringUtil.isNotEmpty(dealerDto.getCityCode()) && dealerDto.getIfRegionCodeByCity().intValue() == 1) {
            QueryWrapper<SvcdOrgRegion> queryWrapper = new QueryWrapper();
            queryWrapper.last(" where code = (SELECT pcode FROM svcd_org_region where region_type = 'province' and code = (SELECT pcode FROM svcd_org_region where region_type = 'city' and region_code = '"+dealerDto.getCityCode()+"'))");
            List<SvcdOrgRegion> orgRegionList =  svcdOrgRegionService.list(queryWrapper);
            if(orgRegionList.size() > 0) {
                DealerDto paramDto = new DealerDto();
                paramDto.setRegionCode(orgRegionList.get(0).getRegionCode());
                list = svcdChannelOrganizationMapper.getAgentList(paramDto);
            }
        }

        if(list == null) {
            list = new ArrayList<>();
        }

        //设置代理商图片
        if(list.size() > 0) {
            setDealerImage(list);
        }

        //排序
        if(isValid(String.valueOf(dealerDto.getLatitude())) && isValid(String.valueOf(dealerDto.getLongitude()))) {
            order(list,dealerDto.getLongitude(),dealerDto.getLatitude());
        }

        //将总部排序到末尾
        if(dealerDto.getDefaultHeadquarters().intValue() == 1) {
            headquartersInTheEnd(list);
        }

        //强制移除总部
        for(DealerVo vo : list){
            if("76600019".equals(vo.getDealerCode())) {
                list.remove(vo);
                break;
            }
        }

        return list;
    }

    @Override
    public DealerVo getHeadquarters() {
        DealerVo headquarters;
        DealerDto dealerDto = new DealerDto();
        dealerDto.setDealerCode("76600019");
        List<DealerVo> list = svcdChannelOrganizationMapper.getAgentList(dealerDto);
        if(list == null || list.size() == 0) {
            headquarters = new DealerVo();
            headquarters.setDealerCode("76600019");
            headquarters.setDealerName("Audi house of Progress SH");
            headquarters.setDealerContacts("销售员");
            headquarters.setCityCode("310100");
            headquarters.setCityName("上海市/上海市");
            headquarters.setImageUrl(getDealerDefaultImageUrl(headquarters));
            headquarters.setThumbnailUrl(getDealerDefaultThumbnailImageUrl(headquarters));
        } else {
            headquarters = list.get(0);
            //设置代理商图片
            if(list.size() > 0) {
                setDealerImage(list);
            }
        }
        return headquarters;
    }

    @Override
    public List<DealerVo> getDealerListByOfficialWebsite(DealerDto dealerDto) {
        //默认查询全部
        if(dealerDto.getSearchType() == null) {
            dealerDto.setSearchType(0);
        }
        //默认取门店图片
        if(dealerDto.getHaveImage() == null) {
            dealerDto.setHaveImage(1);
        }

        List<DealerVo> list = svcdChannelOrganizationMapper.getDealerListByOfficialWebsite(dealerDto);
//        log.error("dealerVo: "+JSONObject.toJSONString(list));
        if(list == null) {
            list = new ArrayList<>();
        }
        //设置代理商图片
        if(dealerDto.getHaveImage() == 1 && list.size() > 0) {
            setDealerImage(list);
        }

        // 检索结果包含 服务商
        if(dealerDto.getSearchType() != 2) {
            for(DealerVo vo : list) {
                // 服务商 处理经纬度,地址,服务电话
                if(vo.getDealerCode().indexOf("743") == 0 && vo.getDealerType() != 4) {
                    if(vo.getAfterSalesLatitude() != null) {
                        vo.setLatitude(vo.getAfterSalesLatitude());
                        vo.setAfterSalesLatitude(null);
                    }
                    if(vo.getAfterSalesLongitude() != null) {
                        vo.setLongitude(vo.getAfterSalesLongitude());
                        vo.setAfterSalesLongitude(null);
                    }
                    if(StringUtil.isNotEmpty(vo.getAfterSalesAdrress())) {
                        vo.setDealerAdrress(vo.getAfterSalesAdrress());
                        vo.setAfterSalesAdrress(null);
                    }
                    if(StringUtil.isNotEmpty(vo.getServicePhone())) {
                        vo.setDealerPhone(vo.getServicePhone());
                    }
                }
            }
        }

        //排序
        if(isValid(String.valueOf(dealerDto.getLatitude())) && isValid(String.valueOf(dealerDto.getLongitude()))) {
            order(list,dealerDto.getLongitude(),dealerDto.getLatitude());
        }

        //如果列表包含总部，则将总部移至列表末尾
        DealerVo voTemp = null;
        for(DealerVo vo : list){
            if("76600019".equals(vo.getDealerCode())) {
                voTemp = vo;
                list.remove(vo);
                break;
            }
        }
//        if(voTemp != null) {
//            list.add(voTemp);
//        }

        //用户中心售后渠道商合并
        List<DealerVo> collect = getStringDealerVoMap(list).values().stream().collect(Collectors.toList());

        //searchType 为1,0 填补用户中心的dealerType=1,2
        DealerDto dealerDto2 = new DealerDto();
        dealerDto2.setProvinceCode(dealerDto.getProvinceCode());
        dealerDto2.setCityCode(dealerDto.getCityCode());
        dealerDto2.setSearchType(0);
        Map<String, DealerVo> stringDealerVoMap = getStringDealerVoMap(svcdChannelOrganizationMapper.getDealerListByOfficialWebsite(dealerDto2));
        if (!stringDealerVoMap.isEmpty()){
            collect.stream().forEach(a->{
                if(a.getDealerCode().length() < 3){
                    return;
                }
                DealerVo dealerVo = stringDealerVoMap.get(a.getDealerCode().substring(3));
                if (dealerVo!=null&& StrUtil.isNotBlank(dealerVo.getDealerTypeStr())){
                    BeanUtils.copyProperties(dealerVo,a);
                }
            });
        }
//        log.error("collect: "+JSONObject.toJSONString(collect));
        collect.forEach(l->Optional.ofNullable(l.getHasNum()).ifPresent(n->{
            if (n.intValue()>0){
                l.setControlStatus("有");
            }else {
                l.setControlStatus("");
            }
        }));
        return collect;
    }

    private Map<String, DealerVo> getStringDealerVoMap(List<DealerVo> list) {
        Map<String,DealerVo> repeatMap = new HashMap<>();
        ArrayList types = new ArrayList<Integer>();
        list.stream().forEach(a->{
            if(a.getDealerCode().length() < 3){
                return;
            }
            if (repeatMap.containsKey(a.getDealerCode().substring(3))){
                DealerVo dealerVo = repeatMap.get(a.getDealerCode().substring(3));
                types.add(a.getDealerType());
                types.add(dealerVo.getDealerType());
                //只合并用户中心
                if (!types.contains(4)){
                    repeatMap.put(IdUtil.fastUUID(),a);
                    types.clear();
                    return;
                }
                types.clear();
                if (a.getDealerType().equals(4)){
                    dealerVo.setAfterSalesAdrress(a.getAfterSalesAdrress());
                    dealerVo.setAfterSalesLongitude(a.getAfterSalesLongitude());
                    dealerVo.setAfterSalesLatitude(a.getAfterSalesLatitude());
                    dealerVo.setServicePhone(a.getServicePhone());
                    dealerVo.setFawAudiBatteryMaintenanceCenter(a.getFawAudiBatteryMaintenanceCenter());
                    dealerVo.setHotPhone24(a.getHotPhone24());
                    dealerVo.setRescuePhone(a.getRescuePhone());
                    dealerVo.setSaicAudiMaintenanceLevel(a.getSaicAudiMaintenanceLevel());
                    dealerVo.setHasNum(a.getHasNum());
                }else {
                    dealerVo.setDealerPhone(a.getDealerPhone());
                    dealerVo.setDealerAdrress(a.getDealerAdrress());
                    dealerVo.setLongitude(a.getLongitude());
                    dealerVo.setLatitude(a.getLatitude());
                    dealerVo.setHasNum(a.getHasNum());
                }
                return;
            }
            repeatMap.put(a.getDealerCode().substring(3),a);
        });
        return repeatMap;
    }

    @Override
    public List<DealerVo> getOrgList(DealerDto dealerDto) {
        //默认取门店图片
        if(dealerDto.getHaveImage() == null) {
            dealerDto.setHaveImage(1);
        }
        List<DealerVo> list = svcdChannelOrganizationMapper.getOrgList(dealerDto);
        if(list == null) {
            list = new ArrayList<>();
        }

        //设置代理商图片
        if(dealerDto.getHaveImage() == 1 && list.size() > 0) {
            setDealerImage(list);
        }

        for(DealerVo vo : list) {
            // 服务商 处理经纬度,地址
            if(vo.getDealerCode().indexOf("743") == 0) {
                if(vo.getAfterSalesLatitude() != null) {
                    vo.setLatitude(vo.getAfterSalesLatitude());
                    vo.setAfterSalesLatitude(null);
                }
                if(vo.getAfterSalesLongitude() != null) {
                    vo.setLongitude(vo.getAfterSalesLongitude());
                    vo.setAfterSalesLongitude(null);
                }
                if(StringUtil.isNotEmpty(vo.getAfterSalesAdrress())) {
                    vo.setDealerAdrress(vo.getAfterSalesAdrress());
                    vo.setAfterSalesAdrress(null);
                }
                if(StringUtil.isNotEmpty(vo.getServicePhone())) {
                    vo.setDealerPhone(vo.getServicePhone());
                }
            }
        }

        return list;
    }

    @Override
    public JSONObject getDealerRequestParamByOfficialWebsite(DealerDto dealerDto) {
        boolean haveDealer = false;// 代理商
        boolean haveExhibitionHallForm_1 = false;// 奥迪都市店
        boolean haveExhibitionHallForm_2 = false;// 奥迪之城
        boolean haveExhibitionHallForm_3 = false;// 奥迪进取汇
        boolean haveExhibitionHallForm_4 = false;// 用户中心
        boolean haveExhibitionHallForm_5 = false;// 轻量版都市店
        boolean haveAfterSales = false;// 服务商
        boolean haveNewEnergy = false;// 新能源车售后

        JSONObject jSONObject = new JSONObject();
        JSONArray jSONArray = new JSONArray();
        JSONArray dealerJSONArray = new JSONArray();
        JSONArray afterSalesJSONArray = new JSONArray();
        jSONObject.put("paramList",jSONArray);

        //全部
        if(dealerDto.getSearchType() == null || dealerDto.getSearchType() == 0) {
            haveDealer = true;// 代理商
            haveExhibitionHallForm_1 = true;// 奥迪都市店
            haveExhibitionHallForm_2 = true;// 奥迪之城
            haveExhibitionHallForm_3 = true;// 奥迪进取汇
            haveExhibitionHallForm_4 = true;// 用户中心
            haveExhibitionHallForm_5 = true;// 轻量版都市店
            haveAfterSales = true;// 服务商
        }
        //代理商
        else if(dealerDto.getSearchType() == 2) {
            haveDealer = true;// 代理商
            haveExhibitionHallForm_1 = true;// 奥迪都市店
            haveExhibitionHallForm_2 = true;// 奥迪之城
            haveExhibitionHallForm_3 = true;// 奥迪进取汇
            haveExhibitionHallForm_4 = true;// 用户中心
            haveExhibitionHallForm_5 = true;// 轻量版都市店
        }
        //服务商
        else if(dealerDto.getSearchType() == 1) {
            haveAfterSales = true;// 服务商
        }

        if(haveDealer) {
            if(haveExhibitionHallForm_3) {
                JSONObject dealer_3 = new JSONObject();
                dealer_3.put("description","奥迪进取汇");
                dealer_3.put("value","1-2");
                dealerJSONArray.add(dealer_3);
            }
            if(haveExhibitionHallForm_2) {
                JSONObject dealer_2 = new JSONObject();
                dealer_2.put("description","奥迪之城");
                dealer_2.put("value","1-3");
                dealerJSONArray.add(dealer_2);
            }
            if(haveExhibitionHallForm_1) {
                JSONObject dealer_1 = new JSONObject();
                dealer_1.put("description","奥迪都市店");
                dealer_1.put("value","1-4");
                dealerJSONArray.add(dealer_1);
            }
            if(haveExhibitionHallForm_4) {
                JSONObject dealer_1 = new JSONObject();
                dealer_1.put("description","用户中心");
                dealer_1.put("value","1-5");
                dealerJSONArray.add(dealer_1);
            }
            if(haveExhibitionHallForm_5) {
                JSONObject dealer_1 = new JSONObject();
                dealer_1.put("description","轻量版都市店");
                dealer_1.put("value","1-6");
                dealerJSONArray.add(dealer_1);
            }
            JSONObject dealer = new JSONObject();
            dealer.put("description","授权代理商");
            dealer.put("value","1");
            dealer.put("list",dealerJSONArray);
            jSONArray.add(dealer);
        }
        if(haveAfterSales) {
            if(haveNewEnergy) {
                JSONObject afterSales_1 = new JSONObject();
                afterSales_1.put("description","新能源车售后");
                afterSales_1.put("value","2-4");
                afterSalesJSONArray.add(afterSales_1);
            }
            JSONObject afterSales = new JSONObject();
            afterSales.put("description","授权服务商");
            afterSales.put("value","2");
            afterSales.put("list",afterSalesJSONArray);
            jSONArray.add(afterSales);
        }
        return jSONObject;
    }

    @Override
    public List<DealerVo> getDealerListByAiChe(DealerDto dealerDto) {
        //默认返回总部
        if(dealerDto.getDefaultHeadquarters() == null) {
            dealerDto.setDefaultHeadquarters(0);
        }

        //默认门店图片
        if(dealerDto.getHaveImage() == null) {
            dealerDto.setHaveImage(1);
        }

        List<DealerVo> list = svcdChannelOrganizationMapper.getAgentList(dealerDto);

        if(dealerDto.getIfRegionCodeByCity() != null && dealerDto.getIfRegionCodeByCity() == 1) {
            //如果该城市没有代理商，则取该城市所在省份的所有代理商
            if((list == null || list.isEmpty()) && StringUtil.isNotEmpty(dealerDto.getCityCode())) {
                if(StringUtil.isEmpty(dealerDto.getProvinceCode())) {
                    QueryWrapper<SvcdCity> queryWrapper = new QueryWrapper();
                    queryWrapper.eq("city_code",dealerDto.getCityCode());
                    List<SvcdCity> cityList = svcdCityService.list(queryWrapper);
                    if(!cityList.isEmpty()) {
                        dealerDto.setCityCode("");
                        dealerDto.setProvinceCode(String.valueOf(cityList.get(0).getProvinceCode()));
                        list = svcdChannelOrganizationMapper.getAgentList(dealerDto);
                    }
                } else {
                    dealerDto.setCityCode("");
                    list = svcdChannelOrganizationMapper.getAgentList(dealerDto);
                }
            }

            //如果该省份没有代理商，则取该省份所在大区的所有代理商
            if((list == null || list.isEmpty()) && StringUtil.isNotEmpty(dealerDto.getCityCode())) {
                QueryWrapper<SvcdOrgRegion> queryWrapper = new QueryWrapper();
                queryWrapper.last(" where code = (SELECT pcode FROM svcd_org_region where region_type = 'province' and code = (SELECT pcode FROM svcd_org_region where region_type = 'city' and region_code = '"+dealerDto.getCityCode()+"'))");
                List<SvcdOrgRegion> orgRegionList =  svcdOrgRegionService.list(queryWrapper);
                if(!orgRegionList.isEmpty()) {
                    dealerDto.setCityCode("");
                    dealerDto.setProvinceCode("");
                    dealerDto.setRegionCode(orgRegionList.get(0).getRegionCode());
                    list = svcdChannelOrganizationMapper.getAgentList(dealerDto);
                }
            }
        }

        if(list == null) {
            list = new ArrayList<>();
        }

        //默认添加总部信息
        if(dealerDto.getDefaultHeadquarters() == 1) {
            Set<String> dealerCodes = list.stream().map(DealerVo::getDealerCode).collect(Collectors.toSet());
            if (!dealerCodes.contains("76600019")) {
                list.add(getHeadquarters());//取总部信息
            }
        }

        //设置代理商图片
        if(dealerDto.getHaveImage() == 1 && list.size() > 0) {
            setDealerImage(list);
        }

        //门店评分保留1位小数
        for(DealerVo vo : list) {
            if(StringUtil.isEmpty(vo.getEvaluateScore())) {
                continue;
            }
            try {
                vo.setEvaluateScore(new BigDecimal(vo.getEvaluateScore()).setScale(1, RoundingMode.HALF_UP).toString());
            } catch (Exception e) {
                vo.setEvaluateScore("0.0");
                continue;
            }
        }

        //排序
        if(isValid(String.valueOf(dealerDto.getLatitude())) && isValid(String.valueOf(dealerDto.getLongitude()))) {
            order(list,dealerDto.getLongitude(),dealerDto.getLatitude());
        }

        //将总部排序到末尾
        if(dealerDto.getDefaultHeadquarters().intValue() == 1) {
            headquartersInTheEnd(list);
        }

        //移除总部
        for(DealerVo vo : list){
            if("76600019".equals(vo.getDealerCode())) {
                list.remove(vo);
                break;
            }
        }

        return list;
    }

    @Override
    public List<DealerVo> getDealerListByCityWide(DealerDto dealerDto) {
        // 默认门店图片
        if (dealerDto.getHaveImage() == null) {
            dealerDto.setHaveImage(1);
        }
        List<DealerVo> list = null;
        // 经纬度有效
        boolean effectivePositioning = isValid(String.valueOf(dealerDto.getLatitude())) && isValid(String.valueOf(dealerDto.getLongitude()));
        if (!effectivePositioning && StringUtils.isEmpty(dealerDto.getCityCode())) {
            // 没有开启定位且没有传城市编码则默认上海
            dealerDto.setCityCode("310100");
        }
        // 查询经销商的数据
        list = svcdChannelOrganizationMapper.getAgentList(dealerDto);
        // 如果该城市没有代理商,则取就近城市(取全部的进行地理位置计算) || 没有传城市,则取最近城市
        if ((CollectionUtils.isEmpty(list) && StringUtil.isNotEmpty(dealerDto.getCityCode()))
            || StringUtil.isEmpty(dealerDto.getCityCode())) {
            dealerDto.setCityCode("");
            dealerDto.setCityName("");
            dealerDto.setProvinceCode("");
            list = svcdChannelOrganizationMapper.getAgentList(dealerDto);
            if (CollectionUtils.isEmpty(list)) {
                return new ArrayList<>();
            }
            // 排序
            if (isValid(String.valueOf(dealerDto.getLatitude())) && isValid(String.valueOf(dealerDto.getLongitude()))) {
                order(list, dealerDto.getLongitude(), dealerDto.getLatitude());
            }
            // 获取距离最近的一个
            DealerVo recentDealerVo = list.get(0);
            // 跟经销商获取该经销商所在城市的列表
            dealerDto.setCityCode(recentDealerVo.getCityCode());
            list = svcdChannelOrganizationMapper.getAgentList(dealerDto);
        }
        //强制移除总部
        for(DealerVo vo : list){
            if("76600019".equals(vo.getDealerCode())) {
                list.remove(vo);
                break;
            }
        }
        // 排序
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        if (isValid(String.valueOf(dealerDto.getLatitude())) && isValid(String.valueOf(dealerDto.getLongitude()))) {
            order(list, dealerDto.getLongitude(), dealerDto.getLatitude());
        }
        // HOP or AudiCity
        List<String> hopOrAudiCityDealerList = new ArrayList<>();
        hopOrAudiCityDealerList.add("76621029");
        hopOrAudiCityDealerList.add("76649019");
        hopOrAudiCityDealerList.add("76633019");
        hopOrAudiCityDealerList.add("76600989");
        // 查询门店活动
        List<String> dealerCodeList = list.stream().map(DealerVo::getDealerCode).collect(Collectors.toList());
        List<Activity4IntraCity> activity4IntraCities = audiActivityServiceFeign.list4IntraCity(dealerCodeList);
        Map<String, List<Activity4IntraCity>> activity4IntraCityMap = null;
        // 进行经销商内容排序
        if (CollectionUtils.isNotEmpty(activity4IntraCities)) {
            activity4IntraCityMap =
                activity4IntraCities.stream().collect(Collectors.groupingBy(x -> x.getDealerCode()));
        }
        List<DealerVo> resultList =
            cityWideDealerSortRuleService.sortByCityWideRule(list, hopOrAudiCityDealerList, activity4IntraCityMap);

        // 设置代理商图片
        if (dealerDto.getHaveImage() == 1) {
            setDealerImage(resultList);
        }
        // 门店评分保留1位小数
        setEvaluateScore(resultList);

        return resultList;
    }

    /**
     * @description 门店评分保留1位小数
     */
    private void setEvaluateScore(List<DealerVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (DealerVo vo : list) {
            if (StringUtil.isEmpty(vo.getEvaluateScore())) {
                continue;
            }
            try {
                vo.setEvaluateScore(new BigDecimal(vo.getEvaluateScore()).setScale(1, RoundingMode.HALF_UP).toString());
            } catch (Exception e) {
                vo.setEvaluateScore("0.0");
                continue;
            }
        }
    }

    @Override
    public List<DealerVo> getDealerListByTestDrive(DealerDto dealerDto) {
        //默认 门店图片
        if(dealerDto.getHaveImage() == null) {
            dealerDto.setHaveImage(1);
        }
        //默认 取城市->省份->大区的数据
        if(dealerDto.getIfRegionCodeByCity() == null) {
            dealerDto.setIfRegionCodeByCity(1);
        }

        List<DealerVo> list = svcdChannelOrganizationMapper.getAgentList(dealerDto);

        if(dealerDto.getIfRegionCodeByCity() != null && dealerDto.getIfRegionCodeByCity() == 1) {
            //如果该城市没有代理商，则取该城市所在省份的所有代理商
            if((list == null || list.isEmpty()) && StringUtil.isNotEmpty(dealerDto.getCityCode())) {
                if(StringUtil.isEmpty(dealerDto.getProvinceCode())) {
                    QueryWrapper<SvcdCity> queryWrapper = new QueryWrapper();
                    queryWrapper.eq("city_code",dealerDto.getCityCode());
                    List<SvcdCity> cityList = svcdCityService.list(queryWrapper);
                    if(!cityList.isEmpty()) {
                        dealerDto.setCityCode("");
                        dealerDto.setProvinceCode(String.valueOf(cityList.get(0).getProvinceCode()));
                        list = svcdChannelOrganizationMapper.getAgentList(dealerDto);
                    }
                } else {
                    dealerDto.setCityCode("");
                    list = svcdChannelOrganizationMapper.getAgentList(dealerDto);
                }
            }

            //如果该省份没有代理商，则取该省份所在大区的所有代理商
            if((list == null || list.isEmpty()) && StringUtil.isNotEmpty(dealerDto.getCityCode())) {
                QueryWrapper<SvcdOrgRegion> queryWrapper = new QueryWrapper();
                queryWrapper.last(" where code = (SELECT pcode FROM svcd_org_region where region_type = 'province' and code = (SELECT pcode FROM svcd_org_region where region_type = 'city' and region_code = '"+dealerDto.getCityCode()+"'))");
                List<SvcdOrgRegion> orgRegionList =  svcdOrgRegionService.list(queryWrapper);
                if(!orgRegionList.isEmpty()) {
                    dealerDto.setCityCode("");
                    dealerDto.setProvinceCode("");
                    dealerDto.setRegionCode(orgRegionList.get(0).getRegionCode());
                    list = svcdChannelOrganizationMapper.getAgentList(dealerDto);
                }
            }
        }

        if(list == null) {
            list = new ArrayList<>();
        }

        //设置代理商图片
        if(dealerDto.getHaveImage() == 1 && list.size() > 0) {
            setDealerImage(list);
        }

        //数据处理
        for(int i = 0 ; i < list.size() ; i++) {
            DealerVo vo = list.get(i);
            //移除总部信息
            if("76600019".equals(vo.getDealerCode())) {
                list.remove(i);
                i--;
                continue;
            }

            //门店评分保留1位小数
            if(StringUtil.isNotEmpty(vo.getEvaluateScore())) {
                try {
                    vo.setEvaluateScore(new BigDecimal(vo.getEvaluateScore()).setScale(1, RoundingMode.HALF_UP).toString());
                } catch (Exception e) {
                    vo.setEvaluateScore("0.0");
                }
            }
        }

        //排序
        if(isValid(String.valueOf(dealerDto.getLatitude())) && isValid(String.valueOf(dealerDto.getLongitude()))) {
            order(list,dealerDto.getLongitude(),dealerDto.getLatitude());
        }

        return list;
    }

    //获取门店图片
    private void setDealerImage(List<DealerVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Set<String> dealerCodes = list.stream().map(i->i.getDealerCode()).collect(Collectors.toSet());
        QueryWrapper<SvcdChannelOrganizationFile> fileQuery = new QueryWrapper<>();
        fileQuery.eq("file_type","7").in("dealer_code", new ArrayList<>(dealerCodes));
        List<SvcdChannelOrganizationFile> fileList = svcdChannelOrganizationFileMapper.selectList(fileQuery);
        String dealerImagePath;
        for(DealerVo vo : list) {
            dealerImagePath = ossConfig.getDealerImagePath() + "/" + vo.getDealerCode() + "/";
            for(SvcdChannelOrganizationFile fileObj : fileList) {
                if(vo.getDealerCode().equals(fileObj.getDealerCode())) {
                    if(fileObj.getName() != null && fileObj.getName().indexOf("封面图片") == 0) {
                        vo.setImageUrl(dealerImagePath+fileObj.getUrl());
                    }
                    else if(fileObj.getName() != null && fileObj.getName().indexOf("细节图片") == 0) {
                        vo.setThumbnailUrl(dealerImagePath+fileObj.getUrl());
                    }
                }
            }
            //设置默认封面图
            if(vo.getImageUrl() == null || "".equals(vo.getImageUrl())) {
                vo.setImageUrl(getDealerDefaultImageUrl(vo));
            }
            //设置默认略缩图
            if(vo.getThumbnailUrl() == null || "".equals(vo.getThumbnailUrl())) {
                vo.setThumbnailUrl(getDealerDefaultThumbnailImageUrl(vo));
            }
//            vo.setImageUrl(getDealerDefaultImageUrl(vo));
//            vo.setThumbnailUrl(getDealerDefaultThumbnailImageUrl(vo));
        }
    }

    //经纬度值有效性判断
    private Boolean isValid(String a){
        if(StringUtil.isEmpty(a)) {
            return false;
        }
        try {
            return Math.abs(Double.parseDouble(a) - 0) > 0.001;
        } catch (Exception e) {
            return false;
        }
    }

    //取默认图片 封面图
    private String getDealerDefaultImageUrl(DealerVo dealerVo){
        if("76600019".equals(dealerVo.getDealerCode())) {
            return dealerHeadquartersImageUrl;
        } else if(dealerVo.getDealerCode().indexOf("766") == 0) {
            return dealerDefaultImageUrl;
        } else if(dealerVo.getDealerCode().indexOf("743") == 0) {
            return afterSalesDefaultImageUrl;
        }
        return dealerDefaultImageUrl;
    }

    //取默认图片 略缩图
    private String getDealerDefaultThumbnailImageUrl(DealerVo dealerVo){
        if("76600019".equals(dealerVo.getDealerCode())) {
            return dealerHeadquartersThumbnailUrl;
        } else if(dealerVo.getDealerCode().indexOf("766") == 0) {
            return dealerDefaultThumbnailUrl;
        } else if(dealerVo.getDealerCode().indexOf("743") == 0) {
            return afterSalesDefaultThumbnailUrl;
        }
        return dealerDefaultThumbnailUrl;
    }

    //按距离由近至远排序
    private void order(List<DealerVo> list,Double longitude,Double latitude) {
        //计算经纬度距离
        for(DealerVo dealer : list) {
            if(isValid(dealer.getLatitude()) && isValid(dealer.getLongitude())) {
                dealer.setDistance(BigDecimal.valueOf(StringUtil.gitDistance(Double.parseDouble(dealer.getLongitude()),Double.parseDouble(dealer.getLatitude()),longitude,latitude)).setScale(2, RoundingMode.HALF_UP).doubleValue());
            }
        }
        //按距离由近到远排序
        DealerVo dealerTemp;
        for(int i = 0 ; i < list.size() ; i ++) {
            for(int j = 0 ; j < list.size()-i-1 ; j ++) {
                if(list.get(j).getDistance() == null && list.get(j+1).getDistance() != null) {
                    dealerTemp = list.get(j);
                    list.set(j,list.get(j+1));
                    list.set(j+1,dealerTemp);
                }
                else if(list.get(j+1).getDistance() != null && list.get(j).getDistance().doubleValue() > list.get(j+1).getDistance().doubleValue()) {
                    dealerTemp = list.get(j);
                    list.set(j,list.get(j+1));
                    list.set(j+1,dealerTemp);
                }
            }
        }
    }

    //将总部门店排到列表末尾
    private void headquartersInTheEnd(List<DealerVo> list) {
        // 没有总部加总部
        Set<String> dealerCodes = list.stream().map(i->i.getDealerCode()).collect(Collectors.toSet());
        if (!dealerCodes.contains("76600019")) {
            list.add(getHeadquarters());//取总部信息
        } else {
            DealerVo voTemp = null;
            for(DealerVo vo : list) {
                if("76600019".equals(vo.getDealerCode())) {
                    voTemp = vo;
                    list.remove(vo);
                    break;
                }
            }
            if(voTemp != null) {
                list.add(voTemp);
            }
        }
    }

    @Override
    public List<SvcdChannelOrganization> selectSvcdChannelOrganizationByPCode(String pCode) {
        return svcdChannelOrganizationMapper.selectSvcdChannelOrganizationByPCode(pCode);
    }
}
