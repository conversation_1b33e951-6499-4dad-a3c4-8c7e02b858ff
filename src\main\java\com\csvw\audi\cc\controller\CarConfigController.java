package com.csvw.audi.cc.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.dto.CustomSourceDto;
import com.csvw.audi.cc.entity.dto.accb.AudiConfigDto;
import com.csvw.audi.cc.entity.dto.accb.OptionBriefDto;
import com.csvw.audi.cc.entity.dto.vwcc.*;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.entity.vo.*;
import com.csvw.audi.cc.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@RestController
@RequestMapping("/api/v1/carconfig")
@Api(tags = "车辆配置")
public class CarConfigController extends BaseController {

    @Autowired
    private IACCBService iaccbService;

    @Autowired
    private ICarCustomOptionService optionService;

    @Autowired
    private ICarCustomService carCustomService;

    @Autowired
    private IOptionService typeOptionService;

    @Autowired
    private IParameterService parameterService;

    @PostMapping
    public AjaxMessage<CarCustomDetail> addcarconfig(@RequestHeader(value = "X-User-Id", required = false) String userId,
                                                     @RequestHeader(value = "X-User-Mobile", required = false) String userMobile,
                                                     @RequestParam String customColorId){
        String[] colors = {"COLOR_EXTERIEUR:3ZA2", "COLOR_EXTERIEUR:B9A2"};
        List<String> cl = Arrays.asList(colors);
        if (customColorId == null || !cl.contains(customColorId)){
            return new AjaxMessage<>("40004", "参数异常：customColorId", null);
        }
        CarCustom carCustom = carCustomService.addCarCustomConfig(userId,userMobile,customColorId);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        return new AjaxMessage<>("00", "成功", detail);
    }

    @PutMapping
    public AjaxMessage<CarCustomDetail> updatecarconfig(@RequestHeader(value = "X-User-Id", required = false) String userId,
                                                     @RequestHeader(value = "X-User-Mobile", required = false) String userMobile,
                                                     @RequestParam String customColorId, @RequestParam String ccid){
        String[] colors = {"COLOR_EXTERIEUR:3ZA2", "COLOR_EXTERIEUR:B9A2"};
        List<String> cl = Arrays.asList(colors);
        if (customColorId == null || !cl.contains(customColorId)){
            return new AjaxMessage<>("40004", "参数异常：customColorId", null);
        }
        CarCustom cc = carCustomService.getById(ccid);
        if (cc == null || !cc.getUserId().equals(userId)){
            return new AjaxMessage<>("40004", "配置单不可修改", null);
        }
        CarCustom carCustom = carCustomService.updateCarCustomConfig(userId,userMobile,customColorId, ccid);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        return new AjaxMessage<>("00", "成功", detail);
    }

    @GetMapping
    public AjaxMessage<CarCustomDetail> carconfig(@RequestParam String ccid){
        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        AudiConfigDto brief = new AudiConfigDto();
        brief.setModelId(carCustom.getAccbModelId());
        brief.setModelCode(carCustom.getAccbModelCode());
        brief.setTypeId(carCustom.getAccbTypeId());
        brief.setTypeCode(carCustom.getAccbTypeCode());
        brief.setTypeDesc(carCustom.getAccbTypeDesc());
        brief.setModelYear(carCustom.getModelYear());
        brief.setModelDesc(carCustom.getModelDesc());
        QueryWrapper<CarCustomOption> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ccid", carCustom.getCcid());
        List<CarCustomOption> optionList = optionService.list(queryWrapper);
        List<OptionBriefDto> briefDtos = new ArrayList<>();
        String outColorCode="";
        for(CarCustomOption i : optionList){
            OptionBriefDto briefDto = new OptionBriefDto();
            BeanUtils.copyProperties(i, briefDto);
            briefDtos.add(briefDto);
            if (i.getCategory().equals("COLOR_EXTERIEUR")){
                outColorCode = i.getCode();
            }
        }
        brief.setOptions(briefDtos);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        ConfigDetail configDetail = new ConfigDetail();
        ModelDetail modelDetail = new ModelDetail();
        modelDetail.setModelCode("498B2Y-GPAHPAH-GPDWPDW-GPF3PF3-GPS6PS6-GWA6WA6-MEIH5MD-MRAD53D-MBBO6FJ-MHSW8IZ-MLSE9VS");
        modelDetail.setCustomModelCode("498B2Y-GPAHPAH-GPDWPDW-GPF3PF3-GPS6PS6-GWA6WA6-MEIH5MD-MRAD53D-MBBO6FJ-MHSW8IZ-MLSE9VS");
        modelDetail.setModelNameCn(carCustom.getAccbTypeDesc());
        modelDetail.setModelYear("2022");
        modelDetail.setOmdModelVersion("0");
        modelDetail.setHeadImageUrl("test/2021/06/02/a7l/type.png");
        modelDetail.setModelPrice("508000.0");
        configDetail.setCarModel(modelDetail);
        ColorDetail insideColor = new ColorDetail();
        insideColor.setColorCode("RI");
        insideColor.setColorNameCn("高定琥珀棕");
        insideColor.setImageUrl("");
        configDetail.setInsideColor(insideColor);
        configDetail.setOptionList(new ArrayList<>());
        configDetail.setTotalPrice(BigDecimal.valueOf(508000.0));
        ColorDetail outsideColor = new ColorDetail();
        if (outColorCode.equals("COLOR_EXTERIEUR:3ZA2")){
            outsideColor.setColorCode("3ZA2");
            outsideColor.setColorNameCn("青山黛");
            outsideColor.setImageUrl("test/2021/06/02/a7l/green.2cd52d60.png");
            modelDetail.setImageUrl("test/2021/06/02/a7l/green2.png");
        }else if (outColorCode.equals("COLOR_EXTERIEUR:B9A2")){
            outsideColor.setColorCode("B9A2");
            outsideColor.setColorNameCn("星河蓝");
            outsideColor.setImageUrl("test/2021/06/02/a7l/blue.1458756a.png");
            modelDetail.setImageUrl("test/2021/06/02/a7l/blue2.png");
        }
        configDetail.setOutsideColor(outsideColor);
        SeriesDetail seriesDetail = new SeriesDetail();
        seriesDetail.setSeriesCode("49");
        seriesDetail.setSeriesNameCn(carCustom.getModelDesc());
        configDetail.setCarSeries(seriesDetail);
        /*List<Option> options = typeOptionService.list().stream().map(i->{
            Option option = new Option();
            option.setImageUrl(i.getPreview());
            option.setOptionNameCn(i.getDescription());
            option.setOptionCode(i.getCode());
            option.setOptionClassification(i.getClassification());
            option.setOptionClassification2nd(i.getCategory());
            return option;
        }).collect(Collectors.toList());

        configDetail.setOptionList(options);*/
        detail.setConfigDetail(configDetail);

        return new AjaxMessage<>("00", "成功", detail);
    }

    @GetMapping("/default")
    public AjaxMessage<DefaultConfigVo> defaultConfig(){
        DefaultConfigVo configVo = new DefaultConfigVo();
        List<ColorVo> res = new ArrayList<>();
        ColorVo v1 = new ColorVo();
        v1.setCustomColorId("COLOR_EXTERIEUR:3ZA2");
        v1.setCustomColorCode("COLOR_EXTERIEUR:3ZA2");
        v1.setColorCode("COLOR_EXTERIEUR:3ZA2");
        v1.setColorNameCn("青山黛");
        v1.setImageUrl("test/2021/06/02/a7l/green.2cd52d60.png");
        v1.setColorFlag("1");
        v1.setIsDefaultColor("1");
        res.add(v1);
        ColorVo v2 = new ColorVo();
        v2.setCustomColorId("COLOR_EXTERIEUR:B9A2");
        v2.setCustomColorCode("COLOR_EXTERIEUR:B9A2");
        v2.setColorCode("COLOR_EXTERIEUR:B9A2");
        v2.setColorNameCn("星河蓝");
        v2.setImageUrl("test/2021/06/02/a7l/blue.1458756a.png");
        v2.setColorFlag("1");
        v2.setIsDefaultColor("0");
        res.add(v2);
        ColorVo v3 = new ColorVo();
        v3.setCustomColorId("COLOR_INTERIEUR:RI");
        v3.setCustomColorCode("COLOR_INTERIEUR:RI");
        v3.setColorCode("COLOR_INTERIEUR:RI");
        v3.setColorFlag("0");
        v3.setColorNameCn("高定琥珀棕");
        v3.setIsDefaultColor("1");
        res.add(v3);
        configVo.setColorList(res);
        configVo.setOptionList(typeOptionService.list());
        LambdaQueryWrapper<Parameter> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Parameter::getTypeCode, "TYPE:498B2Y-GPAHPAH-GPDWPDW-GPF3PF3-GPS6PS6-GWA6WA6-MEIH5MD-MRAD53D-MBBO6FJ-MHSW8IZ-MLSE9VS")
                .eq(Parameter::getModelYear, 2022).orderByAsc(Parameter::getWeight);
        List<Parameter> parameters = parameterService.list(queryWrapper);
        List<ParameterVo> vos = parameters.stream().map(i->{
            ParameterVo vo = new ParameterVo();
            BeanUtils.copyProperties(i, vo);
            return vo;
        }).collect(Collectors.toList());
        configVo.setParameterList(vos);
        return successMessage(configVo);
    }

    @GetMapping("/accb")
    public AjaxMessage<CarCustomDetail> carconfigAccb(@RequestParam String ccid){
        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        AudiConfigDto brief = new AudiConfigDto();
        BeanUtils.copyProperties(carCustom, brief);
        QueryWrapper<CarCustomOption> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ccid", carCustom.getCcid());
        List<CarCustomOption> optionList = optionService.list(queryWrapper);
        List<OptionBriefDto> briefDtos = new ArrayList<>();
        String outColorCode="";
        for(CarCustomOption i : optionList){
            OptionBriefDto briefDto = new OptionBriefDto();
            BeanUtils.copyProperties(i, briefDto);
            briefDtos.add(briefDto);
            if (i.getCategory().equals("COLOR_EXTERIEUR")){
                outColorCode = i.getCode();
            }
        }
        brief.setOptions(briefDtos);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        ConfigDetail configDetail = new ConfigDetail();
        ModelDetail modelDetail = new ModelDetail();
        modelDetail.setModelCode("498B2Y");
        modelDetail.setModelNameCn(carCustom.getAccbTypeDesc());
        modelDetail.setModelYear("2022");
        modelDetail.setOmdModelVersion("0");
        modelDetail.setHeadImageUrl("test/2021/06/02/a7l/type.png");
        modelDetail.setModelPrice("508000.0");
        configDetail.setCarModel(modelDetail);
        ColorDetail insideColor = new ColorDetail();
        insideColor.setColorCode("COLOR_INTERIEUR:RI");
        insideColor.setColorNameCn("高定琥珀棕");
        insideColor.setImageUrl("");
        configDetail.setInsideColor(insideColor);
        ColorDetail outsideColor = new ColorDetail();
        if (outColorCode.equals("COLOR_EXTERIEUR:3ZA2")){
            outsideColor.setColorCode("COLOR_EXTERIEUR:3ZA2");
            outsideColor.setColorNameCn("青山黛");
            outsideColor.setImageUrl("test/2021/06/02/a7l/green.2cd52d60.png");
            modelDetail.setImageUrl("test/2021/06/02/a7l/green2.png");
        }else if (outColorCode.equals("COLOR_EXTERIEUR:B9A2")){
            outsideColor.setColorCode("COLOR_EXTERIEUR:B9A2");
            outsideColor.setColorNameCn("星河蓝");
            outsideColor.setImageUrl("test/2021/06/02/a7l/blue.1458756a.png");
            modelDetail.setImageUrl("test/2021/06/02/a7l/blue2.png");
        }
        configDetail.setOutsideColor(outsideColor);
        SeriesDetail seriesDetail = new SeriesDetail();
        seriesDetail.setSeriesCode("49");
        seriesDetail.setSeriesNameCn(carCustom.getModelDesc());
        configDetail.setCarSeries(seriesDetail);
        detail.setConfigDetail(configDetail);

        return new AjaxMessage<>("00", "成功", detail);
    }


    @PostMapping("audiCode")
    public AjaxMessage<String> audiCarconfig(@RequestBody AudiConfigDto audiConfigDto){
        String audiCode = iaccbService.genAudiCode(audiConfigDto);
        return new AjaxMessage<>("00", "成功", audiCode);
    }

    @GetMapping("audiConfig")
    public AjaxMessage<AudiConfigDto> audiCarconfig(@RequestParam String audiCode){
        AudiConfigDto configDto = iaccbService.audiConfig(audiCode);
        return new AjaxMessage<>("00", "成功", configDto);
    }

    @PostMapping("/source")
    @ApiOperation("ccid来源")
    @Transactional
    public AjaxMessage<CustomSourceVo> source(@Validated @RequestBody CustomSourceDto sourceDto, BindingResult bindingResult) throws Exception {
        validParam(bindingResult);
        LambdaQueryWrapper<CarCustomSource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
//                .eq(CarCustomSource::getName, sourceDto.getName())
                .eq(CarCustomSource::getApp, sourceDto.getApp())
                .eq(CarCustomSource::getDealerCode, sourceDto.getDealerCode())
//                .eq(CarCustomSource::getDealerName, sourceDto.getDealerName())
        ;
        CarCustomSource source = new CarCustomSource();
        CustomSourceVo vo = new CustomSourceVo();
        List<CarCustomSource> exists = source.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(exists)){
            CarCustomSource carCustomSource = exists.get(0);
            BeanUtils.copyProperties(carCustomSource, vo);
            vo.setSourceId(String.valueOf(carCustomSource.getSourceId()));
        }else {
            BeanUtils.copyProperties(sourceDto, source);
            source.setCreateTime(LocalDateTime.now());
            source.setType("2");
            source.insert();
            BeanUtils.copyProperties(source, vo);
            vo.setSourceId(String.valueOf(source.getSourceId()));
        }
        return successMessage(vo);
    }

}

