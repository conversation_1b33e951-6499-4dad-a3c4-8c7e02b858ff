package com.csvw.audi.cc.filter;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.List;

@Slf4j
@WebFilter(filterName = "corsFilter", urlPatterns = "/api/v1/carShoppingCart/getCarShoppingCartListV2")
public class CorsFilter implements Filter {
    @Value("${cors.allowed.origins}")
    private String origins;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        log.info("origins: {}",origins);
        //判断跨域
        String originReq = ((HttpServletRequest) servletRequest).getHeader("Origin");
        List<String> originList = Arrays.asList(origins.split(","));
        log.info("originsoriginReq: {}",originReq);
        if(StringUtils.isNotEmpty(originReq)){
            if (originList.contains(originReq)) {
                filterChain.doFilter(servletRequest, servletResponse);
                return;
            }
            HttpServletResponse httpResponse = (HttpServletResponse) servletResponse;
            httpResponse.setStatus(HttpServletResponse.SC_FORBIDDEN);
            PrintWriter writer = httpResponse.getWriter();
            writer.print("Invalid CORS request");
        }else{
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }

    }

    @Override
    public void destroy() {

    }
}
