package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.po.CarBestRecommendStock;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.vo.BestRecommendCarVo;

/**
 * <p>
 * OMD推荐车库存表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
public interface ICarBestRecommendStockService extends IService<CarBestRecommendStock> {

    void plusStockNum(Long id, Integer stockNum);

    boolean validRecommendFixStock(BestRecommendCarVo bestRecommendId, String dealerCode);

    boolean validRecommendStock(BestRecommendCarVo bestRecommendId, String dealerCode);
}
