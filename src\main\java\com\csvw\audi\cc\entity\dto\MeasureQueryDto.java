package com.csvw.audi.cc.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MeasureQueryDto {

    private String modelLineId;

    @ApiModelProperty(value = "外饰颜色code")
    private String colorCode;

    @ApiModelProperty(value = "内饰颜色code")
    private String interiorCode;

    @ApiModelProperty(value = "轮毂code")
    private String radCode;

    @ApiModelProperty(value = "面料code")
    private String sibCode;

    @ApiModelProperty(value = "座椅code")
    private String vosCode;

    @ApiModelProperty(value = "饰条code")
    private String eihCode;

    @ApiModelProperty(value = "私人订制标识，0:无前置选装，1:前置选装包含轮毂，2:前置选装包含座椅，3:前置选装包含面料，4:前置选装包好座椅和面料")
    private Integer prFlag;

    private List<String> optionCodes;

    private List<String> containOptionCodes;

    private Integer optionCodeNum;
}
