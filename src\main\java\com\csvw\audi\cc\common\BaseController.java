package com.csvw.audi.cc.common;

import com.csvw.audi.cc.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
public class BaseController {

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private HttpServletResponse response;

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public AjaxMessage<Object> exceptionHandler(Exception e){
        String path = "";
        if (request != null){
            path = request.getRequestURI();
        }
        if( e instanceof ServiceException){
            ServiceException serviceException = (ServiceException) e;
            StringBuffer buffer = new StringBuffer();
            buffer.append("path: ").append(path).append("; ")
                    .append("code: ").append(serviceException.getCode()).append("; ")
                    .append("message: ").append(serviceException.getMessage()).append("; ")
                    .append("errors: ").append(serviceException.getErrors()).append("; ")
                    .append("errorLog: ").append(serviceException.getErrorLog()).append("; ")
                    .append("data: ").append(serviceException.getData()).append(";");
            log.warn(buffer.toString(), e);
            if (serviceException.getStatus() != null){
                response.setStatus(serviceException.getStatus());
            }
            return new AjaxMessage<>(serviceException.getCode(), e.getMessage(), serviceException.getErrors(), serviceException.getData());
        }
        log.error("path: "+path+"; 捕获接口异常", e);
        log.info("path: "+path+"; 捕获接口异常", e);
        return failureMessage("02", "网络繁忙，请稍后重试", null);
    }

    protected <T> AjaxMessage<T> failureMessage(String code, String errors, T data) {
        return new AjaxMessage<T>(code, "异常失败", errors, data);
    }

    protected void validParam(BindingResult bindingResult) throws ServiceException {
        if (bindingResult.hasErrors()) {
            StringBuffer sb = new StringBuffer();
            for (int i=0; i<bindingResult.getFieldErrors().size(); i++){
                sb.append(bindingResult.getFieldErrors().get(i).getField()).append(";");
            }
            throw new ServiceException("参数校验错误", "400001", sb.toString());
        }
    }

    protected <T> AjaxMessage<T> successMessage(T data){
        return new AjaxMessage<>("00", "成功", data);
    }


    protected <T> AjaxMessage<T> failureMessage(T data){
        return new AjaxMessage<>("01", "失败", data);
    }

    protected <T> AjaxMessage<T> eventMessage(String code, String message, String errors, T data){
        return new AjaxMessage<>(code, message, errors, data);
    }
}
