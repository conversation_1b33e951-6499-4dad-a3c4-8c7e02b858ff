package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.dto.SvcdAfterSalesDto;
import com.csvw.audi.cc.entity.po.SvcdAfterSales;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.vo.SvcdAfterSalesVo;

import java.util.List;

/**
 * <p>
 * 售后服务商 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
public interface ISvcdAfterSalesService extends IService<SvcdAfterSales> {

    public List<SvcdAfterSalesVo> getAfterSalesList(SvcdAfterSalesDto afterSalesDto);

    public SvcdAfterSalesVo getAfterSalesByAgent(String agentCode);

    public List<SvcdAfterSalesVo> getAfterSalesListForReservation(String userId,SvcdAfterSalesDto afterSalesDto);

}
