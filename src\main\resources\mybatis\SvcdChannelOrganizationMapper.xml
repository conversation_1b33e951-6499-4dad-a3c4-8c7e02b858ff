<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.SvcdChannelOrganizationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.SvcdChannelOrganization">
        <id column="channel_organization_id" property="channelOrganizationId" />
        <result column="dealer_code" property="dealerCode" />
        <result column="dealer_name" property="dealerName" />
        <result column="simple_name" property="simpleName" />
        <result column="dealer_en_name" property="dealerEnName" />
        <result column="dealer_type" property="dealerType" />
        <result column="province_code" property="provinceCode" />
        <result column="city_code" property="cityCode" />
        <result column="area_code" property="areaCode" />
        <result column="post_code" property="postCode" />
        <result column="email" property="email" />
        <result column="operate_address" property="operateAddress" />
        <result column="reply_date" property="replyDate" />
        <result column="agree_date" property="agreeDate" />
        <result column="org_accept_date" property="orgAcceptDate" />
        <result column="injoy_date" property="injoyDate" />
        <result column="contract_date" property="contractDate" />
        <result column="agree_cancel_date" property="agreeCancelDate" />
        <result column="out_accept_date" property="outAcceptDate" />
        <result column="out_revoke_date" property="outRevokeDate" />
        <result column="out_date" property="outDate" />
        <result column="business_status" property="businessStatus" />
        <result column="sales_phone" property="salesPhone" />
        <result column="hot_phone24" property="hotPhone24" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="contact" property="contact" />
        <result column="contact_phone" property="contactPhone" />
        <result column="company_code" property="companyCode" />
        <result column="investor_code" property="investorCode" />
        <result column="rem_code" property="remCode" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        channel_organization_id, dealer_code, dealer_name, simple_name, dealer_en_name, dealer_type, province_code,
        city_code, area_code, post_code, email, operate_address, reply_date, agree_date, org_accept_date, injoy_date,
        contract_date, agree_cancel_date, out_accept_date, out_revoke_date, out_date, business_status, sales_phone,
        hot_phone24, longitude, latitude, contact, contact_phone, company_code, investor_code, rem_code, deleted
    </sql>

    <select id="getAgentList" resultType="com.csvw.audi.cc.entity.vo.DealerVo" parameterType="com.csvw.audi.cc.entity.dto.DealerDto">
        SELECT DISTINCT
            org.dealer_code,
            org.longitude,
            org.latitude,
            org.simple_name AS dealer_name,
            org.dealer_en_name,
            agent.exhibition_hall_address AS dealer_adrress,
            org.contact AS dealer_contacts,
            org.sales_phone AS dealer_phone,
            org.province_code,
            org.city_code,
            org.area_code,
            province.name AS province_name,
            city.name AS city_name,
            area.name AS area_name,
            org.business_status,
            org.rem_code,
            org.region_code,
            org.working_day,
            org.non_working_day,
            org.evaluate_score,
            org.remark,
            org.hot_phone24,
            agent.exhibition_hall_form as exhibitionHallForm
        FROM
            svcd_channel_organization org
        LEFT JOIN
            svcd_city city ON org.city_code = city.city_code
        LEFT JOIN
             svcd_province province ON org.province_code = province.province_code
        LEFT JOIN
            svcd_area area ON org.area_code = area.area_code
        LEFT JOIN
            svcd_sales_agent agent on org.dealer_code = agent.dealer_code
        WHERE
            1 = 1
            AND org.deleted = 0
            AND org.dealer_type in (0,2,4)
            AND org.dealer_code LIKE '766%'
        <choose>
            <when test="businessStatus == 0">
            </when>
            <when test="businessStatus != null and businessStatus != ''">
                AND org.business_status = #{businessStatus,jdbcType=VARCHAR}
            </when>
            <otherwise>
                AND org.business_status in ('3','6')
            </otherwise>
        </choose>
        <if test="dealerCode != null and dealerCode != ''">
            AND org.dealer_code = #{dealerCode,jdbcType=VARCHAR}
        </if>
        <if test="dealerName != null and dealerName != ''">
            AND org.simple_name LIKE concat(concat("%",#{dealerName,jdbcType=VARCHAR}),"%")
        </if>
        <if test="cityName != null and cityName != ''">
            AND city.name LIKE concat(concat("%",#{cityName,jdbcType=VARCHAR}),"%")
        </if>
        <if test="cityCode != null and cityCode != ''">
            AND org.city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            AND org.province_code = #{provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="regionCode != null and regionCode != ''">
            AND org.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <choose>
            <when test="exhibitionHallForm == 1">
                AND agent.exhibition_hall_form = 1
            </when>
            <when test="exhibitionHallForm == 2">
                AND agent.exhibition_hall_form = 2
            </when>
            <when test="exhibitionHallForm == 3">
                AND agent.exhibition_hall_form = 3
            </when>
            <when test="exhibitionHallForm == 4">
                AND agent.exhibition_hall_form = 4
            </when>
            <when test="exhibitionHallForm == 5">
                AND agent.exhibition_hall_form = 5
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </select>

    <select id="getDealerListByOfficialWebsite" resultType="com.csvw.audi.cc.entity.vo.DealerVo" parameterType="com.csvw.audi.cc.entity.dto.DealerDto">
        SELECT DISTINCT
            org.dealer_code,
            org.longitude,
            org.latitude,
            org.dealer_name AS dealer_full_name,
            org.simple_name AS dealer_name,
            org.dealer_en_name,
            agent.exhibition_hall_address AS dealer_adrress,
            IFNULL(agent.exhibition_hall_form,'') as exhibition_hall_form,
            org.contact AS dealer_contacts,
            org.sales_phone AS dealer_phone,
            org.province_code,
            org.city_code,
            org.area_code,
            org.dealer_type,
            org.service_code,
            province.name AS province_name,
            city.name AS city_name,
            area.name AS area_name,
            after_sales.rescue_phone,
            after_sales.east_longitude AS after_sales_longitude,
            after_sales.north_latitude AS after_sales_latitude,
            after_sales.dealer_after_sale_type,
            org.operate_address AS after_sales_adrress,
            IFNULL(after_sales.saic_audi_maintenance_level,'') as saic_audi_maintenance_level,
            after_sales.service_phone,
            org.rem_code,
            org.region_code,
            after_sales.saic_audi_battery_maintenance_center as faw_audi_battery_maintenance_center,
            company.name as company_name,
            org.working_day,
            org.non_working_day,
            org.service_working_day_remarks,
            org.service_working_day,
            org.non_service_working_day,
            org.evaluate_score,
            org.remark,
            org.hot_phone24,
            ( SELECT count(*) FROM `svcd_channel_organization_policy` p WHERE p.`dealer_code` = org.`dealer_code` and `control_status` = '有' ) as has_num
        FROM
            svcd_channel_organization org
        LEFT JOIN
            svcd_city city ON org.city_code = city.city_code
        LEFT JOIN
            svcd_province province ON org.province_code = province.province_code
        LEFT JOIN
            svcd_area area ON org.area_code = area.area_code
        LEFT JOIN
            svcd_sales_agent agent on org.dealer_code = agent.dealer_code
        LEFT JOIN
            svcd_after_sales after_sales on org.dealer_code = after_sales.dealer_code
        LEFT JOIN
            svcd_company company ON org.company_code = company.company_code AND company.name is not null AND company.name <![CDATA[ <> ]]> ''
        LEFT JOIN
            svcd_channel_organization_policy policy on policy.dealer_code = org.dealer_code
        WHERE
            1 = 1
            AND org.deleted = 0
            AND org.city_code IS NOT NULL
            AND org.province_code IS NOT NULL

        <choose>
            <when test="businessStatus == 0">
            </when>
            <when test="businessStatus != null and businessStatus != ''">
                AND org.business_status = #{businessStatus,jdbcType=VARCHAR}
            </when>
            <otherwise>
                AND org.business_status in ('3','6')
            </otherwise>
        </choose>
        <if test="dealerCode != null and dealerCode != ''">
            AND org.dealer_code =  #{dealerCode,jdbcType=VARCHAR}
        </if>
        <choose>
            <when test="searchType == 0">
                AND ( org.dealer_type in (0,1,2,4))
            </when>
            <when test="searchType == 1">
                AND ( org.dealer_type = 1 or (org.dealer_type = 0 and policy.control_status = '有') or (org.dealer_type = 4 and policy.control_status = '有') )
            </when>
            <when test="searchType == 2">
                AND org.dealer_type in (0,2,4)
            </when>
            <otherwise>
                AND ( org.dealer_type in (0,1,2,4))
            </otherwise>
        </choose>
        <if test="provinceCode != null and provinceCode != ''">
            AND org.province_code = #{provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null and cityCode != ''">
            AND org.city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="dealerName != null and dealerName != ''">
            AND org.simple_name LIKE concat(concat("%",#{dealerName,jdbcType=VARCHAR}),"%")
        </if>
        <if test="phoneOrName != null and phoneOrName != ''">
            AND (
                org.simple_name LIKE concat(concat("%",#{phoneOrName,jdbcType=VARCHAR}),"%")
                OR org.dealer_name LIKE concat(concat("%",#{phoneOrName,jdbcType=VARCHAR}),"%")
                OR org.sales_phone LIKE concat(concat("%",#{phoneOrName,jdbcType=VARCHAR}),"%")
                OR after_sales.service_phone LIKE concat(concat("%",#{phoneOrName,jdbcType=VARCHAR}),"%")
                OR after_sales.rescue_phone LIKE concat(concat("%",#{phoneOrName,jdbcType=VARCHAR}),"%")
                OR company.name LIKE concat(concat("%",#{phoneOrName,jdbcType=VARCHAR}),"%")
            )
        </if>
        <choose>
            <when test="dealerAfterSaleType == 1">
                AND after_sales.dealer_after_sale_type = '1'
            </when>
            <when test="dealerAfterSaleType == 2">
                AND after_sales.dealer_after_sale_type = '2'
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test="isNewEnergy == 1">
            AND after_sales.saic_audi_maintenance_level = 'A'
        </if>
        <if test="exhibitionHallFormList != null and exhibitionHallFormList.size()>0">
            AND (
                agent.exhibition_hall_form IN
                <foreach collection="exhibitionHallFormList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or
                after_sales.exhibition_hall_form IN
                <foreach collection="exhibitionHallFormList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            )
        </if>
        <choose>
            <when test="exhibitionHallForm == 1">
                AND agent.exhibition_hall_form = 1
            </when>
            <when test="exhibitionHallForm == 2">
                AND agent.exhibition_hall_form = 2
            </when>
            <when test="exhibitionHallForm == 3">
                AND agent.exhibition_hall_form = 3
            </when>
            <when test="exhibitionHallForm == 4">
                AND ( agent.exhibition_hall_form = 4 or after_sales.exhibition_hall_form=4)
            </when>
            <when test="exhibitionHallForm == 5">
                AND agent.exhibition_hall_form = 5
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test="fawAudiBatteryMaintenanceCenter != null and fawAudiBatteryMaintenanceCenter != ''">
            AND after_sales.faw_audi_battery_maintenance_center = #{fawAudiBatteryMaintenanceCenter,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getOrgList" resultType="com.csvw.audi.cc.entity.vo.DealerVo" parameterType="com.csvw.audi.cc.entity.dto.DealerDto">
        SELECT DISTINCT
            org.dealer_code,
            org.longitude,
            org.latitude,
            org.dealer_name AS dealer_full_name,
            org.simple_name AS dealer_name,
            org.dealer_en_name,
            agent.exhibition_hall_address AS dealer_adrress,
            org.contact AS dealer_contacts,
            org.sales_phone AS dealer_phone,
            org.province_code,
            org.city_code,
            org.area_code,
            org.dealer_type,
            province.name AS province_name,
            city.name AS city_name,
            area.name AS area_name,
            after_sales.rescue_phone,
            after_sales.east_longitude AS after_sales_longitude,
            after_sales.north_latitude AS after_sales_latitude,
            org.operate_address AS after_sales_adrress,
            after_sales.service_phone,
            org.rem_code,
            org.region_code,
            org.working_day,
            org.non_working_day,
            org.evaluate_score,
            org.remark,
            org.hot_phone24
        FROM
            svcd_channel_organization org
        LEFT JOIN
            svcd_city city ON org.city_code = city.city_code
        LEFT JOIN
            svcd_province province ON org.province_code = province.province_code
        LEFT JOIN
            svcd_area area ON org.area_code = area.area_code
        LEFT JOIN
            svcd_sales_agent agent on org.dealer_code = agent.dealer_code
        LEFT JOIN
            svcd_after_sales after_sales on org.dealer_code = after_sales.dealer_code
        WHERE
        1 = 1
        AND org.deleted = 0
        <choose>
            <when test="businessStatus == 0">
            </when>
            <when test="businessStatus != null and businessStatus != ''">
                AND org.business_status = #{businessStatus,jdbcType=VARCHAR}
            </when>
            <otherwise>
                AND org.business_status in ('3','6')
            </otherwise>
        </choose>
        <choose>
            <when test="searchType == 0">
            </when>
            <when test="searchType == 1">
                AND org.dealer_type in (0,1)
                AND org.business_status = '3'
            </when>
            <when test="searchType == 2">
                AND org.dealer_type in (0,2,4)
                AND org.dealer_code LIKE '766%'
                AND org.business_status in ('3','6')
            </when>
            <otherwise>
                AND org.dealer_type IN (0,1,2,4)
                AND (org.dealer_code LIKE '766%')
            </otherwise>
        </choose>
        <if test="dealerCode != null and dealerCode != ''">
            AND org.dealer_code =  #{dealerCode,jdbcType=VARCHAR}
        </if>
        <if test="dealerName != null and dealerName != ''">
            AND org.simple_name LIKE concat(concat("%",#{dealerName,jdbcType=VARCHAR}),"%")
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            AND org.province_code = #{provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null and cityCode != ''">
            AND org.city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="regionCode != null and regionCode != ''">
            AND org.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test="remCode != null and remCode != ''">
            AND org.rem_code =  #{remCode,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectSvcdChannelOrganizationByPCode"
            resultType="com.csvw.audi.cc.entity.po.SvcdChannelOrganization">
        SELECT sco.* FROM svcd_channel_organization sco inner join svcd_org_region sor on(sco.province_code =
        sor.region_code ) where sor.pcode = #{pCode}
    </select>

</mapper>
