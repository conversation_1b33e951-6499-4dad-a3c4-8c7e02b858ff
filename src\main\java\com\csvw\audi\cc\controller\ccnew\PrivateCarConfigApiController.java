package com.csvw.audi.cc.controller.ccnew;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.common.utils.JsonString;
import com.csvw.audi.cc.entity.dto.CarCustomDto;
import com.csvw.audi.cc.entity.dto.CarModelDto;
import com.csvw.audi.cc.entity.dto.CustomSeriesParam;
import com.csvw.audi.cc.entity.dto.accb.AudiConfigDto;
import com.csvw.audi.cc.entity.dto.accb.OptionBriefDto;
import com.csvw.audi.cc.entity.dto.omd.OmdModelDto;
import com.csvw.audi.cc.entity.dto.vwcc.*;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.entity.vo.*;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.service.*;
import com.csvw.audi.cc.service.impl.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/private/api/v1/cc")
@Api(tags = "车辆配置内部接口")
@Slf4j
public class PrivateCarConfigApiController extends BaseController {

    @Autowired
    private ICarCustomOptionService optionService;

    @Autowired
    private IOptionService typeOptionService;

    @Autowired
    private ICarCustomSeriesService customSeriesService;

    @Autowired
    private OmdServiceImpl omdService;

    @Autowired
    private ICarCustomService carCustomService;

    @Autowired
    private ICarRecommendService recommendService;

    @Autowired
    private CarRecommendCustomServiceImpl recommendCustomService;

    @Autowired
    private CacheServiceImpl cacheService;

    @Autowired
    private ICarMeasureMadeOriginConfigService measureMadeOriginConfigService;

    @Autowired
    private ICarMeasureMadeConfigService measureMadeConfigService;

    @Autowired
    private OmdCcCompareServiceImpl omdCcCompareService;

    @Autowired
    private ICarOmdStockMapService stockMapService;

    @Autowired
    private CarConfigSnapshotService carConfigSnapshot;

    @Autowired
    private ICarOmdDataMapService omdDataMapService;

    @Autowired
    private ICarOmdVehicleTypeService omdVehicleTypeService;

    @GetMapping("/syncAllPrice")
    @ApiOperation("同步所有价格数据")
    private AjaxMessage syncAllPrice(){
        log.info("同步omd所有价格数据");
        omdService.syncTypePrice(null);
        omdService.syncCurrentTypeColorPrice(null);
        omdService.syncCurrentTypePrPrice(null);
        omdService.syncCurrentTypePrpkgPrice(null);
        cacheService.cacheEvictCc();
        return successMessage(null);
    }

    @GetMapping("/syncUpdatePrice")
    @ApiOperation("同步所有价格数据")
    private AjaxMessage syncUpdatePrice(){
        LocalDateTime date = LocalDateTime.now();
        log.info("同步OMD前三天价格数据，{}", date);
        int i = 1;
        do {
            omdService.syncTypePrice(date);
            omdService.syncCurrentTypeColorPrice(date);
            omdService.syncCurrentTypePrPrice(date);
            omdService.syncCurrentTypePrpkgPrice(date);
            date = date.minusDays(i);
            i = i+1;
        }while (i<4);
        cacheService.cacheEvictCc();
        return successMessage(LocalDateTime.now());
    }

    @GetMapping("/customSeries")
    @ApiOperation("自定义车系(车系)")
    public AjaxMessage<List<CustomSeriesVo>> customSeries(@RequestHeader(value = "channel", defaultValue = Constant.MASTER_CHANNEL) String channel, CustomSeriesParam seriesParam) throws Exception {
        List<CarCustomSeries> data = customSeriesService.listCustomSeries(channel, seriesParam);
        List<CustomSeriesVo> vos = new ArrayList<>();
        if (data != null){
            vos = data.stream().map(i->{
                CustomSeriesVo vo = new CustomSeriesVo();
                BeanUtils.copyProperties(i, vo);
                return vo;
            }).collect(Collectors.toList());
        }
        return successMessage(vos);
    }

    @GetMapping(value = {"/syncOmdRecommendCar"})
    @ApiOperation("同步OMD畅销推荐车")
    public AjaxMessage<String> bestRecommendCar(){
        // 同步畅销推荐车
        recommendService.syncOmdBestRecommendCar();
        stockMapService.handleRecommendStockMap();
        // 同步畅销推荐车库存
        recommendService.syncOmdBestRecommendCarHqVehicle();

        // 同步经销商库存车
        omdService.syncStockRecommendModel();
        // 增量处理经销商库存车
        recommendService.convertStockRecommendCar();
        stockMapService.handleStockRecommendStockMap();
        // 经销商库存车，库存同步
        recommendService.syncOmdBestRecommendCarStockVehicle();


        cacheService.cacheBestRecommendEvictCc();
        return successMessage("");
    }

//    @GetMapping(value = {"/convertTest"})
//    @ApiOperation("库存车转换测试")
    public AjaxMessage<String> convertTest(){
        recommendService.convertStockRecommendCar();
        cacheService.cacheBestRecommendEvictCc();
        return successMessage("");
    }

    @GetMapping(value = {"/convertMeasureMadeOriginData"})
    @ApiOperation("同步omd半订制")
    public AjaxMessage<String> convertMeasureMadeOriginData() throws Exception {
        measureMadeOriginConfigService.syncOmdMeasureSemiCustomizationModel();
        measureMadeOriginConfigService.handleMeasureCcUniqueCode();
        stockMapService.handleMeasureStockMap();
        // 筑梦复制到筑梦新生
//        measureMadeOriginConfigService.handleMeasureZhuMengYef();
        measureMadeOriginConfigService.convertMeasureMade();
        cacheService.cacheEvictCc();
        return successMessage("");
    }

    @GetMapping(value = {"/convertMeasureTest"})
    @ApiOperation("转换半订制原始数据，测试接口")
    public AjaxMessage<String> convertMeasureTest() throws Exception {
        measureMadeOriginConfigService.convertMeasureMade();
        measureMadeOriginConfigService.handleMeasureCcUniqueCode();
        return successMessage("");
    }

    @PostMapping(value = {"/convertOmdModelOriginData"})
    @ApiOperation("Omd车辆数据转换")
    public AjaxMessage<CarModelDto> convertOmdModelOriginData(OmdModelDto omdModelDto) throws Exception {
        CarModelDto carModelDto = omdService.convertOriginData(omdModelDto);
        return successMessage(carModelDto);
    }

    @PostMapping(value = {"/convertOmdModelOriginData/{api}"})
    @ApiOperation("Omd车辆数据转换带API映射")
    public AjaxMessage<CarModelDto> convertOmdModelOriginData(OmdModelDto omdModelDto, @PathVariable String api) throws Exception {
        OmdModelDto omdModelMappedDto = omdDataMapService.getOmdDataFromMap(omdModelDto, api);
        omdModelDto = omdModelMappedDto != null ? omdModelMappedDto : omdModelDto;
        CarModelDto carModelDto = omdService.convertOriginData(omdModelDto);
        return successMessage(carModelDto);
    }

    @GetMapping(value = {"/syncOmdRecommendCarStock"})
    @ApiOperation("同步OMD库存车库存")
    public AjaxMessage<String> syncOmdRecommendCarStock(){
        // 同步畅销推荐车库存
        recommendService.syncOmdBestRecommendCarHqVehicle();

        // 经销商库存车，库存同步
        recommendService.syncOmdBestRecommendCarStockVehicle();

        return successMessage("");
    }

    /**
     * cop ccdetail
     * to cop to OMD
     * @param ccid
     * @return
     */
    @GetMapping("/old")
    public AjaxMessage<CarCustomDetail> oldCarConfig(@RequestParam String ccid){
        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        AudiConfigDto brief = new AudiConfigDto();
        BeanUtils.copyProperties(carCustom, brief);
        QueryWrapper<CarCustomOption> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ccid", carCustom.getCcid());
        List<CarCustomOption> optionList = optionService.list(queryWrapper);
        List<OptionBriefDto> briefDtos = new ArrayList<>();
        String outColorCode="";
        for(CarCustomOption i : optionList){
            OptionBriefDto briefDto = new OptionBriefDto();
            BeanUtils.copyProperties(i, briefDto);
            briefDtos.add(briefDto);
            if (i.getCategory().equals("COLOR_EXTERIEUR")){
                outColorCode = i.getCode();
            }
        }
        brief.setOptions(briefDtos);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        ConfigDetail configDetail = new ConfigDetail();
        ModelDetail modelDetail = new ModelDetail();
        modelDetail.setModelCode("498B2Y-GPAHPAH-GPDWPDW-GPF3PF3-GPS6PS6-GWA6WA6-MEIH5MD-MRAD53D-MBBO6FJ-MHSW8IZ-MLSE9VS");
        modelDetail.setModelNameCn(carCustom.getAccbTypeDesc());
        modelDetail.setModelYear("2022");
        modelDetail.setOmdModelVersion("0");
        modelDetail.setHeadImageUrl("test/2021/06/02/a7l/type.png");
        modelDetail.setModelPrice("508000.0");
        modelDetail.setCustomModelCode("498B2Y-GPAHPAH-GPDWPDW-GPF3PF3-GPS6PS6-GWA6WA6-MEIH5MD-MRAD53D-MBBO6FJ-MHSW8IZ-MLSE9VS");
        configDetail.setCarModel(modelDetail);
        ColorDetail insideColor = new ColorDetail();
        insideColor.setColorCode("RI");
        insideColor.setColorNameCn("高定琥珀棕");
        insideColor.setImageUrl("");
        configDetail.setInsideColor(insideColor);
        configDetail.setOptionList(new ArrayList<>());
        configDetail.setTotalPrice(BigDecimal.valueOf(508000.0));
        ColorDetail outsideColor = new ColorDetail();
        if (outColorCode.equals("COLOR_EXTERIEUR:3ZA2")){
            outsideColor.setColorCode("3ZA2");
            outsideColor.setColorNameCn("青山黛");
            outsideColor.setImageUrl("test/2021/06/02/a7l/green.2cd52d60.png");
            modelDetail.setImageUrl("test/2021/06/02/a7l/green2.png");
        }else if (outColorCode.equals("COLOR_EXTERIEUR:B9A2")){
            outsideColor.setColorCode("B9A2");
            outsideColor.setColorNameCn("星河蓝");
            outsideColor.setImageUrl("test/2021/06/02/a7l/blue.1458756a.png");
            modelDetail.setImageUrl("test/2021/06/02/a7l/blue2.png");
        }
        configDetail.setOutsideColor(outsideColor);
        SeriesDetail seriesDetail = new SeriesDetail();
        seriesDetail.setSeriesCode("49");
        seriesDetail.setSeriesNameCn(carCustom.getModelDesc());
        configDetail.setCarSeries(seriesDetail);
//        List<Option> options = typeOptionService.list().stream().map(i->{
//            Option option = new Option();
//            option.setImageUrl(i.getPreview());
//            option.setOptionNameCn(i.getDescription());
//            option.setOptionCode(i.getCode());
//            option.setOptionClassification(i.getClassification());
//            option.setOptionClassification2nd(i.getCategory());
//            return option;
//        }).collect(Collectors.toList());
//        configDetail.setOptionList(options);
        detail.setConfigDetail(configDetail);

        return new AjaxMessage<>("00", "成功", detail);
    }

    /**
     * cop ccdetail
     * to cop to OMD
     * @param ccid
     * @return
     */
    @GetMapping()
    public AjaxMessage<CarCustomDetail> carConfig(@RequestParam String ccid) throws Exception {

        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        if (carCustom == null){
            return failureMessage(null);
        }
        if (StringUtils.isBlank(carCustom.getModelLineId())){
            return this.oldCarConfig(ccid);
        }
        CarCustomDetail customDetail;
        customDetail = carConfigSnapshot.carConfigOmd(carCustom);
        if (customDetail.getConfigDetail().getCarModel().getModelPrice() instanceof String){
            customDetail.getConfigDetail().getCarModel().setModelPrice("0");
        }
        if (customDetail.getConfigDetail().getTotalPrice() instanceof String){
            customDetail.getConfigDetail().setTotalPrice(BigDecimal.ZERO);
        }
        return new AjaxMessage<>("00", "成功", customDetail);
    }

    /**
     * 包含下线车配置
     */
    @GetMapping("/all")
    public AjaxMessage<CarCustomDetail> carConfigAll(@RequestParam String ccid) throws Exception {

        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        if (carCustom == null){
            return failureMessage(null);
        }
        if (StringUtils.isBlank(carCustom.getModelLineId())){
            return this.oldCarConfig(ccid);
        }
        CarCustomDetail customDetail;
        if (carCustom.getBestRecommendId() != null){
            customDetail = recommendCustomService.getCarConfigDetailOmdAll(carCustom);
        }else {
            customDetail = carCustomService.getCarConfigDetailOmdAll(carCustom);
        }
        if (ccid != null && customDetail != null) {
            customDetail.setMeasureId(JsonString.valueOf(carCustom.getMeasureId()));
            log.info("carConfigAll, ccid: " + ccid + "customDetail: " + JSONObject.toJSONString(customDetail));
        }
        if (customDetail.getConfigDetail().getCarModel().getModelPrice() instanceof String){
            customDetail.getConfigDetail().getCarModel().setModelPrice("0");
        }
        if (customDetail.getConfigDetail().getTotalPrice() instanceof String){
            customDetail.getConfigDetail().setTotalPrice(BigDecimal.ZERO);
        }
        return new AjaxMessage<>("00", "成功", customDetail);
    }

    /**
     * cop ccdetail
     * to cop to contract
     * @param ccid
     * @return
     */
    @GetMapping("/contract")
    public AjaxMessage<CarCustomDetail> carConfigCustom(@RequestParam String ccid) throws Exception {

        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        if (carCustom == null){
            return failureMessage(null);
        }
        if (StringUtils.isBlank(carCustom.getModelLineId())){
            return this.oldCarConfig(ccid);
        }
        CarCustomDetail customDetail;
        customDetail = carConfigSnapshot.carConfigContract(carCustom);
        if (customDetail.getConfigDetail().getCarModel().getModelPrice() instanceof String){
            customDetail.getConfigDetail().getCarModel().setModelPrice("0");
        }
        if (customDetail.getConfigDetail().getTotalPrice() instanceof String){
            customDetail.getConfigDetail().setTotalPrice(BigDecimal.ZERO);
        }
        return new AjaxMessage<>("00", "成功", customDetail);
    }

    @GetMapping(value = {"/bestRecommendStock"})
    @ApiOperation("畅销推荐车库存操作")
    @ApiImplicitParams({@ApiImplicitParam(value = "operate", name="库存操作，1：锁库存，2：解锁库存"),
            @ApiImplicitParam(value = "dealerCode", name="经销商编码，如果经销商没有库存，锁总部库存")})
    public Integer bestRecommendCarStock(@RequestParam Integer operate, @RequestParam Long ccid, @RequestParam(required = false) String dealerCode){
        try {
            switch (operate){
                case 1: recommendService.lockBestRecommendStock(ccid, dealerCode); break;
                case 2: recommendService.unlockBestRecommendStock(ccid); break;
            }
        }catch (Exception e){
            log.error("库存操作失败", e);
            return 0;
        }

        return 1;
    }

    @GetMapping(value = {"/bestRecommendStockNum"})
    @ApiOperation("查询畅销推荐车库存")
    @ApiImplicitParams({@ApiImplicitParam(value = "dealerCode", name="经销商编码，如果经销商没有库存，锁总部库存")})
    public Long bestRecommendStockNum(@RequestParam Long ccid, @RequestParam(required = false) String dealerCode) throws ServiceException {
        return recommendService.bestRecommendStockNum(ccid, dealerCode);
    }

    @GetMapping(value = {"/measureMadeStock"})
    @ApiOperation("半订制车库存操作")
    @ApiImplicitParams(@ApiImplicitParam(value = "operate", name="库存操作，1：锁库存，2：解锁库存"))
    public Integer measureMadeStock(@RequestParam Integer operate, @RequestParam Long ccid){
        try {
            switch (operate){
                case 1: measureMadeConfigService.lockMeasureMadeStock(ccid); break;
                case 2: measureMadeConfigService.unlockMeasureMadeStock(ccid); break;
            }
        }catch (Exception e){
            log.error("库存操作失败", e);
            return 0;
        }

        return 1;
    }

    @GetMapping(value = "{ccid}")
    @ApiOperation("获取配置单")
    public CarCustomVo carconfig(@PathVariable("ccid") String ccid) throws Exception {
        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        if (carCustom == null){
            return null;
        }
        CarCustomVo customVo = carConfigSnapshot.getCarCustomVo(carCustom);
        return customVo;
    }

    @PostMapping("/internal")
    @ApiOperation("内部配车")
    public AjaxMessage<CarCustomDetail> addInternalCarConfig(@RequestHeader(value = "X-User-Id", required = false) String userId,
                                                     @RequestHeader(value = "X-User-Mobile", required = false) String userMobile,
                                                     @RequestHeader(value = "validConfig", defaultValue = "true") Boolean validConfig,
                                                     @RequestBody CarCustomDto carCustomDto) throws Exception {
        CarCustom carCustom = carCustomService.addInternalCarCustomConfig(userId,userMobile, validConfig,carCustomDto);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        return new AjaxMessage<>("00", "成功", detail);
    }

    @PostMapping(value = "/config/list")
    @ApiOperation("获取配置单列表")
    public List<CarCustomDetailVo> configCustoms(@RequestBody List<Long> ccids){
        return carCustomService.listCustomDetail(ccids);
    }

    @GetMapping(value = {"/cacheEvict"})
    @ApiOperation("ccpro清除缓存")
    public AjaxMessage cacheEvict(){
        cacheService.cacheEvictCc();
        return successMessage(null);
    }

    @PostMapping(value = {"/omdOrderConfigValid"})
    @ApiOperation("omd订单配置-CC比较")
    public void omdOrderConfigValid(MultipartFile file, HttpServletResponse response) throws Exception {
        InputStream stream = null;
        try {
            stream = file.getInputStream();
            omdCcCompareService.omdOrderConfigValid(stream, response);
        }finally {
            if (stream != null){
                stream.close();
            }
        }
    }

    @GetMapping(value = {"/countSum"})
    @ApiOperation("统计配置单数量")
    public AjaxMessage countSum(String userId,String startDate,String endDate){
        return successMessage(carCustomService.countSum(userId,startDate,endDate));
    }

    /**
     * key
     * @param ccid
     * @return
     * @throws Exception
     */
    @GetMapping(value = {"/normalConfigToMeasure"})
    @ApiOperation("普通配置单查询是否需要转半定")
    public AjaxMessage<String> normalConfigToMeasure(Long ccid) throws Exception {
        String res = measureMadeOriginConfigService.ccMatchMeasure(ccid);
        return new AjaxMessage<>("00", "成功", res);
    }

    /**
     * @param key
     * @return
     * @throws Exception
     */
    @PostMapping(value = {"/normalConfigToMeasure"})
    @ApiOperation("半订制转换")
    public AjaxMessage<Object> normalConfigToMeasure(@RequestHeader(value = "X-User-Id", required = false) String userId,
                                                     String key) throws Exception {
        measureMadeOriginConfigService.normalConfigToMeasure(userId, key);
        return new AjaxMessage<>("00", "成功", null);
    }


    @GetMapping(value = {"/measure"})
    @ApiOperation("查询半订制")
    public AjaxMessage<CarMeasureMadeOriginConfig> measureOriginConfig(Long measureId) throws Exception {
        CarMeasureMadeOriginConfig originConfig = measureMadeOriginConfigService.measureOriginConfigByMeasureId(measureId);
        return new AjaxMessage<>("00", "成功", originConfig);
    }

    @Autowired private ICarCustomSnapshotService snapshotService;
    @GetMapping(value = {"/historySnapshotV2"})
    @ApiOperation("历史配置单快照")
    public AjaxMessage<String> historySnapshot() throws Exception {
        snapshotService.historySnapshot();
        return new AjaxMessage<>("00", "成功", null);
    }

    @PostMapping(value = {"/batchUpdateSnapshot"})
    @ApiOperation("内部批量更新配置单快照")
    public AjaxMessage<String> batchUpdateSnapshot(@RequestBody List<Long> ccids) throws Exception {
        ccids.forEach(i-> {
            try {
                snapshotService.snapshotCarCustom(carCustomService.getById(i), LocalDateTime.now());
            } catch (Exception e) {
                log.error("内部批量更新配置单快照异常", e);
            }
        });
        return new AjaxMessage<>("00", "成功", null);
    }

    @PostMapping(value = {"/omdMeasureCarConfig"})
    @ApiOperation("omd半定制配置器配车（ccid）")
    public AjaxMessage<CarCustomDetail> addOmdMeasureCarConfig(@RequestHeader(value = "X-User-Id", required = false) String userId,
                                                     @RequestHeader(value = "X-User-Mobile", required = false) String userMobile,
                                                     @RequestHeader(value = "channel", required = false) String channel,
                                                     Long semiCustomizationModelId, Long sourceId, String entryPoint, String packetEquityId) throws Exception {
        CarMeasureMadeOriginConfig originConfig = measureMadeOriginConfigService.getOne(new LambdaQueryWrapper<CarMeasureMadeOriginConfig>().eq(CarMeasureMadeOriginConfig::getSemiCustomizationModelId, semiCustomizationModelId));
        CarMeasureMadeConfig measureMadeConfig = measureMadeConfigService.getOne(new LambdaQueryWrapper<CarMeasureMadeConfig>().eq(CarMeasureMadeConfig::getMeasureOriginId, originConfig.getMeasureOriginId()));
        CarCustom carCustom = measureMadeConfigService.addCarCustomConfig(userId, userMobile, measureMadeConfig.getMeasureId(), sourceId, channel, entryPoint, packetEquityId);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        return new AjaxMessage<>("00", "成功", detail);
    }

    @PostMapping(value = {"/bestSellRecommendConfig"} )
    @ApiOperation("配置器omd总部推荐车配车（ccid）")
    public AjaxMessage<CarCustomDetail> addcarconfigByBestSellRecommend(@RequestHeader(value = "X-User-Id", required = false) String userId,
                                                                    @RequestHeader(value = "X-User-Mobile", required = false) String userMobile,
                                                                        @RequestHeader(value="X-Member-Id", required = false) String memberId,
                                                                        @RequestHeader(value = "channel") String channel,
                                                                    @RequestParam Long bestSellRecommendModelId, @RequestParam String entryPoint, String sourceId) throws Exception {
        Long bestRecommendId = recommendService.findHqRecommendId(bestSellRecommendModelId);
        if(bestRecommendId == null){
            return new AjaxMessage<>("400404", "车辆信息不存在", null);
        }
        CarCustom carCustom = carCustomService.addCarCustomConfigByBestRecommend(userId, memberId,userMobile,bestRecommendId, sourceId, channel, entryPoint);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        return new AjaxMessage<>("00", "成功", detail);
    }

    @PostMapping(value = {"/stockRecommendConfig"} )
    @ApiOperation("配置器omd经销商推荐车配车（ccid）")
    public AjaxMessage<CarCustomDetail> addcarconfigByStockRecommend(@RequestHeader(value = "X-User-Id", required = false) String userId,
                                                                    @RequestHeader(value = "X-User-Mobile", required = false) String userMobile,
                                                                     @RequestHeader(value="X-Member-Id", required = false) String memberId,
                                                                     @RequestHeader(value = "channel") String channel,
                                                                    @RequestParam Long stockRecommendModelId, @RequestParam String entryPoint, String sourceId) throws Exception {
        Long bestRecommendId = recommendService.findDealerRecommendId(stockRecommendModelId);
        if(bestRecommendId == null){
            return new AjaxMessage<>("400404", "车辆信息不存在", null);
        }
        CarCustom carCustom = carCustomService.addCarCustomConfigByBestRecommend(userId, memberId,userMobile,bestRecommendId, sourceId, channel, entryPoint);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        return new AjaxMessage<>("00", "成功", detail);
    }

    @PostMapping(value = {"/updateAudiCode"})
    @ApiOperation("更新推荐车audicode")
    public AjaxMessage<String> operateRecommendCar(@RequestParam String modelLineId, @RequestParam String audiCode) throws Exception {
        recommendService.update(new LambdaUpdateWrapper<CarRecommend>().set(CarRecommend::getAudiCodeContent, null)
                .set(CarRecommend::getAudiCode, audiCode).eq(CarRecommend::getModelLineId, modelLineId));
        return new AjaxMessage<>("00", "成功", null);
    }

}
