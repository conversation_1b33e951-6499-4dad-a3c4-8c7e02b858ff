package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.dto.CustomSeriesDto;
import com.csvw.audi.cc.entity.dto.SeriesParamDto;
import com.csvw.audi.cc.entity.po.CarCustomSeries;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csvw.audi.cc.entity.vo.CustomSeriesVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 自定义车系 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
public interface CarCustomSeriesMapper extends BaseMapper<CarCustomSeries> {

    CustomSeriesDto getCustomSeriesDto(@Param("channel") String channel, @Param("customSeriesId") String customSeriesId);

    List<CarCustomSeries> listCustomSeries(CarCustomSeries carCustomSeries);

    List<CustomSeriesVo> listCustomSeriesVo(SeriesParamDto paramDto);
}
