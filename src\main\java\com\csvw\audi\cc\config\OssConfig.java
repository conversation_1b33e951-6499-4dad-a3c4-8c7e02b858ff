package com.csvw.audi.cc.config;

import com.csvw.audi.common.config.OssServer;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/9/19 9:50
 * @description oss配置参数信息
 */
@Configuration
@ConfigurationProperties(prefix = "oss.aliyun")
@Data
public class OssConfig {

    private String accessKeyId;
    private String accessKeySecret;
    private String bucketName;
    private String endpoint;
    private String segmentationUrl;
    private String dealerImagePath;
    private String svcdUserImagePath;

    @Bean
    OssServer getOssServer() {
        return new OssServer(endpoint, accessKeyId, accessKeySecret);
    }
}
