<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarOmdVehicleTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarOmdVehicleType">
        <id column="id" property="id" />
        <result column="accb_type_code" property="accbTypeCode" />
        <result column="model_code" property="modelCode" />
        <result column="model_year" property="modelYear" />
        <result column="model_version" property="modelVersion" />
        <result column="color_code" property="colorCode" />
        <result column="interior_code" property="interiorCode" />
        <result column="wheel" property="wheel" />
        <result column="pr_list" property="prList" />
        <result column="classify" property="classify" />
        <result column="seat" property="seat" />
        <result column="create_time" property="createTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, accb_type_code, model_code, model_year, model_version, color_code, interior_code, wheel, pr_list, classify, seat, create_time, del_flag
    </sql>

</mapper>
