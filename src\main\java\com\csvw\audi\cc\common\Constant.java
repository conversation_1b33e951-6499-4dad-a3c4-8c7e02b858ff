package com.csvw.audi.cc.common;

import java.util.Arrays;
import java.util.List;

public class Constant {
    public final static String MASTER_CHANNEL = "master"; // 主数据
    public final static String ONEAPP_CHANNEL = "oneapp"; // oneapp数据
    public final static String MINIP = "minip";
    public final static String SPHERE = "sphere";
    public final static String OFFICIAL = "official-pc";
    public final static String DRM_CHANNEL = "drm"; // drm数据
    public final static String DRM_LOCAL_CHANNEL = "drm-local"; // drm数据
    public final static String AMS_MINI_APP_CHANNEL = "ams-minip"; // oneapp数据
    public final static String VSEARCH_CHANNEL = "vsearch"; // oneapp数据

    public final static String SERIES_ID_A7L = "49"; //A7L
    public final static String SERIES_ID_Q5E = "G4"; //Q5e

    public final static String SERIES_ID_Q6 = "G6";//Q6

    public final static String BRAND_CODE = "A";


    public final static List<String> NEED_TYPE_FLAG = Arrays.asList(Constant.ONEAPP_CHANNEL, Constant.OFFICIAL, Constant.SPHERE, Constant.MINIP, Constant.VSEARCH_CHANNEL, Constant.DRM_LOCAL_CHANNEL);


    public final static String PRICE_NOT_HANDLE = "敬请期待";
}
