package com.csvw.audi.cc.entity.dto;

import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class CustomCheckDto {
    private String customModelLineId;
    // 所有装备参数
    private List<ModelLineOptionVo> optionParams;
    // 标准装备参数
    private List<ModelLineOptionVo> standardOptions;
    // 可选装备
    private List<ModelLineOptionVo> optionalOptions;
    // 不可选装备
    private List<ModelLineOptionVo> notOptionalOptions;
    // 选装包包含装备
    private List<ModelLineOptionVo> packetOptionItems;
    // 依赖列表
    private Map<String, List<ModelLineOptionVo>> packetOptionItemMap;
    // 依赖时，不可选可变为可选
    private List<ModelLineOptionVo> dependOptions;
    // 依赖列表
    private Map<String, List<ModelLineOptionVo>> dependMap;
    // 冲突列表
    private Map<String, List<ModelLineOptionVo>> conflictMap;
    // 组合包
    private Map<String, List<String>> combineMap;
    // 附带
    private Map<String, List<String>> attachMap;
}
