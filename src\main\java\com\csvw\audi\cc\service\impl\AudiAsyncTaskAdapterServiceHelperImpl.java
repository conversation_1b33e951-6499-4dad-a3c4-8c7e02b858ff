package com.csvw.audi.cc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.entity.dto.FanTaskPush2023DTO;
import com.csvw.audi.cc.entity.vo.ReviceTaskVo;
import com.csvw.audi.cc.feign.AudiAsyncTaskAdapterService;
import com.csvw.audi.cc.service.IAudiAsyncTaskAdapterServiceHelper;
import com.csvw.audi.common.uti.DateUtil;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

import static com.csvw.audi.cc.common.Constant.*;

@Service
public class AudiAsyncTaskAdapterServiceHelperImpl implements IAudiAsyncTaskAdapterServiceHelper {

    private final AudiAsyncTaskAdapterService audiAsyncTaskAdapterService;

    @Value("${fan-task-2023.topic}")
    private String topic;

    @Value("${fan-task-2023.endTime}")
    private String endTime;

    @Value("${spring.application.name}")
    private String serviceName;

    @Autowired
    public AudiAsyncTaskAdapterServiceHelperImpl(
            AudiAsyncTaskAdapterService audiAsyncTaskAdapterService) {
        this.audiAsyncTaskAdapterService = audiAsyncTaskAdapterService;
    }

    private boolean notSend() {
        LocalDateTime localDateTime = LocalDateTime.parse(endTime, DateUtil.SECOND_FORMAT2);
        return LocalDateTime.now().isAfter(localDateTime);
    }

    private void completeTask(FanTaskPush2023DTO param) {
        String msg = JSONObject.toJSONString(param);
        audiAsyncTaskAdapterService.reviceTask(
                new ReviceTaskVo().setServiceName(serviceName)
                        .setTopic(topic).setTag(topic)
                        .setData(msg)
        );
    }

    @Override
    public void finishCarShoppingCart(String userIdpId, String accbTypeCode) {
        if (notSend()) {
            return;
        }
        String carSeries = "";
        if (Strings.isNotBlank(accbTypeCode) && accbTypeCode.length() >= 7) {
            String code = accbTypeCode.substring(5, 7);
            switch (code) {
                case SERIES_ID_A7L:
                    carSeries = "奥迪A7L";
                    break;
                case SERIES_ID_Q5E:
                    carSeries = "奥迪Q5";
                    break;
                case SERIES_ID_Q6:
                    carSeries = "奥迪Q6";
                    break;
                default:
            }
        }

        LocalDateTime now = LocalDateTime.now();
        FanTaskPush2023DTO param = new FanTaskPush2023DTO()
                .setType(4).setUserIdpId(userIdpId)
                .setCompleteTime(now.format(DateUtil.SECOND_FORMAT2))
                .setCarSeries(carSeries);
        this.completeTask(param);
    }
}
