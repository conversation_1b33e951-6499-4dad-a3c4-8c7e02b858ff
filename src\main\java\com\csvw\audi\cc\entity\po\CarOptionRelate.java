package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 选装关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarOptionRelate对象", description="选装关系表")
public class CarOptionRelate extends Model<CarOptionRelate> {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    private String customSeriesId;

    @ApiModelProperty(value = "配置线id")
    private String modelLineId;

    @ApiModelProperty(value = "选装项id")
    private String optionId;

    @ApiModelProperty(value = "选装项code")
    private String optionCode;

    @ApiModelProperty(value = "选装类型")
    private String optionCategory;

    @ApiModelProperty(value = "选装关系项id")
    private String optionRelateId;

    @ApiModelProperty(value = "选装关系code")
    private String optionRelateCode;

    @ApiModelProperty(value = "选装关系类型")
    private String optionRelateCategory;

    @ApiModelProperty(value = "选装关系类型，conflict：冲突，depend：依赖，attach：附带，combine：组合")
    private String relateType;

    private Integer defaultDepend;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;

    private LocalDateTime createTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
