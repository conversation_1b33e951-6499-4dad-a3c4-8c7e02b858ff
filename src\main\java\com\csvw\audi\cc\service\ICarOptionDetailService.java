package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.po.CarOptionDetail;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.vo.CarOptionDetailVo;

import java.util.List;

/**
 * <p>
 * 车系配置项细节 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-16
 */
public interface ICarOptionDetailService extends IService<CarOptionDetail> {
    List<CarOptionDetailVo> listOptionDetailVo(CarOptionDetailVo carOptionDetailVo) throws NoSuchFieldException, IllegalAccessException, Exception;
}
