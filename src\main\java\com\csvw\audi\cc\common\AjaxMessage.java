package com.csvw.audi.cc.common;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-27
 */
@Data
@NoArgsConstructor
public class AjaxMessage<T> {

    public AjaxMessage(String code, String message, T data){
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public AjaxMessage(String code, String message, String errors, T data){
        this.code = code;
        this.message = message;
        this.errors = errors;
        this.data = data;
    }

    /**
     * 状态码
     */
    private String code;

    /**
     * 消息内容
     */
    private String message;

    private String errors;

    private long timestamp = System.currentTimeMillis();

    /**
     * 数据
     */
    private T data;

}
