package com.csvw.audi.cc.common;

import com.obs.services.ObsClient;
import com.obs.services.model.ObsObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2022/9/22 19:40
 * @description
 */
@Slf4j
@Service
public class ObsService {

//    @Value("${obs.end-point}")
    private String endPoint;
//    @Value("${obs.ak}")
    private String ak;
//    @Value("${obs.sk}")
    private String sk;
//    @Value("${obs.bucket-name}")
    private String bucketName;

//    public InputStream obsGetObjectInputStream(String objectKey) {
//        // 创建ObsClient实例
//        ObsClient obsClient = new ObsClient(ak, sk, endPoint);
//        ObsObject obsObject = obsClient.getObject(bucketName, objectKey);
//        // 读取对象内容
//        InputStream input = null;
//        ByteArrayOutputStream bos = new ByteArrayOutputStream();
//        try {
//            input = obsObject.getObjectContent();
//            byte[] b = new byte[1024];
//            int len;
//            while ((len = input.read(b)) != -1) {
//                bos.write(b, 0, len);
//            }
//        } catch (Exception e) {
//            log.info("obs获取图片异常-{}", objectKey);
//            log.info("", e);
//        } finally {
//            if (input != null) {
//                try {
//                    input.close();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//        return new ByteArrayInputStream(bos.toByteArray());
//    }
}
