package com.csvw.audi.cc.common.utils;

import com.csvw.audi.cc.common.Constant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ChannelDataUtils {

    public static <T> List<T> channelData(List<T> ts, Class<T> tClass, String channel, String idFiledName, boolean needChannel) throws NoSuchFieldException, IllegalAccessException {
        if(CollectionUtils.isEmpty(ts) || StringUtils.isBlank(idFiledName)){
            return new ArrayList<>();
        }

        Field idField = tClass.getDeclaredField(idFiledName);
        idField.setAccessible(true);
        Field channelField = tClass.getDeclaredField("channel");
        channelField.setAccessible(true);
        Map<Object, T> mMap = new HashMap<>();
        Map<Object, T> cMap = new HashMap<>();
        List<T> mL = new ArrayList<>();
        List<T> cL = new ArrayList<>();
        for(T i : ts){
            Object channelObj = channelField.get(i);
            Object idObj = idField.get(i);
            if (channelObj.equals(channel)){
                if (cMap.get(idObj) == null) {
                    cMap.put(idObj, i);
                    cL.add(i);
                }
            }else if (channelObj.equals(Constant.MASTER_CHANNEL)){
                if (mMap.get(idObj) == null) {
                    mMap.put(idObj, i);
                    mL.add(i);
                }
            }
        }
        List<T> res = new ArrayList<>();
        Field[] fields = tClass.getDeclaredFields();
        for (Field f : fields){
            f.setAccessible(true);
        }
        for (T m : mL) {
            T c = cMap.get(idField.get(m));
            if (needChannel && c == null){
                continue;
            }else {
                if(c==null){
                    c = m;
                    res.add(c);
                    continue;
                }
            }

            for (Field field : fields) {
                Object f = field.get(c);
                if (f == null) {
                    field.set(c, field.get(m));
                }
            }
            res.add(c);
        }
        return res;
    }

}
