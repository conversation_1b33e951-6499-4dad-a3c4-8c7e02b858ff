package com.csvw.audi.cc.entity.dto.accb;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OptionBriefDto {

    public OptionBriefDto(String code, String category, String description){
        this.code = code;
        this.category = category;
        this.description = description;
    }

    public OptionBriefDto(String code){
        this.code = code;
    }

    @ApiModelProperty("配置项code")
    private String code;
    @ApiModelProperty("配置项类型")
    private String category;
    @ApiModelProperty("配置项描述")
    private String description;
    @ApiModelProperty(value = "配置项id")
    private String optionId;
}
