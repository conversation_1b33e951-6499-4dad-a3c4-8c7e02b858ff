package com.csvw.audi.cc.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class LoanParam {
    private Long loanRuleId;
    @ApiModelProperty(value = "价格")
    private BigDecimal price;
    @ApiModelProperty(value = "首付")
    private BigDecimal payment;
    @ApiModelProperty(value = "首付比例")
    private BigDecimal paymentRatio;
    @ApiModelProperty(value = "贷款月期")
    private String month;
}
