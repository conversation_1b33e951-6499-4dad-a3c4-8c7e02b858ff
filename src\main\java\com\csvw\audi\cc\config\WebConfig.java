//package com.csvw.audi.cc.config;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.config.annotation.CorsRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
//@Configuration
//public class WebConfig implements WebMvcConfigurer {
//    private static final Logger log = LoggerFactory.getLogger(WebConfig.class);
//    @Value("${cors.allowed.origins}")
//    private String[] allowedOrigins;
//
//    @Value("${cors.allowed.methods}")
//    private String[] allowedMethods;
//
//    @Value("${cors.max.age}")
//    private long maxAge;
//
//    @Override
//    public void addCorsMappings(CorsRegistry registry) {
//        log.info("allowedOrigins:{}",allowedOrigins);
//        registry.addMapping("/api/v1/carShoppingCart/getCarShoppingCartList")
//                .allowedOrigins(allowedOrigins) //允许请求网址
//                .allowedMethods(allowedMethods) //请求方法
//                .allowedHeaders("*") // 请求头
//                .allowCredentials(true)
//                .maxAge(maxAge);
//    }
//
//}