package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 销售代理商
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SvcdSalesAgent对象", description = "销售代理商")
public class SvcdSalesAgent extends Model<SvcdSalesAgent> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "数据id")
    @TableId(value = "sales_agent_id", type = IdType.AUTO)
    private Long salesAgentId;

    @ApiModelProperty(value = "渠道商代码")
    private String dealerCode;

    @ApiModelProperty(value = "招募经理")
    private Long recruitmentManager;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "展厅形态（1:都市店\2:奥迪之城\3:品牌体验中心\4:用户中心\5:轻量版都市店）")
    private Long exhibitionHallForm;

    @ApiModelProperty(value = "展厅级别（1:CA1\\2:CB1\\3:SC1\\4:SD1\\5:SE1\\6:CC1\\7:SL1）")
    private Long exhibitionHallLevel;

    @ApiModelProperty(value = "合同租赁面积")
    private Long leaseArea;

    @ApiModelProperty(value = "实际使用面积")
    private Long usedArea;

    @ApiModelProperty(value = "展厅所在商场")
    private String exhibitionHallMarket;

    @ApiModelProperty(value = "展厅地址")
    private String exhibitionHallAddress;

    @ApiModelProperty(value = "交付空间所在商场")
    private String deliverSpaceMarket;

    @ApiModelProperty(value = "交付空间所在地址")
    private String deliverSpaceAddress;

    private Long parkingSpaceNum;

    private String dealerGeneralManager;

    private String dealerGeneralManagerPhone;

    private String dealerGeneralManagerEmail;

    private String replyConfirmDate;

    private String loiSignDate;

    private String projectConfirmDate;

    private String sceneDiscloseDate;

    private String sceneStartDate;

    private String sceneCheckDate;

    private String hardwareCheckConfirmDate;

    private String organizationCheckDate;

    private String digitalEquipmentCheckDate;

    private String chargeCheckDate;

    private String contractTakeEffectDate;

    private String contractEffectDate;

    private String intentionCancelDate;

    private String outAcceptDate;

    private String outDate;

    private String salesPhone;

    private String afterSaleCode;

    private String spare1;

    private String spare2;

    private String spare3;

    private Long deleted;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;

    @ApiModelProperty(value = "售后服务验收日期")
    private String afterSaleServiceCheckDate;
    @ApiModelProperty(value = "PDI 区域面积（㎡）")
    private String pdiArea;
    @ApiModelProperty(value = "PDI 工位数")
    private String pdiStation;
    @ApiModelProperty(value = "PDI 区域地址")
    private String pdiAddress;
    @ApiModelProperty(value = "投资人类别")
    private String investorType;
    @ApiModelProperty(value = "关联渠道商代码")
    private String rDealerCode;
    @ApiModelProperty(value = "售后代码")
    private String serviceCode;

    @Override
    protected Serializable pkVal() {
        return this.salesAgentId;
    }

}
