package com.csvw.audi.cc.entity.dto.accb;

import lombok.Data;

import java.util.List;

@Data
public class EquipmentParam {
    private String brand_id;
    private String brand_code;
    private String model_id;
    private String model_code;
    private List<OptionParam> options;
    private CustomCondition custom_conditions;

    @Data
    public static class CustomCondition {
        private CustomOption options;
    }

    @Data
    public static class CustomOption {
        private List<String> one_of;
        private List<String> none_of;
    }

    @Data
    public static class OptionParam {
        private String id;
        private String category;
    }
}
