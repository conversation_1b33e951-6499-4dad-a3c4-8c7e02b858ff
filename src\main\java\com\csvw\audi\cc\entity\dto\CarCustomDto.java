package com.csvw.audi.cc.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

@Data
public class CarCustomDto {
    @ApiModelProperty("配置线id")
    private String modelLineId;
    @ApiModelProperty("内饰颜色面料id")
    private String sibInterieurId;
    @ApiModelProperty("配置单来源id")
    private String sourceId;
    @ApiModelProperty("选配id")
    private Set<String> optionIds;
    @ApiModelProperty("配车入口")
    private String entryPoint;
    private String depositType;
    private String mstMgrpId;
}
