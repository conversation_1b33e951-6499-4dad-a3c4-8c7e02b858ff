package com.csvw.audi.cc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.entity.dto.*;
import com.csvw.audi.cc.entity.dto.omd.OmdModelDto;
import com.csvw.audi.cc.entity.enumeration.OptionCategoryEnum;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.entity.vo.*;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.mapper.CarModelLineTypeMapper;
import com.csvw.audi.cc.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 车辆ABC类配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Service
@Slf4j
public class CarModelLineTypeServiceImpl extends ServiceImpl<CarModelLineTypeMapper, CarModelLineType> implements ICarModelLineTypeService {

    @Autowired
    private CarModelLineTypeMapper mapper;

    @Autowired
    private ICarOmdVehicleTypeService vehicleTypeService;

    @Autowired
    private ICarModelLineTypeService modelLineTypeService;

    @Autowired
    private ICarModelLineTypeOptionService modelLineTypeOptionService;

    @Autowired
    private ICarRecommendService carRecommendService;

    @Autowired
    private ICarModelLineService modelLineService;

    @Autowired
    private ICarModelLineOptionService modelLineOptionService;
    private final List<String> specialLine = Arrays.asList(
            // a7l 黑白
            "TYPE:498BZY-GWAEWAE",
            "TYPE:G6ICAY-MREIH14-MHUDKS1-GPCEPCE-GPS1PS1-GYECYEC-MEIH5MD-MRAD53E-MLSE9VS",
            "TYPE:G6ICAY-MREIH14-MHUDKS1-GPCEPCE-GPS8PS8-MCHRQJ3-GWTYWTY-GYEBYEB-MEIH5MD-MRAD53E-MLSE9VS",
            // q5e 黑/武/影
            "TYPE:G4ICC3-MSIBN4X-GYEAYEA-MSIH4A4-MEIH5TD-MRAD56F-MLSE9VS",
            "TYPE:G4ICF3-MSIBN4X-MLCPQQ9-GYEBYEB-MRAD56F",
            // a7l 45 55影黑
            "TYPE:498BZY-GPGCPGC-GWAEWAE-GYEHYEH",
            "TYPE:498B2Y-MHUDKS1-GPCYPCY-GPGCPGC-GWAEWAE-GYEKYEK-MLSE9VS",
            "TYPE:498BZY-MREII56-GPAHPAH-GPS6PS6-GYEAYEA-MLRA2PF-MRAD53D",
            "TYPE:498B2Y-MESTGZ2-MREII56-GPAHPAH-GPCYPCY-GPDWPDW-GPS6PS6-GWA6WA6-GYECYEC-MEIH5MK-MRAD53D-MLSE9VS",
            // Q6 行云影黑
            "TYPE:G6ICBY-MREIH14-MHUDKS1-GPS2PS2-GYEDYED-MEIH5MD-MRAD53E-MLSE9VS",

            // Q6 黑影2024
            "TYPE:G6ICAY-MREIH14-MHUDKS1-GPCEPCE-GPC2PC2-GPS8PS8-GWTYWTY-GYEBYEB-MEIH5MD-MRAD53E-MLSE9VS",
            "TYPE:G6ICAY-MREIH14-MHUDKS1-GPCEPCE-GPC2PC2-GPS1PS1-GYECYEC-MEIH5MD-MRAD53E-MLSE9VS",
            "TYPE:G6ICBY-MREIH14-MHUDKS1-GPC2PC2-GPS2PS2-GYEDYED-MEIH5MD-MRAD53E-MLSE9VS"
            );

    @Override
    public String findBeforeCheckType(ModelLineTypeDto modelLineTypeDto) {
        return mapper.findBeforeCheckType(modelLineTypeDto);
    }

    @Override
    public ModelTypeResultDto findType(ModelLineTypeDto modelLineTypeDto) {
        return mapper.findType(modelLineTypeDto);
    }

    @Override
    public List<PersonalOptionVo.PersonalCompose> queryPersonalComposes(TypePrListQueryDto queryDto) {
        List<PersonalOptionVo.PersonalCompose> optionComposes = new ArrayList<>();
        List<TypePrs> prs = this.mapper.queryTypePrs(queryDto);
        if (CollectionUtils.isNotEmpty(prs)){
            for(TypePrs typePrs : prs){
                if (typePrs == null)
                    continue;
                if (StringUtils.isBlank(typePrs.getPrList())){
                    queryDto.setPrList(null);
                    optionComposes.add(new PersonalOptionVo.PersonalCompose("推荐组合", new ArrayList<>(), mapper.queryComposeTypeIdsOfA(queryDto), typePrs.getSeats()));
                    continue;
                }
                String prCodes = typePrs.getPrList();
                String[] codes = prCodes.split(",");
                List<ModelLineOptionVo> composePrs = new ArrayList<>();
                boolean flag = true;
                for(String code : codes){
                    List<ModelLineOptionVo> codeVo = queryDto.getOptionVoList().stream()
                            .filter(v->v.getOptionCode().equals(code) && v.getStatus() != null && v.getStatus().intValue() == 2).collect(Collectors.toList());
                    if (codeVo.size() < 1){
                        flag = false;
                        break;
                    }
                    composePrs.add(codeVo.get(0));
                }
                if (flag) {
                    queryDto.setPrList(prCodes);
                    optionComposes.add(new PersonalOptionVo.PersonalCompose("推荐组合", composePrs, mapper.queryComposeTypeIdsOfA(queryDto), typePrs.getSeats()));
                }
            }
        }
        optionComposes.sort((c1, c2)->{
            if (c1.getComposePrice() instanceof BigDecimal && c2.getComposePrice() instanceof BigDecimal){
                return ((BigDecimal)c1.getComposePrice()).compareTo((BigDecimal) c2.getComposePrice());
            }else {
                return 0;
            }
        });
        for (int i=0;i<optionComposes.size();i++){
            optionComposes.get(i).setComposeName("推荐组合" + (i+1));
        }
        return optionComposes;
    }

    @Override
    public boolean filterOption(TypePrListQueryDto queryDto) {
        ModelLineOptionVo o = queryDto.getOptionVo();
        if (o == null || o.getCategory() == null){
            return false;
        }
        ModelLineTypeDto typeParam = new ModelLineTypeDto();
        typeParam.setAccbTypeCode(queryDto.getAccbTypeCode());
        typeParam.setModelYear(queryDto.getModelYear());
        typeParam.setModelVersion(queryDto.getModelVersion());
        LambdaQueryWrapper<CarModelLineType> typeQ = new LambdaQueryWrapper<>();
        typeQ.eq(CarModelLineType::getAccbTypeCode, queryDto.getAccbTypeCode())
                .eq(CarModelLineType::getModelYear, queryDto.getModelYear())
                .eq(CarModelLineType::getModelVersion, queryDto.getModelVersion())
                .eq(CarModelLineType::getStatus, 1);
        if (OptionCategoryEnum.OUTCOLOR.getValue().equals(o.getCategory())){
            typeQ.eq(CarModelLineType::getExterieurColorCode, o.getOptionCode());
            typeParam.setExtCode(o.getOptionCode());
        }else if(OptionCategoryEnum.WHEEL.getValue().equals(o.getCategory())){
            typeQ.eq(CarModelLineType::getRadCode, o.getOptionCode());
            typeParam.setRadCode(o.getOptionCode());
        }else if(OptionCategoryEnum.EIH.getValue().equals(o.getCategory())){
            typeQ.eq(CarModelLineType::getEihCode, o.getOptionCode());
            typeParam.setEihCode(o.getOptionCode());
        }else {
            return false;
        }
        if (StringUtils.isNotBlank(queryDto.getSeats())){
            typeParam.setSeat(queryDto.getSeats());
            typeQ.eq(CarModelLineType::getSeats, queryDto.getSeats());
        }
        typeQ.eq(CarModelLineType::getStatus, 1).last("limit 1");
        boolean existType = this.count(typeQ) > 0;
        if (specialLine.contains(queryDto.getModelLineVo().getAccbTypeCode())){
            OptionParamDto optionParamDto = new OptionParamDto();
            optionParamDto.setModelLineId(queryDto.getModelLineVo().getModelLineId());
            optionParamDto.setCategory(OptionCategoryEnum.OUTCOLOR.getValue());
            optionParamDto.setChannel(Constant.MASTER_CHANNEL);
            optionParamDto.setStatus(2);
            optionParamDto.setDelFlag(0);
            List<ModelLineOptionVo> optionVos = modelLineOptionService.modelLineOptionQueryStrict(optionParamDto);
            // 通过颜色车型码，匹配类型
            if (CollectionUtils.isNotEmpty(optionVos)) {
                typeParam.setExtCodes(optionVos.stream().map(ModelLineOptionVo::getOptionCode).collect(Collectors.toList()));
            }
        }
        if (existType){
            Set<String> ids = mapper.queryTypeIdsOfA(typeParam);
            queryDto.getOptionVo().setTypeIdsOfA(ids);
        }
        return existType;
    }

    @Override
    public boolean filterInterOption(TypePrListQueryDto o) {
        if (o.getSibInterieurVo() == null){
            return false;
        }
        ModelLineTypeDto typeParam = new ModelLineTypeDto();
        typeParam.setAccbTypeCode(o.getAccbTypeCode());
        typeParam.setModelYear(o.getModelYear());
        typeParam.setModelVersion(o.getModelVersion());
        typeParam.setSibCode(o.getSibInterieurVo().getSibOptionCode());
        typeParam.setInCode(o.getSibInterieurVo().getInterieurOptionCode());
        LambdaQueryWrapper<CarModelLineType> typeQ = new LambdaQueryWrapper<>();
        typeQ.eq(CarModelLineType::getAccbTypeCode, o.getAccbTypeCode())
                .eq(CarModelLineType::getModelYear, o.getModelYear())
                .eq(CarModelLineType::getModelVersion, o.getModelVersion())
                .eq(CarModelLineType::getSibCode, o.getSibInterieurVo().getSibOptionCode())
                .eq(CarModelLineType::getInterieurColorCode, o.getSibInterieurVo().getInterieurOptionCode())
                .eq(CarModelLineType::getStatus, 1);
        if (StringUtils.isNotBlank(o.getSeats())){
            typeParam.setSeat(o.getSeats());
            typeQ.eq(CarModelLineType::getSeats, o.getSeats());
        }
        typeQ.last("limit 1");
        boolean existType = this.count(typeQ) > 0;
        if (specialLine.contains(o.getModelLineVo().getAccbTypeCode())){
            OptionParamDto optionParamDto = new OptionParamDto();
            optionParamDto.setModelLineId(o.getModelLineVo().getModelLineId());
            optionParamDto.setCategory(OptionCategoryEnum.OUTCOLOR.getValue());
            optionParamDto.setChannel(Constant.MASTER_CHANNEL);
            optionParamDto.setStatus(2);
            optionParamDto.setDelFlag(0);
            List<ModelLineOptionVo> optionVos = modelLineOptionService.modelLineOptionQueryStrict(optionParamDto);
            // 通过颜色车型码，匹配类型
            if (CollectionUtils.isNotEmpty(optionVos)) {
                typeParam.setExtCodes(optionVos.stream().map(ModelLineOptionVo::getOptionCode).collect(Collectors.toList()));
            }
        }
        if (existType){
            Set<String> ids = mapper.queryTypeIdsOfA(typeParam);
            o.getSibInterieurVo().setTypeIdsOfA(ids);
        }
        return existType;
    }

    @Override
    public void convertOmdAbcData() throws ServiceException {
        try {
            mapper.flushErrorTypeOptionData();
            mapper.flushErrorTypeData();
            int page = 0;
            int pageSize = 1000;
            String lastString = "limit " + page+" , "+pageSize;
            LambdaQueryWrapper<CarOmdVehicleType> typeQ = new LambdaQueryWrapper<>();
            typeQ.eq(CarOmdVehicleType::getDelFlag, 0).orderByAsc(CarOmdVehicleType::getCreateTime).last(lastString);
            List<CarOmdVehicleType> types = vehicleTypeService.list(typeQ);
            while (CollectionUtils.isNotEmpty(types))
            {
                types.forEach(i-> {
                    if ("C".equals(i.getClassify())){
                        CarModelLineType type = new CarModelLineType();
                        type.setOmdVehicleTypeId(i.getId());
                        type.setCreateTime(LocalDateTime.now());
                        type.setAccbTypeCode(i.getAccbTypeCode());
                        type.setModelYear(i.getModelYear());
                        type.setModelVersion(i.getModelVersion());
                        type.setSeats(i.getSeats());
                        type.setClassify(i.getClassify());
                        modelLineTypeService.save(type);
                        return ;
                    }
                    try {
                        modelLineTypeService.handleOmdVehicleType(i);
                    } catch (Exception e) {
                        log.error("ABC处理异常，数据："+ JSONObject.toJSONString(i), e);
                    }
                });

                page ++;
                lastString = "limit " + page*pageSize +" , "+pageSize;
                typeQ = new LambdaQueryWrapper<>();
                typeQ.eq(CarOmdVehicleType::getDelFlag, 0).orderByAsc(CarOmdVehicleType::getCreateTime).last(lastString);
                types = vehicleTypeService.list(typeQ);
            }
        }catch (Exception e){
            log.error("转换omd数据失败", e);
            throw new ServiceException("500501", "处理ABC类数据异常", e.getMessage());
        }
    }

    @Override
    public void handleOmdVehicleType(CarOmdVehicleType vehicleType) throws Exception {
        CarModelLineType type = new CarModelLineType();
        List<ModelLineOptionVo> prOptions = new ArrayList<>();
        OmdModelDto omdModelDto = new OmdModelDto();
        omdModelDto.setModelCode(vehicleType.getAccbTypeCode());
        omdModelDto.setModelYear(vehicleType.getModelYear());
        omdModelDto.setModelVersion(vehicleType.getModelVersion());
        omdModelDto.setColorCode(vehicleType.getColorCode());
        omdModelDto.setInteriorCode(vehicleType.getInteriorCode());
        omdModelDto.setPrList(StringUtils.isNotBlank(vehicleType.getPrList())?vehicleType.getPrList()+","+vehicleType.getWheel() : vehicleType.getWheel());
        BestRecommendCarVo recommendCarVo = carRecommendService.bestRecommendToVo(omdModelDto, Constant.MASTER_CHANNEL);
        List<String> notInCategory = Arrays.asList("RAD", "COLOR_EXTERIEUR", "COLOR_INTERIEUR", "SIB", "EIH");
        List<ModelLineOptionVo> personalOptionVos = modelLineService.modelLinePersonalOption(Constant.MASTER_CHANNEL,
                recommendCarVo.getModelLine().getModelLineId(), notInCategory);
        Set<String> canSelectCodes = new HashSet<>();
        if (CollectionUtils.isNotEmpty(personalOptionVos)) {
            canSelectCodes.addAll(personalOptionVos.stream().filter(o->o.getStatus() != null && o.getStatus().intValue() == 2)
                    .map(ModelLineOptionVo::getOptionCode).collect(Collectors.toSet()));
        }
        if (recommendCarVo == null){
            return;
        }
        type.setAccbTypeCode(recommendCarVo.getModelLine().getAccbTypeCode().replaceFirst("TYPE:", ""));
        type.setSeats(vehicleType.getSeats());
        type.setModelVersion(recommendCarVo.getModelLine().getVersion());
        type.setModelYear(recommendCarVo.getModelLine().getModelYear());
        type.setClassify("A/B".equals(vehicleType.getClassify())?"A" : vehicleType.getClassify());
        type.setOmdVehicleTypeId(vehicleType.getId());
        type.setStatus(0);
        type.setCreateTime(LocalDateTime.now());
        type.setInterieurColorCode(recommendCarVo.getModelLineSibInterieurVo().getInterieurOptionCode());
        type.setSibCode(recommendCarVo.getModelLineSibInterieurVo().getSibOptionCode());
        recommendCarVo.getOptions().forEach(o->{
            if (o.getCategory() == null ){
                if (!canSelectCodes.contains(o.getOptionCode())){
                    type.setStatus(2);
                }
                prOptions.add(o);
                return;
            }
            if (OptionCategoryEnum.OUTCOLOR.getValue().equals(o.getCategory())) {
                type.setExterieurColorCode(o.getOptionCode());
            }else if (OptionCategoryEnum.INCOLOR.getValue().equals(o.getCategory())) {
                return;
            }else if (OptionCategoryEnum.SIB.getValue().equals(o.getCategory())) {
                return;
            }else if (OptionCategoryEnum.EIH.getValue().equals(o.getCategory())) {
                type.setEihCode(o.getOptionCode());
            }else if (OptionCategoryEnum.WHEEL.getValue().equals(o.getCategory())) {
                type.setRadCode(o.getOptionCode());
            }else {
                if (!canSelectCodes.contains(o.getOptionCode())){
                    type.setStatus(2);
                }
                prOptions.add(o);
            }
        });
        List<ModelLineOptionVo> optionVos;
        String channel = Constant.MASTER_CHANNEL;
        if (StringUtils.isBlank(type.getEihCode())){
            String category = OptionCategoryEnum.EIH.getValue();
            optionVos = modelLineService.modelLineOption(channel, recommendCarVo.getModelLine().getModelLineId(), category);
            for (ModelLineOptionVo o : optionVos) {
                if (o.getStatus() != null && o.getStatus().intValue() == 1) {
                    type.setEihCode(o.getOptionCode());
                    break;
                }
            }
        }
        if (StringUtils.isBlank(type.getRadCode())){
            String category = OptionCategoryEnum.WHEEL.getValue();
            optionVos = modelLineService.modelLineOption(channel, recommendCarVo.getModelLine().getModelLineId(), category);
            for (ModelLineOptionVo o : optionVos) {
                if (o.getStatus() != null && o.getStatus().intValue() == 1) {
                    type.setRadCode(o.getOptionCode());
                    break;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(prOptions)){
            type.setPrList(prOptions.stream().map(ModelLineOptionVo::getOptionCode).sorted().collect(Collectors.joining(",")));
        }
        type.setPrNum(prOptions==null?0:prOptions.size());
        modelLineTypeService.save(type);
        prOptions.forEach(o->{
            CarModelLineTypeOption typeOption = new CarModelLineTypeOption();
            typeOption.setModelLineTypeId(type.getId());
            typeOption.setCode(o.getOptionCode());
            modelLineTypeOptionService.save(typeOption);
        });
    }

    @Override
    public void updateModelLineTypeFlagByABC(Integer classifyVersion) throws Exception {
        List<ModelClassify> modelClassifies = mapper.queryModelClassify();
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineVo> modelLineVos = modelLineService.listModelLine(modelParamDto);
        Map<String, List<ModelLineVo>> uniqueCodeModel = new HashMap<>();
        modelLineVos.forEach(i->{
            String modelUniqueCode = i.getAccbTypeCode()+i.getModelYear()+i.getVersion();
            if (uniqueCodeModel.get(modelUniqueCode) == null){
                uniqueCodeModel.put(modelUniqueCode, new ArrayList<>());
            }
            uniqueCodeModel.get(modelUniqueCode).add(i);
        });
        LambdaUpdateWrapper<CarModelLine> mU = new LambdaUpdateWrapper<>();
        mU.set(CarModelLine::getTypeFlag, null).set(CarModelLine::getClassifyVersion, null);
        modelLineService.update(mU);
        modelClassifies.forEach(i->{
            List<ModelLineVo> lines = uniqueCodeModel.get(i.getModelUniqueCode());
            if (CollectionUtils.isEmpty(lines)){
                return;
            }
            if (lines.size()>1) {
                for (ModelLineVo lineVo : lines) {
                    if (specialLine.contains(lineVo.getAccbTypeCode())) {
                        OptionParamDto optionParamDto = new OptionParamDto();
                        optionParamDto.setModelLineId(lineVo.getModelLineId());
                        optionParamDto.setCategory(OptionCategoryEnum.OUTCOLOR.getValue());
                        optionParamDto.setChannel(Constant.MASTER_CHANNEL);
                        optionParamDto.setStatus(2);
                        optionParamDto.setDelFlag(0);
                        List<ModelLineOptionVo> optionVos = modelLineOptionService.modelLineOptionQueryStrict(optionParamDto);
                        // 通过颜色车型码，匹配类型
                        if (CollectionUtils.isNotEmpty(optionVos)) {
                            ModelLineTypeDto paramDto = new ModelLineTypeDto(lineVo.getAccbTypeCode(), lineVo.getModelYear(), lineVo.getVersion());
                            paramDto.setExtCodes(optionVos.stream().map(ModelLineOptionVo::getOptionCode).collect(Collectors.toList()));
                            List<ModelClassify> modelQueryClassifies = mapper.queryModelClassifyByParam(paramDto);
                            if (CollectionUtils.isNotEmpty(modelQueryClassifies)) {
                                String typeFlag = Arrays.stream(modelQueryClassifies.get(0).getModelClassify().split(",")).sorted().collect(Collectors.joining());
                                LambdaUpdateWrapper<CarModelLine> linesU = new LambdaUpdateWrapper<>();
                                linesU.set(CarModelLine::getTypeFlag, typeFlag)
                                        .eq(CarModelLine::getModelLineId, lineVo.getModelLineId())
                                        .in(CarModelLine::getChannel, Constant.NEED_TYPE_FLAG);
                                modelLineService.update(linesU);
                                LambdaUpdateWrapper<CarModelLine> lineVersionU = new LambdaUpdateWrapper<>();
                                lineVersionU.set(CarModelLine::getClassifyVersion, classifyVersion)
                                        .eq(CarModelLine::getModelLineId, lineVo.getModelLineId())
                                        .eq(CarModelLine::getChannel, Constant.MASTER_CHANNEL);
                                modelLineService.update(lineVersionU);
                            }
                        }
                    }

                }
                return;
            }
            Set<String> modelLineIds = lines.stream().map(ModelLineVo::getModelLineId).collect(Collectors.toSet());
            String typeFlag = Arrays.stream(i.getModelClassify().split(",")).sorted().collect(Collectors.joining());
            LambdaUpdateWrapper<CarModelLine> linesU = new LambdaUpdateWrapper<>();
            linesU.set(CarModelLine::getTypeFlag, typeFlag)
                    .in(CarModelLine::getModelLineId, modelLineIds).in(CarModelLine::getChannel, Constant.NEED_TYPE_FLAG);
            modelLineService.update(linesU);
            LambdaUpdateWrapper<CarModelLine> lineVersionU = new LambdaUpdateWrapper<>();
            lineVersionU.set(CarModelLine::getClassifyVersion, classifyVersion)
                    .in(CarModelLine::getModelLineId, modelLineIds)
                    .eq(CarModelLine::getChannel, Constant.MASTER_CHANNEL);
            modelLineService.update(lineVersionU);
        });
    }

    @Override
    public void exchangeOmdAbcData(LocalDateTime lastTime) {
        if (lastTime != null) {
            mapper.offlineTypeOption(lastTime);
            mapper.offlineType(lastTime);
        }

        LambdaUpdateWrapper<CarModelLineType> typeOnline = new LambdaUpdateWrapper<>();
        typeOnline.set(CarModelLineType::getStatus, 1).eq(CarModelLineType::getStatus, 0);
        modelLineTypeService.update(typeOnline);

    }

    @Override
    public List<TypeIdsBySeats> listTypeIdsBySeats(ModelLineVo modelLineVo) {
        ModelLineTypeDto typeDto = new ModelLineTypeDto(modelLineVo.getAccbTypeCode(), modelLineVo.getModelYear(), modelLineVo.getVersion());
        if (specialLine.contains(modelLineVo.getAccbTypeCode())){
            OptionParamDto optionParamDto = new OptionParamDto();
            optionParamDto.setModelLineId(modelLineVo.getModelLineId());
            optionParamDto.setCategory(OptionCategoryEnum.OUTCOLOR.getValue());
            optionParamDto.setChannel(Constant.MASTER_CHANNEL);
            optionParamDto.setStatus(2);
            optionParamDto.setDelFlag(0);
            List<ModelLineOptionVo> optionVos = modelLineOptionService.modelLineOptionQueryStrict(optionParamDto);
            // 通过颜色车型码，匹配类型
            if (CollectionUtils.isNotEmpty(optionVos)) {
                typeDto.setExtCodes(optionVos.stream().map(ModelLineOptionVo::getOptionCode).collect(Collectors.toList()));
            }
        }
        List<String> seats = mapper.listSeats(typeDto);
        List<TypeIdsBySeats> res = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(seats)) {
            seats.forEach(i->{
                typeDto.setSeat(i);
                TypeIdsBySeats bySeats = new TypeIdsBySeats();
                bySeats.setSeats(i);
                bySeats.setTypeIdsOfA(mapper.queryTypeIdsOfA(typeDto));
                res.add(bySeats);
            });
        }
        return res;
    }


}
