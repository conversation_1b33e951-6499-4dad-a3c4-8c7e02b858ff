package com.csvw.audi.cc.controller.ccnew;


import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.dto.ModelParamDto;
import com.csvw.audi.cc.entity.dto.OptionRelDto;
import com.csvw.audi.cc.entity.dto.OptionRelateParam;
import com.csvw.audi.cc.entity.dto.SeriesParamDto;
import com.csvw.audi.cc.entity.vo.CustomSeriesVo;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.entity.vo.ModelLineSibInterieurVo;
import com.csvw.audi.cc.entity.vo.ModelLineVo;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;


@RestController
@RequestMapping("/open/api/v1/cc/hop")
@Api(tags = "HOP车辆配置开放接口")
@Slf4j
public class CarConfigHopOpenApiController extends BaseController {

    @Autowired
    private ICarCustomOptionService optionService;

    @Autowired
    private ICarRenderService renderService;

    @Autowired
    private ICarCustomService customService;

    @Autowired
    private ICarCustomSeriesService customSeriesService;

    @Autowired
    private ICarModelLineService modelLineService;

    @Autowired
    private ICarModelLineOptionService modelLineOptionService;

    @Autowired
    private ICarOptionRelateService optionRelateService;

    @Autowired
    private ICarRecommendService recommendService;

    @Autowired
    private ICarModelLineSibInterieurService sibInterieurService;

    @Autowired
    private ICarOmdPriceTypeService priceTypeService;

    @GetMapping("/modelLine/configs/color_exterieur")
    @ApiOperation("配置线外饰")
    public AjaxMessage<List<ModelLineOptionVo>> modelLineExterieur(String modelLineId) throws Exception {
        String channel = "hop";
        String category = "COLOR_EXTERIEUR";
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, modelLineId, category);
        return successMessage(optionVos);
    }

    @GetMapping(value = {"/modelLine/configs/sib_color_interieur"})
    @ApiOperation("配置线内饰面料整合")
    public AjaxMessage<List<ModelLineSibInterieurVo>> modelLineSibInterieur(String modelLineId, String sibInterieurId) throws Exception {
        String channel = "hop";
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        List<ModelLineSibInterieurVo> sibInterieurVos = sibInterieurService.modelLineSibInterieur(channel, modelLineId, sibInterieurId);
        OptionRelateParam optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLineId);
        optionRelateParam.setRelateTypes(Arrays.asList("conflict", "depend"));
        optionRelateParam.setNotInRelateCategory(Arrays.asList("COLOR_INTERIEUR", "COLOR_EXTERIEUR", "SIB"));
        List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        Map<String, List<OptionRelDto>> oMap = new HashMap<>();
        relDtos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel == null) {
                oRel = new ArrayList<>();
                oMap.put(i.getOptionId(), oRel);
            }
            oRel.add(i);
        });
        sibInterieurVos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getSibInterieurId());
            if (oRel != null){
                i.setSibInterieurRelates(oRel);
            }
        });
        return successMessage(sibInterieurVos);
    }

    @GetMapping(value = {"/modelLine/configs/vos"})
    @ApiOperation("配置线座椅")
    public AjaxMessage<List<ModelLineOptionVo>> modelLineVos(String modelLineId) throws Exception {
        String channel = "hop";
        String category = "VOS";
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, modelLineId, category);
        return successMessage(optionVos);
    }

    @GetMapping(value = {"/modelLine/configs/eih"})
    @ApiOperation("配置线饰条")
    public AjaxMessage<List<ModelLineOptionVo>> modelLineEid(String modelLineId) throws Exception {
        String channel = "hop";
        String category = "EIH";
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, modelLineId, category);
        return successMessage(optionVos);
    }

    @GetMapping(value = {"/modelLine/configs/rad"})
    @ApiOperation("配置线轮毂")
    public AjaxMessage<List<ModelLineOptionVo>> modelLineRad(String modelLineId) throws Exception {
        String channel = "hop";
        String category = "RAD";
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, modelLineId, category);
        return successMessage(optionVos);
    }

    @GetMapping("/customSeries")
    @ApiOperation("自定义车系(车系)")
    public AjaxMessage<List<CustomSeriesVo>> customSeries() throws Exception {
        String channel = "hop";
        SeriesParamDto paramDto = new SeriesParamDto();
        paramDto.setChannel(channel);
        List<CustomSeriesVo> data = customSeriesService.listCustomSeriesVo(paramDto);
        return successMessage(data);
    }

    @GetMapping("/modelLine")
    @ApiOperation("配置线(车型)")
    public AjaxMessage<List<ModelLineVo>> modelLine(@RequestParam String customSeriesId) throws Exception {
        String channel = "hop";
        ModelParamDto paramDto = new ModelParamDto();
        paramDto.setCustomSeriesId(customSeriesId);
        paramDto.setChannel(channel);
        paramDto.setDelFlag(0);
        List<ModelLineVo> data = modelLineService.listModelLine(paramDto);
        return successMessage(data);
    }

}

