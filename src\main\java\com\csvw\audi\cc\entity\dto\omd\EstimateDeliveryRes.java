package com.csvw.audi.cc.entity.dto.omd;

import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.entity.vo.ModelLineSibInterieurVo;
import com.csvw.audi.cc.entity.vo.ModelLineVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class EstimateDeliveryRes {
    @ApiModelProperty(value = "物流周期（天）")
    private String lgsCycle;
    @ApiModelProperty(value = "资源类型:1-现车(总部库存或者代理商库存),0-非现车(管线或者排产)")
    private String stockVehicleFlag;
    @ApiModelProperty(value = "下线周期（天）")
    private String estimateOfflineCycle;
    @ApiModelProperty(value = "预计交付日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDate estimateDate;
    private List<SimilarityModel> ccSimilarityModelList;
    private List<SimilarityModelList> similarityModelList;

    @Data
    public static class SimilarityModel {
        @ApiModelProperty(value = "预计交付时间(天)")
        private String estimateDeliveryTime;
        @ApiModelProperty(value = "预计交付日期")
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
        private LocalDate estimateDate;
        @ApiModelProperty("相似度（%）")
        private String similarity;
        private ModelLineVo modelLine;
        private List<ModelLineOptionVo> options;
        private ModelLineSibInterieurVo sibInterieur;
        private String uniqueCode;
        private Object totalPrice;
    }
    @Data
    public static class SimilarityModelList {
        private String modelCode;
        private String modelYear;
        private String modelVersion;
        private String interiorCode;
        private String prList;
        private String colorCode;
        private String estimateDeliveryTime;
        private String similarity;
    }
}
