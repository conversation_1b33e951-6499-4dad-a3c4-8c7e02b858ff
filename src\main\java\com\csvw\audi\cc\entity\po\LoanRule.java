package com.csvw.audi.cc.entity.po;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="LoanRule对象", description="")
public class LoanRule extends Model<LoanRule> {

    private static final long serialVersionUID=1L;

      @TableId(value = "loan_rule_id", type = IdType.ASSIGN_ID)
    private Long loanRuleId;

    @ApiModelProperty(value = "金融机构")
    private String loanAgency;

    @ApiModelProperty(value = "金融机构code")
    private String loanAgencyCode;

    @ApiModelProperty(value = "最低首付比例")
    private BigDecimal minPaymentRatio;

    @ApiModelProperty(value = "利率")
    private String interestRates;

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "顺序权重")
    private Integer weight;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.loanRuleId;
    }

}
