package com.csvw.audi.cc.component.kafkaService.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.csvw.audi.cc.component.kafkaService.SvcdKafkaService;
import com.csvw.audi.cc.entity.po.SvcdProvince;
import com.csvw.audi.cc.mapper.SvcdProvinceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service("svcdProvinceKafkaService")
public class SvcdProvinceKafkaServiceImpl implements SvcdKafkaService {

    @Autowired
    private SvcdProvinceMapper svcdProvinceMapper;

    @Override
    public void insertData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdProvince province = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdProvince.class);
        province.setCreatedAt(nowDate);
        province.setUpdatedAt(nowDate);
        province.insert();
    }

    @Override
    public void updateData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdProvince province = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdProvince.class);
        Long provinceCode = province.getProvinceCode();

        QueryWrapper<SvcdProvince> provinceQusery = new QueryWrapper<>();
        provinceQusery.eq("province_code",provinceCode);
        List<SvcdProvince> list = province.selectList(provinceQusery);
        if(list == null || list.size() == 0) {
            province.setCreatedAt(nowDate);
            province.setUpdatedAt(nowDate);
            province.insert();
        } else {
            province.setUpdatedAt(nowDate);

            UpdateWrapper<SvcdProvince> provinceUpdateWrapper = new UpdateWrapper<>();
            provinceUpdateWrapper.eq("province_code",provinceCode);
            province.setProvinceCode(null);//主键置空，防止数据库更新报错
            svcdProvinceMapper.update(province,provinceUpdateWrapper);
        }
    }
}
