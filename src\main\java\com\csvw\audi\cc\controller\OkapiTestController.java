package com.csvw.audi.cc.controller;


import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.dto.okapi.*;
import com.csvw.audi.cc.entity.dto.omd.EstimateDeliveryBody;
import com.csvw.audi.cc.entity.dto.omd.EstimateDeliveryRes;
import com.csvw.audi.cc.entity.dto.omd.OmdObjectRes;
import com.csvw.audi.cc.entity.dto.omd.OmdParam;
import com.csvw.audi.cc.feign.OkapiFeign;
import com.csvw.audi.cc.service.impl.CacheServiceImpl;
import com.csvw.audi.cc.service.impl.CarRecommendServiceImpl;
import com.csvw.audi.cc.service.impl.OkapiService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/test/okapi")
@Api(tags = "test okapi")
@Slf4j
public class OkapiTestController extends BaseController {

    @Autowired
    OkapiFeign okapiFeign;

    @Autowired
    OkapiService okapiService;

    @Autowired
    private CacheServiceImpl cacheService;

    @Autowired
    CarRecommendServiceImpl carRecommendService;

    @GetMapping("/series")
    public AjaxMessage<OkapiRes<List<SeriesDto>>> series(){
        OkapiRes<List<SeriesDto>> res = okapiFeign.series("A");
        return successMessage(res);
    }

    @GetMapping("/models")
    public AjaxMessage<OkapiRes<List<ModelsDto>>> models(String seriesCode){
        OkapiRes<List<ModelsDto>> res = okapiFeign.models("A", seriesCode);
        return successMessage(res);
    }

    @GetMapping("/modelsPrice")
    public AjaxMessage<OkapiRes<List<ModelsPriceDto>>> modelsPrice(String seriesCode, String modelUniqueCode){
        OkapiRes<List<ModelsPriceDto>> res = okapiFeign.modelsPrice("A", seriesCode, modelUniqueCode);
        return successMessage(res);
    }

    @PostMapping("/labelConfig")
    public AjaxMessage<OkapiRes<List<LabelConfigResDto>>> labelConfig(@RequestBody LabelConfigParamDto paramDto){
        OkapiRes<List<LabelConfigResDto>> res = okapiFeign.modelLabelConfig(paramDto);
        return successMessage(res);
    }

    @GetMapping("/modelMaterial")
    public AjaxMessage<OkapiRes<List<ModelMaterialDto>>> modelMaterial(String seriesCode, String subModelRuleId){
        OkapiRes<List<ModelMaterialDto>> res = okapiFeign.seriesModelMaterial("A", seriesCode, subModelRuleId);
        return successMessage(res);
    }

    @GetMapping("/salesSeries")
    public AjaxMessage<OkapiRes<List<SalesSeriesDto>>> salesSeries(){
        OkapiRes<List<SalesSeriesDto>> res = okapiFeign.salesSeries("A");
        return successMessage(res);
    }

    @GetMapping("/salesModels")
    public AjaxMessage<OkapiRes<List<SalesModelsDto>>> salesModels(String seriesCode){
        OkapiRes<List<SalesModelsDto>> res = okapiFeign.salesModels("A", seriesCode);
        return successMessage(res);
    }

    @GetMapping("/salesModelDetail")
    public AjaxMessage<OkapiRes<List<SalesModelsDetail>>> salesModelDetail(String seriesCode, String modelUniqueCode){
        OkapiRes<List<SalesModelsDetail>> res = okapiFeign.salesModelsDetail("A", seriesCode, modelUniqueCode);
        return successMessage(res);
    }

    @GetMapping("/sync")
    public AjaxMessage<OkapiRes<List<SeriesDto>>> sync(){
        okapiService.syncOkapiData();
        cacheService.cacheEvictCc();
        return successMessage(null);
    }

}

