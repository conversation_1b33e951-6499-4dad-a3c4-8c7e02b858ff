package com.csvw.audi.cc.service;

import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.entity.dto.accb.*;

import java.util.List;

public interface IACCBService {

    List<ACCBCarModels> allModels(String lang, String product);

    List<TypeDto> modelTypes(String lang, String product, String modelId);

    List<OptionDto> typeOptions(String language, String product, String typeId);

    List<InteriorOptionDto> typeInteriorOptions(String language, String product, String modelId, String typeId);

    String genAudiCode(AudiConfigDto audiConfigDto);

    AudiConfigDto audiConfig(String audiCode);

    List<ConfigPreviewDto> preview(String typeCode, String modelYear, String exteriorCode, String interiorCode);

    List<OptionDto> refreshOption(String language, String product, RefreshOptionParam optionParam);

    List<EquipmentDto> standardEquipment(String language, String product, EquipmentParam equipmentParam);

    public JSONObject getAudiCode(String audiCode) throws Exception;
}
