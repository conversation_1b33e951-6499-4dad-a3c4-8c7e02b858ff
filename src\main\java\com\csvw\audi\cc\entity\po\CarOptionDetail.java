package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 车系配置项细节
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarOptionDetail对象", description="车系配置项细节")
public class CarOptionDetail extends Model<CarOptionDetail> {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    private String optionDetailId;

    @ApiModelProperty(value = "配置项id")
    private String optionId;

    @ApiModelProperty(value = "配置项编码")
    private String optionCode;

    @ApiModelProperty(value = "配置项细节描述")
    private String optionDesc;

    @ApiModelProperty(value = "装备图片")
    private String imageUrl;

    @ApiModelProperty(value = "数据渠道")
    private String channel;

    @ApiModelProperty(value = "权重")
    private Integer weight;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
