package com.csvw.audi.cc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csvw.audi.cc.entity.dto.SibInterieurQueryDto;
import com.csvw.audi.cc.entity.po.CarModelLineSibInterieur;
import com.csvw.audi.cc.entity.vo.ModelLineSibInterieurVo;

import java.util.List;

/**
 * <p>
 * 配置线内饰面料关联 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-19
 */
public interface CarModelLineSibInterieurMapper extends BaseMapper<CarModelLineSibInterieur> {

    List<ModelLineSibInterieurVo> listModelLineSibInterieur(ModelLineSibInterieurVo param);

    List<ModelLineSibInterieurVo> queryModelLineSibInterieur(SibInterieurQueryDto param);
}
