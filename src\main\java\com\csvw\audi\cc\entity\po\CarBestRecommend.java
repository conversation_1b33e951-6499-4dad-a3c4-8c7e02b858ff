package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 畅销推荐车
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarBestRecommend对象", description="畅销推荐车")
public class CarBestRecommend extends Model<CarBestRecommend> {

    private static final long serialVersionUID=1L;

      @TableId(value = "best_recommend_id", type = IdType.ASSIGN_ID)
    private Long bestRecommendId;

    @ApiModelProperty(value = "配置线ID")
    private String modelLineId;

    @ApiModelProperty(value = "omd畅销车记录id")
    private Long bestSellRecommendModelId;

    @ApiModelProperty(value = "内饰面料ID")
    private String sibInterieurId;

    @ApiModelProperty(value = "库存")
    private Long stockNum;

    @ApiModelProperty(value = "下单数量")
    private Long orderNum;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.bestRecommendId;
    }

}
