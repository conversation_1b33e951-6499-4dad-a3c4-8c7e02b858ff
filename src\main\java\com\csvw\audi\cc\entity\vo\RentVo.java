package com.csvw.audi.cc.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RentVo {
    @ApiModelProperty(value = "融资租赁金额")
    private BigDecimal price;
    @ApiModelProperty(value = "先付")
    private BigDecimal payment;
    @ApiModelProperty(value = "先付比例")
    private BigDecimal paymentRatio;
    @ApiModelProperty(value = "尾款比例")
    private BigDecimal balanceRatio;
    @ApiModelProperty(value = "尾款金额")
    private BigDecimal balance;
    @ApiModelProperty(value = "活动利率")
    private String interestRate;
    @ApiModelProperty(value = "月租金")
    private BigDecimal monthPay;
}
