package com.csvw.audi.cc;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.GlobalConfig;
import com.baomidou.mybatisplus.generator.config.PackageConfig;
import com.baomidou.mybatisplus.generator.config.StrategyConfig;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import org.junit.Test;

public class CodeGenerate {
    @Test
    public void testCase(){
        String packageName = "com.csvw.audi.cc";
//        String[] tableNames = {"car_parameter","car_series","car_custom_series","car_model","car_model_line","car_model_line_parameter","car_option","car_model_line_option","car_package_item", "car_option_relate", "car_option_detail"};
        String[] tableNames = {"car_model_line_type", "car_model_line_type_option", "car_omd_vehicle_type"};
        generateByTables(packageName,tableNames);

    }

    private static void generateByTables(String packageName, String... tableNames) {
        //1. 全局配置
        GlobalConfig config = new GlobalConfig();
        config.setActiveRecord(true) // 是否支持AR模式
                .setAuthor("<EMAIL>") // 作者
                .setOutputDir("/Users/<USER>/Project/CodeGen") // 生成路径
                .setFileOverride(true)  // 文件覆盖
                .setIdType(IdType.ASSIGN_ID) // 主键策略
                .setServiceName("I%sService")  // 设置生成的service接口的名字的首字母是否为I
                // IEmployeeService
                .setSwagger2(true)
                .setBaseResultMap(true)//生成基本的resultMap
                .setBaseColumnList(true);//生成基本的SQL片段

        //2. 数据源配置
        DataSourceConfig dsConfig  = new DataSourceConfig();
        dsConfig.setDbType(DbType.MYSQL)  // 设置数据库类型
                .setDriverName("com.mysql.cj.jdbc.Driver")
//                .setUrl("***********************************************************************************")
//                .setUsername("audi_dev")
//                .setPassword("p7e_dN3W");
                .setUrl("*******************************************")
                .setUsername("root")
                .setPassword("1234qwer");


        //3. 策略配置globalConfiguration中
        StrategyConfig stConfig = new StrategyConfig();
        stConfig.setCapitalMode(true) //全局大写命名
                .setNaming(NamingStrategy.underline_to_camel) // 数据库表映射到实体的命名策略
//                .setTablePrefix("t_back_")
                .setInclude(tableNames)
                .setEntityLombokModel(true);  // 生成的表

        //4. 包名策略配置
        PackageConfig pkConfig = new PackageConfig();
        pkConfig.setParent(packageName)
                .setMapper("mapper")//dao
                .setService("service")//servcie
                .setServiceImpl("service.impl")
                .setController("controller")//controller
                .setEntity("entity.po")
                .setXml("mybatis");//mapper.xml

        //5. 整合配置
        AutoGenerator ag = new AutoGenerator();
        ag.setGlobalConfig(config)
                .setDataSource(dsConfig)
                .setStrategy(stConfig)
                .setPackageInfo(pkConfig);

        //6. 执行
        ag.execute();
    }


}
