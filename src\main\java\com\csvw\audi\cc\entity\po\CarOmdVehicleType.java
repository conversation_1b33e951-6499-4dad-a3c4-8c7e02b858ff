package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * omd同步ABC类配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarOmdVehicleType对象", description="omd同步ABC类配置")
public class CarOmdVehicleType extends Model<CarOmdVehicleType> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    private String accbTypeCode;

    @ApiModelProperty(value = "车型编码")
    private String modelCode;

    @ApiModelProperty(value = "年款")
    private String modelYear;

    @ApiModelProperty(value = "版本号")
    private String modelVersion;

    @ApiModelProperty(value = "外饰编码")
    private String colorCode;

    @ApiModelProperty(value = "内饰颜色编码")
    private String interiorCode;

    @ApiModelProperty(value = "轮毂")
    private String wheel;

    @ApiModelProperty(value = "配置线选装")
    private String prList;

    @ApiModelProperty(value = "分类，不能为空，A、B、C、A/B")
    private String classify;

    private Integer classifyVersion;

    @ApiModelProperty(value = "座位")
    private String seats;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
