package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.config.AppConfig;
import com.csvw.audi.cc.entity.dto.BestRecommendQueryParam;
import com.csvw.audi.cc.entity.dto.CarBestRecommendDto;
import com.csvw.audi.cc.entity.enumeration.StockTypeEnum;
import com.csvw.audi.cc.entity.po.CarBestRecommend;
import com.csvw.audi.cc.mapper.CarBestRecommendMapper;
import com.csvw.audi.cc.service.ICarBestRecommendService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 畅销推荐车 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-08
 */
@Service
public class CarBestRecommendServiceImpl extends ServiceImpl<CarBestRecommendMapper, CarBestRecommend> implements ICarBestRecommendService {

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private CarBestRecommendMapper bestRecommendMapper;

    @Override
    public List<CarBestRecommendDto> listBestRecommendByDealerCode(String dealerCode, String customSeriesId) {
        BestRecommendQueryParam queryParam = new BestRecommendQueryParam();
        queryParam.setCustomSeriesId(customSeriesId);
        if (dealerCode == null || dealerCode.equals(appConfig.getHqDealerCode())){
            // 查询总部库存推荐车
            queryParam.setType(StockTypeEnum.HQ.getValue());
        }else {
            // 查询经销商推荐车
            queryParam.setDealerCode(dealerCode);
            queryParam.setType(StockTypeEnum.DEALER.getValue());
        }
        return bestRecommendMapper.listBestRecommend(queryParam);
    }

    @Override
    public List<CarBestRecommendDto> listBestRecommendByDealerCodeFix(String dealerCode, String customSeriesId) {
        BestRecommendQueryParam queryParam = new BestRecommendQueryParam();
        queryParam.setCustomSeriesId(customSeriesId);
        if (dealerCode == null || dealerCode.equals(appConfig.getHqDealerCode())){
            // 查询总部库存推荐车
            queryParam.setType(StockTypeEnum.HQ.getValue());
            return bestRecommendMapper.listBestRecommend(queryParam);
        }else {
            // 查询经销商推荐车
            queryParam.setDealerCode(dealerCode);
            queryParam.setType(StockTypeEnum.DEALER.getValue());
            return bestRecommendMapper.listBestRecommendFix(queryParam);
        }

    }
}
