package com.csvw.audi.cc.component.kafkaService.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.csvw.audi.cc.component.kafkaService.SvcdKafkaService;
import com.csvw.audi.cc.entity.po.SvcdInvestor;
import com.csvw.audi.cc.mapper.SvcdInvestorMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service("svcdInvestorKafkaService")
public class SvcdInvestorKafkaServiceImpl implements SvcdKafkaService {

    @Autowired
    private SvcdInvestorMapper svcdInvestorMapper;

    @Override
    public void insertData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdInvestor investor = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdInvestor.class);
        investor.setCreatedAt(nowDate);
        investor.setUpdatedAt(nowDate);
        investor.insert();
    }

    @Override
    public void updateData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdInvestor investor = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdInvestor.class);

        QueryWrapper<SvcdInvestor> investorQusery = new QueryWrapper<>();
        investorQusery.eq("code",investor.getCode());
        List<SvcdInvestor> list = investor.selectList(investorQusery);
        if(list == null || list.size() == 0) {
            investor.setCreatedAt(nowDate);
            investor.setUpdatedAt(nowDate);
            investor.insert();
        } else {
            investor.setInvestorId(list.get(0).getInvestorId());
            investor.setUpdatedAt(nowDate);
            investor.updateById();
        }
    }
}
