package com.csvw.audi.cc.controller.svcd;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.dto.AdminOrgBankDto;
import com.csvw.audi.cc.entity.dto.DealerDto;
import com.csvw.audi.cc.entity.po.SvcdChannelOrganizationPolicy;
import com.csvw.audi.cc.entity.vo.AdminOrgBankVo;
import com.csvw.audi.cc.entity.vo.DealerVo;
import com.csvw.audi.cc.entity.vo.DealerVo2;
import com.csvw.audi.cc.service.IAdminOrgBankService;
import com.csvw.audi.cc.service.ISvcdChannelOrganizationPolicyService;
import com.csvw.audi.cc.service.ISvcdChannelOrganizationService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "代理商")
@RestController
@RequestMapping("/api/v1/dealerController")
@Slf4j
public class DealerController extends BaseController {

    @Autowired
    private ISvcdChannelOrganizationService svcdChannelOrganizationService;

    @Autowired
    private ISvcdChannelOrganizationPolicyService svcdChannelOrganizationPolicyService;

    @Autowired
    private IAdminOrgBankService adminOrgBankService;

    @ApiOperation("获取最近的代理商")
    @GetMapping("/getNearestDealer")
    public AjaxMessage<DealerVo> getNearestAgent(DealerDto dealerDto) {
        DealerVo nearestDealer = null;
        List<DealerVo> list = svcdChannelOrganizationService.getNearestAgent(dealerDto);
        nearestDealer = list.get(0);

        return new AjaxMessage<>("00", "成功", nearestDealer);
    }

    @ApiOperation("获取代理商列表-根据当前坐标按距离排序")
    @GetMapping("/getNearestDealerList")
    public AjaxMessage<List<DealerVo>> getNearestDealerList(DealerDto dealerDto) {
        List<DealerVo> list = svcdChannelOrganizationService.getNearestAgent(dealerDto);

        return new AjaxMessage<>("00", "成功", list);
    }

    @ApiOperation("获取代理商列表")
    @GetMapping("/getDealerList")
    public AjaxMessage<List<DealerVo>> getAgentList(DealerDto dealerDto) {
        List<DealerVo> list = svcdChannelOrganizationService.getAgentList(dealerDto);
        return new AjaxMessage<>("00", "成功", list);
    }

    @ApiOperation("爱车页-获取代理商列表")
    @GetMapping("/getDealerListByAiChe")
    public AjaxMessage<List<DealerVo>> getDealerListByAiChe(DealerDto dealerDto) {
        List<DealerVo> list = svcdChannelOrganizationService.getDealerListByAiChe(dealerDto);
        return new AjaxMessage<>("00", "成功", list);
    }

    @ApiOperation("同城活动-获取代理商列表")
    @PostMapping("/getDealerListByCityWide")
    public AjaxMessage<List<DealerVo>> getDealerListByCityWide(@RequestBody DealerDto dealerDto) {
        List<DealerVo> list = svcdChannelOrganizationService.getDealerListByCityWide(dealerDto);
        return new AjaxMessage<>("00", "成功", list);
    }

    @ApiOperation("预约试驾-获取代理商列表")
    @GetMapping("/getDealerListByTestDrive")
    public AjaxMessage<List<DealerVo>> getDealerListByTestDrive(DealerDto dealerDto) {
        List<DealerVo> list = svcdChannelOrganizationService.getDealerListByTestDrive(dealerDto);
        return new AjaxMessage<>("00", "成功", list);
    }

    @ApiOperation("获取代理商详情")
    @GetMapping("/getDealerDetails")
    public AjaxMessage<DealerVo> getAgentDetails(@RequestParam String dealerCode, Integer defaultHeadquarters) {
        DealerDto dealerDto = new DealerDto();
        dealerDto.setDealerCode(dealerCode);
        dealerDto.setDefaultHeadquarters(defaultHeadquarters);
        List<DealerVo> list = svcdChannelOrganizationService.getAgentList(dealerDto);
        return new AjaxMessage<>("00", "成功", list.size() == 0 ? null : list.get(0));
    }

    @ApiOperation("获取渠道商列表（代理商+服务商）")
    @GetMapping("/getOrgList")
    public AjaxMessage<List<DealerVo>> getOrgList(DealerDto dealerDto) {
        return new AjaxMessage<>("00", "成功", svcdChannelOrganizationService.getOrgList(dealerDto));
    }

    @ApiOperation("获取代理商/服务商列表-官网")
    @GetMapping("/getDealerListByOfficialWebsite")
    public AjaxMessage<List<DealerVo2>> getDealerListByOfficialWebsite(DealerDto dealerDto) {
        String searchEnum = dealerDto.getSearchEnum();
        if (StringUtils.isNotEmpty(searchEnum)) {
            String[] enums = searchEnum.split(",");
            List<Integer> exhibitionHallFormList = new ArrayList<>();
            if (ArrayUtils.isNotEmpty(enums)) {
                Arrays.stream(enums).collect(Collectors.toSet()).forEach(type -> {
                    switch (type) {
                        case "1":
                            dealerDto.setSearchType(2);
                            break;// 代理商-全部
                        case "1-2":
                            exhibitionHallFormList.add(3);
                            break;// 奥迪进取汇
                        case "1-3":
                            exhibitionHallFormList.add(2);
                            break;// 奥迪之城
                        case "1-4":
                            exhibitionHallFormList.add(1);
                            break;// 奥迪都市店
                        case "1-5":
                            exhibitionHallFormList.add(4);
                            break;// 用户中心
                        case "1-6":
                            exhibitionHallFormList.add(5);
                            break;// 轻量版都市店
                        case "2":
                            dealerDto.setSearchType(1);
                            break;// 服务商-全部
                        case "2-1":
                            dealerDto.setDealerAfterSaleType("1");
                            break;// 销售+售后绑定
                        case "2-2":
                            dealerDto.setDealerAfterSaleType("2");
                            break;// 独立授权服务商
                        case "2-4":
                            dealerDto.setIsNewEnergy(1);
                            break;// 新能源车售后
                        case "2-5":
                            dealerDto.setFawAudiBatteryMaintenanceCenter("1");
                            break;// 高压电池维修
                    }
                });
                dealerDto.setExhibitionHallFormList(exhibitionHallFormList);
            }
        }
        List<DealerVo> list = svcdChannelOrganizationService.getDealerListByOfficialWebsite(dealerDto);
//        log.info("List<DealerVo>:{}",JSONObject.toJSONString(list));
        List<DealerVo2> collect = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)){
            collect = list.stream().map(a -> {
                DealerVo2 vo2 = new DealerVo2();
                BeanUtils.copyProperties(a, vo2);
                vo2.setDealerType(a.getDealerType()+"");
                if (StrUtil.isNotBlank(a.getDealerTypeStr())){
                    vo2.setDealerType(a.getDealerTypeStr());
                }
                vo2.setPolicyList(svcdChannelOrganizationPolicyService.list(Wrappers.<SvcdChannelOrganizationPolicy>lambdaQuery()
                        .eq(SvcdChannelOrganizationPolicy::getDealerCode, a.getDealerCode())));
                return vo2;
            }).collect(Collectors.toList());
        }
        return new AjaxMessage<>("00", "成功", collect);
    }

    @ApiOperation("获取参数列表-官网")
    @GetMapping("/getDealerReqParamByOfficialWebsite")
    public AjaxMessage<JSONObject> getDealerRequestParamByOfficialWebsite(DealerDto dealerDto) {
        // 默认查询全部
        if (dealerDto.getSearchType() == null) {
            dealerDto.setSearchType(0);
        }
        return new AjaxMessage<>("00", "成功",
            svcdChannelOrganizationService.getDealerRequestParamByOfficialWebsite(dealerDto));
    }

    @ApiOperation("获取渠道商对应的金融机构")
    @GetMapping("/getOrgBankList")
    public AjaxMessage<List<AdminOrgBankVo>> getOrgBankList(@RequestParam String dealerCode) {
        AdminOrgBankDto dto = new AdminOrgBankDto();
        dto.setDealerCode(dealerCode);
        List<AdminOrgBankVo> list = adminOrgBankService.getOrgBankList(dto);
        return new AjaxMessage<>("00", "成功", list);
    }

    @ApiOperation("获取渠道商对应的金融机构")
    @GetMapping("/getOrgBankListForPage")
    public AjaxMessage<PageInfo<AdminOrgBankVo>> getOrgBankListForPage(@RequestParam String dealerCode,
        @RequestParam(name = "page", defaultValue = "0") int pageNum,
        @RequestParam(name = "size", defaultValue = "10") int pageSize) {
        Page<AdminOrgBankVo> page = new Page<>(pageNum, pageSize);
        AdminOrgBankDto dto = new AdminOrgBankDto();
        dto.setDealerCode(dealerCode);
        PageInfo<AdminOrgBankVo> pageInfo = adminOrgBankService.getOrgBankList(dto, page);
        return new AjaxMessage<>("00", "成功", pageInfo);
    }

}
