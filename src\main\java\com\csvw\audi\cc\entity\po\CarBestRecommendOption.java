package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 畅销推荐车配置项
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarBestRecommendOption对象", description="畅销推荐车配置项")
public class CarBestRecommendOption extends Model<CarBestRecommendOption> {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    private Long bestRecommendId;

    @ApiModelProperty(value = "配置ID")
    private String optionId;

    @ApiModelProperty(value = "配置编码")
    private String optionCode;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
