package com.csvw.audi.cc.controller.ccnew;


import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.entity.vo.ModelLineOptionTagParam;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.entity.vo.RecommendCarSphereVo;
import com.csvw.audi.cc.service.ICarModelLineService;
import com.csvw.audi.cc.service.impl.OmdCcCompareServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 车配标签 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@RestController
@RequestMapping("/api/v1/cc")
@Api(tags = "车配标签接口")
public class CarTagController extends BaseController {

    @Autowired
    private ICarModelLineService modelLineService;

    @GetMapping(value = {"/tag/optionList", "/public/tag/optionList"})
    @ApiOperation("标签查询配置项")
    public AjaxMessage<List<ModelLineOptionVo>> tagOptionList(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, @RequestParam String modelLineId, @RequestParam String tagCode) throws Exception {
        ModelLineOptionTagParam tagParam = new ModelLineOptionTagParam();
        tagParam.setModelLineId(modelLineId);
        tagParam.setChannel(channel);
        tagParam.setTagCode(tagCode);
        List<ModelLineOptionVo> res = modelLineService.modelLineTagOption(tagParam);
        return successMessage(res);
    }

}

