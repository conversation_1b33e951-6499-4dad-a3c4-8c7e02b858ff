package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.common.utils.ChannelDataUtils;
import com.csvw.audi.cc.entity.dto.OptionParamDto;
import com.csvw.audi.cc.entity.po.CarOption;
import com.csvw.audi.cc.entity.vo.CarOptionDetailVo;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.entity.vo.SeriesOptionVo;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.mapper.CarOptionMapper;
import com.csvw.audi.cc.service.ICarOmdPriceTypeService;
import com.csvw.audi.cc.service.ICarOptionDetailService;
import com.csvw.audi.cc.service.ICarOptionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 车系配置项 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Service
public class CarOptionServiceImpl extends ServiceImpl<CarOptionMapper, CarOption> implements ICarOptionService {

    @Autowired
    private CarOptionMapper optionMapper;

    @Autowired
    private ICarOmdPriceTypeService priceTypeService;

    @Autowired
    private ICarOptionDetailService optionDetailService;

    @Override
    public List<ModelLineOptionVo> optionQueryByOptionCodes(String channel, String customSeriesId, String modelLineId, List<String> optionCodes) throws NoSuchFieldException, IllegalAccessException {
        OptionParamDto param = new OptionParamDto();
        param.setCustomSeriesId(customSeriesId);
        param.setChannel(channel);
        param.setNotOptionTypes(Arrays.asList("packet-item"));
        param.setOptionCodes(optionCodes);
        List<ModelLineOptionVo> optionVos = optionMapper.carOptionQuery(param);
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            optionVos = ChannelDataUtils.channelData(optionVos, ModelLineOptionVo.class, channel, "optionId", false);
        }
        optionVos = optionVos.stream().map(i->{
            if (i.getStatus() != null && i.getStatus().intValue() != 1) {
                try {
                    i.setPrice(priceTypeService.optionPrice(modelLineId, i.getOptionCode(), i.getCategory()));
                } catch (ServiceException e) {
                    i.setPrice(null);
                }
            }
            CarOptionDetailVo carOptionDetailVo = new CarOptionDetailVo();
            carOptionDetailVo.setOptionId(i.getOptionId());
            carOptionDetailVo.setDelFlag(0);
            carOptionDetailVo.setChannel(channel);
            try {
                i.setOptionDetailList(optionDetailService.listOptionDetailVo(carOptionDetailVo));
            } catch (Exception e) {
                log.error("配置项详情查询异常", e);
            }
            return i;
        }).collect(Collectors.toList());
        return optionVos;
    }

    @Override
    public List<CarOption> listPacketSpecialItem(Set<String> optionIds, String modelLineId) {
        return optionMapper.listPacketSpecialItem(optionIds, modelLineId);
    }

    @Override
    public List<SeriesOptionVo> seriesOptions(String channel, String customSeriesId) {
        return optionMapper.seriesOptions(customSeriesId);
    }

}
