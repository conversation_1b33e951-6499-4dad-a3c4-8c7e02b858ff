package com.csvw.audi.cc.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SnapshotUpdateDto {
    @ApiModelProperty(value = "omd清单id")
    private Long omdVehicleTypeId;

    @ApiModelProperty(value = "车辆清单类型")
    private String classify;

    private Integer classifyVersion;

    @ApiModelProperty(value = "预计交付周期")
    private String estimateDelivery;

    @ApiModelProperty(value = "定金类型，1：排产，2：库存")
    private String depositType;
}
