spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      filters: stat,config
      initial-size: '8'
      max-active: '10'
      min-idle: '5'
      query-timeout: '6000'
      remove-abandoned-timeout: '1800'
      transaction-query-timeout: '6000'
      type: com.alibaba.druid.pool.DruidDataSource
      url: *********************************************************************************************************************
      username: root
      password: 123456
audi-car-config:
  accb:
#    url: https://accb-api.audibrand.cn
#    host: accb-api.audibrand.cn
#    secret: bc5adda0388a496d91a88fd6fbb4511e
#    key: 16d9980fa26345e88c8b371303fdda72
    host: pre-accb-api.audibrand.cn
    url: https://pre-accb-api.audibrand.cn
    key: e0f97ac39ba64668838ccc8921d68550
    secret: f50123af146c40049c83802d8f90be8b
    lang: en
    product: CHINA_SAIC
  infra:
    appId: Vu3rx4iuPqZD
    appSecret: dnQAVhjD5qbHkI1Yn9LoWggitDc1pUEd
    url: http://*************:60066/audi_noprod/infra/test

api:
  swagger:
    enabled: true
    basePackage: com.csvw.audi.cc.controller
    contact: <EMAIL>
    description: 车辆配置器服务
    title: audi-car-config
    version: '2.0'
logging:
  level:
    com.csvw.audi: debug
