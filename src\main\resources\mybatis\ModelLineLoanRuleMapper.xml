<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.ModelLineLoanRuleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.ModelLineLoanRule">
        <id column="id" property="id" />
        <result column="model_line_id" property="modelLineId" />
        <result column="loan_rule_id" property="loanRuleId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, model_line_id, loan_rule_id, create_time, update_time
    </sql>

    <select id="selectModelLineIds" resultType="string">
        SELECT DISTINCT(lr.model_line_id) FROM `model_line_loan_rule` lr LEFT JOIN `car_model_line` l on l.`model_line_id` = lr.`model_line_id`
        WHERE l.`channel` = 'master'
        <if test="customSeriesId != null and customSeriesId != ''">
            and l.`custom_series_id` = #{customSeriesId}
        </if>
    </select>

</mapper>
