<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarModelLineOptionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarModelLineOption">
        <id column="id" property="id" />
        <result column="option_id" property="optionId" />
        <result column="option_weight" property="optionWeight" />
        <result column="model_line_id" property="modelLineId" />
        <result column="default_config" property="defaultConfig" />
        <result column="status" property="status" />
        <result column="condition" property="condition" />
        <result column="image_url" property="imageUrl" />
        <result column="image_url_detail" property="imageUrlDetail" />
        <result column="image_url_list" property="imageUrlList" />
        <result column="channel" property="channel" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <resultMap id="ModelLineOptionVoMap" type="com.csvw.audi.cc.entity.vo.ModelLineOptionVo">
        <result column="option_id" property="optionId" />
        <result column="option_name" property="optionName" />
        <result column="option_code" property="optionCode" />
        <result column="category" property="category" />
        <result column="option_type" property="optionType" />
        <result column="option_type_name" property="optionTypeName" />
        <result column="image_url" property="imageUrl" />
        <result column="image_url_detail" property="imageUrlDetail" />
        <result column="image_url_list" property="imageUrlList" />
        <result column="status" property="status" />
        <result column="condition" property="condition" />
        <result column="channel" property="channel" />
        <result column="model_line_id" property="modelLineId" />
        <collection property="optionRelates" column="{option_id=option_id, model_line_id=model_line_id}" select="listOptionRel"></collection>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, option_id, option_weight, model_line_id, default_config, status, `condition`, image_url, image_url_detail, image_url_list, channel, create_time, update_time, del_flag
    </sql>


    <select id="listModelLineOptionWithoutRel" resultType="com.csvw.audi.cc.entity.vo.ModelLineOptionVo"
            parameterType="com.csvw.audi.cc.entity.vo.ModelLineOptionVo">
        select p.option_id, ifnull(mlo.option_name, p.option_name) option_name, p.option_code, p.category, p.option_type, t.type_name as option_type_name, ifnull(mlo.image_url, p.image_url) image_url, ifnull(mlo.image_url_detail, p.image_url_detail) image_url_detail, ifnull(mlo.image_url_list, p.image_url_list) image_url_list, p.description,
            p.remark, p.channel, mlo.`status`, mlo.`condition`, mlo.model_line_id, ifnull(mlo.default_config, p.default_config) default_config
        from car_option p
        left join car_type t on t.type = p.option_type and t.custom_series_id = p.custom_series_id
        left join car_model_line_option mlo on mlo.option_id = p.option_id
        where p.option_type != 'packet-item'
        <if test="channel == null or channel == '' or channel == 'master'">
            and (mlo.`channel` = 'master' and p.`channel` = 'master')
        </if>
        <if test="channel != null and channel != '' and channel != 'master'">
            and ((mlo.`channel` = 'master' and p.`channel` = 'master') or (mlo.`channel` = #{channel} and p.`channel` = #{channel})or (mlo.`channel` = 'master'  and p.`channel` = #{channel}))
        </if>
        <if test="modelLineId != null and modelLineId != ''">
            and mlo.model_line_id = #{modelLineId}
        </if>
        <if test="category != null and category != ''">
            and p.category = #{category}
        </if>
        <if test="status != null">
            and mlo.status = #{status}
        </if>
        <if test="delFlag != null">
            and p.del_flag = #{delFlag}
        </if>
        <if test="hasCode != null and hasCode == 1">
            and p.option_code is not null and p.option_code != ''
        </if>
        order by mlo.option_weight;
    </select>

    <select id="listModelLineOptionByTag" resultType="com.csvw.audi.cc.entity.vo.ModelLineOptionVo"
            parameterType="com.csvw.audi.cc.entity.vo.ModelLineOptionTagParam">
        select p.option_id, ifnull(mlo.option_name, p.option_name) option_name, p.option_code, p.category, p.option_type, t.type_name as option_type_name, ifnull(mlo.image_url, p.image_url) image_url, ifnull(mlo.image_url_detail, p.image_url_detail) image_url_detail, ifnull(mlo.image_url_list, p.image_url_list) image_url_list, p.description,
        p.remark, p.channel, mlo.`status`, mlo.`condition`, mlo.model_line_id, ifnull(mlo.default_config, p.default_config) default_config
        from car_tag_relate ctr
        left join car_tag ct on ctr.tag_code = ct.tag_code
        left join car_option p on p.option_id = ctr.option_id
        left join car_type t on t.type = p.option_type and t.custom_series_id = p.custom_series_id
        left join car_model_line_option mlo on mlo.option_id = p.option_id
        where p.option_type != 'packet-item' and ct.del_flag = 0 and ctr.del_flag = 0
        and ctr.tag_type = '2'
        <if test="channel == null or channel == '' or channel == 'master'">
            and (mlo.`channel` = 'master' and p.`channel` = 'master')
        </if>
        <if test="channel != null and channel != '' and channel != 'master'">
            and ((mlo.`channel` = 'master' and p.`channel` = 'master') or (mlo.`channel` = #{channel} and p.`channel` = #{channel})or (mlo.`channel` = 'master'  and p.`channel` = #{channel}))
        </if>
        <if test="modelLineId != null and modelLineId != ''">
            and mlo.model_line_id = #{modelLineId} and ctr.model_line_id = #{modelLineId}
        </if>
        <if test="category != null and category != ''">
            and p.category = #{category}
        </if>
        <if test="status != null">
            and mlo.status = #{status}
        </if>
        <if test="delFlag != null">
            and p.del_flag = #{delFlag}
        </if>
        <if test="hasCode != null and hasCode == 1">
            and p.option_code is not null and p.option_code != ''
        </if>
        <if test="tagCode != null and tagCode != null">
            and ct.tag_code = #{tagCode}
        </if>
        order by ctr.weight;
    </select>

    <select id="listOptionRel" resultType="com.csvw.audi.cc.entity.dto.OptionRelDto">
        select option_id, option_relate_id, option_relate_code, option_relate_category, relate_type from car_option_relate where model_line_id = #{model_line_id} and option_id = #{option_id} and relate_type in ("depend", "conflict")
    </select>
    <select id="listModelLineOption" resultMap="ModelLineOptionVoMap"
            parameterType="com.csvw.audi.cc.entity.vo.ModelLineOptionVo">
        select p.option_id, ifnull(mlo.option_name, p.option_name) option_name, p.option_code, p.category, p.option_type, t.type_name as option_type_name, ifnull(mlo.image_url, p.image_url) image_url, ifnull(mlo.image_url_detail, p.image_url_detail) image_url_detail, ifnull(mlo.image_url_list, p.image_url_list) image_url_list,
            p.description, p.remark, p.channel, mlo.`status`, mlo.`condition`, mlo.model_line_id, mlo.channel as mlo_channel, ifnull(mlo.default_config, p.default_config) default_config
        from car_option p
        left join car_type t on t.type = p.option_type and t.custom_series_id = p.custom_series_id
        left join car_model_line_option mlo on mlo.option_id = p.option_id
        where p.option_type != 'packet-item'
        <if test="optionId != null and optionId != ''">
            and p.option_id = #{optionId}
        </if>
        <if test="channel == null or channel == '' or channel == 'master'">
            and (mlo.`channel` = 'master' and p.`channel` = 'master')
        </if>
        <if test="channel != null and channel != '' and channel != 'master'">
            and ((mlo.`channel` = 'master' and p.`channel` = 'master') or (mlo.`channel` = #{channel} and p.`channel` = #{channel})or (mlo.`channel` = 'master'  and p.`channel` = #{channel}))
        </if>
        <if test="modelLineId != null and modelLineId != ''">
            and mlo.model_line_id = #{modelLineId}
        </if>
        <if test="category != null and category != ''">
            and p.category = #{category}
        </if>
        <if test="status != null">
            and mlo.status = #{status}
        </if>
        <if test="delFlag != null">
            and p.del_flag = #{delFlag}
        </if>
        order by mlo.option_weight;
    </select>

    <select id="listModelLinePersonalOption" resultType="com.csvw.audi.cc.entity.vo.ModelLineOptionVo">
        select p.option_id, ifnull(mlo.option_name, p.option_name) option_name, p.option_code, p.category, p.option_type, t.type_name as option_type_name, ifnull(mlo.image_url, p.image_url) image_url, ifnull(mlo.image_url_detail, p.image_url_detail) image_url_detail, ifnull(mlo.image_url_list, p.image_url_list) image_url_list, p.description,
            p.remark, p.channel, mlo.`status`, mlo.`condition`, ifnull(mlo.default_config, p.default_config) default_config
        from car_option p
        left join car_type t on t.type = p.option_type and t.custom_series_id = p.custom_series_id
        left join car_model_line_option mlo on mlo.option_id = p.option_id
        <where>
            <if test="type != null and type == 'all'">
                p.option_type != 'packet-item'
            </if>
            <if test="type != null and type == 'personal'">
                and p.option_type in ('personal', 'packet')
            </if>
            <if test="type != null and type == 'personalWithEquity'">
                and p.option_type in ('personal', 'packet', 'packet-equity')
            </if>
            <if test="channel == null or channel == '' or channel == 'master'">
                and (mlo.`channel` = 'master' and p.`channel` = 'master')
            </if>
            <if test="channel != null and channel != '' and channel != 'master'">
                and ((mlo.`channel` = 'master' and p.`channel` = 'master') or (mlo.`channel` = #{channel} and p.`channel` = #{channel})or (mlo.`channel` = 'master'  and p.`channel` = #{channel}))
            </if>
            <if test="delFlag != null">
                and p.del_flag = #{delFlag}
            </if>
            <if test="modelLineId != null and modelLineId != ''">
                and mlo.model_line_id = #{modelLineId}
            </if>
            <if test="notInCategory != null">
                and (
                        p.category is null or
                        p.category not in
                        <foreach collection="notInCategory" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    )
            </if>
        </where>
        order by mlo.option_weight;
    </select>

    <select id="listModelLinePacketItem" resultType="com.csvw.audi.cc.entity.vo.ModelLineOptionVo" parameterType="com.csvw.audi.cc.entity.dto.OptionParamDto">
        select p.option_id, ifnull(mlo.option_name, p.option_name) option_name, p.option_code, p.category, p.option_type, t.type_name as option_type_name, ifnull(mlo.image_url, p.image_url) image_url, ifnull(mlo.image_url_detail, p.image_url_detail) image_url_detail, ifnull(mlo.image_url_list, p.image_url_list) image_url_list, p.description,
            p.remark, p.channel, mlo.`status`, mlo.`condition`, pi.package_id
        from car_package_item pi
        left join car_option p on pi.package_item_id = p.option_id
        left join car_type t on t.type = p.option_type and t.custom_series_id = p.custom_series_id
        left join car_model_line_option mlo on mlo.option_id = p.option_id
        <where>
            <if test="modelLineId != null and modelLineId != ''">
                mlo.model_line_id = #{modelLineId}
            </if>
            <if test="channel == null or channel == '' or channel == 'master'">
                and (mlo.`channel` = 'master' and p.`channel` = 'master')
            </if>
            <if test="channel != null and channel != '' and channel != 'master'">
                and ((mlo.`channel` = 'master' and p.`channel` = 'master') or (mlo.`channel` = #{channel} and p.`channel` = #{channel})or (mlo.`channel` = 'master'  and p.`channel` = #{channel}))
            </if>
            <if test="delFlag != null">
                and p.del_flag = #{delFlag}
            </if>
        </where>
        order by mlo.option_weight;
    </select>

    <select id="listOptionPacketItem" resultType="com.csvw.audi.cc.entity.vo.ModelLineOptionVo" parameterType="com.csvw.audi.cc.entity.dto.OptionParamDto">
        select p.option_id, ifnull(mlo.option_name, p.option_name) option_name, p.option_code, p.category, p.option_type, t.type_name as option_type_name, ifnull(mlo.image_url, p.image_url) image_url, ifnull(mlo.image_url_detail, p.image_url_detail) image_url_detail, ifnull(mlo.image_url_list, p.image_url_list) image_url_list, p.description,
            p.remark, p.channel, mlo.`status`, mlo.`condition`, pi.package_id
        from car_package_item pi
        left join car_option p on pi.package_item_id = p.option_id
        left join car_type t on t.type = p.option_type and t.custom_series_id = p.custom_series_id
        left join car_model_line_option mlo on mlo.option_id = p.option_id
        <where>
            <if test="optionId != null and optionId != ''">
                pi.package_id = #{optionId}
            </if>
            <if test="modelLineId != null and modelLineId != ''">
                and mlo.model_line_id = #{modelLineId}
            </if>
            <if test="category != null and category != ''">
                and p.category = #{category}
            </if>
            <if test="channel == null or channel == '' or channel == 'master'">
                and (mlo.`channel` = 'master' and p.`channel` = 'master')
            </if>
            <if test="channel != null and channel != '' and channel != 'master'">
                and ((mlo.`channel` = 'master' and p.`channel` = 'master') or (mlo.`channel` = #{channel} and p.`channel` = #{channel})or (mlo.`channel` = 'master'  and p.`channel` = #{channel}))
            </if>
            <if test="optionIds != null">
                and pi.package_id in
                <foreach collection="optionIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="categories != null">
                and p.category in
                <foreach collection="categories" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="delFlag != null">
                and p.del_flag = #{delFlag}
            </if>
        </where>
        order by mlo.option_weight;
    </select>

    <select id="listOptionPacketItemByDrm" resultType="com.csvw.audi.cc.entity.vo.ModelLineOptionVo" parameterType="com.csvw.audi.cc.entity.dto.OptionPriceQuery">
        select p.option_id, ifnull(mlo.option_name, p.option_name) option_name, p.option_code, p.category, p.option_type, t.type_name as option_type_name, ifnull(mlo.image_url, p.image_url) image_url, ifnull(mlo.image_url_detail, p.image_url_detail) image_url_detail, ifnull(mlo.image_url_list, p.image_url_list) image_url_list, p.description,
            p.remark, p.channel, mlo.`status`, mlo.`condition`, pi.package_id
        from car_option pio
        left join car_package_item pi on pi.package_id = pio.option_id
        left join car_option p on pi.package_item_id = p.option_id and pio.channel = p.channel
        left join car_type t on t.type = p.option_type and t.custom_series_id = p.custom_series_id
        left join car_model_line_option mlo on mlo.option_id = p.option_id
        left join car_model_line ml on ml.model_line_id = mlo.model_line_id
        left join car_model m on ml.model_id = m.model_id and ml.channel = m.channel
        where
        pio.option_code = #{code} and ml.accb_type_code=#{accbTypeCode} and p.channel = 'master'
        and ml.channel = 'master' and ml.version=#{version} and m.model_year=#{modelYear} and p.del_flag=0
        order by mlo.option_weight;
    </select>

    <select id="findPaketItemFromCodes" resultType="com.csvw.audi.cc.entity.vo.ModelLineOptionVo">
        select p.option_id, ifnull(mlo.option_name, p.option_name) option_name, p.option_code, p.category, p.option_type, ifnull(mlo.image_url, p.image_url) image_url, ifnull(mlo.image_url_detail, p.image_url_detail) image_url_detail, ifnull(mlo.image_url_list, p.image_url_list) image_url_list, p.description,
        p.remark, p.channel, mlo.`status`, mlo.`condition`, pi.package_id, mlo.`model_line_id`
        from car_package_item pi
        left join car_option p on pi.package_item_id = p.option_id and p.`channel` = 'master'
        left join car_option op on op.`option_id`  = pi.`package_id` and op.`channel` = 'master'
        left join car_model_line_option mlo on mlo.option_id = p.option_id
        where mlo.`status` != 0 and op.`custom_series_id` = #{customSeriesId}
        and `model_line_id` = #{modelLineId}
        and op.`category` = 'PACKET' and p.option_code = #{itemCode}
        and op.option_code in
        <foreach collection="optionCodes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>

    </select>

    <select id="modelLineOptionQueryStrict" resultType="com.csvw.audi.cc.entity.vo.ModelLineOptionVo" parameterType="com.csvw.audi.cc.entity.dto.OptionParamDto">
        select p.option_id, ifnull(mlo.option_name, p.option_name) option_name, p.option_code, p.category, p.option_type, t.type_name as option_type_name, ifnull(mlo.image_url, p.image_url) image_url, ifnull(mlo.image_url_detail, p.image_url_detail) image_url_detail, ifnull(mlo.image_url_list, p.image_url_list) image_url_list,
        p.description, p.remark, p.channel,
        mlo.`status`, mlo.`condition`, mlo.model_line_id, ifnull(mlo.default_config, p.default_config) default_config
        from car_option p
        left join car_type t on t.type = p.option_type and t.custom_series_id = p.custom_series_id
        left join car_model_line_option mlo on mlo.option_id = p.option_id
        <where>
            <if test="optionId != null and optionId != ''">
                p.option_id = #{optionId}
            </if>
            <if test="optionIds != null">
                and p.option_id in
                <foreach collection="optionIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="optionCode != null">
                and p.option_code = #{optionCode}
            </if>
            <if test="category != null">
                and p.category = #{category}
            </if>
            <if test="optionCodes != null">
                and p.option_code in
                <foreach collection="optionCodes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="notOptionTypes != null">
                and p.option_type not in
                <foreach collection="notOptionTypes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="customSeriesId != null and customSeriesId != ''">
                and p.custom_series_id = #{customSeriesId}
            </if>
            <if test="modelLineId != null and modelLineId != ''">
                and mlo.model_line_id = #{modelLineId}
            </if>
            <if test="channel == null or channel == '' or channel == 'master'">
                and ((mlo.`channel` = 'master' and p.`channel` = 'master') or (mlo.`channel` is null and p.`channel` = 'master'))
            </if>
            <if test="channel != null and channel != '' and channel != 'master'">
                and ( mlo.`channel` is null or (mlo.`channel` = 'master' and p.`channel` = 'master') or (mlo.`channel` = #{channel} and p.`channel` = #{channel})or (mlo.`channel` = 'master'  and p.`channel` = #{channel}))
            </if>
            <if test="delFlag != null">
                and p.del_flag = #{delFlag}
            </if>
            <if test="status != null">
                and mlo.status = #{status}
            </if>
        </where>
        order by mlo.option_weight;
    </select>

    <select id="modelLineOptionQuery" resultType="com.csvw.audi.cc.entity.vo.ModelLineOptionVo" parameterType="com.csvw.audi.cc.entity.dto.OptionParamDto">
        select p.option_id, ifnull(mlo.option_name, p.option_name) option_name, p.option_code, p.category, p.option_type, t.type_name as option_type_name, ifnull(mlo.image_url, p.image_url) image_url, ifnull(mlo.image_url_detail, p.image_url_detail) image_url_detail, ifnull(mlo.image_url_list, p.image_url_list) image_url_list,
        p.description, p.remark, p.channel,
        mlo.`status`, mlo.`condition`, mlo.model_line_id, ifnull(mlo.default_config, p.default_config) default_config, mlo.equipment_rights
        from car_option p
        left join car_type t on t.type = p.option_type and t.custom_series_id = p.custom_series_id
        left join car_model_line_option mlo on mlo.option_id = p.option_id
        <where>
            <if test="optionId != null and optionId != ''">
                 p.option_id = #{optionId}
            </if>
            <if test="optionIds != null and optionIds.size() > 0">
                and p.option_id in
                <foreach collection="optionIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="optionCodes != null">
                and p.option_code in
                <foreach collection="optionCodes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="notOptionTypes != null">
                and p.option_type not in
                <foreach collection="notOptionTypes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="customSeriesId != null and customSeriesId != ''">
                and p.custom_series_id = #{customSeriesId}
            </if>
            <if test="modelLineId != null and modelLineId != ''">
                and (mlo.model_line_id = #{modelLineId} or mlo.model_line_id is null)
            </if>
            <if test="channel == null or channel == '' or channel == 'master'">
                and ((mlo.`channel` = 'master' and p.`channel` = 'master') or (mlo.`channel` is null and p.`channel` = 'master'))
            </if>
            <if test="channel != null and channel != '' and channel != 'master'">
                and ( mlo.`channel` is null or (mlo.`channel` = 'master' and p.`channel` = 'master') or (mlo.`channel` = #{channel} and p.`channel` = #{channel})or (mlo.`channel` = 'master'  and p.`channel` = #{channel}))
            </if>
            <if test="delFlag != null">
                and p.del_flag = #{delFlag}
            </if>
            <if test="status != null">
                and mlo.status = #{status}
            </if>
        </where>
        order by mlo.option_weight;
    </select>


    <select id="optionQuery" resultType="com.csvw.audi.cc.entity.vo.ModelLineOptionVo" parameterType="com.csvw.audi.cc.entity.dto.OptionParamDto">
        select p.option_id, ifnull(mlo.option_name, p.option_name) option_name, p.option_code, p.category, p.option_type, t.type_name as option_type_name, ifnull(mlo.image_url, p.image_url) image_url, ifnull(mlo.image_url_detail, p.image_url_detail) image_url_detail, ifnull(mlo.image_url_list, p.image_url_list) image_url_list,
        p.description, p.remark, p.channel,
        mlo.`status`, mlo.`condition`, mlo.model_line_id, ifnull(mlo.default_config, p.default_config) default_config
        from car_option p
        left join car_type t on t.type = p.option_type and t.custom_series_id = p.custom_series_id
        left join car_model_line_option mlo on mlo.option_id = p.option_id
        <where>
            <if test="optionId != null and optionId != ''">
                p.option_id = #{optionId}
            </if>
            <if test="customSeriesId != null and customSeriesId != ''">
                and p.custom_series_id = #{customSeriesId}
            </if>
            <if test="optionIds != null">
                and p.option_id in
                <foreach collection="optionIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="optionCodes != null">
                and p.option_code in
                <foreach collection="optionCodes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="notOptionTypes != null">
                and p.option_type not in
                <foreach collection="notOptionTypes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="modelLineId != null and modelLineId != ''  ">
                and (mlo.model_line_id = #{modelLineId} or mlo.model_line_id is null)
            </if>
            <if test="channel == null or channel == '' or channel == 'master'">
                and ((mlo.`channel` = 'master' and p.`channel` = 'master') or (mlo.`channel` is null and p.`channel` = 'master'))
            </if>
            <if test="channel != null and channel != '' and channel != 'master'">
                and ( mlo.`channel` is null or (mlo.`channel` = 'master' and p.`channel` = 'master') or (mlo.`channel` = #{channel} and p.`channel` = #{channel})or (mlo.`channel` = 'master'  and p.`channel` = #{channel}))
            </if>
            <if test="delFlag != null">
                and p.del_flag = #{delFlag}
            </if>
        </where>
        order by mlo.option_weight;
    </select>

    <select id="listModelLineOptionCompare" resultType="com.csvw.audi.cc.entity.vo.ModelLineOptionCompareVo"
            parameterType="com.csvw.audi.cc.entity.vo.ModelLineOptionVo">
        select p.option_id, ifnull(mlo.option_name, p.option_name) option_name, p.option_code, p.category, p.option_type, t.type_name as option_type_name, ifnull(mlo.image_url, p.image_url) image_url, ifnull(mlo.image_url_detail, p.image_url_detail) image_url_detail, ifnull(mlo.image_url_list, p.image_url_list) image_url_list, p.description,
        p.remark, p.channel, mlo.`status`, mlo.`condition`, mlo.model_line_id, ifnull(mlo.default_config, p.default_config) default_config
        from car_option p
        left join car_type t on t.type = p.option_type and t.custom_series_id = p.custom_series_id
        left join car_model_line_option mlo on mlo.option_id = p.option_id
        where p.option_type != 'packet-item'
        <if test="channel == null or channel == '' or channel == 'master'">
            and (mlo.`channel` = 'master' and p.`channel` = 'master')
        </if>
        <if test="channel != null and channel != '' and channel != 'master'">
            and ((mlo.`channel` = 'master' and p.`channel` = 'master') or (mlo.`channel` = #{channel} and p.`channel` = #{channel})or (mlo.`channel` = 'master'  and p.`channel` = #{channel}))
        </if>
        <if test="modelLineId != null and modelLineId != ''">
            and mlo.model_line_id = #{modelLineId}
        </if>
        <if test="category != null and category != ''">
            and p.category = #{category}
        </if>
        <if test="status != null">
            and mlo.status = #{status}
        </if>
        <if test="delFlag != null">
            and p.del_flag = #{delFlag}
        </if>
        order by p.option_weight;
    </select>

    <select id="listModelLinePacketItemByCode" resultType="com.csvw.audi.cc.entity.vo.ModelLineOptionVo">
        SELECT item.option_id, ifnull(mloitem.option_name, item.option_name) option_name, item.option_code, item.category, item.option_type, ifnull(mloitem.image_url, item.image_url) image_url, ifnull(mloitem.image_url_detail, item.image_url_detail) image_url_detail, ifnull(mloitem.image_url_list, item.image_url_list) image_url_list,
        item.description, item.remark, item.channel,
        mloitem.`status`, mloitem.`condition`, mloitem.model_line_id, ifnull(mloitem.default_config, item.default_config) default_config
        FROM `car_option` packet LEFT JOIN `car_package_item` packet_item on packet.`option_id`  = packet_item.`package_id`
        LEFT JOIN `car_option` item on packet_item.`package_item_id` = item.`option_id`
        LEFT JOIN `car_model_line_option` mloitem on item.`option_id` = mloitem.`option_id`
        WHERE packet.`category` = 'PACKET' and packet.`channel` = 'master' and item.`channel` = 'master'
        and item.option_code = #{optionCode}
        and packet.`option_id` in
        <foreach collection="optionIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>
