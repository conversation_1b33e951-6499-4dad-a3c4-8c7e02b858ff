package com.csvw.audi.cc.entity.vo;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Data
public class ProdSkuUserRight {

    private static final long serialVersionUID=1L;

    private Long id;

    private Long prodSkuId;

    /**
     * 车型编号是带TYPE:的前缀的
     */
    private String carModelId;

    private String rights;

    private Integer idx;

    /**
     * 1. 用户权益 2限量号
     */
    private Integer type;

    private String title;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private Boolean deleted;
    protected Serializable pkVal() {
        return this.id;
    }

}
