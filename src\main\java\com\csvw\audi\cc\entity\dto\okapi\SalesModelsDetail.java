package com.csvw.audi.cc.entity.dto.okapi;

import lombok.Data;

import java.util.List;

@Data
public class SalesModelsDetail {
    private String brandCode;
    private String subBrandCode;
    private String seriesCode;
    private String modelUnicode;
    private String modelUnicodeShort;
    private String baseModelCode;
    private String internalModelNameZh;
    private String internalModelNameEn;
    private String externalModelNameZh;
    private String externalModelYear;
    private String externalModelYearDesc;
    private String externalSeriesName;
    private String externalFullModelName;
    private String sopYearWeek;
    private List<DetailParameterDto> children;
    private String modelYear;
    private String modelVersion;
    private String prList;
    private String msrp;
    @Data
    public static class DetailParameterDto{
        private String prodCode;
        private String prodValue;
        private String prodNameZh;
        private String prodNameEn;
    }
}
