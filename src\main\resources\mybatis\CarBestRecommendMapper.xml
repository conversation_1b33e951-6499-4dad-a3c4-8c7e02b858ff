<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarBestRecommendMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarBestRecommend">
        <id column="best_recommend_id" property="bestRecommendId" />
        <result column="model_line_id" property="modelLineId" />
        <result column="best_sell_recommend_model_id" property="bestSellRecommendModelId" />
        <result column="sib_interieur_id" property="sibInterieurId" />
        <result column="stock_num" property="stockNum" />
        <result column="order_num" property="orderNum" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        best_recommend_id, model_line_id, best_sell_recommend_model_id, sib_interieur_id, stock_num, order_num, create_time, update_time, del_flag
    </sql>
    
    <select id="listBestRecommend" parameterType="com.csvw.audi.cc.entity.dto.BestRecommendQueryParam"
            resultType="com.csvw.audi.cc.entity.dto.CarBestRecommendDto">
        SELECT r.*, rs.recommend_model_id FROM `car_model_line` m LEFT JOIN  `car_best_recommend` r on m.`model_line_id` = r.`model_line_id`
        LEFT JOIN `car_best_recommend_stock` rs on r.`best_recommend_id` = rs.`best_recommend_id`
        WHERE rs.`del_flag` = 0 and m.`channel` = 'master' and m.del_flag = 0
        <if test = "type != null">
            and rs.`type` = #{type}
        </if>
        <if test = "dealerCode != null and dealerCode != ''">
            and rs.`dealer_net_code` = #{dealerCode}
        </if>
        <if test = "customSeriesId != null and customSeriesId != ''">
            and m.`custom_series_id` = #{customSeriesId}
        </if>
    </select>

    <select id="listBestRecommendFix" parameterType="com.csvw.audi.cc.entity.dto.BestRecommendQueryParam"
            resultType="com.csvw.audi.cc.entity.dto.CarBestRecommendDto">
        SELECT r.*, rs.recommend_model_id FROM `car_model_line` m LEFT JOIN  `car_best_recommend` r on m.`model_line_id` = r.`model_line_id`
        LEFT JOIN `car_best_recommend_stock` rs on r.`best_recommend_id` = rs.`best_recommend_id`
        WHERE rs.`del_flag` = 0 and m.`channel` = 'master' and m.del_flag = 0 and ((rs.`type` = #{type} and rs.`dealer_net_code` = #{dealerCode}) or (rs.`type` = 'hq'))
        <if test = "customSeriesId != null and customSeriesId != ''">
            and m.`custom_series_id` = #{customSeriesId}
        </if>
        group by r.best_recommend_id
    </select>

</mapper>
