package com.csvw.audi.cc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Data
@RefreshScope
@Component
@ConfigurationProperties(prefix = "cop-order.datasource")
public class CopOrderDatasourceConfig {

    private String url;
    private String password;
    private String username;
    private Integer maxActive;
    private Integer initialSize;
    private Integer minIdle;

}
