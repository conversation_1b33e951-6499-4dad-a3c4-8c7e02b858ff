package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.dto.CarCustomDto;
import com.csvw.audi.cc.entity.dto.CustomCheckDto;
import com.csvw.audi.cc.entity.dto.CustomSeriesDto;
import com.csvw.audi.cc.entity.dto.vwcc.CarCustomDetail;
import com.csvw.audi.cc.entity.po.CarCustom;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.po.CarModelLine;
import com.csvw.audi.cc.entity.vo.*;
import com.csvw.audi.cc.exception.ServiceException;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
public interface ICarCustomService extends IService<CarCustom> {

    CarCustom addCarCustomConfig(String userId,String userMobile,String customColorId);

    CarCustom addCarCustomConfig(String memberId, String userId, String userMobile, String channel, CarCustomDto carCustomDto) throws Exception;

    CarCustom updateCarCustomConfig(String userId, String userMobile, String customColorId, String ccid);

    CarCustom updateCarCustomConfig(String memberId, String userId, String userMobile, CarCustomDto carCustomDto, String ccid) throws Exception;

    AudiConfigVo getAudiConfigVo(String ccid) throws Exception;

    CarCustomDetail getCarConfigDetailOmd(CarCustom carCustom) throws Exception;

    CarCustomDetail getCarConfigDetail(String channel, CarCustom carCustom) throws Exception;

    CustomSeriesDto getCustomSeriesInfo(String channel, CarCustom carCustom) throws Exception;

    CarCustomVo getCarCustom(CarCustom carCustom) throws Exception;

    String refreshCcid(CarCustom carCustom) throws Exception;

    CustomCheckDto checkCustom(CarModelLine modelLine, List<ModelLineOptionVo> options) throws ServiceException;

    void bindCc(String userId, String userMobile, String ccid) throws Exception;

    CarCustomDetail addCarconfigFromDRM(String memberId, String userId, String userMobile, String channel, SyncToCCProVo syncToCCProVo) throws Exception;

    CarCustomDetail getCarConfigDetailContract(CarCustom carCustom) throws Exception;

    CarCustom addCarCustomConfigByBestRecommend(String userId, String memberId, String userMobile, Long bestRecommendId, String sourceId, String channel, String entryPoint) throws Exception;

    public CarCustomVo a7OrQ5Byccid(String ccid);

    List<CarCustomDetailVo> listCustomDetail(List<Long> ccids);

    CarCustomDetail getCarConfigDetailOmdAll(CarCustom carCustom) throws Exception;

    CarCustom addInternalCarCustomConfig(String userId, String userMobile, Boolean validConfig, CarCustomDto carCustomDto) throws Exception;

    void validCcidWithUniqueCode(Long ccid, String uniqueCode) throws Exception;

    int countSum(String userId,String startDate,String endDate);

    CarCustom updateNew(String ccid) throws Exception;
}
