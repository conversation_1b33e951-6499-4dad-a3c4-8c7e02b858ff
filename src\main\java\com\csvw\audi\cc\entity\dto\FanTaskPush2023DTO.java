package com.csvw.audi.cc.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class FanTaskPush2023DTO {

    @ApiModelProperty("完成任务用户 idpId")
    private String userIdpId;

    @ApiModelProperty("任务类型 1-新手任务 2-邀请注册，3-浏览车型圈，4-配置车型，5-邀请试驾，6-门店打卡")
    private int type;

    @ApiModelProperty("完成时间(yyyy-MM-dd HH:mm:ss)")
    private String completeTime;

    @ApiModelProperty("邀请用户 idpId")
    private String inviteUserIdpId;

    @ApiModelProperty("邀请时间(yyyy-MM-dd HH:mm:ss)")
    private String inviteTime;

    @ApiModelProperty("车系")
    private String carSeries;
}
