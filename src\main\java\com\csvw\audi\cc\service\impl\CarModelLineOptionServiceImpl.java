package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.entity.dto.OptionParamDto;
import com.csvw.audi.cc.entity.dto.OptionPriceQuery;
import com.csvw.audi.cc.entity.po.CarModelLineOption;
import com.csvw.audi.cc.entity.po.CarOptionRelate;
import com.csvw.audi.cc.entity.vo.ModelLineOptionCompareVo;
import com.csvw.audi.cc.entity.vo.ModelLineOptionTagParam;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.mapper.CarModelLineOptionMapper;
import com.csvw.audi.cc.service.ICarModelLineOptionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 配置线配置项 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Service
public class CarModelLineOptionServiceImpl extends ServiceImpl<CarModelLineOptionMapper, CarModelLineOption> implements ICarModelLineOptionService {

    @Autowired
    private CarModelLineOptionMapper modelLineOptionMapper;

    @Override
    public List<ModelLineOptionVo> listModelLineOption(ModelLineOptionVo modelLineOptionVo) {
        return modelLineOptionMapper.listModelLineOption(modelLineOptionVo);
    }

    @Override
    public List<ModelLineOptionVo> listModelLinePacketItem(String channel, String modelLineId, Integer delFlag) {
        return modelLineOptionMapper.listModelLinePacketItem(channel, modelLineId, delFlag);
    }

    @Override
    public List<ModelLineOptionVo> listModelLinePersonalOption(String modelLineId, String channel, Integer delFlag, List<String> notInCategory, String type) {
        return modelLineOptionMapper.listModelLinePersonalOption(modelLineId, channel, delFlag, notInCategory, type);
    }

    @Override
    public List<ModelLineOptionVo> listModelLinePacketItem(OptionParamDto optionParamDto) {
        return modelLineOptionMapper.listOptionPacketItem(optionParamDto);
    }

    @Override
    public List<ModelLineOptionVo> modelLineOptionQuery(OptionParamDto param) {
        return modelLineOptionMapper.modelLineOptionQuery(param);
    }

    @Override
    public List<ModelLineOptionVo> modelLineOptionQueryStrict(OptionParamDto param) {
        return modelLineOptionMapper.modelLineOptionQueryStrict(param);
    }

    @Override
    public List<ModelLineOptionVo> listModelLineOptionWithoutRel(ModelLineOptionVo param) {
        return modelLineOptionMapper.listModelLineOptionWithoutRel(param);
    }

    @Override
    public List<ModelLineOptionVo> listOptionPacketItemByDrm(OptionPriceQuery param) {
        return modelLineOptionMapper.listOptionPacketItemByDrm(param);
    }

    @Override
    public List<ModelLineOptionCompareVo> listModelLineOptionCompare(ModelLineOptionVo param) {
        return modelLineOptionMapper.listModelLineOptionCompare(param);
    }

    @Override
    public List<ModelLineOptionVo> optionQuery(OptionParamDto param) {
        return modelLineOptionMapper.optionQuery(param);
    }

    @Override
    public List<ModelLineOptionVo> listModelLinePacketItemByCode(String modelLineId, String optionCode, Set<String> optionIds) {
        return modelLineOptionMapper.listModelLinePacketItemByCode(modelLineId, optionCode, optionIds);
    }

    @Override
    public List<ModelLineOptionVo> listModelLineOptionByTag(ModelLineOptionTagParam param) {
        return modelLineOptionMapper.listModelLineOptionByTag(param);
    }

}
