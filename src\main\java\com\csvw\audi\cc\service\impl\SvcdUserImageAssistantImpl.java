package com.csvw.audi.cc.service.impl;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csvw.audi.cc.config.OssConfig;
import com.csvw.audi.cc.entity.po.SvcdUser;
import com.csvw.audi.cc.mapper.SvcdUserMapper;
import com.csvw.audi.cc.service.ISvcdUserImageAssistant;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/3/21 14:18
 * @description svcd用户头像处理辅助类
 */
@Slf4j
@Service
public class SvcdUserImageAssistantImpl extends AbsSvcdImageToOssService implements ISvcdUserImageAssistant {

    @Autowired
    private OssConfig ossConfig;
    @Autowired
    private SvcdUserMapper svcdUserMapper;

    @Override
    public void findAndStorageSvcdUserImage(List<String> userUIDs) {
        try {
            LambdaQueryWrapper<SvcdUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            if (CollectionUtils.isNotEmpty(userUIDs)) {
                lambdaQueryWrapper.in(SvcdUser::getUserUid, userUIDs);
            }
            lambdaQueryWrapper.isNotNull(SvcdUser::getPic).ne(SvcdUser::getPic, "").orderByDesc(SvcdUser::getCreatedAt)
                .groupBy(SvcdUser::getUserUid);
            List<SvcdUser> svcdUsers = svcdUserMapper.selectList(lambdaQueryWrapper);
            svcdUsers.stream().filter(user -> StringUtils.isNotEmpty(user.getPic())).forEach(user -> {
                // 下载图片进行oss转存
                publicUpload(user.getPic(), user.getUserUid());
            });
        } catch (Exception e) {
            log.info("svcd用户--->{}", CollectionUtils.isEmpty(userUIDs) ? null : JSON.toJSONString(userUIDs));
            log.info("svcd用户图片转存异常", e);
        }

    }

    @Override
    public void asyncFindAndStorageSvcdUserImage(List<SvcdUser> svcdUserList) {
        if (CollectionUtils.isEmpty(svcdUserList)) {
            log.info("userUIDs 数据为空");
            return;
        }
        long startMillis = System.currentTimeMillis();
        log.info(startMillis + "开始svcd用户线下图片更新");
        CompletableFuture.runAsync(() -> {
            Set<String> userUidCollect = svcdUserList.stream().map(SvcdUser::getUserUid)
                .filter(userUid -> StringUtils.isNotEmpty(userUid)).collect(Collectors.toSet());
            try {
                this.findAndStorageSvcdUserImage(userUidCollect.stream().collect(Collectors.toList()));
            } catch (Exception e) {
                log.info("网发推送来的svcd用户图片上传oss异常");
                log.error("", e);
            }
        }).whenComplete((v, e) -> {
            if (e == null) {
                long endMillis = System.currentTimeMillis();
                log.info(endMillis + "svcd用户线下图片更新完成, 耗时 " + (endMillis - startMillis) + " 毫秒");
            }
        });
    }

    @Override
    String findUploadRelDirPathPosition(String unique) {
        String filePath = ossConfig.getSvcdUserImagePath() + "/" + unique;
        return filePath;
    }

    @Override
    public String getSvcdUserPicOssFullPathURI(SvcdUser svcdUser) {
        if(Objects.nonNull(svcdUser) && StringUtils.isEmpty(svcdUser.getPic())){
            return null;
        }
        String filePath = ossConfig.getSvcdUserImagePath() + "/" + svcdUser.getUserUid() + "/" + svcdUser.getPic();
        return filePath;
    }
}
