package com.csvw.audi.cc.controller.ccnew;

import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.entity.po.CarCustomSeries;
import com.csvw.audi.cc.entity.vo.AdminModel;
import com.csvw.audi.cc.entity.vo.AdminSeries;
import com.csvw.audi.cc.service.ICarCustomSeriesService;
import com.csvw.audi.cc.service.ICarModelLineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/private/api/v1/cc/admin")
@Api(tags = "车辆配置内部接口-提供运营侧下拉框")
@Slf4j
public class PrivateAdminCcApiController extends BaseController {

    @Autowired
    private ICarCustomSeriesService customSeriesService;

    @Autowired
    private ICarModelLineService modelLineService;

    @GetMapping("/customSeries")
    @ApiOperation("车系")
    public List<AdminSeries> customSeries() throws Exception {
        List<CarCustomSeries> series = customSeriesService.lambdaQuery().eq(CarCustomSeries::getChannel, Constant.MASTER_CHANNEL).list();
        return series.stream().map(i->new AdminSeries(i.getCustomSeriesId(), i.getCustomSeriesCode(), i.getCustomSeriesName())).collect(Collectors.toList());
    }

    @GetMapping("/modelLine")
    @ApiOperation("车型")
    public List<AdminModel> customModelLine(String customSeriesId) throws Exception {
        List<AdminModel> models = modelLineService.listAdminModels(customSeriesId);
        return models;
    }


}
