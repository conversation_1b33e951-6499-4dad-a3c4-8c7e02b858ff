package com.csvw.audi.cc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csvw.audi.cc.entity.dto.CcOrderInfo;
import com.csvw.audi.cc.entity.po.CarBestRecommendCustomSync;
import com.csvw.audi.cc.entity.po.CarStockLog;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.mapper.CarBestRecommendCustomSyncMapper;
import com.csvw.audi.cc.mapper.CarStockLogMapper;
import com.csvw.audi.cc.service.ICarBestRecommendCustomSyncService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csvw.audi.cc.service.ICarStockLogService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 库存车配置单订单状态同步 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-26
 */
@Service
public class CarBestRecommendCustomSyncServiceImpl extends ServiceImpl<CarBestRecommendCustomSyncMapper, CarBestRecommendCustomSync> implements ICarBestRecommendCustomSyncService {

    @Autowired
    private CarStockLogMapper stockLogMapper;

    @Autowired
    private ICarStockLogService stockLogService;

    @Autowired
    private CopOrderDbServiceImpl orderDbService;

    @Override
    public void syncRecommendCustom(Long recommendModelId, String type) {
        // 查询需要继续同步的同步一下
        LambdaQueryWrapper<CarBestRecommendCustomSync> customSyncQuery = new LambdaQueryWrapper<>();
        customSyncQuery.eq(CarBestRecommendCustomSync::getRecommendModelId, recommendModelId)
                .eq(CarBestRecommendCustomSync::getType, type)
                .ne(CarBestRecommendCustomSync::getStatus, 1);
        List<CarBestRecommendCustomSync> syncList = this.list(customSyncQuery);
        // 同步变更
        syncList.forEach(i->{
            try {
                CcOrderInfo orderInfo = orderDbService.listOrderByCcid(i.getCcid());
                if (orderInfo != null){
                    i.setOrderId(orderInfo.getOrderId());
                    i.setOrderOmdStatus(String.valueOf(orderInfo.getSyncStatus()));
                    i.setOrderStatus(orderInfo.getOrderStatus());
                    // 同步成功的
                    if (orderInfo.getSyncStatus() != null && orderInfo.getSyncStatus().intValue() == 2){
                        i.setStatus(1);
                    }
                    // 已取消，已关闭的
                    List<String> clientOverStatus = Arrays.asList("98", "99");
                    if (orderInfo.getOrderStatus() != null && clientOverStatus.contains(orderInfo.getOrderStatus())){
                        i.setStatus(1);
                    }
                }else {
                    i.setStatus(2);
                    // ccid最近一次状态为解锁，status 1
                    LambdaQueryWrapper<CarStockLog> stockLogQ = new LambdaQueryWrapper<>();
                    stockLogQ.eq(CarStockLog::getCcid, i.getCcid()).orderByDesc(CarStockLog::getCreateTime).last("limit 1");
                    CarStockLog log = stockLogService.getOne(stockLogQ);
                    if (log != null && log.getOperate().intValue() == 2) {
                        // 已解锁库存
                        i.setStatus(1);
                    }

                }

                i.setUpdateTime(LocalDateTime.now());
                i.updateById();
            } catch (ServiceException e) {
                log.error("同步查询异常", e);
            }
        });

        // 查询未同步，同步一下
        List<CarStockLog> logs = stockLogMapper.findLogToSync(recommendModelId, type);
        // 同步新增sync
        if (logs != null){
            logs.forEach(i->{
                CarBestRecommendCustomSync customSync = new CarBestRecommendCustomSync();
                customSync.setCcid(i.getCcid());
                customSync.setBestRecommendId(i.getBestRecommendId());
                customSync.setRecommendModelId(i.getRecommendModelId());
                customSync.setType(i.getType());
                customSync.setCreateTime(LocalDateTime.now());
                try {
                    CcOrderInfo orderInfo = orderDbService.listOrderByCcid(i.getCcid());
                    if (orderInfo != null){
                        customSync.setOrderId(orderInfo.getOrderId());
                        customSync.setOrderOmdStatus(String.valueOf(orderInfo.getSyncStatus()));
                        customSync.setOrderStatus(orderInfo.getOrderStatus());
                        if (orderInfo.getOrderStatus() != null && orderInfo.getSyncStatus().intValue() == 2){
                            customSync.setStatus(1);
                        }
                    }else {
                        customSync.setStatus(2);
                        // ccid最近一次状态为解锁，status 1
                        LambdaQueryWrapper<CarStockLog> stockLogQ = new LambdaQueryWrapper<>();
                        stockLogQ.eq(CarStockLog::getCcid, i.getCcid()).orderByDesc(CarStockLog::getCreateTime).last("limit 1");
                        CarStockLog log = stockLogService.getOne(stockLogQ);
                        if (log != null && log.getOperate().intValue() == 2) {
                            // 已解锁库存
                            customSync.setStatus(1);
                        }
                    }
                    customSync.insert();
                } catch (ServiceException e) {
                    log.error("同步查询异常", e);
                }
            });
        }
    }
}
