package com.csvw.audi.cc.feign;

import com.csvw.audi.cc.entity.dto.CopOrderDto;
import com.csvw.audi.cc.entity.dto.CopResultDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(value = "cop-order-admin", url = "${audi.purple-int-scq}/cop-prod-query")
public interface AudiOrderAdminFeign {

    /**
     * 获取订单详情
     * @return
     */
    @GetMapping("/api/v1/admin/orders/{orderId}")
    CopResultDto<CopOrderDto> getOrderDetail(@PathVariable("orderId") String orderId);
}
