<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarModelLineParameterMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarModelLineParameter">
        <id column="id" property="id" />
        <result column="parameter_id" property="parameterId" />
        <result column="model_id" property="modelId" />
        <result column="model_line_id" property="modelLineId" />
        <result column="parameter_value" property="parameterValue" />
        <result column="parameter_weight" property="parameterWeight" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parameter_id, model_id, model_line_id, parameter_value, parameter_weight, create_time, update_time, del_flag
    </sql>

    <select id="listModelLineParameter" resultType="com.csvw.audi.cc.entity.vo.ModelLineParameterVo">
        select p.parameter_id, p.parameter_name, p.parameter_type, p.parameter_type_name, mlp.model_id, mlp.model_line_id, mlp.parameter_value
        from car_model_line_parameter mlp
        left join car_parameter p on mlp.parameter_id = p.parameter_id
        <where>
            <if test="modelLineId != null and modelLineId != ''">
                mlp.model_line_id = #{modelLineId}
            </if>
        </where>
        order by p.weight
    </select>

</mapper>
