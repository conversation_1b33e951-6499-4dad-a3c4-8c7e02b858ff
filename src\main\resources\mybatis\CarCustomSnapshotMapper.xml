<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarCustomSnapshotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarCustomSnapshot">
        <id column="snapshot_id" property="snapshotId" />
        <result column="ccid" property="ccid" />
        <result column="audi_code" property="audiCode" />
        <result column="accb_model_id" property="accbModelId" />
        <result column="accb_model_code" property="accbModelCode" />
        <result column="model_desc" property="modelDesc" />
        <result column="accb_type_id" property="accbTypeId" />
        <result column="accb_type_code" property="accbTypeCode" />
        <result column="accb_type_desc" property="accbTypeDesc" />
        <result column="model_year" property="modelYear" />
        <result column="model_line_id" property="modelLineId" />
        <result column="sib_interieur_id" property="sibInterieurId" />
        <result column="best_recommend_id" property="bestRecommendId" />
        <result column="measure_id" property="measureId" />
        <result column="source_id" property="sourceId" />
        <result column="entry_point" property="entryPoint" />
        <result column="deposit_type" property="depositType" />
        <result column="model_price" property="modelPrice" />
        <result column="total_price" property="totalPrice" />
        <result column="omd_snapshot" property="omdSnapshot" />
        <result column="contract_snapshot" property="contractSnapshot" />
        <result column="detail_snapshot" property="detailSnapshot" />
        <result column="configCode" property="configCode" />
        <result column="user_id" property="userId" />
        <result column="user_mobile" property="userMobile" />
        <result column="create_time" property="createTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        snapshot_id, ccid, audi_code, accb_model_id, accb_model_code, model_desc, accb_type_id, accb_type_code, accb_type_desc, model_year, model_line_id, sib_interieur_id, best_recommend_id, measure_id, source_id, entry_point, deposit_type, model_price, total_price, omd_snapshot, contract_snapshot, detail_snapshot, configCode, user_id, user_mobile, create_time, remark
    </sql>

</mapper>
