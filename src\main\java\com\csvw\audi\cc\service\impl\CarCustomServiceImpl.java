package com.csvw.audi.cc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.common.utils.JsonString;
import com.csvw.audi.cc.config.AppConfig;
import com.csvw.audi.cc.entity.dto.*;
import com.csvw.audi.cc.entity.dto.accb.AudiConfigDto;
import com.csvw.audi.cc.entity.dto.accb.OptionBriefDto;
import com.csvw.audi.cc.entity.dto.vwcc.*;
import com.csvw.audi.cc.entity.dto.vwcc.Option;
import com.csvw.audi.cc.entity.enumeration.*;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.entity.vo.*;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.feign.AudiEshopFeign;
import com.csvw.audi.cc.feign.CopOrderQueryFeign;
import com.csvw.audi.cc.mapper.CarCustomMapper;
import com.csvw.audi.cc.mapper.CarModelLineOptionMapper;
import com.csvw.audi.cc.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Service
@Slf4j
public class CarCustomServiceImpl extends ServiceImpl<CarCustomMapper, CarCustom> implements ICarCustomService {

    @Autowired
    private IACCBService iaccbService;

    @Autowired
    private ICarCustomService carCustomService;

    @Autowired
    private ICarCustomOptionService carCustomOptionService;

    @Autowired
    private ICarOptionService optionService;

    @Autowired
    private ICarModelLineService modelLineService;

    @Autowired
    private ICarCustomSeriesService customSeriesService;

    @Autowired
    private ICarOmdPriceTypeService priceTypeService;

    @Autowired
    private ICarModelLineOptionService modelLineOptionService;

    @Autowired
    private ICarSibInterieurService sibInterieurService;

    @Autowired
    private ICarOptionRelateService optionRelateService;

    @Autowired
    private ICarConfigImageService configImageService;

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private ICarOptionService carOptionService;

    @Autowired
    private ICarSibInterieurService carSibInterieurService;

    @Autowired
    private AudiEshopFeign eshopFeign;

    @Autowired
    private ICarBestRecommendService bestRecommendService;

    @Autowired
    private ICarBestRecommendOptionService bestRecommendOptionService;

    @Autowired
    private ICarModelLineSibInterieurService modelLineSibInterieurService;

    @Autowired
    private CarModelLineOptionMapper modelLineOptionMapper;

    @Autowired
    private CarCustomMapper carCustomMapper;

    @Autowired
    private ICarModelLineSpecialOptionService specialOptionService;

    @Autowired
    private ICarCustomEntryPointService entryPointService;

    @Autowired
    private ICarModelLineExtService lineExtService;

    @Autowired private ICarCustomSnapshotService customSnapshotService;

    @Autowired
    private CopOrderQueryFeign copOrderQueryFeign;


    ExecutorService audiCodeExecutor = Executors.newFixedThreadPool(10);

    @Override
    public CarCustom addCarCustomConfig(String memberId, String userId, String userMobile, String channel, CarCustomDto carCustomDto) throws Exception {
        String trackId = UUID.randomUUID().toString();
        if (userId != null && carCustomDto != null) {
            log.info("add cc userId: " + userId + " customParam: " + JSONObject.toJSONString(carCustomDto) + " trackId:" + trackId);
        }
        LambdaQueryWrapper<CarCustomEntryPoint> entryPointQ = new LambdaQueryWrapper<>();
        entryPointQ.eq(CarCustomEntryPoint::getChannel, channel)
                .eq(CarCustomEntryPoint::getEntryPoint, carCustomDto.getEntryPoint())
                .eq(CarCustomEntryPoint::getStatus, 1);
        if (entryPointService.getOne(entryPointQ) == null){
            throw new ServiceException("400401", "参数异常：entryPoint", null);
        }
        //参数校验
        validCarModelLineCustom(carCustomDto);

        List<String> optionIds = Lists.newArrayList(carCustomDto.getOptionIds().iterator());
        if (CollectionUtils.isEmpty(optionIds)){
            throw new ServiceException("400401", "参数异常：optionIds", null);
        }
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(carCustomDto.getModelLineId());
        modelParamDto.setChannel(Constant.ONEAPP_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (lines == null || lines.size() != 1){
            throw new ServiceException("400401", "参数异常：modelLineId", null);
        }
        if (lines.get(0).getFromOmd() != null && lines.get(0).getFromOmd() == 1 && StringUtils.isBlank(carCustomDto.getMstMgrpId())){
            throw new ServiceException("400401", "参数异常：mstMgrpld", null);
        }
        ModelLineVo modelLine = lines.get(0);
        LambdaQueryWrapper<CarSibInterieur> snQ = new LambdaQueryWrapper<>();
        snQ.eq(CarSibInterieur::getSibInterieurId, carCustomDto.getSibInterieurId())
                .eq(CarSibInterieur::getChannel, Constant.MASTER_CHANNEL)
                .eq(CarSibInterieur::getDelFlag, 0);
        CarSibInterieur sn = sibInterieurService.getOne(snQ);
        if (sn == null){
            throw new ServiceException("400401", "参数异常：sibInterieurId", null);
        }
        if (!optionIds.contains(sn.getSibOptionId())) {
            carCustomDto.getOptionIds().add(sn.getSibOptionId());
        }
        if (!optionIds.contains(sn.getInterieurOptionId())) {
            carCustomDto.getOptionIds().add(sn.getInterieurOptionId());
        }
        CustomSeriesDto seriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
        AudiConfigDto audiConfigDto = new AudiConfigDto();
        audiConfigDto.setModelId(seriesDto.getAccbModelId());
        audiConfigDto.setModelCode(seriesDto.getAccbModelCode());
        audiConfigDto.setModelDesc(seriesDto.getSeriesName());
        audiConfigDto.setTypeId(modelLine.getAccbTypeId());
        audiConfigDto.setTypeCode(modelLine.getAccbTypeCode());
        audiConfigDto.setTypeDesc(modelLine.getModelLineName());
        audiConfigDto.setModelYear(modelLine.getModelYear());
        audiConfigDto.setModelVersion(modelLine.getVersion());;
        audiConfigDto.setHeadline(modelLine.getModelLineName());
        List<OptionBriefDto> optionBriefDtoList = new ArrayList<>();
        OptionParamDto optionParamDto = new OptionParamDto();
        optionParamDto.setOptionIds(optionIds);
        optionParamDto.setChannel(Constant.MASTER_CHANNEL);
        optionParamDto.setDelFlag(0);
        optionParamDto.setModelLineId(modelLine.getModelLineId());
        List<ModelLineOptionVo> options = modelLineOptionService.optionQuery(optionParamDto);
        if (CollectionUtils.isEmpty(options)){
            throw new ServiceException("400401", "参数异常：optionIds", null);
        }
        options.forEach(i->
            optionBriefDtoList.add(new OptionBriefDto(i.getCategory()+":"+i.getOptionCode(), i.getCategory(),i.getOptionName())));
        audiConfigDto.setOptions(optionBriefDtoList);

        CarCustom carCustom = new CarCustom();
        carCustom.setDepositType(carCustomDto.getDepositType());
        carCustom.setEntryPoint(carCustomDto.getEntryPoint());
        carCustom.setSibInterieurId(carCustomDto.getSibInterieurId());
        carCustom.setAccbModelId(seriesDto.getAccbModelId());
        carCustom.setAccbModelCode(seriesDto.getAccbModelCode());
        carCustom.setModelDesc(seriesDto.getCustomSeriesName());
        carCustom.setAccbTypeId(modelLine.getAccbTypeId());
        carCustom.setAccbTypeCode(modelLine.getAccbTypeCode());
        carCustom.setAccbTypeDesc(modelLine.getModelLineName());
        carCustom.setModelLineId(modelLine.getModelLineId());
        carCustom.setModelYear(modelLine.getModelYear());
        carCustom.setUserId(userId);
        carCustom.setMemberId(memberId);
        carCustom.setUserMobile(userMobile);
        carCustom.setMstMgrpId(carCustomDto.getMstMgrpId());
        carCustom.setCreateTime(LocalDateTime.now());
        carCustom.setCreateUser(userId);
        if (StringUtils.isNotBlank(carCustomDto.getSourceId())) {
            CarCustomSource source = new CarCustomSource();
            if (source.selectById(carCustomDto.getSourceId()) == null) {
                throw new ServiceException("400401", "参数异常：sourceId", null);
            }
            carCustom.setSourceId(Long.valueOf(carCustomDto.getSourceId()));
        }
        CcEstimateDeliveryParam estimateDeliveryParam = new CcEstimateDeliveryParam();
        estimateDeliveryParam.setCustomSeriesId(modelLine.getCustomSeriesId());
        estimateDeliveryParam.setModelLineId(modelLine.getModelLineId());
        estimateDeliveryParam.setOptionIds(new ArrayList<>(carCustomDto.getOptionIds()));
        estimateDeliveryParam.setBeforeCheck(false);
        CcEstimateVo estimateVo = modelLineService.ccEstimate(estimateDeliveryParam);
        if(StringUtils.isBlank(modelLine.getOmdModelUnicode())) {
            if (estimateVo == null || estimateVo.getType() == null || modelLine.getTypeFlag() == null || !modelLine.getTypeFlag().contains(estimateVo.getType())) {
                throw new ServiceException("400401", "参数异常：不支持当前配置清单", null);
            }
        }
        if (Arrays.asList("A", "B").contains(estimateVo.getType())) {
            carCustom.setDepositType(DepositTypeEnum.STOCK.getType());
            carCustom.setOmdVehicleTypeId(estimateVo.getOmdVehicleTypeId());
        }
        carCustom.setClassify(estimateVo.getType());
        carCustom.setEstimateDelivery(estimateVo.getDeliveryTime());
        carCustom.setClassifyVersion(modelLine.getClassifyVersion());
        carCustom.insert();
        options.forEach(i->{
            CarCustomOption carCustomOption = new CarCustomOption();
            carCustomOption.setCcid(carCustom.getCcid());
            carCustomOption.setOptionId(i.getOptionId());
            carCustomOption.setCategory(i.getCategory());
            carCustomOption.setCode(i.getOptionCode());
            carCustomOption.setDescription(i.getOptionName());
            carCustomOption.insert();
        });
        log.info("add cc userId: " + userId + " customParam: " +
                JSONObject.toJSONString(carCustomDto) + " trackId: " + trackId + " ccid: "+ String.valueOf(carCustom.getCcid()));
//        audiCodeExecutor.execute(()->this.ccGenAudiCode(carCustom, audiConfigDto));
        customSnapshotService.snapshotCarCustom(carCustom);
        return carCustom;
    }

    @Override
    public CarCustom addInternalCarCustomConfig(String userId, String userMobile, Boolean validConfig, CarCustomDto carCustomDto) throws Exception {
        String trackId = UUID.randomUUID().toString();
        if (userId != null && carCustomDto != null) {
            log.info("add cc userId: " + userId + " customParam: " + JSONObject.toJSONString(carCustomDto) + " trackId:" + trackId);
        }
        //参数校验
        if(validConfig) {
            validInternalCarModelLineCustom(carCustomDto);
        }
        List<String> optionIds = Lists.newArrayList(carCustomDto.getOptionIds().iterator());
        if (CollectionUtils.isEmpty(optionIds)){
            throw new ServiceException("400401", "参数异常：optionIds", null);
        }
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(carCustomDto.getModelLineId());
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (lines == null || lines.size() != 1){
            throw new ServiceException("400401", "参数异常：modelLineId", null);
        }
        ModelLineVo modelLine = lines.get(0);
        LambdaQueryWrapper<CarSibInterieur> snQ = new LambdaQueryWrapper<>();
        snQ.eq(CarSibInterieur::getSibInterieurId, carCustomDto.getSibInterieurId())
                .eq(CarSibInterieur::getChannel, Constant.MASTER_CHANNEL)
                .eq(CarSibInterieur::getDelFlag, 0);
        CarSibInterieur sn = sibInterieurService.getOne(snQ);
        if (sn == null){
            throw new ServiceException("400401", "参数异常：sibInterieurId", null);
        }
        if (!optionIds.contains(sn.getSibOptionId())) {
            carCustomDto.getOptionIds().add(sn.getSibOptionId());
        }
        if (!optionIds.contains(sn.getInterieurOptionId())) {
            carCustomDto.getOptionIds().add(sn.getInterieurOptionId());
        }
        CustomSeriesDto seriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
        AudiConfigDto audiConfigDto = new AudiConfigDto();
        audiConfigDto.setModelId(seriesDto.getAccbModelId());
        audiConfigDto.setModelCode(seriesDto.getAccbModelCode());
        audiConfigDto.setModelDesc(seriesDto.getSeriesName());
        audiConfigDto.setTypeId(modelLine.getAccbTypeId());
        audiConfigDto.setTypeCode(modelLine.getAccbTypeCode());
        audiConfigDto.setTypeDesc(modelLine.getModelLineName());
        audiConfigDto.setModelYear(modelLine.getModelYear());
        audiConfigDto.setModelVersion(modelLine.getVersion());;
        audiConfigDto.setHeadline(modelLine.getModelLineName());
        List<OptionBriefDto> optionBriefDtoList = new ArrayList<>();
        OptionParamDto optionParamDto = new OptionParamDto();
        optionParamDto.setOptionIds(optionIds);
        optionParamDto.setChannel(Constant.MASTER_CHANNEL);
        optionParamDto.setDelFlag(0);
        optionParamDto.setModelLineId(modelLine.getModelLineId());
        List<ModelLineOptionVo> options = modelLineOptionService.optionQuery(optionParamDto);
        if (CollectionUtils.isEmpty(options)){
            throw new ServiceException("400401", "参数异常：optionIds", null);
        }
        options.forEach(i->
                optionBriefDtoList.add(new OptionBriefDto(i.getCategory()+":"+i.getOptionCode(), i.getCategory(),i.getOptionName())));
        audiConfigDto.setOptions(optionBriefDtoList);

        CarCustom carCustom = new CarCustom();
        carCustom.setSibInterieurId(carCustomDto.getSibInterieurId());
        carCustom.setAccbModelId(seriesDto.getAccbModelId());
        carCustom.setAccbModelCode(seriesDto.getAccbModelCode());
        carCustom.setModelDesc(seriesDto.getCustomSeriesName());
        carCustom.setAccbTypeId(modelLine.getAccbTypeId());
        carCustom.setAccbTypeCode(modelLine.getAccbTypeCode());
        carCustom.setAccbTypeDesc(modelLine.getModelLineName());
        carCustom.setModelLineId(modelLine.getModelLineId());
        carCustom.setModelYear(modelLine.getModelYear());
        carCustom.setUserId(userId);
        carCustom.setUserMobile(userMobile);
        carCustom.setCreateTime(LocalDateTime.now());
        carCustom.setCreateUser(userId);
        carCustom.setDepositType(carCustomDto.getDepositType());
        carCustom.setEntryPoint(carCustomDto.getEntryPoint());
        if (EntryPointEnum.LONG_STOCK.getValue().equals(carCustomDto.getEntryPoint())){
            carCustom.setDepositType(DepositTypeEnum.STOCK.getType());
            carCustom.setClassify("A");
            carCustom.setEstimateDelivery(DeliveryTimeEnum.A.getValue());
        }
        if (StringUtils.isNotBlank(carCustomDto.getSourceId())) {
            CarCustomSource source = new CarCustomSource();
            if (source.selectById(carCustomDto.getSourceId()) == null) {
                throw new ServiceException("400401", "参数异常：sourceId", null);
            }
            carCustom.setSourceId(Long.valueOf(carCustomDto.getSourceId()));
        }
        carCustom.insert();
        options.forEach(i->{
            CarCustomOption carCustomOption = new CarCustomOption();
            carCustomOption.setCcid(carCustom.getCcid());
            carCustomOption.setOptionId(i.getOptionId());
            carCustomOption.setCategory(i.getCategory());
            carCustomOption.setCode(i.getOptionCode());
            carCustomOption.setDescription(i.getOptionName());
            carCustomOption.insert();
        });
        customSnapshotService.snapshotCarCustom(carCustom);
        log.info("add internal cc userId: " + userId + " customParam: " +
                JSONObject.toJSONString(carCustomDto) + " trackId: " + trackId + " ccid: "+ String.valueOf(carCustom.getCcid()));
//        audiCodeExecutor.execute(()->this.ccGenAudiCode(carCustom, audiConfigDto));
        return carCustom;
    }

    @Override
    public void validCcidWithUniqueCode(Long ccid, String uniqueCode) throws Exception {
        CarCustom carCustom = this.getById(Long.valueOf(ccid));
        CarCustomDetail detail = this.getCarConfigDetailOmd(carCustom);
        String modelCode = detail.getConfigDetail().getCarModel().getModelCode().replaceFirst("TYPE:", "");
        String outColorCode = detail.getConfigDetail().getOutsideColor().getColorCode();
        String inColorCode = detail.getConfigDetail().getInsideColor().getColorCode();
        List<String> prList = detail.getConfigDetail().getOptionList().stream().map(Option::getOptionCode).collect(Collectors.toList());
        String uniqueCodeTmp = uniqueCode;
        if(StringUtils.isNotBlank(uniqueCodeTmp)){
            if (!uniqueCodeTmp.startsWith(modelCode)){
                throw new ServiceException("50000", "相似车配置单，车型校验不通过");
            }
            uniqueCodeTmp = uniqueCodeTmp.replaceFirst(modelCode, "");
            if (!uniqueCodeTmp.startsWith(outColorCode)){
                throw new ServiceException("50000", "相似车配置单，外饰校验不通过");
            }
            uniqueCodeTmp = uniqueCodeTmp.replaceFirst(outColorCode, "");
            if (!uniqueCodeTmp.startsWith(inColorCode)){
                throw new ServiceException("50000", "相似车配置单，内饰校验不通过");
            }
            uniqueCodeTmp = uniqueCodeTmp.replaceFirst(inColorCode, "");
            if (StringUtils.isBlank(uniqueCodeTmp) && CollectionUtils.isEmpty(prList)){
                return;
            }
            List<String> uniquePrList = Arrays.stream(uniqueCodeTmp.split(",")).collect(Collectors.toList());
            String[] modelSplit = modelCode.split("-");
            List<String> modelOptionCodes = new ArrayList<>();
            if (modelSplit.length > 1) {
                for (int i = 1; i < modelSplit.length; i++) {
                    if (modelSplit[i].length() == 7){
                        modelOptionCodes.add(modelSplit[i].substring(4));
                    }
                }
            }
            uniquePrList.removeAll(modelOptionCodes);
            if (!(uniquePrList.containsAll(prList) && prList.containsAll(uniquePrList))){
                throw new ServiceException("50000", "相似车配置单，选装校验不通过");
            }
        }else{
            throw new ServiceException("50000", "相似车配置单，uniqueCode异常");
        }


    }

    @Override
    public void bindCc(String userId, String userMobile, String ccid) throws Exception {
        LambdaQueryWrapper<CarCustom> customQuery = new LambdaQueryWrapper<>();
        customQuery.eq(CarCustom::getCcid, ccid);
        CarCustom carCustom = carCustomService.getOne(customQuery);
        if ( carCustom == null || (StringUtils.isNotBlank(carCustom.getUserId()))){
            throw new ServiceException("400401", "参数异常：ccid", null);
        }
        customSnapshotService.bindUserSnapshotCarCustom(Long.valueOf(ccid), userId, userMobile, LocalDateTime.now());
        log.info("bind cc userId: " + userId + " ccid:" + ccid);
    }

    @Override
    public CarCustom updateCarCustomConfig(String memberId, String userId, String userMobile, CarCustomDto carCustomDto, String ccid) throws Exception {
        if (userId != null && carCustomDto != null && ccid != null) {
            log.info("update cc userId: " + userId + " customParam: " + JSONObject.toJSONString(carCustomDto) + " ccid:"+ccid);
        }
        LambdaQueryWrapper<CarCustom> customQuery = new LambdaQueryWrapper<>();
        customQuery.eq(CarCustom::getCcid, ccid).eq(CarCustom::getUserId, userId);
        if (!"ams".equals(userId)){
            if(carCustomService.getOne(customQuery) == null) {
                throw new ServiceException("400401", "参数异常：ccid", null);
            }
        }else {
            CarCustom oldCc = carCustomService.getOne(Wrappers.<CarCustom>lambdaQuery().eq(CarCustom::getCcid, ccid));
            userId = oldCc.getUserId();
            memberId = oldCc.getMemberId();
        }
        if (carCustomDto.getEntryPoint() != null && carCustomDto.getEntryPoint().equals("ONEAPP_LONGSTOCK")){
            throw new ServiceException("400401", "长库龄配置单，不可修改", null);
        }
        AjaxMessage<Integer> eshopRes = eshopFeign.canUpdateCCConfig(ccid);
        if (eshopRes.getCode().equals("00")){
            if (eshopRes.getData().intValue() == 0){
                log.error("ccid:{} could not update, eshop res: {}", ccid, JSONObject.toJSONString(eshopRes));
                throw new ServiceException("400401", "配置单不可更改", null);
            }
        }else {
            log.error("ccid:{} update fail, eshop res: {}", ccid, JSONObject.toJSONString(eshopRes));
            throw new ServiceException("50000", "系统错误，请稍后再试", null);
        }

        //参数校验
        validCarModelLineCustom(carCustomDto);
        List<String> optionIds = Lists.newArrayList(carCustomDto.getOptionIds().iterator());
        if (CollectionUtils.isEmpty(optionIds)){
            throw new ServiceException("400401", "参数异常：optionIds", null);
        }
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(carCustomDto.getModelLineId());
        modelParamDto.setChannel(Constant.ONEAPP_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (lines == null || lines.size() != 1){
            throw new ServiceException("400401", "参数异常：modelLineId", null);
        }
        if (lines.get(0).getFromOmd() != null && lines.get(0).getFromOmd() == 1 && StringUtils.isBlank(carCustomDto.getMstMgrpId())){
            throw new ServiceException("400401", "参数异常：mstMgrpld", null);
        }
        ModelLineVo modelLine = lines.get(0);
        LambdaQueryWrapper<CarSibInterieur> snQ = new LambdaQueryWrapper<>();
        snQ.eq(CarSibInterieur::getSibInterieurId, carCustomDto.getSibInterieurId())
                .eq(CarSibInterieur::getChannel, Constant.MASTER_CHANNEL)
                .eq(CarSibInterieur::getDelFlag, 0);
        CarSibInterieur sn = sibInterieurService.getOne(snQ);
        if (sn == null){
            throw new ServiceException("400401", "参数异常：sibInterieurId", null);
        }
        if (!optionIds.contains(sn.getSibOptionId())) {
            carCustomDto.getOptionIds().add(sn.getSibOptionId());
        }
        if (!optionIds.contains(sn.getInterieurOptionId())) {
            carCustomDto.getOptionIds().add(sn.getInterieurOptionId());
        }
        CustomSeriesDto seriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
        AudiConfigDto audiConfigDto = new AudiConfigDto();
        audiConfigDto.setModelId(seriesDto.getAccbModelId());
        audiConfigDto.setModelCode(seriesDto.getAccbModelCode());
        audiConfigDto.setModelDesc(seriesDto.getSeriesName());
        audiConfigDto.setTypeId(modelLine.getAccbTypeId());
        audiConfigDto.setTypeCode(modelLine.getAccbTypeCode());
        audiConfigDto.setTypeDesc(modelLine.getModelLineName());
        audiConfigDto.setModelYear(modelLine.getModelYear());
        audiConfigDto.setModelVersion(modelLine.getVersion());
        audiConfigDto.setHeadline(modelLine.getModelLineName());
        List<OptionBriefDto> optionBriefDtoList = new ArrayList<>();
        OptionParamDto optionParamDto = new OptionParamDto();
        optionParamDto.setOptionIds(optionIds);
        optionParamDto.setChannel(Constant.MASTER_CHANNEL);
        optionParamDto.setDelFlag(0);
        optionParamDto.setModelLineId(modelLine.getModelLineId());
        List<ModelLineOptionVo> options = modelLineOptionService.optionQuery(optionParamDto);
        if (CollectionUtils.isEmpty(options)){
            throw new ServiceException("400401", "参数异常：optionIds", null);
        }
        options.forEach(i->
                optionBriefDtoList.add(new OptionBriefDto(i.getCategory()+":"+i.getOptionCode(), i.getCategory(),i.getOptionName())));
        audiConfigDto.setOptions(optionBriefDtoList);

        CarCustom carCustom = new CarCustom();
        carCustom.setSibInterieurId(carCustomDto.getSibInterieurId());
        carCustom.setAccbModelId(seriesDto.getAccbModelId());
        carCustom.setAccbModelCode(seriesDto.getAccbModelCode());
        carCustom.setModelDesc(seriesDto.getSeriesName());
        carCustom.setAccbTypeId(modelLine.getAccbTypeId());
        carCustom.setAccbTypeCode(modelLine.getAccbTypeCode());
        carCustom.setAccbTypeDesc(modelLine.getModelLineName());
        carCustom.setModelLineId(modelLine.getModelLineId());
        carCustom.setModelYear(modelLine.getModelYear());
        carCustom.setUpdateTime(LocalDateTime.now());
        carCustom.setUpdateUser(userId);
        carCustom.setCcid(Long.valueOf(ccid));
        carCustom.setMemberId(memberId);
        carCustom.setMstMgrpId(carCustomDto.getMstMgrpId());
        CcEstimateDeliveryParam estimateDeliveryParam = new CcEstimateDeliveryParam();
        estimateDeliveryParam.setCustomSeriesId(modelLine.getCustomSeriesId());
        estimateDeliveryParam.setModelLineId(modelLine.getModelLineId());
        estimateDeliveryParam.setOptionIds(new ArrayList<>(carCustomDto.getOptionIds()));
        estimateDeliveryParam.setBeforeCheck(false);
        CcEstimateVo estimateVo = modelLineService.ccEstimate(estimateDeliveryParam);
        if(StringUtils.isBlank(modelLine.getOmdModelUnicode())) {
            if (estimateVo == null || estimateVo.getType() == null || modelLine.getTypeFlag() == null
                    || !modelLine.getTypeFlag().contains(estimateVo.getType())) {
                throw new ServiceException("400401", "参数异常：不支持当前配置清单", null);
            }
        }
        if (Arrays.asList("A", "B").contains(estimateVo.getType())) {
            carCustom.setDepositType(DepositTypeEnum.STOCK.getType());
            carCustom.setOmdVehicleTypeId(estimateVo.getOmdVehicleTypeId());
        }
        carCustom.setClassify(estimateVo.getType());
        carCustom.setEstimateDelivery(estimateVo.getDeliveryTime());
        carCustom.setClassifyVersion(modelLine.getClassifyVersion());
        carCustom.updateById();
        LambdaUpdateWrapper<CarCustom> ccU = new LambdaUpdateWrapper<>();
        ccU.eq(CarCustom::getCcid, ccid).set(CarCustom::getInvalidReason, null).set(CarCustom::getValid, 1);
        carCustomService.update(ccU);
        LambdaQueryWrapper<CarCustomOption> deleteQueryWrapper = new LambdaQueryWrapper<>();
        deleteQueryWrapper.eq(CarCustomOption::getCcid, ccid);
        carCustomOptionService.remove(deleteQueryWrapper);
        options.forEach(i->{
            CarCustomOption carCustomOption = new CarCustomOption();
            carCustomOption.setCcid(carCustom.getCcid());
            carCustomOption.setOptionId(i.getOptionId());
            carCustomOption.setCategory(i.getCategory());
            carCustomOption.setCode(i.getOptionCode());
            carCustomOption.setDescription(i.getOptionName());
            carCustomOption.insert();
        });
//        audiCodeExecutor.execute(()->this.ccGenAudiCode(carCustom, audiConfigDto));
        customSnapshotService.snapshotCarCustom(carCustom, LocalDateTime.now());
        return carCustom;
    }

    @Override
    public AudiConfigVo getAudiConfigVo(String ccid) throws Exception {
        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        if (carCustom == null){
            throw new ServiceException("400401", "参数异常：ccid", null);
        }
        AudiConfigVo brief = new AudiConfigVo();
        // 兼容老版本数据
        if (StringUtils.isNotBlank(carCustom.getModelLineId())){
            ModelParamDto modelParamDto = new ModelParamDto();
            modelParamDto.setDelFlag(0);
            modelParamDto.setModelLineId(carCustom.getModelLineId());
            modelParamDto.setChannel(Constant.MASTER_CHANNEL);
            List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
            if (lines == null || lines.size() != 1){
                throw new ServiceException("500", "服务异常", null);
            }
            ModelLineVo modelLine = lines.get(0);
            CustomSeriesDto seriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
            brief.setAccbModelCode(seriesDto.getAccbModelCode());
            brief.setAccbModelId(seriesDto.getAccbModelId());
            brief.setCustomSeriesCode(seriesDto.getCustomSeriesCode());
            brief.setCustomSeriesName(seriesDto.getCustomSeriesName());
            brief.setSeriesId(seriesDto.getSeriesId());
            brief.setSeriesCode(seriesDto.getSeriesCode());
            brief.setSeriesName(seriesDto.getSeriesName());
            brief.setModelLineId(modelLine.getModelLineId());
            brief.setModelLineName(modelLine.getModelLineName());
            brief.setModelLineCode(modelLine.getModelLineCode());
            brief.setAccbTypeCode(modelLine.getAccbTypeCode());
            brief.setAccbTypeId(modelLine.getAccbTypeId());
            brief.setModelId(modelLine.getModelId());
            brief.setModelCode(modelLine.getModelCode());
            brief.setModelYear(modelLine.getModelYear());
            brief.setModelVersion((modelLine.getVersion()));
        }
        BeanUtils.copyProperties(carCustom, brief);
        brief.setCcid(ccid);
        LambdaQueryWrapper<CarCustomOption> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CarCustomOption::getCcid, carCustom.getCcid());
        List<CarCustomOption> optionList = carCustomOptionService.list(queryWrapper);
        List<OptionBriefDto> briefDtos = new ArrayList<>();
        optionList.forEach(i->{
            OptionBriefDto briefDto = new OptionBriefDto();
            BeanUtils.copyProperties(i, briefDto);
            briefDtos.add(briefDto);
        });
        brief.setOptions(briefDtos);
        return brief;
    }

    @Override
    public CarCustomDetail getCarConfigDetailOmd(CarCustom carCustom) throws Exception {

        QueryWrapper<CarCustomOption> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ccid", carCustom.getCcid());
        List<CarCustomOption> optionList = carCustomOptionService.list(queryWrapper);
        List<String> optionCodes = optionList.stream().map(CarCustomOption::getCode).collect(Collectors.toList());
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(carCustom.getModelLineId());
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (lines == null ||  lines.size() != 1){
            throw new ServiceException("500", "服务异常", null);
        }
        ModelLineVo modelLine = lines.get(0);
        CustomSeriesDto customSeriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
        CarCustomDetail detail = new CarCustomDetail();
        if(StringUtils.isNotBlank(carCustom.getMemberId())){
            JSONObject couponRes = copOrderQueryFeign.couponInfo(String.valueOf(carCustom.getCcid()), customSeriesDto.getSeriesCode(), carCustom.getMemberId());
            if (couponRes.getJSONArray("data")!=null && couponRes.getJSONArray("data").size()>0){
                List<CouponInfoVo> couponInfoVos = couponRes.getJSONArray("data").toJavaList(CouponInfoVo.class);
                detail.setCouponNo(couponInfoVos.get(0).getMpEquityNo());
            }
        }
        detail.setValid(carCustom.getValid());
        detail.setInvalidReason(carCustom.getInvalidReason());
        detail.setUpdateFlag(carCustom.getUpdateFlag());
        detail.setUpdateContent(carCustom.getUpdateContent());
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        detail.setDepositType(carCustom.getDepositType());
        detail.setConfigTime(carCustom.getCreateTime());
        detail.setEstimateDelivery(carCustom.getEstimateDelivery());
        detail.setClassify(carCustom.getClassify());
        detail.setMstMgrpId(carCustom.getMstMgrpId());
        if (detail.getDepositType() == null){
            detail.setDepositType(DepositTypeEnum.STOCK.getType());
        }

        ConfigDetail configDetail = new ConfigDetail();
        SeriesDetail seriesDetail = new SeriesDetail();
        seriesDetail.setSeriesCode(customSeriesDto.getSeriesCode());
        seriesDetail.setSeriesNameCn(carCustom.getModelDesc());
        configDetail.setCarSeries(seriesDetail);

        ModelDetail modelDetail = new ModelDetail();
        modelDetail.setModelCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        modelDetail.setModelNameCn(carCustom.getAccbTypeDesc());
        modelDetail.setModelYear(modelLine.getModelYear());
        modelDetail.setOmdModelVersion(modelLine.getVersion());
        modelDetail.setModelPrice(modelLine.getPrice());
        modelDetail.setCustomModelCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        configDetail.setCarModel(modelDetail);

        ColorDetail insideColor = new ColorDetail();
        configDetail.setInsideColor(insideColor);
        configDetail.setOptionList(new ArrayList<>());
        ColorDetail outsideColor = new ColorDetail();
        configDetail.setOutsideColor(outsideColor);
        OptionRelateParam optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLine.getModelLineId());
        optionRelateParam.setRelateTypes(Arrays.asList("attach", "unattach"));
        List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        OptionRelateParam combineRelateParam = new OptionRelateParam();
        combineRelateParam.setRelateTypes(Arrays.asList("combine"));
        List<OptionRelDto> relCombineDtos =  optionRelateService.listOptionRelDto(combineRelateParam);
        relDtos.addAll(relCombineDtos);
        Map<String, List<OptionRelDto>> oMap = new HashMap<>();
        List<OptionRelDto> modelAttach = new ArrayList<>();
        relDtos.forEach(i->{
            if (i.getOptionId() == null && "attach".equals(i.getRelateType())){
                modelAttach.add(i);
            }else {
                List<OptionRelDto> oRel = oMap.get(i.getOptionId());
                if (oRel == null) {
                    oRel = new ArrayList<>();
                    oMap.put(i.getOptionId(), oRel);
                }
                oRel.add(i);
            }
        });
        List<Option> options = new ArrayList<>();
        List<String> optionIds = new ArrayList<>();
        boolean containRad = false;
        List<String> unattachCodes = relDtos.stream().filter(i->"unattach".equals(i.getRelateType()) && optionCodes.contains(i.getOptionCode()))
                .map(OptionRelDto::getOptionRelateCode)
                .collect(Collectors.toList());
        for(CarCustomOption i : optionList){
            OptionParamDto optionParamDto = new OptionParamDto();
            optionParamDto.setModelLineId(modelLine.getModelLineId());
            optionParamDto.setOptionId(i.getOptionId());
            optionParamDto.setChannel(Constant.MASTER_CHANNEL);
            optionParamDto.setDelFlag(0);
            List<ModelLineOptionVo> optionVos = modelLineOptionService.modelLineOptionQuery(optionParamDto);
            if (optionVos == null || optionVos.size() == 0){
                throw new ServiceException("400401", "参数异常：optionIds", null);
            }
            ModelLineOptionVo optionVo = optionVos.get(0);
            boolean discount = false;
            if (StringUtils.isNotBlank(detail.getCouponNo()) && "1".equals(optionVo.getEquipmentRights())){
                discount = true;
            }
            // 过滤选装包的件与标装
            if (optionVo.getOptionType().equals("packet-item") || (optionVo.getStatus() != null && optionVo.getStatus().intValue() == 1)){
                continue;
            }
            List<ModelLineOptionVo> items =  modelLineOptionMapper.findPaketItemFromCodes(customSeriesDto.getCustomSeriesId(), modelLine.getModelLineId(), i.getCode(), optionCodes);
            if (CollectionUtils.isNotEmpty(items)){
                continue;
            }
            optionIds.add(i.getOptionId());
            if (i.getCategory() != null && i.getCategory().equals("COLOR_EXTERIEUR")){
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                if (code.startsWith("COLOR_EXTERIEUR:")){
                    code = code.replaceFirst("COLOR_EXTERIEUR:", "");
                }
                outsideColor.setColorCode(code);
                outsideColor.setColorNameCn(i.getDescription());
                BigDecimal outsideColorPrice = priceTypeService.optionPrice(modelLine.getModelLineId(), code, i.getCategory());
                outsideColor.setPrice(outsideColorPrice);
                if (discount){
                    outsideColor.setDiscount(outsideColor.getPrice());
                }
                if (oMap.get(i.getOptionId()) != null) {
                    for (OptionRelDto relDto : oMap.get(i.getOptionId())) {
                        if (relDto.getRelateType().equals("attach")) {
                            Option optionItem = new Option();
                            optionItem.setOptionCode(relDto.getOptionRelateCode());
                            options.add(optionItem);
                        }
                    }
                }
            }else if (i.getCategory() != null && i.getCategory().equals("COLOR_INTERIEUR")){
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                if (code.startsWith("COLOR_INTERIEUR:")){
                    code = code.replaceFirst("COLOR_INTERIEUR:", "");
                }
                insideColor.setColorCode(code);
                insideColor.setColorNameCn(i.getDescription());
                if (oMap.get(i.getOptionId()) != null) {
                    for (OptionRelDto relDto : oMap.get(i.getOptionId())) {
                        if (relDto.getRelateType().equals("attach")) {
                            Option optionItem = new Option();
                            optionItem.setOptionCode(relDto.getOptionRelateCode());
                            options.add(optionItem);
                        }
                    }
                }
            }else {
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                String[] codes = code.split(":");
                if (codes.length == 1){
                    code = codes[0];
                }else if (codes.length == 2){
                    code = codes[1];
                }
                // a7l 45黑影特殊规则，给到omd切换配置线去掉选装包
                if(modelDetail.getModelCode().equals("498BZY-GPGCPGC-GWAEWAE-GYEHYEH")
                        && modelDetail.getModelYear().equals("2022") && modelDetail.getOmdModelVersion().equals("0")){
                    switch (code){
                        case "GZ2+PT1+2C7+8IZ+YEI":
                            modelDetail.setModelCode("498BZY-MESTGZ2-GPGCPGC-GPT1PT1-GWAEWAE-GYEIYEI-MLSS2C7-MHSW8IZ");
                            modelDetail.setCustomModelCode("498BZY-MESTGZ2-GPGCPGC-GPT1PT1-GWAEWAE-GYEIYEI-MLSS2C7-MHSW8IZ");
                            modelDetail.setModelPrice(priceTypeService.typePrice(new PriceCondition("498BZY-MESTGZ2-GPGCPGC-GPT1PT1-GWAEWAE-GYEIYEI-MLSS2C7-MHSW8IZ",
                                    "498BZY", "2022", "0")));
                            continue;
                        case "PCY+PT1+2V3+9VS+YEJ":
                            modelDetail.setModelCode("498BZY-GPCYPCY-GPGCPGC-GPT1PT1-GWAEWAE-GYEJYEJ-MFRI2V3-MLSE9VS");
                            modelDetail.setCustomModelCode("498BZY-GPCYPCY-GPGCPGC-GPT1PT1-GWAEWAE-GYEJYEJ-MFRI2V3-MLSE9VS");
                            modelDetail.setModelPrice(priceTypeService.typePrice(new PriceCondition("498BZY-GPCYPCY-GPGCPGC-GPT1PT1-GWAEWAE-GYEJYEJ-MFRI2V3-MLSE9VS",
                                    "498BZY", "2022", "0")));
                            continue;
                    }
                }

                // a7l 2024款 45 黑影规则
                List<String> a7lTypeChange = Arrays.asList("70acb326-11d4-4354-adac-da7ac1ea3479", "bc19d515-1ab9-4494-9297-947fba48b5d0");
                if(a7lTypeChange.contains(carCustom.getModelLineId())){
                    switch (code){
                        case "PCY+9VS+PDW+WA6+YEB":
                            String accbTypeCode = "498BZY-MREII56-GPAHPAH-GPCYPCY-GPDWPDW-GPS6PS6-GWA6WA6-GYEBYEB-MEIH5MK-MRAD53D-MLSE9VS";
                            modelDetail.setModelCode(accbTypeCode);
                            modelDetail.setCustomModelCode(accbTypeCode);
                            modelDetail.setModelPrice(priceTypeService.typePrice(new PriceCondition(accbTypeCode,
                                    "498BZY", "2024", "0")));
                            continue;
                        case "5MK":
                            continue;
                    }
                }
                Option option = new Option();
                option.setOptionNameCn(i.getDescription());
                option.setOptionCode(code);
                option.setOptionClassification(i.getCategory());
                option.setOptionClassification2nd(i.getCategory());
                boolean combine = false;
                if (oMap.get(i.getOptionId()) != null){
                    for (OptionRelDto relDto: oMap.get(i.getOptionId())) {
                        if (relDto.getRelateType().equals("combine")) {
                            Option optionItem = new Option();
                            optionItem.setOptionCode(relDto.getOptionRelateCode());
                            String itemCategory = relDto.getOptionRelateCategory() == null?"":relDto.getOptionRelateCategory();
                            optionItem.setOptionPrice(priceTypeService.optionPrice(modelLine.getModelLineId(), relDto.getOptionRelateCode(), itemCategory));
                            if(discount) {
                                optionItem.setDiscount(optionItem.getOptionPrice());
                            }
                            options.add(optionItem);
                            combine = true;
                        }else if (relDto.getRelateType().equals("attach")) {
                            Option optionItem = new Option();
                            optionItem.setOptionCode(relDto.getOptionRelateCode());
                            options.add(optionItem);
                        }
                    }
                }
                if (!combine) {
                    options.add(option);
                }
                BigDecimal optionPrice = priceTypeService.optionPrice(modelLine.getModelLineId(), code, i.getCategory());
                option.setOptionPrice(optionPrice);
                if(discount) {
                    option.setDiscount(option.getOptionPrice());
                }
            }
        }
        // 车辆自带编码
        for(OptionRelDto relDto : modelAttach){
            Option optionItem = new Option();
            optionItem.setOptionCode(relDto.getOptionRelateCode());
            options.add(optionItem);
        }
        if (CollectionUtils.isNotEmpty(unattachCodes)){
            options.removeIf(o->unattachCodes.contains(o.getOptionCode()));
        }

        PriceComputeParam priceComputeParam = new PriceComputeParam();
        priceComputeParam.setModelLineId(modelLine.getModelLineId());
        priceComputeParam.setOptionIds(optionIds);
        priceComputeParam.setCouponNo(detail.getCouponNo());
        configDetail.setTotalPrice(modelLineService.computePrice(priceComputeParam));
        configDetail.setDiscount(modelLineService.computeDiscount(priceComputeParam));
        if (modelLine.getSpecialLine().intValue() == 1){
            // 处理特殊配置线
            List<CarModelLineSpecialOption> specialOptions = specialOptionService.listModelLineSpecialOption(modelLine.getModelLineId());
            if(CollectionUtils.isEmpty(specialOptions)){
                throw new ServiceException("50000", "特殊配置线，配置单异常");
            }
            specialOptions.forEach(i->{
                Option option = new Option();
                option.setOptionCode(i.getOptionCode());
                option.setOptionClassification(i.getCategory());
                option.setOptionClassification2nd(i.getCategory());
                options.add(option);
            });
        }
        configDetail.setOptionList(options);
        detail.setConfigDetail(configDetail);
        return detail;
    }

    private boolean handlePriceControl(String modelLineId, String channel){
        LambdaQueryWrapper<CarModelLineExt> lineExtQuery = new LambdaQueryWrapper<>();
        lineExtQuery.eq(CarModelLineExt::getModelLineId, modelLineId).
                eq(CarModelLineExt::getPriceUnhandle, 1).
                eq(CarModelLineExt::getChannel, channel).
                eq(CarModelLineExt::getDelFlag, 0);
        return CollectionUtils.isNotEmpty(lineExtService.list(lineExtQuery));
    }

    @Override
    public CarCustomDetail getCarConfigDetail(String channel, CarCustom carCustom) throws Exception {
        String unHandlePrice = null;
        if (handlePriceControl(carCustom.getModelLineId(), channel)){
            unHandlePrice = Constant.PRICE_NOT_HANDLE;
        }
        QueryWrapper<CarCustomOption> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ccid", carCustom.getCcid());
        List<CarCustomOption> optionList = carCustomOptionService.list(queryWrapper);
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(carCustom.getModelLineId());
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (lines == null || lines.size() != 1){
            throw new ServiceException("500", "服务异常", null);
        }

        ModelLineVo modelLine = lines.get(0);
        CustomSeriesDto customSeriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
        CarCustomDetail detail = new CarCustomDetail();
        if(StringUtils.isNotBlank(carCustom.getMemberId())){
            JSONObject couponRes = copOrderQueryFeign.couponInfo(String.valueOf(carCustom.getCcid()), customSeriesDto.getSeriesCode(), carCustom.getMemberId());
            if (couponRes.getJSONArray("data")!=null && couponRes.getJSONArray("data").size()>0){
                List<CouponInfoVo> couponInfoVos = couponRes.getJSONArray("data").toJavaList(CouponInfoVo.class);
                detail.setCouponNo(couponInfoVos.get(0).getMpEquityNo());
            }
        }
        detail.setValid(carCustom.getValid());
        detail.setInvalidReason(carCustom.getInvalidReason());
        detail.setUpdateFlag(carCustom.getUpdateFlag());
        detail.setUpdateContent(carCustom.getUpdateContent());
        detail.setBestRecommendId(JsonString.valueOf(carCustom.getBestRecommendId()));
        detail.setCcId(JsonString.valueOf(carCustom.getCcid()));
        detail.setDepositType(carCustom.getDepositType());
        detail.setConfigTime(carCustom.getCreateTime());
        detail.setEstimateDelivery(carCustom.getEstimateDelivery());
        detail.setClassify(carCustom.getClassify());
        detail.setMstMgrpId(carCustom.getMstMgrpId());
        if (detail.getDepositType() == null){
            detail.setDepositType(DepositTypeEnum.STOCK.getType());
        }

        ConfigDetail configDetail = new ConfigDetail();
        SeriesDetail seriesDetail = new SeriesDetail();
        seriesDetail.setCustomSeriesId(customSeriesDto.getCustomSeriesId());
        seriesDetail.setSeriesCode(customSeriesDto.getSeriesCode());
        seriesDetail.setOmdSeriesCode(customSeriesDto.getOmdSeriesCode());
        seriesDetail.setSeriesNameCn(carCustom.getModelDesc());
        configDetail.setCarSeries(seriesDetail);

        ModelDetail modelDetail = new ModelDetail();
        modelDetail.setModellineId(modelLine.getModelLineId());
        modelDetail.setModelCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        modelDetail.setModelNameCn(carCustom.getAccbTypeDesc());
        modelDetail.setModelYear(modelLine.getModelYear());
        modelDetail.setOmdModelVersion(modelLine.getVersion());
        if (customSeriesDto.getCustomSeriesCode().equals("49")){
            modelDetail.setDeliveryTime(appConfig.getCc().getDelivery());
        }else if (customSeriesDto.getCustomSeriesCode().equals("G4")){
            modelDetail.setDeliveryTime(appConfig.getCc().getQ5eDelivery());
        }
        modelDetail.setModelLineCode(modelLine.getModelLineCode());
        modelDetail.setModelPrice(modelLine.getPrice());
        modelDetail.setCustomModelCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        configDetail.setCarModel(modelDetail);
        ColorDetail insideColor = new ColorDetail();
        if (carCustom.getSibInterieurId() != null) {
            SibInterieurQueryDto sibInterieurQueryDto = new SibInterieurQueryDto();
            sibInterieurQueryDto.setChannel(channel);
            sibInterieurQueryDto.setModelLineId(carCustom.getModelLineId());
            sibInterieurQueryDto.setSibInterieurId(carCustom.getSibInterieurId());
            sibInterieurQueryDto.setDelFlag(0);
            List<ModelLineSibInterieurVo> sibInterieurs = modelLineSibInterieurService.modelLineSibInterieur(sibInterieurQueryDto);
            ModelLineSibInterieurVo sibInterieur = sibInterieurs.get(0);
            insideColor.setColorNameCn(sibInterieur.getDescription());
            insideColor.setColorCode(sibInterieur.getSibInterieurCode());
            insideColor.setImageUrl(sibInterieur.getImageUrl());
            insideColor.setPrice(sibInterieur.getPrice());
        }
        configDetail.setInsideColor(insideColor);
        configDetail.setOptionList(new ArrayList<>());
        ColorDetail outsideColor = new ColorDetail();
        configDetail.setOutsideColor(outsideColor);

        List<Option> options = new ArrayList<>();
        String exterieurCode = null;
        String radCode = null;
        List<String> optionIds = new ArrayList<>();
        for(CarCustomOption i : optionList){
            optionIds.add(i.getOptionId());
            OptionParamDto optionParam = new OptionParamDto();
            optionParam.setModelLineId(modelLine.getModelLineId());
            optionParam.setDelFlag(0);
            optionParam.setChannel(channel);
            optionParam.setOptionId(i.getOptionId());
            List<ModelLineOptionVo> optionVos = modelLineOptionService.modelLineOptionQuery(optionParam);
            String imageUrlList = null;
            String imageUrl = null;
            boolean discount = false;
            if (!CollectionUtils.isEmpty(optionVos)){
                for(ModelLineOptionVo o : optionVos){
                    if (o.getChannel().equals(channel)){
                        imageUrlList = o.getImageUrlList();
                        imageUrl = o.getImageUrl();
                    }
                }
                if (optionVos.size() == 1){
                    imageUrlList = optionVos.get(0).getImageUrlList();
                    imageUrl = optionVos.get(0).getImageUrl();
                    if(StringUtils.isNotBlank(detail.getCouponNo()) && "1".equals(optionVos.get(0).getEquipmentRights())){
                        discount = true;
                    }
                }
            }
            if (i.getCategory() != null && i.getCategory().equals("COLOR_EXTERIEUR")){
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                if (code.startsWith("COLOR_EXTERIEUR:")){
                    code = code.replaceFirst("COLOR_EXTERIEUR:", "");
                }
                exterieurCode = code;
                outsideColor.setColorCode(code);
                outsideColor.setColorNameCn(i.getDescription());
                outsideColor.setImageUrl(imageUrl);
                BigDecimal outsideColorPrice = priceTypeService.optionPrice(modelLine.getModelLineId(), code, i.getCategory());
                outsideColor.setPrice(outsideColorPrice);
                if (unHandlePrice != null){
                    outsideColor.setPrice(unHandlePrice);
                }
                if (discount){
                    outsideColor.setDiscount(outsideColor.getPrice());
                }
            }else if (i.getCategory()!= null && (i.getCategory().equals("COLOR_INTERIEUR") || i.getCategory().equals("SIB"))){
                continue;
            }else {
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                String[] codes = code.split(":");
                if (codes.length == 1){
                    code = codes[0];
                }else if (codes.length == 2){
                    code = codes[1];
                }
                if (OptionCategoryEnum.WHEEL.getValue().equals(i.getCategory())){
                    radCode = code;
                }
                Option option = new Option();
                option.setOptionNameCn(i.getDescription());
                option.setOptionCode(code);
                option.setOptionClassification(i.getCategory());
                option.setOptionClassification2nd(i.getCategory());
                option.setImageUrl(imageUrlList);
                options.add(option);
                BigDecimal optionPrice = priceTypeService.optionPrice(modelLine.getModelLineId(), code, i.getCategory());
                option.setOptionPrice(optionPrice);
                if (unHandlePrice != null){
                    option.setOptionPrice(unHandlePrice);
                }
                if (discount){
                    option.setDiscount(option.getOptionPrice());
                }
            }
        }
        // 查询默认轮毂
        if (radCode == null){
            ModelLineOptionVo optionVo = new ModelLineOptionVo();
            optionVo.setModelLineId(modelLine.getModelLineId());
            optionVo.setStatus(1);
            optionVo.setCategory("RAD");
            optionVo.setDelFlag(0);
            List<ModelLineOptionVo> optionVos = modelLineOptionService.listModelLineOption(optionVo);
            if (CollectionUtils.isEmpty(optionVos)){
                throw new ServiceException("50001", "数据异常");
            }
            radCode = optionVos.get(0).getOptionCode();
        }
        modelDetail.setHeadImageUrl(configImageService.getCcHeadImageUrl(channel, carCustom.getModelLineId(), exterieurCode));
        modelDetail.setImageUrl(configImageService.getCcLeftImageUrl(channel, carCustom.getModelLineId(), exterieurCode, radCode));
        PriceComputeParam priceComputeParam = new PriceComputeParam();
        priceComputeParam.setModelLineId(modelLine.getModelLineId());
        priceComputeParam.setOptionIds(optionIds);
        priceComputeParam.setCustomSeriesId(customSeriesDto.getCustomSeriesId());
        priceComputeParam.setCouponNo(detail.getCouponNo());
        configDetail.setTotalPrice(modelLineService.computePrice(priceComputeParam));
        configDetail.setDiscount(modelLineService.computeDiscount(priceComputeParam));
        configDetail.setOptionList(options);
        detail.setConfigDetail(configDetail);
        if (carCustom.getMeasureId() != null){
            detail.setMeasureId(JsonString.valueOf(carCustom.getMeasureId()));
        }
        return detail;
    }

    @Override
    public CustomSeriesDto getCustomSeriesInfo(String channel, CarCustom carCustom) throws Exception{
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(carCustom.getModelLineId());
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (lines == null || lines.size() != 1){
            throw new ServiceException("500", "服务异常", null);
        }
        ModelLineVo modelLine = lines.get(0);
        CustomSeriesDto customSeriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
        return customSeriesDto;
    }


    @Override
    public CarCustomVo getCarCustom(CarCustom carCustom) throws Exception {
        CarCustomVo vo = new CarCustomVo();
        vo.setValid(carCustom.getValid());
        vo.setInvalidReason(carCustom.getInvalidReason());
        vo.setUpdateFlag(carCustom.getUpdateFlag());
        vo.setUpdateContent(carCustom.getUpdateContent());
        vo.setConfigTime(carCustom.getCreateTime());
        QueryWrapper<CarCustomOption> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ccid", carCustom.getCcid());
        List<CarCustomOption> optionList = carCustomOptionService.list(queryWrapper);
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(carCustom.getModelLineId());
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (lines == null || lines.size() != 1){
            throw new ServiceException("500", "服务异常", null);
        }
        ModelLineVo modelLine = lines.get(0);
        vo.setCustomSeriesId(modelLine.getCustomSeriesId());
        vo.setModelLineId(modelLine.getModelLineId());
        vo.setSibInterieurId(carCustom.getSibInterieurId());
        vo.setCcid(String.valueOf(carCustom.getCcid()));
        vo.setModelLinePrice(modelLine.getPrice());
        vo.setEstimateDelivery(carCustom.getEstimateDelivery());
        vo.setClassify(carCustom.getClassify());
        List<String> optionIds = new ArrayList<>();
        vo.setCustomOptionVoList(optionList.stream().map(i->{
            optionIds.add(i.getOptionId());
            if ((i.getCategory().equals("SIB") || i.getCategory().equals("COLOR_INTERIEUR")) && StringUtils.isNotBlank(carCustom.getSibInterieurId())){
                return null;
            }
            CarCustomOptionVo customOptionVo = new CarCustomOptionVo();
            customOptionVo.setOptionId(i.getOptionId());
            customOptionVo.setOptionCode(i.getCode());
            customOptionVo.setCategory(i.getCategory());
            BigDecimal optionPrice = null;
            try {
                optionPrice = priceTypeService.optionPrice(carCustom.getModelLineId(), i.getCode(), i.getCategory());
            } catch (ServiceException e) {
                log.warn("配置项价格异常", e);
            }
            customOptionVo.setPrice(optionPrice);
            return customOptionVo;
        }).filter(i->i != null).collect(Collectors.toList()));
        if (StringUtils.isNotBlank(carCustom.getSibInterieurId())){
            LambdaQueryWrapper<CarSibInterieur> sibInterQ = new LambdaQueryWrapper<>();
            sibInterQ.eq(CarSibInterieur::getSibInterieurId, carCustom.getSibInterieurId())
                    .eq(CarSibInterieur::getChannel, Constant.MASTER_CHANNEL)
                    .eq(CarSibInterieur::getDelFlag, 0);
            CarSibInterieur sibInterieur = sibInterieurService.getOne(sibInterQ);
            BigDecimal optionPrice = priceTypeService.optionPrice(carCustom.getModelLineId(), sibInterieur.getSibOptionCode(), sibInterieur.getSibOptionCode());
            vo.setSibInterieurPrice(optionPrice);
        }
        Set<String> categories = vo.getCustomOptionVoList().stream().map(CarCustomOptionVo::getCategory).collect(Collectors.toSet());
        if (!categories.contains(OptionCategoryEnum.WHEEL.getValue())){
            // 标装轮毂
            List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(Constant.MASTER_CHANNEL, modelLine.getModelLineId(), OptionCategoryEnum.WHEEL.getValue());
            for (ModelLineOptionVo optionVo : optionVos){
                if (optionVo.getStatus() != null && optionVo.getStatus().intValue() == 1){
                    CarCustomOptionVo customOptionVo = new CarCustomOptionVo();
                    customOptionVo.setOptionId(optionVo.getOptionId());
                    customOptionVo.setOptionCode(optionVo.getOptionCode());
                    customOptionVo.setCategory(optionVo.getCategory());
                    customOptionVo.setPrice(BigDecimal.ZERO);
                    vo.getCustomOptionVoList().add(customOptionVo);
                    break;
                }
            }
        }
        PriceComputeParam priceComputeParam = new PriceComputeParam();
        priceComputeParam.setModelLineId(modelLine.getModelLineId());
        priceComputeParam.setOptionIds(optionIds);
        vo.setTotalPrice(modelLineService.computePrice(priceComputeParam));
        return vo;
    }

    @Override
    public String refreshCcid(CarCustom carCustom) throws Exception {
        String oldCcid = String.valueOf(carCustom.getCcid());
        Long ccid = carCustom.getCcid();
        carCustom.setCcid(null);
        carCustom.insert();
        log.info("refresh ccid: {} , new ccid: {}", oldCcid, carCustom.getCcid() );
        LambdaQueryWrapper<CarCustomOption> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CarCustomOption::getCcid, ccid);
        List<CarCustomOption> options = carCustomOptionService.list(queryWrapper);
        Long newCcid = carCustom.getCcid();
        if (options != null){
            options.forEach(i->{
                i.setCcid(newCcid);
                i.setCustomOptionId(null);
                i.insert();
            });
        }
        customSnapshotService.snapshotFromOld(ccid, newCcid);
        return String.valueOf(newCcid);
    }

    @Override
    public CarCustom addCarCustomConfig(String userId,String userMobile,String customColorId) {

        AudiConfigDto audiConfigDto = new AudiConfigDto();
        audiConfigDto.setModelId("f48a0f2e-124c-5364-9601-a47bc80e85b1");
        audiConfigDto.setModelCode("MODEL:50805_35636");
        audiConfigDto.setModelDesc("Audi A7 L CKD");
        audiConfigDto.setTypeId("55473782-583e-5d95-a8fc-3d69acffc7e3");
        audiConfigDto.setTypeCode("TYPE:498B2Y-GPAHPAH-GPDWPDW-GPF3PF3-GPS6PS6-GWA6WA6-MEIH5MD-MRAD53D-MBBO6FJ-MHSW8IZ-MLSE9VS");
        audiConfigDto.setTypeDesc("A7L edition one");
        audiConfigDto.setModelYear("2022");
        audiConfigDto.setModelVersion("0");
        LambdaQueryWrapper<CarModelLine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CarModelLine::getAccbTypeCode, audiConfigDto.getTypeCode()).eq(CarModelLine::getDelFlag, 0).eq(CarModelLine::getChannel, Constant.MASTER_CHANNEL);
        List<CarModelLine> modelLines = modelLineService.list(queryWrapper);
        CarCustom carCustom = new CarCustom();
        Map<String, String> colorMap = new HashMap<>();

        if(!CollectionUtils.isEmpty(modelLines)) {
            LambdaQueryWrapper<CarSibInterieur> sibInQ = new LambdaQueryWrapper<>();
            sibInQ.eq(CarSibInterieur::getInterieurOptionCode, "RI").eq(CarSibInterieur::getSibOptionCode, "N2R").
                    eq(CarSibInterieur::getChannel, Constant.MASTER_CHANNEL).eq(CarSibInterieur::getDelFlag, 0);
            List<CarSibInterieur> sibInterieurs = sibInterieurService.list(sibInQ);
            if (!CollectionUtils.isEmpty(sibInterieurs)){
                carCustom.setSibInterieurId(sibInterieurs.get(0).getSibInterieurId());
            }
            carCustom.setModelLineId(modelLines.get(0).getModelLineId());
            LambdaQueryWrapper<CarOption> carOptionQ = new LambdaQueryWrapper<>();
            carOptionQ.eq(CarOption::getChannel, Constant.MASTER_CHANNEL)
                    .in(CarOption::getCategory, "COLOR_EXTERIEUR", "COLOR_INTERIEUR")
                    .eq(CarOption::getDelFlag, 0).eq(CarOption::getCustomSeriesId, modelLines.get(0).getCustomSeriesId());
            List<CarOption> carOptions = optionService.list(carOptionQ);
            if (carOptions != null){
                for (CarOption option: carOptions){
                    colorMap.put(option.getOptionCode(), option.getOptionId());
                }
            }
        }
        List<OptionBriefDto> optionBriefDtoList = new ArrayList<>();
        if (customColorId.equals("COLOR_EXTERIEUR:3ZA2")){
            optionBriefDtoList.add(new OptionBriefDto("COLOR_EXTERIEUR:3ZA2", "COLOR_EXTERIEUR","青山黛（edition one限定色）", colorMap.get("3ZA2")));
        }else if (customColorId.equals("COLOR_EXTERIEUR:B9A2")){
            optionBriefDtoList.add(new OptionBriefDto("COLOR_EXTERIEUR:B9A2", "COLOR_EXTERIEUR","星河蓝", colorMap.get("B9A2")));
        }
        optionBriefDtoList.add(new OptionBriefDto("COLOR_INTERIEUR:RI", "COLOR_INTERIEUR","高定琥珀棕", colorMap.get("RI")));
        String audiCode = iaccbService.genAudiCode(audiConfigDto);
        carCustom.setAccbModelId("f48a0f2e-124c-5364-9601-a47bc80e85b1");
        carCustom.setAccbModelCode("MODEL:50805_35636");
        carCustom.setModelDesc("Audi A7 L CKD");
        carCustom.setAccbTypeId("55473782-583e-5d95-a8fc-3d69acffc7e3");
        carCustom.setAccbTypeCode("TYPE:498B2Y-GPAHPAH-GPDWPDW-GPF3PF3-GPS6PS6-GWA6WA6-MEIH5MD-MRAD53D-MBBO6FJ-MHSW8IZ-MLSE9VS");
        carCustom.setAccbTypeDesc("A7L edition one");
        carCustom.setModelYear("2022");
        carCustom.setAudiCode(audiCode);
        carCustom.setUserId(userId);
        carCustom.setUserMobile(userMobile);
        carCustom.setCreateTime(LocalDateTime.now());
        carCustom.setCreateUser(userId);


        carCustom.insert();
        optionBriefDtoList.forEach(i->{
            CarCustomOption carCustomOption = new CarCustomOption();
            BeanUtils.copyProperties(i, carCustomOption);
            carCustomOption.setCcid(carCustom.getCcid());
            carCustomOption.insert();
        });
        return carCustom;
    }



    @Override
    public CarCustom updateCarCustomConfig(String userId, String userMobile, String customColorId, String ccid) {
        AudiConfigDto audiConfigDto = new AudiConfigDto();
        audiConfigDto.setModelId("f48a0f2e-124c-5364-9601-a47bc80e85b1");
        audiConfigDto.setModelCode("MODEL:50805_35636");
        audiConfigDto.setModelDesc("Audi A7 L CKD");
        audiConfigDto.setTypeId("55473782-583e-5d95-a8fc-3d69acffc7e3");
        audiConfigDto.setTypeCode("TYPE:498B2Y-GPAHPAH-GPDWPDW-GPF3PF3-GPS6PS6-GWA6WA6-MEIH5MD-MRAD53D-MBBO6FJ-MHSW8IZ-MLSE9VS");
        audiConfigDto.setTypeDesc("A7L edition one");
        audiConfigDto.setModelYear("2022");
        audiConfigDto.setModelVersion("0");
        LambdaQueryWrapper<CarModelLine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CarModelLine::getAccbTypeCode, audiConfigDto.getTypeCode()).eq(CarModelLine::getDelFlag, 0).eq(CarModelLine::getChannel, Constant.MASTER_CHANNEL);
        List<CarModelLine> modelLines = modelLineService.list(queryWrapper);
        CarCustom carCustom = new CarCustom();
        Map<String, String> colorMap = new HashMap<>();

        if(!CollectionUtils.isEmpty(modelLines)) {
            LambdaQueryWrapper<CarSibInterieur> sibInQ = new LambdaQueryWrapper<>();
            sibInQ.eq(CarSibInterieur::getInterieurOptionCode, "RI").eq(CarSibInterieur::getSibOptionCode, "N2R").
                    eq(CarSibInterieur::getChannel, Constant.MASTER_CHANNEL).eq(CarSibInterieur::getDelFlag, 0);
            List<CarSibInterieur> sibInterieurs = sibInterieurService.list(sibInQ);
            if (!CollectionUtils.isEmpty(sibInterieurs)){
                carCustom.setSibInterieurId(sibInterieurs.get(0).getSibInterieurId());
            }
            carCustom.setModelLineId(modelLines.get(0).getModelLineId());
            LambdaQueryWrapper<CarOption> carOptionQ = new LambdaQueryWrapper<>();
            carOptionQ.eq(CarOption::getChannel, Constant.MASTER_CHANNEL)
                    .in(CarOption::getCategory, "COLOR_EXTERIEUR", "COLOR_INTERIEUR")
                    .eq(CarOption::getDelFlag, 0).eq(CarOption::getCustomSeriesId, modelLines.get(0).getCustomSeriesId());
            List<CarOption> carOptions = optionService.list(carOptionQ);
            if (carOptions != null){
                for (CarOption option: carOptions){
                    colorMap.put(option.getOptionCode(), option.getOptionId());
                }
            }
        }
        List<OptionBriefDto> optionBriefDtoList = new ArrayList<>();
        if (customColorId.equals("COLOR_EXTERIEUR:3ZA2")){
            optionBriefDtoList.add(new OptionBriefDto("COLOR_EXTERIEUR:3ZA2", "COLOR_EXTERIEUR","青山黛（edition one限定色）", colorMap.get("3ZA2")));
        }else if (customColorId.equals("COLOR_EXTERIEUR:B9A2")){
            optionBriefDtoList.add(new OptionBriefDto("COLOR_EXTERIEUR:B9A2", "COLOR_EXTERIEUR","星河蓝", colorMap.get("B9A2")));
        }
        optionBriefDtoList.add(new OptionBriefDto("COLOR_INTERIEUR:RI", "COLOR_INTERIEUR","高定琥珀棕", colorMap.get("RI")));
        String audiCode = iaccbService.genAudiCode(audiConfigDto);
        carCustom.setAccbModelId("f48a0f2e-124c-5364-9601-a47bc80e85b1");
        carCustom.setAccbModelCode("MODEL:50805_35636");
        carCustom.setModelDesc("Audi A7 L CKD");
        carCustom.setAccbTypeId("55473782-583e-5d95-a8fc-3d69acffc7e3");
        carCustom.setAccbTypeCode("TYPE:498B2Y-GPAHPAH-GPDWPDW-GPF3PF3-GPS6PS6-GWA6WA6-MEIH5MD-MRAD53D-MBBO6FJ-MHSW8IZ-MLSE9VS");
        carCustom.setAccbTypeDesc("A7L edition one");
        carCustom.setModelYear("2022");
        carCustom.setAudiCode(audiCode);
        carCustom.setUserId(userId);
        carCustom.setUserMobile(userMobile);
        carCustom.setCreateTime(LocalDateTime.now());
        carCustom.setCreateUser(userId);
        carCustom.setCcid(Long.valueOf(ccid));
        carCustom.updateById();
        LambdaQueryWrapper<CarCustomOption> optionQ = new LambdaQueryWrapper<>();
        optionQ.eq(CarCustomOption::getCcid, ccid);
        carCustomOptionService.remove(optionQ);
        optionBriefDtoList.forEach(i->{
            CarCustomOption carCustomOption = new CarCustomOption();
            BeanUtils.copyProperties(i, carCustomOption);
            carCustomOption.setCcid(carCustom.getCcid());
            carCustomOption.insert();
        });
        return carCustom;
    }

    private void ccGenAudiCode(CarCustom carCustom, AudiConfigDto audiConfigDto){
        String audiCode = iaccbService.genAudiCode(audiConfigDto);
        LambdaUpdateWrapper<CarCustom> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CarCustom::getCcid, carCustom.getCcid()).set(CarCustom::getAudiCode, audiCode);
        carCustomService.update(updateWrapper);
    }

    private void validCarModelLineCustom(CarCustomDto carCustomDto) throws ServiceException {
        // valid 配置线和配置项
        LambdaQueryWrapper<CarModelLine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CarModelLine::getModelLineId, carCustomDto.getModelLineId())
                .eq(CarModelLine::getChannel, Constant.MASTER_CHANNEL).eq(CarModelLine::getDelFlag, 0);
        CarModelLine line = modelLineService.getOne(queryWrapper);
        if (line == null){
            throw new ServiceException("400401", "参数异常：modelLineId", null);
        }
        List<ModelLineOptionVo> options = new ArrayList<>();
        ModelLineOptionVo sibOption = null, radOption = null, vosOption = null;
        List<ModelLineOptionVo> nStatusOptions = new ArrayList<>();
        // 内外饰必须，确定其他配置项可选
        if (!CollectionUtils.isEmpty(carCustomDto.getOptionIds())){
            boolean hasExterieur=false, hasInterieur=false;
            for (String optionId: carCustomDto.getOptionIds()){
                OptionParamDto optionParamDto = new OptionParamDto();
                optionParamDto.setModelLineId(carCustomDto.getModelLineId());
                optionParamDto.setOptionId(optionId);
                optionParamDto.setChannel(Constant.MASTER_CHANNEL);
                optionParamDto.setDelFlag(0);
                List<ModelLineOptionVo> optionVos = modelLineOptionService.modelLineOptionQueryStrict(optionParamDto);
                if (optionVos == null || optionVos.size() == 0){
                    throw new ServiceException("400401", "参数异常：optionIds", "配置线选项异常"+JSONObject.toJSONString(optionVos));
                }
                ModelLineOptionVo optionVo = optionVos.get(0);
                if (StringUtils.isNotBlank(optionVo.getCategory())) {
                    if (optionVo.getCategory().equals(OptionCategoryEnum.OUTCOLOR.getValue())) {
                        hasExterieur = true;
                    } else if (optionVo.getCategory().equals(OptionCategoryEnum.INCOLOR.getValue())) {
                        hasInterieur = true;
                    } else if (OptionCategoryEnum.SIB.getValue().equals(optionVo.getCategory())){
                        sibOption = optionVo;
                    } else if (OptionCategoryEnum.SEET.getValue().equals(optionVo.getCategory())){
                        vosOption = optionVo;
                    } else if (OptionCategoryEnum.WHEEL.getValue().equals(optionVo.getCategory())){
                        radOption = optionVo;
                    }
                }
                if (optionVo.getStatus() == null || optionVo.getStatus().intValue() == 0 || "packet-item".equals(optionVo.getOptionType())){
                    nStatusOptions.add(optionVo);
                }
                options.add(optionVo);
            }
            if (!(hasExterieur && hasInterieur)){
                throw new ServiceException("400401", "参数异常：optionIds", "没有内饰颜色/外饰颜色");
            }
        }else {
            throw new ServiceException("400401", "参数异常：optionIds", "没有选配");
        }
        List<CarOptionRelate> conflicts = optionRelateService.listConflictOptionRelate(carCustomDto.getModelLineId(), carCustomDto.getOptionIds());
        if(CollectionUtils.isNotEmpty(conflicts)){
            log.error("新增/修改配置存在冲突, modelLineId: {} , 冲突关系：{}", carCustomDto.getModelLineId(), conflicts);
            throw new ServiceException("400401", "参数异常：optionIds", "配置存在冲突");
        }

        Boolean dependValid =
                optionRelateService.validDepends(carCustomDto.getModelLineId(), carCustomDto.getOptionIds());

        if(!dependValid){
            throw new ServiceException("400401", "参数异常：optionIds", "依赖关系不满足");
        }

        // 校验选装包中的内容，是否包含轮毂，座椅，面料。然后校验是否与参数中的一致
        List<CarOption> items = optionService.listPacketSpecialItem(carCustomDto.getOptionIds(), carCustomDto.getModelLineId());
        if (CollectionUtils.isNotEmpty(items)){
            for (CarOption item: items){
                if (OptionCategoryEnum.WHEEL.getValue().equals(item.getCategory())){
                    if (radOption != null && !radOption.getOptionCode().equals(item.getOptionCode())){
                        log.error("轮毂选项与选装包中的轮毂不一致");
                        throw new ServiceException("400401", "参数异常：optionIds", "轮毂选项与选装包中的轮毂不一致");
                    }
                }else if (OptionCategoryEnum.SEET.getValue().equals(item.getCategory())){
                    if (vosOption != null && !vosOption.getOptionCode().equals(item.getOptionCode())){
                        log.error("座椅选项与选装包中的座椅不一致");
                        throw new ServiceException("400401", "参数异常：optionIds", "座椅选项与选装包中的座椅不一致");
                    }
                }else if (OptionCategoryEnum.SIB.getValue().equals(item.getCategory())){
                    if (sibOption != null && !sibOption.getOptionCode().equals(item.getOptionCode())){
                        log.error("面料选项与选装包中的面料不一致");
                        throw new ServiceException("400401", "参数异常：optionIds", "面料选项与选装包中的面料不一致");
                    }
                }
            }
        }
        // 不可选的配置，判断是否在依赖中，或者在选配的选装包中
        if (CollectionUtils.isNotEmpty(nStatusOptions)) {
            for (ModelLineOptionVo nStatusOption : nStatusOptions){
                List<ModelLineOptionVo> nStatusItems = modelLineOptionService.listModelLinePacketItemByCode(carCustomDto.getModelLineId(), nStatusOption.getOptionCode(), carCustomDto.getOptionIds());
                if (CollectionUtils.isNotEmpty(nStatusItems)){
                    if(nStatusItems.stream().filter(i->i.getStatus() != null && i.getStatus() != 0)
                            .collect(Collectors.toList()).size()>0) {
                        continue;
                    }
                }
                if ("packet-item".equals(nStatusOption.getOptionType())){
                    throw new ServiceException("400401", "参数异常：optionIds", "座椅是选装包中的，但是没有选配选装包");
                }
                List<CarOptionRelate> depends = optionRelateService.listDependedByOptionId(carCustomDto.getModelLineId(), nStatusOption.getOptionId(), carCustomDto.getOptionIds());
                if (CollectionUtils.isEmpty(depends)){
                    throw new ServiceException("400401", "参数异常：optionIds", "存在无规则不可选的选装");
                }
            }
        }
        // 内饰面料+内饰颜色 依赖处理
        LambdaQueryWrapper<CarOptionRelate> inSibRelateQ = new LambdaQueryWrapper<>();
        inSibRelateQ.eq(CarOptionRelate::getOptionId, carCustomDto.getSibInterieurId())
                .eq(CarOptionRelate::getRelateType, OptionRelateEnum.DEPEND.getValue())
                .eq(CarOptionRelate::getModelLineId, carCustomDto.getModelLineId());
        int relatesNum = optionRelateService.count(inSibRelateQ);
        List<CarOptionRelate> depends = optionRelateService.listSibInterieurDependedByOptionId(carCustomDto.getModelLineId(), carCustomDto.getSibInterieurId(), carCustomDto.getOptionIds());
        if (relatesNum>0 && CollectionUtils.isEmpty(depends)){
            throw new ServiceException("400401", "参数异常：sibInterieurId", "存在无规则不可选的选装");
        }


        // todo valid选装包冲突
//        checkCustom(line, options);

    }

    private void validInternalCarModelLineCustom(CarCustomDto carCustomDto) throws ServiceException {
        // valid 配置线和配置项
        LambdaQueryWrapper<CarModelLine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CarModelLine::getModelLineId, carCustomDto.getModelLineId())
                .eq(CarModelLine::getChannel, Constant.MASTER_CHANNEL).eq(CarModelLine::getDelFlag, 0);
        CarModelLine line = modelLineService.getOne(queryWrapper);
        if (line == null){
            throw new ServiceException("400401", "参数异常：modelLineId", null);
        }
        List<ModelLineOptionVo> options = new ArrayList<>();
        ModelLineOptionVo sibOption = null, radOption = null, vosOption = null;
        List<ModelLineOptionVo> nStatusOptions = new ArrayList<>();
        // 内外饰必须，确定其他配置项可选
        if (!CollectionUtils.isEmpty(carCustomDto.getOptionIds())){
            boolean hasExterieur=false, hasInterieur=false;
            for (String optionId: carCustomDto.getOptionIds()){
                OptionParamDto optionParamDto = new OptionParamDto();
                optionParamDto.setModelLineId(carCustomDto.getModelLineId());
                optionParamDto.setOptionId(optionId);
                optionParamDto.setChannel(Constant.MASTER_CHANNEL);
                optionParamDto.setDelFlag(0);
                List<ModelLineOptionVo> optionVos = modelLineOptionService.modelLineOptionQuery(optionParamDto);
                if (optionVos == null || optionVos.size() == 0){
                    throw new ServiceException("400401", "参数异常：optionIds", "配置线选项异常"+JSONObject.toJSONString(optionVos));
                }
                ModelLineOptionVo optionVo = optionVos.get(0);
                if (StringUtils.isNotBlank(optionVo.getCategory())) {
                    if (optionVo.getCategory().equals(OptionCategoryEnum.OUTCOLOR.getValue())
                            && optionVo.getCondition() != null && optionVo.getCondition().intValue() != 0) {
                        hasExterieur = true;
                    } else if (optionVo.getCategory().equals(OptionCategoryEnum.INCOLOR.getValue())) {
                        hasInterieur = true;
                    } else if (OptionCategoryEnum.SIB.getValue().equals(optionVo.getCategory())){
                        sibOption = optionVo;
                    } else if (OptionCategoryEnum.SEET.getValue().equals(optionVo.getCategory())){
                        vosOption = optionVo;
                    } else if (OptionCategoryEnum.WHEEL.getValue().equals(optionVo.getCategory())){
                        radOption = optionVo;
                    }
                }
                if (optionVo.getStatus() == null || optionVo.getStatus().intValue() == 0 || "packet-item".equals(optionVo.getOptionType())){
                    nStatusOptions.add(optionVo);
                }
                options.add(optionVo);
            }
            if (!(hasExterieur && hasInterieur)){
                throw new ServiceException("400401", "参数异常：optionIds", "没有内饰颜色/外饰颜色");
            }
        }else {
            throw new ServiceException("400401", "参数异常：optionIds", "没有选配");
        }
        List<CarOptionRelate> conflicts = optionRelateService.listConflictOptionRelate(carCustomDto.getModelLineId(), carCustomDto.getOptionIds());
        if(CollectionUtils.isNotEmpty(conflicts)){
            log.error("新增/修改配置存在冲突, modelLineId: {} , 冲突关系：{}", carCustomDto.getModelLineId(), conflicts);
            throw new ServiceException("400401", "参数异常：optionIds", "配置存在冲突");
        }

        Boolean dependValid =
                optionRelateService.validDepends(carCustomDto.getModelLineId(), carCustomDto.getOptionIds());

        if(!dependValid){
            throw new ServiceException("400401", "参数异常：optionIds", "依赖关系不满足");
        }

        // 校验选装包中的内容，是否包含轮毂，座椅，面料。然后校验是否与参数中的一致
        List<CarOption> items = optionService.listPacketSpecialItem(carCustomDto.getOptionIds(), carCustomDto.getModelLineId());
        if (CollectionUtils.isNotEmpty(items)){
            for (CarOption item: items){
                if (OptionCategoryEnum.WHEEL.getValue().equals(item.getCategory())){
                    if (radOption != null && !radOption.getOptionCode().equals(item.getOptionCode())){
                        log.error("轮毂选项与选装包中的轮毂不一致");
                        throw new ServiceException("400401", "参数异常：optionIds", "轮毂选项与选装包中的轮毂不一致");
                    }
                }else if (OptionCategoryEnum.SEET.getValue().equals(item.getCategory())){
                    if (vosOption != null && !vosOption.getOptionCode().equals(item.getOptionCode())){
                        log.error("座椅选项与选装包中的座椅不一致");
                        throw new ServiceException("400401", "参数异常：optionIds", "座椅选项与选装包中的座椅不一致");
                    }
                }else if (OptionCategoryEnum.SIB.getValue().equals(item.getCategory())){
                    if (sibOption != null && !sibOption.getOptionCode().equals(item.getOptionCode())){
                        log.error("面料选项与选装包中的面料不一致");
                        throw new ServiceException("400401", "参数异常：optionIds", "面料选项与选装包中的面料不一致");
                    }
                }
            }
        }
        // 内部允许不可选装备选择
        // 不可选的配置，判断是否在依赖中，或者在选配的选装包中
        /*if (CollectionUtils.isNotEmpty(nStatusOptions)) {
            for (ModelLineOptionVo nStatusOption : nStatusOptions){
                List<ModelLineOptionVo> nStatusItems = modelLineOptionService.listModelLinePacketItemByCode(carCustomDto.getModelLineId(), nStatusOption.getOptionCode(), carCustomDto.getOptionIds());
                if (CollectionUtils.isNotEmpty(nStatusItems)){
                    if(nStatusItems.stream().filter(i->i.getStatus() != null && i.getStatus() != 0)
                            .collect(Collectors.toList()).size()>0) {
                        continue;
                    }
                }
                if ("packet-item".equals(nStatusOption.getOptionType())){
                    throw new ServiceException("400401", "参数异常：optionIds", "座椅是选装包中的，但是没有选配选装包");
                }
                List<CarOptionRelate> depends = optionRelateService.listDependedByOptionId(carCustomDto.getModelLineId(), nStatusOption.getOptionId(), carCustomDto.getOptionIds());
                if (CollectionUtils.isEmpty(depends)){
                    throw new ServiceException("400401", "参数异常：optionIds", "存在无规则不可选的选装");
                }
            }
        }*/

    }

    @Override
    public CustomCheckDto checkCustom(CarModelLine modelLine, List<ModelLineOptionVo> options) throws ServiceException {
        String modelLineId = modelLine.getModelLineId();
        CustomCheckDto checkDto = new CustomCheckDto();
        checkDto.setOptionParams(options);
        checkDto.setCustomModelLineId(modelLineId);
        List<ModelLineOptionVo> standardOptions = new ArrayList<>(), optionalOptions = new ArrayList<>(),
                notOptionalOptions = new ArrayList<>(), packageOptionItems = new ArrayList<>(), dependOptions = new ArrayList<>();
        checkDto.setStandardOptions(standardOptions);
        checkDto.setOptionalOptions(optionalOptions);
        checkDto.setNotOptionalOptions(notOptionalOptions);
        checkDto.setPacketOptionItems(packageOptionItems);
        checkDto.setDependOptions(dependOptions);

        Map<String, List<ModelLineOptionVo>> dependMap = new HashMap<>(), conflictMap= new HashMap<>(), packetOptionItemMap= new HashMap<>();
        Map<String, List<String>> combineMap= new HashMap<>(), attachMap= new HashMap<>();
        checkDto.setDependMap(dependMap);
        checkDto.setConflictMap(conflictMap);
        checkDto.setCombineMap(combineMap);
        checkDto.setAttachMap(attachMap);
        checkDto.setPacketOptionItemMap(packetOptionItemMap);
        for(ModelLineOptionVo optionVo : options){
            if (optionVo.getStatus().intValue() == 0){
                notOptionalOptions.add(optionVo);
            }else if (optionVo.getStatus().intValue() == 1){
                standardOptions.add(optionVo);
            }else if (optionVo.getStatus().intValue() == 2){
                optionalOptions.add(optionVo);
            }
            if (OptionCategoryEnum.PACKET.getValue().equals(optionVo.getCategory())){
                OptionParamDto paramDto = new OptionParamDto();
                paramDto.setOptionId(optionVo.getOptionId());
                paramDto.setModelLineId(modelLineId);
                paramDto.setChannel(Constant.MASTER_CHANNEL);
                paramDto.setDelFlag(0);
                List<ModelLineOptionVo> optionItems = modelLineOptionService.listModelLinePacketItem(paramDto);
                optionVo.setPacketItems(optionItems);
                packageOptionItems.addAll(optionItems);
                packetOptionItemMap.put(optionVo.getOptionId(), optionItems);
            }
            OptionRelateParam optionRelateParam = new OptionRelateParam();
            optionRelateParam.setModelLineId(modelLineId);
            optionRelateParam.setOptionId(optionVo.getOptionId());
            optionRelateParam.setNotInRelateCategory(Arrays.asList(OptionRelateEnum.COMBINE.getValue()));
            List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
            if (relDtos != null){
                relDtos.forEach(i->{
                    if (OptionRelateEnum.CONFLICT.getValue().equals(i.getRelateType())){
                        List<ModelLineOptionVo> conflictList = conflictMap.get(optionVo.getOptionId());
                        if (conflictList == null) {
                            conflictList = new ArrayList<>();
                            conflictMap.put(optionVo.getOptionId(), conflictList);
                        }
                        OptionParamDto optionParamDto = new OptionParamDto();
                        optionParamDto.setModelLineId(modelLineId);
                        optionParamDto.setOptionId(i.getOptionRelateId());
                        optionParamDto.setChannel(Constant.MASTER_CHANNEL);
                        optionParamDto.setDelFlag(0);
                        List<ModelLineOptionVo> relateOptVos = modelLineOptionService.modelLineOptionQueryStrict(optionParamDto);
                        if (!CollectionUtils.isEmpty(relateOptVos)){
                            conflictList.add(relateOptVos.get(0));
                        }
                    }else if (OptionRelateEnum.DEPEND.getValue().equals(i.getRelateType())){
                        List<ModelLineOptionVo> dependList = dependMap.get(optionVo.getOptionId());
                        if (dependList == null) {
                            dependList = new ArrayList<>();
                            dependMap.put(optionVo.getOptionId(), dependList);
                        }
                        OptionParamDto optionParamDto = new OptionParamDto();
                        optionParamDto.setModelLineId(modelLineId);
                        optionParamDto.setOptionId(i.getOptionRelateId());
                        optionParamDto.setChannel(Constant.MASTER_CHANNEL);
                        optionParamDto.setDelFlag(0);
                        List<ModelLineOptionVo> relateOptVos = modelLineOptionService.modelLineOptionQueryStrict(optionParamDto);
                        if (!CollectionUtils.isEmpty(relateOptVos)){
                            dependList.add(relateOptVos.get(0));
                            dependOptions.add(relateOptVos.get(0));
                        }
                    }else if (OptionRelateEnum.ATTACH.getValue().equals(i.getRelateType())){
                        List<String> attachList = attachMap.get(optionVo.getOptionId());
                        if (attachList == null) {
                            attachList = new ArrayList<>();
                            attachMap.put(optionVo.getOptionId(), attachList);
                        }
                        attachList.add(i.getOptionRelateCode());
                    }
                });
            }
            OptionRelateParam combineRelateParam = new OptionRelateParam();
            combineRelateParam.setCustomSeriesId(modelLine.getCustomSeriesId());
            combineRelateParam.setOptionId(optionVo.getOptionId());
            combineRelateParam.setRelateTypes(Arrays.asList(OptionRelateEnum.COMBINE.getValue()));
            List<OptionRelDto> combineDtos =  optionRelateService.listOptionRelDto(combineRelateParam);
            if (CollectionUtils.isNotEmpty(combineDtos)){
                combineDtos.forEach(i-> {
                    List<String> combineList = combineMap.get(optionVo.getOptionId());
                    if (combineList == null) {
                        combineList = new ArrayList<>();
                        combineMap.put(optionVo.getOptionId(), combineList);
                    }
                    combineList.add(i.getOptionRelateCode());
                });
            }
        }
        log.info("=======customCheckDto: " + JSONObject.toJSONString(checkDto, SerializerFeature.DisableCircularReferenceDetect));
        validCheckDto(checkDto);
        printOmdCode(checkDto);
        return checkDto;
    }

    private void printOmdCode(CustomCheckDto checkDto) {
        List<ModelLineOptionVo> options = new ArrayList<>();
        options.addAll(checkDto.getOptionalOptions());
        options.addAll(checkDto.getNotOptionalOptions());
        // 排除选装包有的，并加入attach，处理combine
        List<String> codes = new ArrayList<>();
        for (ModelLineOptionVo option : options){
            if (CollectionUtils.isNotEmpty(checkDto.getPacketOptionItems())){
                boolean flag = false;
                for (ModelLineOptionVo packetItem : checkDto.getPacketOptionItems()){
                    if (packetItem.getOptionCode().equals(option.getOptionCode())){
                        flag = true;
                        break;
                    }
                }
                if (flag){
                    continue;
                }
            }
            if (CollectionUtils.isNotEmpty(checkDto.getAttachMap().get(option.getOptionId()))){
                checkDto.getAttachMap().get(option.getOptionId()).forEach(i->codes.add(i));
            }
            if (CollectionUtils.isNotEmpty(checkDto.getCombineMap().get(option.getOptionId()))){
                checkDto.getCombineMap().get(option.getOptionId()).forEach(i->codes.add(i));
                continue;
            }
            codes.add(option.getOptionCode());
        }
        log.info("==========OMD CODES: "+ JSONObject.toJSONString(codes));
    }

    private void validCheckDto(CustomCheckDto checkDto) throws ServiceException {
        // 不可选的是否在 依赖项中
        if (CollectionUtils.isNotEmpty(checkDto.getNotOptionalOptions())){
            for(ModelLineOptionVo notOptionalOption : checkDto.getNotOptionalOptions()){
                boolean flag = false;
                if (CollectionUtils.isEmpty(checkDto.getDependOptions())){
                    throw new ServiceException("400401", "选装异常，包含不可选项");
                }
                for(ModelLineOptionVo dependOption : checkDto.getDependOptions()){
                    if (dependOption.getOptionId().equals(notOptionalOption.getOptionId())){
                        flag = true;
                        break;
                    }
                }
                for(ModelLineOptionVo packetItem : checkDto.getPacketOptionItems()){
                    if (packetItem.getOptionCode().equals(notOptionalOption.getOptionCode())){
                        flag = true;
                        break;
                    }
                }
                if (flag){
                    continue;
                }
                throw new ServiceException("400401", "选装异常，包含不可选项");
            }
        }

        // 校验依赖项是否被选择
        if (CollectionUtils.isNotEmpty(checkDto.getDependOptions())){
            HashMap<String, Boolean> categoryFlag = new HashMap<>();
            for (ModelLineOptionVo dependOption : checkDto.getDependOptions()){
                for (ModelLineOptionVo option : checkDto.getOptionParams()){
                    if (categoryFlag.get(dependOption.getCategory()) == null){
                        categoryFlag.put(dependOption.getCategory(), false);
                    }
                    if (dependOption.getOptionId().equals(option.getOptionId())){
                        categoryFlag.put(dependOption.getCategory(), true);
                        break;
                    }
                }
            }
            if(categoryFlag.values().contains(false)){
                throw new ServiceException("400401", "选装异常，依赖项未选择");
            }
        }
        // 冲突项是否被选择
        if (CollectionUtils.isNotEmpty(checkDto.getConflictMap().keySet())){
            for(Map.Entry<String, List<ModelLineOptionVo>> entry : checkDto.getConflictMap().entrySet()){
                if (CollectionUtils.isNotEmpty(entry.getValue())){
                    for (ModelLineOptionVo conflictOption : entry.getValue()){
                        for (ModelLineOptionVo option : checkDto.getOptionParams()){
                            if (conflictOption.getOptionId().equals(option.getOptionId())){
                                throw new ServiceException("400401", "选装异常，冲突项不可同时选择");
                            }
                        }
                    }
                }
            }
        }
        // 选装件同一家族不可多选
        Map<String, String> selectCategoryMap = new HashMap<>();
        for (ModelLineOptionVo option : checkDto.getOptionParams()){
            if (OptionCategoryEnum.PACKET.getValue().equals(option.getCategory())){
                continue;
            }
            if (selectCategoryMap.get(option.getCategory()) != null){
                throw new ServiceException("400401", "选装异常，选装件同一类型只能选一个");
            }
            selectCategoryMap.put(option.getCategory(), option.getOptionId());
        }
    }

    @Override
    public CarCustomDetail addCarconfigFromDRM(String memberId, String userId, String userMobile, String channel, SyncToCCProVo syncToCCProVo) throws Exception {
        String sibCode = null;//面料code
        String interieurCode = null;//面饰颜色code
        StringBuilder tempSb = new StringBuilder();
        CarCustomDetail detail = new CarCustomDetail();
        CarCustomDto carCustomDto = new CarCustomDto();

        AudiConfigDto configDto = iaccbService.audiConfig(syncToCCProVo.getAudiCode());
        //获取配置线车型
        QueryWrapper<CarModelLine> carModelLineQuery = new QueryWrapper<>();
        carModelLineQuery.eq("accb_type_code",configDto.getTypeCode()).eq("channel","master").eq("del_flag",0);
        List<CarModelLine> carModelLineList = modelLineService.list(carModelLineQuery);
        if(carModelLineList != null && carModelLineList.size() > 0) {
            carCustomDto.setModelLineId(carModelLineList.get(0).getModelLineId());
        } else {
            throw new ServiceException("400401", "参数异常：modelLineId", null);
        }

        //获取配置项
        Set<String> optionIds = new HashSet<>();
        QueryWrapper<CarOption> carOptionQuery = new QueryWrapper<>();
        carOptionQuery.eq("channel","master").eq("del_flag",0).isNotNull("option_code").isNotNull("category");
        List<CarOption> carOptionList = carOptionService.list(carOptionQuery);
        for(OptionBriefDto accbOption : configDto.getOptions()) {
            //面料code
            if("SIB".equals(accbOption.getCategory())) {
                sibCode = accbOption.getCode();
            }

            //面饰颜色code
            if("COLOR_INTERIEUR".equals(accbOption.getCategory())) {
                interieurCode = accbOption.getCode();
            }

            for(CarOption carOption: carOptionList) {
                tempSb.append(carOption.getCategory()).append(":").append(carOption.getOptionCode());
                if(tempSb.toString().equals(accbOption.getCode())) {
                    optionIds.add(carOption.getOptionId());
                    break;
                }
                tempSb.replace(0,tempSb.length(),"");
            }
        }
        if(optionIds.size() > 0) {
            carCustomDto.setOptionIds(optionIds);
        } else {
            throw new ServiceException("400401", "参数异常：optionIds", null);
        }

        //获取内饰-面料
        QueryWrapper<CarSibInterieur> carSibInterieurQuery = new QueryWrapper<>();
        carSibInterieurQuery.eq("del_flag",0)
                .eq("sib_option_code",sibCode.replace("SIB:",""))
                .eq("sib_option_category","SIB")
                .eq("interieur_option_code",interieurCode.replace("COLOR_INTERIEUR:",""))
                .eq("interieur_option_category","COLOR_INTERIEUR");
        List<CarSibInterieur> carSibInterieurList = carSibInterieurService.list(carSibInterieurQuery);
        if (carSibInterieurList != null && carSibInterieurList.size() > 0) {
            carCustomDto.setSibInterieurId(carSibInterieurList.get(0).getSibInterieurId());
        } else {
            throw new ServiceException("400401", "参数异常：sibInterieurId", null);
        }

        //来源id
        carCustomDto.setSourceId(syncToCCProVo.getSourceId());
        log.info("配置参数："+JSONObject.toJSONString(carCustomDto));

        //生成ccid
        CarCustom carCustom = carCustomService.addCarCustomConfig(memberId, userId,userMobile, channel, carCustomDto);
        detail.setCcId(String.valueOf(carCustom.getCcid()));

        return detail;
    }

    @Override
    public CarCustomDetail getCarConfigDetailContract(CarCustom carCustom) throws Exception {
        QueryWrapper<CarCustomOption> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ccid", carCustom.getCcid());
        List<CarCustomOption> optionList = carCustomOptionService.list(queryWrapper);
        ModelParamDto modelParamDto = new ModelParamDto();
//        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(carCustom.getModelLineId());
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (lines == null ||  lines.size() != 1){
            throw new ServiceException("500", "服务异常", null);
        }
        ModelLineVo modelLine = lines.get(0);
        CustomSeriesDto customSeriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
        CarCustomDetail detail = new CarCustomDetail();
        if(StringUtils.isNotBlank(carCustom.getMemberId())){
            JSONObject couponRes = copOrderQueryFeign.couponInfo(String.valueOf(carCustom.getCcid()), customSeriesDto.getSeriesCode(), carCustom.getMemberId());
            if (couponRes.getJSONArray("data")!=null && couponRes.getJSONArray("data").size()>0){
                List<CouponInfoVo> couponInfoVos = couponRes.getJSONArray("data").toJavaList(CouponInfoVo.class);
                detail.setCouponNo(couponInfoVos.get(0).getMpEquityNo());
            }
        }
        detail.setValid(carCustom.getValid());
        detail.setInvalidReason(carCustom.getInvalidReason());
        detail.setUpdateFlag(carCustom.getUpdateFlag());
        detail.setUpdateContent(carCustom.getUpdateContent());
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        detail.setConfigTime(carCustom.getCreateTime());
        detail.setDepositType(carCustom.getDepositType());
        detail.setEstimateDelivery(carCustom.getEstimateDelivery());
        detail.setClassify(carCustom.getClassify());
        detail.setMstMgrpId(carCustom.getMstMgrpId());
        if (detail.getDepositType() == null){
            detail.setDepositType(DepositTypeEnum.STOCK.getType());
        }

        ConfigDetail configDetail = new ConfigDetail();
        SeriesDetail seriesDetail = new SeriesDetail();
        seriesDetail.setSeriesCode(customSeriesDto.getSeriesCode());
        seriesDetail.setSeriesNameCn(carCustom.getModelDesc());
        configDetail.setCarSeries(seriesDetail);

        ModelDetail modelDetail = new ModelDetail();
        modelDetail.setModelCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        modelDetail.setModelNameCn(carCustom.getAccbTypeDesc());
        modelDetail.setModelYear(modelLine.getModelYear());
        modelDetail.setOmdModelVersion(modelLine.getVersion());
        modelDetail.setModelPrice(modelLine.getPrice());
        modelDetail.setCustomModelCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        configDetail.setCarModel(modelDetail);

        ColorDetail insideColor = new ColorDetail();
        configDetail.setInsideColor(insideColor);
        configDetail.setOptionList(new ArrayList<>());
        ColorDetail outsideColor = new ColorDetail();
        configDetail.setOutsideColor(outsideColor);
        BigDecimal totalPrice = null;
        if (modelLine.getPrice() instanceof BigDecimal) {
            totalPrice = (BigDecimal) modelLine.getPrice();
        }
        OptionRelateParam optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLine.getModelLineId());
        optionRelateParam.setRelateTypes(Arrays.asList("attach"));
        List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        OptionRelateParam combineRelateParam = new OptionRelateParam();
        combineRelateParam.setRelateTypes(Arrays.asList("combine"));
        List<OptionRelDto> relCombineDtos =  optionRelateService.listOptionRelDto(combineRelateParam);
        relDtos.addAll(relCombineDtos);
        Map<String, List<OptionRelDto>> oMap = new HashMap<>();
        relDtos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel == null) {
                oRel = new ArrayList<>();
                oMap.put(i.getOptionId(), oRel);
            }
            oRel.add(i);
        });
        List<Option> options = new ArrayList<>();
        List<String> optionIds = new ArrayList<>();
        for(CarCustomOption i : optionList){
            OptionParamDto optionParamDto = new OptionParamDto();
            optionParamDto.setModelLineId(modelLine.getModelLineId());
            optionParamDto.setOptionId(i.getOptionId());
            optionParamDto.setChannel(Constant.MASTER_CHANNEL);
            optionParamDto.setDelFlag(0);
            List<ModelLineOptionVo> optionVos = modelLineOptionService.modelLineOptionQuery(optionParamDto);
            if (optionVos == null || optionVos.size() == 0){
                throw new ServiceException("400401", "参数异常：optionIds", null);
            }
            ModelLineOptionVo optionVo = optionVos.get(0);
            boolean discount = false;
            if (StringUtils.isNotBlank(detail.getCouponNo()) && "1".equals(optionVo.getEquipmentRights())){
                discount = true;
            }

            // 过滤选装包的件与标装
            if (optionVo.getOptionType().equals("packet-item") || (optionVo.getStatus() != null && optionVo.getStatus().intValue() == 1)){
                continue;
            }
            optionIds.add(i.getOptionId());
            if (i.getCategory() != null && i.getCategory().equals("COLOR_EXTERIEUR")){
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                if (code.startsWith("COLOR_EXTERIEUR:")){
                    code = code.replaceFirst("COLOR_EXTERIEUR:", "");
                }
                outsideColor.setColorCode(code);
                outsideColor.setColorNameCn(i.getDescription());
                BigDecimal outsideColorPrice = priceTypeService.optionPrice(modelLine.getModelLineId(), code, i.getCategory());
                outsideColor.setPrice(outsideColorPrice);
                if ( discount ){
                    outsideColor.setDiscount(outsideColor.getPrice());
                }
                if (totalPrice != null && outsideColorPrice != null)
                    totalPrice = totalPrice.add(outsideColorPrice);
                CarSibInterieur sibInterieur = sibInterieurService.getSibInterieur(carCustom.getSibInterieurId(), Constant.MASTER_CHANNEL);
                insideColor.setColorCode(sibInterieur.getSibInterieurCode());
                insideColor.setColorNameCn(sibInterieur.getDescription());
            }else if (Arrays.asList("COLOR_INTERIEUR", "SIB").contains(i.getCategory()) ){
                // 内饰面料不单独发到合同
                continue;
            }else {
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                String[] codes = code.split(":");
                if (codes.length == 1){
                    code = codes[0];
                }else if (codes.length == 2){
                    code = codes[1];
                }
                Option option = new Option();
                option.setOptionNameCn(i.getDescription());
                option.setOptionCode(code);
                option.setOptionClassification(i.getCategory());
                option.setOptionClassification2nd(i.getCategory());
                options.add(option);
                BigDecimal optionPrice = priceTypeService.optionPrice(modelLine.getModelLineId(), code, i.getCategory());
                option.setOptionPrice(optionPrice);
                if ( discount ){
                    option.setDiscount(option.getDiscount());
                }
                if (totalPrice != null && optionPrice != null) {
                    totalPrice = totalPrice.add(optionPrice);
                }
            }
        }
        PriceComputeParam priceComputeParam = new PriceComputeParam();
        priceComputeParam.setModelLineId(modelLine.getModelLineId());
        priceComputeParam.setOptionIds(optionIds);
        priceComputeParam.setCouponNo(detail.getCouponNo());
        configDetail.setTotalPrice(modelLineService.computePrice(priceComputeParam));
        configDetail.setDiscount(modelLineService.computeDiscount(priceComputeParam));
        configDetail.setOptionList(options);
        detail.setConfigDetail(configDetail);
        return detail;
    }

    @Override
    public CarCustom addCarCustomConfigByBestRecommend(String userId, String memberId, String userMobile, Long bestRecommendId, String sourceId, String channel, String entryPoint) throws Exception {
        String trackId = UUID.randomUUID().toString();
        log.info("配置推荐车 userId: {} , trackId: {}", trackId);
        LambdaQueryWrapper<CarCustomEntryPoint> entryPointQ = new LambdaQueryWrapper<>();
        entryPointQ.eq(CarCustomEntryPoint::getChannel, channel)
                .eq(CarCustomEntryPoint::getEntryPoint, entryPoint)
                .eq(CarCustomEntryPoint::getStatus, 1);
        if (entryPointService.getOne(entryPointQ) == null){
            throw new ServiceException("400401", "参数异常：entryPoint", null);
        }
        LambdaQueryWrapper<CarBestRecommend> bestRecommendQuery = new LambdaQueryWrapper<>();
        bestRecommendQuery.eq(CarBestRecommend::getBestRecommendId, bestRecommendId).eq(CarBestRecommend::getDelFlag, 0);
        CarBestRecommend bestRecommend = bestRecommendService.getOne(bestRecommendQuery);
        if (bestRecommend != null){
            BestRecommendCarVo recommendCarVo = new BestRecommendCarVo();
            ModelParamDto paramDto = new ModelParamDto();
            paramDto.setModelLineId(bestRecommend.getModelLineId());
            paramDto.setChannel(channel);
            List<ModelLineVo> modelLineVo = modelLineService.listModelLine(paramDto);
            if (CollectionUtils.isEmpty(modelLineVo)){
                throw new ServiceException("400401", "畅销推荐车配置线异常", "推荐车数据异常");
            }
            recommendCarVo.setModelLine(modelLineVo.get(0));
            ModelLineSibInterieurVo param = new ModelLineSibInterieurVo();
            param.setModelLineId(bestRecommend.getModelLineId());
            param.setSibInterieurId(bestRecommend.getSibInterieurId());
            param.setDelFlag(0);
            param.setChannel(channel);
            List<ModelLineSibInterieurVo> sibInterieurVos = modelLineSibInterieurService.modelLineSibInterieur(param);
            if(CollectionUtils.isNotEmpty(sibInterieurVos)) {
                recommendCarVo.setModelLineSibInterieurVo(sibInterieurVos.get(0));
            }
            List<String> optionIds = bestRecommendOptionService.listRecommendOptionIds(bestRecommend.getBestRecommendId());
            optionIds.add(recommendCarVo.getModelLineSibInterieurVo().getSibOptionId());
            optionIds.add(recommendCarVo.getModelLineSibInterieurVo().getInterieurOptionId());
            List<ModelLineOptionVo> prVos = modelLineService.optionQueryByOptionIds(channel, recommendCarVo.getModelLine().getCustomSeriesId(), bestRecommend.getModelLineId(), optionIds);
            if (CollectionUtils.isNotEmpty(prVos)){
                recommendCarVo.setOptions(prVos);
            }

            ModelLineVo modelLine = recommendCarVo.getModelLine();
            CustomSeriesDto seriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
            AudiConfigDto audiConfigDto = new AudiConfigDto();
            audiConfigDto.setModelId(seriesDto.getAccbModelId());
            audiConfigDto.setModelCode(seriesDto.getAccbModelCode());
            audiConfigDto.setModelDesc(seriesDto.getSeriesName());
            audiConfigDto.setTypeId(modelLine.getAccbTypeId());
            audiConfigDto.setTypeCode(modelLine.getAccbTypeCode());
            audiConfigDto.setTypeDesc(modelLine.getModelLineName());
            audiConfigDto.setModelYear(modelLine.getModelYear());
            audiConfigDto.setModelVersion(modelLine.getVersion());;
            audiConfigDto.setHeadline(modelLine.getModelLineName());
            List<OptionBriefDto> optionBriefDtoList = new ArrayList<>();
            List<ModelLineOptionVo> options = recommendCarVo.getOptions();
            if (CollectionUtils.isEmpty(options)){
                throw new ServiceException("400401", "参数异常：optionIds", null);
            }
            options.forEach(i->
                    optionBriefDtoList.add(new OptionBriefDto(i.getCategory()+":"+i.getOptionCode(), i.getCategory(),i.getOptionName())));
            audiConfigDto.setOptions(optionBriefDtoList);

            CarCustom carCustom = new CarCustom();
            carCustom.setEntryPoint(entryPoint);
            carCustom.setDepositType(DepositTypeEnum.STOCK.getType());
            carCustom.setBestRecommendId(Long.valueOf(bestRecommendId));
            carCustom.setSibInterieurId(recommendCarVo.getModelLineSibInterieurVo().getSibInterieurId());
            carCustom.setAccbModelId(seriesDto.getAccbModelId());
            carCustom.setAccbModelCode(seriesDto.getAccbModelCode());
            carCustom.setModelDesc(seriesDto.getCustomSeriesName());
            carCustom.setAccbTypeId(modelLine.getAccbTypeId());
            carCustom.setAccbTypeCode(modelLine.getAccbTypeCode());
            carCustom.setAccbTypeDesc(modelLine.getModelLineName());
            carCustom.setModelLineId(modelLine.getModelLineId());
            carCustom.setModelYear(modelLine.getModelYear());
            carCustom.setUserId(userId);
            carCustom.setUserMobile(userMobile);
            carCustom.setCreateTime(LocalDateTime.now());
            carCustom.setCreateUser(userId);
            carCustom.setMemberId(memberId);
            if (StringUtils.isNotBlank(sourceId)) {
                CarCustomSource source = new CarCustomSource();
                if (source.selectById(sourceId) == null) {
                    throw new ServiceException("400401", "参数异常：sourceId", null);
                }
                carCustom.setSourceId(Long.valueOf(sourceId));
            }

            CcEstimateDeliveryParam estimateDeliveryParam = new CcEstimateDeliveryParam();
            estimateDeliveryParam.setCustomSeriesId(modelLine.getCustomSeriesId());
            estimateDeliveryParam.setModelLineId(modelLine.getModelLineId());
            estimateDeliveryParam.setOptionIds(optionBriefDtoList.stream().map(OptionBriefDto::getOptionId).collect(Collectors.toList()));
            estimateDeliveryParam.setBeforeCheck(false);
            CcEstimateVo estimateVo = modelLineService.ccEstimate(estimateDeliveryParam);
            // 库存车默认B
            carCustom.setClassify("B");
            carCustom.setEstimateDelivery(DeliveryTimeEnum.B.getValue());
            carCustom.setClassifyVersion(modelLine.getClassifyVersion());
            if (estimateVo != null && estimateVo.getType() != null && "A".equals(estimateVo.getType())) {
                carCustom.setOmdVehicleTypeId(estimateVo.getOmdVehicleTypeId());
                carCustom.setClassify(estimateVo.getType());
                carCustom.setEstimateDelivery(estimateVo.getDeliveryTime());
            }
            carCustom.insert();
            options.forEach(i->{
                CarCustomOption carCustomOption = new CarCustomOption();
                carCustomOption.setCcid(carCustom.getCcid());
                carCustomOption.setOptionId(i.getOptionId());
                carCustomOption.setCategory(i.getCategory());
                carCustomOption.setCode(i.getOptionCode());
                carCustomOption.setDescription(i.getOptionName());
                carCustomOption.insert();
            });
            log.info("add cc userId: " + userId + " bestRecommendId: " +
                    bestRecommendId + " trackId: " + trackId + " ccid: "+ String.valueOf(carCustom.getCcid()));
//            audiCodeExecutor.execute(()->this.ccGenAudiCode(carCustom, audiConfigDto));
            customSnapshotService.snapshotCarCustom(carCustom);
            return carCustom;
        }else {
            throw new ServiceException("400401", "畅销推荐车不存在或者已下架", "未查到相应推荐车");
        }
    }

    @Override
    public CarCustomVo a7OrQ5Byccid(String ccid) {
        return carCustomMapper.a7OrQ5Byccid(ccid);
    }

    @Override
    public List<CarCustomDetailVo> listCustomDetail(List<Long> ccids) {
        if (CollectionUtils.isEmpty(ccids)){
            return Lists.newArrayList();
        }
        CarCustomQueryDto queryDto = new CarCustomQueryDto();
        queryDto.setCcids(ccids);
        return carCustomMapper.listCustomDetail(queryDto);
    }

    @Override
    public CarCustomDetail getCarConfigDetailOmdAll(CarCustom carCustom) throws Exception {
        QueryWrapper<CarCustomOption> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ccid", carCustom.getCcid());
        List<CarCustomOption> optionList = carCustomOptionService.list(queryWrapper);
        List<String> optionCodes = optionList.stream().map(CarCustomOption::getCode).collect(Collectors.toList());
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setModelLineId(carCustom.getModelLineId());
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (lines == null ||  lines.size() != 1){
            throw new ServiceException("500", "服务异常", null);
        }
        ModelLineVo modelLine = lines.get(0);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setValid(carCustom.getValid());
        detail.setInvalidReason(carCustom.getInvalidReason());
        detail.setUpdateFlag(carCustom.getUpdateFlag());
        detail.setUpdateContent(carCustom.getUpdateContent());
        detail.setCcId(String.valueOf(carCustom.getCcid()));

        CustomSeriesDto customSeriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
        ConfigDetail configDetail = new ConfigDetail();
        SeriesDetail seriesDetail = new SeriesDetail();
        seriesDetail.setSeriesCode(customSeriesDto.getSeriesCode());
        seriesDetail.setSeriesNameCn(carCustom.getModelDesc());
        configDetail.setCarSeries(seriesDetail);

        ModelDetail modelDetail = new ModelDetail();
        modelDetail.setModelCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        modelDetail.setModelNameCn(carCustom.getAccbTypeDesc());
        modelDetail.setModelYear(modelLine.getModelYear());
        modelDetail.setOmdModelVersion(modelLine.getVersion());
        modelDetail.setModelPrice(modelLine.getPrice());
        modelDetail.setCustomModelCode(modelLine.getAccbTypeCode().replaceFirst("TYPE:", ""));
        configDetail.setCarModel(modelDetail);

        ColorDetail insideColor = new ColorDetail();
        configDetail.setInsideColor(insideColor);
        configDetail.setOptionList(new ArrayList<>());
        ColorDetail outsideColor = new ColorDetail();
        configDetail.setOutsideColor(outsideColor);
        BigDecimal totalPrice = null;
        if (modelLine.getPrice() instanceof BigDecimal) {
            totalPrice = (BigDecimal) modelLine.getPrice();
        }
        OptionRelateParam optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLine.getModelLineId());
        optionRelateParam.setRelateTypes(Arrays.asList("attach"));
        List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        OptionRelateParam combineRelateParam = new OptionRelateParam();
        combineRelateParam.setRelateTypes(Arrays.asList("combine"));
        List<OptionRelDto> relCombineDtos =  optionRelateService.listOptionRelDto(combineRelateParam);
        relDtos.addAll(relCombineDtos);
        Map<String, List<OptionRelDto>> oMap = new HashMap<>();
        relDtos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel == null) {
                oRel = new ArrayList<>();
                oMap.put(i.getOptionId(), oRel);
            }
            oRel.add(i);
        });
        List<Option> options = new ArrayList<>();
        List<String> optionIds = new ArrayList<>();
        for(CarCustomOption i : optionList){
            OptionParamDto optionParamDto = new OptionParamDto();
            optionParamDto.setModelLineId(modelLine.getModelLineId());
            optionParamDto.setOptionId(i.getOptionId());
            optionParamDto.setChannel(Constant.MASTER_CHANNEL);
            optionParamDto.setDelFlag(0);
            List<ModelLineOptionVo> optionVos = modelLineOptionService.modelLineOptionQuery(optionParamDto);
            if (optionVos == null || optionVos.size() == 0){
                throw new ServiceException("400401", "参数异常：optionIds", null);
            }
            ModelLineOptionVo optionVo = optionVos.get(0);
            // 过滤选装包的件与标装
            if (optionVo.getOptionType().equals("packet-item") || (optionVo.getStatus() != null && optionVo.getStatus().intValue() == 1) ){
                continue;
            }
            List<ModelLineOptionVo> items =  modelLineOptionMapper.findPaketItemFromCodes(customSeriesDto.getCustomSeriesId(), modelLine.getModelLineId(), i.getCode(), optionCodes);
            if (CollectionUtils.isNotEmpty(items)){
                continue;
            }
            optionIds.add(i.getOptionId());
            if (i.getCategory() != null && i.getCategory().equals("COLOR_EXTERIEUR")){
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                if (code.startsWith("COLOR_EXTERIEUR:")){
                    code = code.replaceFirst("COLOR_EXTERIEUR:", "");
                }
                outsideColor.setColorCode(code);
                outsideColor.setColorNameCn(i.getDescription());
                BigDecimal outsideColorPrice = priceTypeService.optionPrice(modelLine.getModelLineId(), code, i.getCategory());
                outsideColor.setPrice(outsideColorPrice);
                if (oMap.get(i.getOptionId()) != null) {
                    for (OptionRelDto relDto : oMap.get(i.getOptionId())) {
                        if (relDto.getRelateType().equals("attach")) {
                            Option optionItem = new Option();
                            optionItem.setOptionCode(relDto.getOptionRelateCode());
                            options.add(optionItem);
                        }
                    }
                }
                if (totalPrice != null && outsideColorPrice != null)
                    totalPrice = totalPrice.add(outsideColorPrice);
            }else if (i.getCategory() != null && i.getCategory().equals("COLOR_INTERIEUR")){
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                if (code.startsWith("COLOR_INTERIEUR:")){
                    code = code.replaceFirst("COLOR_INTERIEUR:", "");
                }
                insideColor.setColorCode(code);
                insideColor.setColorNameCn(i.getDescription());
                if (oMap.get(i.getOptionId()) != null) {
                    for (OptionRelDto relDto : oMap.get(i.getOptionId())) {
                        if (relDto.getRelateType().equals("attach")) {
                            Option optionItem = new Option();
                            optionItem.setOptionCode(relDto.getOptionRelateCode());
                            options.add(optionItem);
                        }
                    }
                }
            }else {
                String code = i.getCode();
                if (code == null){
                    throw new ServiceException("500", "服务异常", null);
                }
                String[] codes = code.split(":");
                if (codes.length == 1){
                    code = codes[0];
                }else if (codes.length == 2){
                    code = codes[1];
                }
                Option option = new Option();
                option.setOptionNameCn(i.getDescription());
                option.setOptionCode(code);
                option.setOptionClassification(i.getCategory());
                option.setOptionClassification2nd(i.getCategory());
                boolean combine = false;
                if (oMap.get(i.getOptionId()) != null){
                    for (OptionRelDto relDto: oMap.get(i.getOptionId())) {
                        if (relDto.getRelateType().equals("combine")) {
                            Option optionItem = new Option();
                            optionItem.setOptionCode(relDto.getOptionRelateCode());
                            String itemCategory = relDto.getOptionRelateCategory() == null?"":relDto.getOptionRelateCategory();
                            optionItem.setOptionPrice(priceTypeService.optionPrice(modelLine.getModelLineId(), relDto.getOptionRelateCode(), itemCategory));
                            options.add(optionItem);
                            combine = true;
                        }else if (relDto.getRelateType().equals("attach")) {
                            Option optionItem = new Option();
                            optionItem.setOptionCode(relDto.getOptionRelateCode());
                            options.add(optionItem);
                        }
                    }
                }
                if (!combine) {
                    options.add(option);
                }
                BigDecimal optionPrice = priceTypeService.optionPrice(modelLine.getModelLineId(), code, i.getCategory());
                option.setOptionPrice(optionPrice);
                if (totalPrice != null && optionPrice != null) {
                    totalPrice = totalPrice.add(optionPrice);
                }
            }
        }
        PriceComputeParam priceComputeParam = new PriceComputeParam();
        priceComputeParam.setModelLineId(modelLine.getModelLineId());
        priceComputeParam.setOptionIds(optionIds);
//        configDetail.setTotalPrice(modelLineService.computePrice(priceComputeParam));
        if (modelLine.getSpecialLine().intValue() == 1){
            // 处理特殊配置线
            List<CarModelLineSpecialOption> specialOptions = specialOptionService.listModelLineSpecialOption(modelLine.getModelLineId());
            if(CollectionUtils.isEmpty(specialOptions)){
                throw new ServiceException("50000", "特殊配置线，配置单异常");
            }
            specialOptions.forEach(i->{
                Option option = new Option();
                option.setOptionCode(i.getOptionCode());
                option.setOptionClassification(i.getCategory());
                option.setOptionClassification2nd(i.getCategory());
                options.add(option);
            });
        }
        configDetail.setOptionList(options);
        detail.setConfigDetail(configDetail);
        return detail;
    }

    @Override
    public int countSum(String userId,String startDate,String endDate) {
        QueryWrapper<CarCustom> queryWrapper = new QueryWrapper<>();
        if(StringUtils.isNotEmpty(userId)) {
            queryWrapper.eq("user_id",userId);
        }
        if(StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate)) {
            queryWrapper.between("create_time",startDate,endDate);
        }
        if(StringUtils.isNotEmpty(startDate)) {
            queryWrapper.ge("create_time",startDate);
        }
        if(StringUtils.isNotEmpty(endDate)) {
            queryWrapper.le("create_time",endDate+" 23:59:59");
        }
        return this.count(queryWrapper);
    }

    @Override
    public CarCustom updateNew(String ccid) throws Exception {
        CarCustom  carCustom = this.getById(ccid);
        if (carCustom == null){
            throw new ServiceException("50001", "配置单不存在");
        }
        if (carCustom.getUpdateFlag() != null && carCustom.getUpdateFlag().intValue() == 1){
            this.lambdaUpdate().eq(CarCustom::getCcid, ccid)
                    .set(CarCustom::getUpdateFlag, 0)
                    .set(CarCustom::getUpdateContent, null).update();
            carCustom = this.getById(ccid);
            customSnapshotService.snapshotCarCustom(carCustom);
        }else {
            throw new ServiceException("50001", "配置单不能更新");
        }
        return carCustom;
    }

}
