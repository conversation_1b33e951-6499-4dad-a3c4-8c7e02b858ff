package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 渠道商组织信息-政策
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SvcdChannelOrganizationPolicy 对象", description="渠道商组织信息-政策")
public class SvcdChannelOrganizationPolicy extends Model<SvcdChannelOrganizationPolicy> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "数据id")
      @TableId(value = "channel_organization_policy_id", type = IdType.AUTO)
    private Long channelOrganizationPolicyId;

    @ApiModelProperty(value = "渠道商组织信息id")
    private Long channelOrganizationId;

    @ApiModelProperty(value = "渠道商编码")
    private String dealerCode;

    @ApiModelProperty(value = "策略类别")
    private String policyType;

    @ApiModelProperty(value = "策略控制点")
    private String policyTypeName;

    @ApiModelProperty(value = "业务类型")
    private String businessCategory;

    @ApiModelProperty(value = "业务控制代码")
    private String businessCode;

    @ApiModelProperty(value = "业务控制项")
    private String businessControl;

    @ApiModelProperty(value = "控制部门")
    private String controlDepartment;

    @ApiModelProperty(value = "控制状态(0:关闭 1:开启)")
    private String controlStatus;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;

    @Override
    protected Serializable pkVal() {
        return this.channelOrganizationPolicyId;
    }

}
