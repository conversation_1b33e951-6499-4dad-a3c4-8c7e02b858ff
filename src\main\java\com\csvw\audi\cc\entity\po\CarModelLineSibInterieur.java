package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 配置线内饰面料关联
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarModelLineSibInterieur对象", description="配置线内饰面料关联")
public class CarModelLineSibInterieur extends Model<CarModelLineSibInterieur> {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "配置项id")
    private String sibInterieurId;

    @ApiModelProperty(value = "权重")
    private Integer weight;

    @ApiModelProperty(value = "配置线id")
    private String modelLineId;

    @ApiModelProperty(value = "默认配置项")
    private Integer defaultConfig;

    private String description;

    @ApiModelProperty(value = "配置线配置项状态（0:无，1:标准装备，2:可选装备）")
    private Integer status;

    @ApiModelProperty(value = "数据渠道")
    private String channel;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
