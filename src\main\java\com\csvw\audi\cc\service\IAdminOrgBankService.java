package com.csvw.audi.cc.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.csvw.audi.cc.entity.dto.AdminOrgBankDto;
import com.csvw.audi.cc.entity.po.AdminOrgBank;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.vo.AdminOrgBankVo;
import com.csvw.audi.cc.entity.vo.ImportRes;
import com.github.pagehelper.PageInfo;

import java.io.InputStream;
import java.util.List;

/**
 * <p>
 * 渠道商对应的金融机构 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-17
 */
public interface IAdminOrgBankService extends IService<AdminOrgBank> {

    public ImportRes dataImport(String userId,InputStream inputStream) throws Exception;

    public List<AdminOrgBankVo> getOrgBankList(AdminOrgBankDto dto);
    PageInfo<AdminOrgBankVo> getOrgBankList(AdminOrgBankDto dto, Page<AdminOrgBankVo> page );

    public void delOrgBankByIds(List<String> orgBankIds);

    public List<AdminOrgBank> getBankList();

}
