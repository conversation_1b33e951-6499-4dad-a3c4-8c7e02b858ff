<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarSeriesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarSeries">
        <id column="id" property="id" />
        <result column="series_id" property="seriesId" />
        <result column="series_code" property="seriesCode" />
        <result column="series_name" property="seriesName" />
        <result column="accb_model_code" property="accbModelCode" />
        <result column="accb_model_id" property="accbModelId" />
        <result column="image_url" property="imageUrl" />
        <result column="channel" property="channel" />
        <result column="weight" property="weight" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, series_id, series_code, series_name, accb_model_code, accb_model_id, image_url, channel, weight, create_time, update_time, del_flag
    </sql>

</mapper>
