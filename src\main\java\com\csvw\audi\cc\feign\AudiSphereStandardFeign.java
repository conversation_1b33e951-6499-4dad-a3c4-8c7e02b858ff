package com.csvw.audi.cc.feign;

import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "audi-sphere-standard")
//@FeignClient(value = "audi-sphere-standard", url = "http://dev-audi-copapi.svwsx.cn/audi-sphere-standard")
public interface AudiSphereStandardFeign {

    @GetMapping("/private/api/car/offeringlist/findOfferingListByUuid")
    JSONObject findOfferingListByUuid(@RequestParam String uuid);
}
