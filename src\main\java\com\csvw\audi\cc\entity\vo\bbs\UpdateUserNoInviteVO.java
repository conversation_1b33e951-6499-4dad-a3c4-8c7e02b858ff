package com.csvw.audi.cc.entity.vo.bbs;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/19 18:17
 * @description 设置用户邀请好友禁止状态及渠道商标记
 */
@Data
public class UpdateUserNoInviteVO {
    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("禁止邀请状态 true：禁止邀请，false：允许邀请")
    private Boolean noInviteStatus;

    @ApiModelProperty("渠道商 0：普通用户 1：渠道商")
    private Integer channelType;

    @ApiModelProperty("禁止天数")
    private Integer days;
}
