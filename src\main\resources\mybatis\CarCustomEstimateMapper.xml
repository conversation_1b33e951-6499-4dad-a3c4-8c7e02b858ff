<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarCustomEstimateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarCustomEstimate">
        <id column="id" property="id" />
        <result column="from_ccid" property="fromCcid" />
        <result column="ccid" property="ccid" />
        <result column="unique_code" property="uniqueCode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, from_ccid, ccid, unique_code, create_time, update_time, del_flag
    </sql>

</mapper>
