package com.csvw.audi.cc.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MeasureVo implements Serializable {
    @ApiModelProperty(value = "半订制化车辆配置")
    private List<MeasureConfigCode> measureConfigCodeList;
    @ApiModelProperty(value = "半订制化可选外饰")
    private List<ModelLineOptionVo> colorExterieur;
    @ApiModelProperty(value = "半订制化可选内饰颜色面料")
    private List<ModelLineSibInterieurVo> sibInterieur;
    @ApiModelProperty(value = "半订制化可选轮毂")
    private List<ModelLineOptionVo> rad;
    @ApiModelProperty(value = "半订制化可选座椅")
    private List<ModelLineOptionVo> vos;
    @ApiModelProperty(value = "半订制化可选饰条")
    private List<ModelLineOptionVo> eih;
    @ApiModelProperty(value = "半订制化可选私人订制")
    private List<ModelLineOptionVo> personals;
    @ApiModelProperty(value = "存在六座包")
    private Integer seatPacketOptional6;
    @ApiModelProperty(value = "七座包")
    private Integer seatPacketOptional7;
}
