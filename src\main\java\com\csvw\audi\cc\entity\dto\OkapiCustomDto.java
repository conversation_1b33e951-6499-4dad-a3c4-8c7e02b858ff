package com.csvw.audi.cc.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

@Data
public class OkapiCustomDto {
    @ApiModelProperty("配置单来源id")
    private String sourceId;
    @ApiModelProperty("配车入口")
    private String entryPoint;
    private String depositType;
    private String mstMgrpId;
    @ApiModelProperty(value = "配置线代码, 不能为空", required = true)
    private String modelCode;
    @ApiModelProperty(value = "车型年, 不能为空", required = true)
    private String modelYear;
    @ApiModelProperty(value = "车型版本, 不能为空", required = true)
    private String modelVersion;
    @ApiModelProperty(value = "内饰, 不能为空", required = true)
    private String interiorCode;
    @ApiModelProperty("选装列表（包含选装包，选装件），逗号分开;可以为空")
    private String prList;
    @ApiModelProperty(value = "颜色代码, 不能为空", required = true)
    private String colorCode;
    @ApiModelProperty(value ="品牌，不能为空，C-SKODA/V-SVW/A-AUDI", required = true)
    private String classCode;
    private String ccid;
}
