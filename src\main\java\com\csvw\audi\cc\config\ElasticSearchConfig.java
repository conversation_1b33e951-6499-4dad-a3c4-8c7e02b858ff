package com.csvw.audi.cc.config;

import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

@Configuration
public class ElasticSearchConfig {
    @Bean
    public RestHighLevelClient esClient(AppConfig appConfig){
        String host = appConfig.getEs().getHost();
        if (host == null){
            return new RestHighLevelClient(RestClient.builder(new HttpHost("localhost", 9200, "http")));
        }else {
            String[] hosts = host.split(",");
            HttpHost[] httpHostList = new HttpHost[hosts.length];
            int i = 0;
            for (String h : hosts){
                String words[] = h.split(":");
                if (words.length != 3){
                    continue;
                }
                httpHostList[i] = new HttpHost(words[1].replaceFirst("//", ""), Integer.parseInt(words[2]), words[0]);
                ++i;
            }
            return new RestHighLevelClient(RestClient.builder(Arrays.copyOf(httpHostList, i)));
        }

    }
}
