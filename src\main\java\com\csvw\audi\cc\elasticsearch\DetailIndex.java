package com.csvw.audi.cc.elasticsearch;

import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.entity.dto.vwcc.CarCustomDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
public class DetailIndex extends BaseIndex{

    private static final String detailMappings;

    static {
        String text = null;
        try {
            InputStream inputStream = OmdDetailIndex.class.getResourceAsStream("/index/mapping/detailMapping.json");
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] buf = new byte[8192];
            int i = -1;
            while ((i = inputStream.read(buf)) >0){
                bos.write(buf, 0, i);
            }
            text = bos.toString("UTF-8");
        }catch (Exception e){
            log.error("mapping 错误", e);
        }
        detailMappings = text;
    }

    public DetailIndex(RestHighLevelClient client, String channel) throws Exception {
        super(client);
        this.indexPrefix = "car-config-detail" + "-" + channel;
        this.mappings = detailMappings;
    }

    public void batchAddIndex(List<CarCustomDetail> detailList) throws IOException {
        BulkRequest bulkRequest = new BulkRequest();
        if (CollectionUtils.isNotEmpty(detailList)){
            for (CarCustomDetail detail : detailList) {
                IndexRequest request = indexRequest("doc", detail.getCcId(), detail, detail.getConfigTime());
                bulkRequest.add(request);
            }
            BulkResponse response = client.bulk(bulkRequest, RequestOptions.DEFAULT);
            for (BulkItemResponse bulkItemResponses: response){
               DocWriteResponse docWriteResponse = bulkItemResponses.getResponse();
               IndexResponse indexResponse = (IndexResponse) docWriteResponse;
               System.out.println(indexResponse.toString());
            }
        }
    }

    public CarCustomDetail getDetailBySnapshotId(Long snapshotId) throws IOException {
        SearchRequest searchRequest = new SearchRequest(getQueryIndex());
        searchRequest.types("doc");
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        searchRequest.source(sourceBuilder);
        sourceBuilder.query(QueryBuilders.matchQuery("_id", snapshotId.toString()));
        sourceBuilder.from(0);
        sourceBuilder.size(1);
        SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
        if (response.getHits().totalHits<1){
            return null;
        }
        return JSONObject.parseObject(response.getHits().getHits()[0].getSourceAsString(), CarCustomDetail.class);
    }

}
