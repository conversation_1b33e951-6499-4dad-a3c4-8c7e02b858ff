<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarTagRelateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarTagRelate">
        <id column="id" property="id" />
        <result column="tag_name" property="tagName" />
        <result column="tag_code" property="tagCode" />
        <result column="model_line_id" property="modelLineId" />
        <result column="model_line_name" property="modelLineName" />
        <result column="option_id" property="optionId" />
        <result column="option_code" property="optionCode" />
        <result column="option_name" property="optionName" />
        <result column="tag_type" property="tagType" />
        <result column="weight" property="weight" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tag_name, tag_code, model_line_id, model_line_name, option_id, option_code, option_name, tag_type, weight, create_time, update_time, del_flag
    </sql>

    <select id="tagQuery" parameterType="com.csvw.audi.cc.entity.dto.TagQueryDto" resultType="com.csvw.audi.cc.entity.vo.CarTagVo">
        select * from car_tag_relate tr left join car_tag t on tr.tag_code = t.tag_code
        where tr.model_line_id = #{modelLineId} and t.del_flag = 0 and tr.del_flag = 0
        <if test="tagType != null and tagType == 1">
            and tr.tag_type = #{tagType} and tr.option_id is null;
        </if>
        <if test="tagType != null and tagType == 2">
            and tr.tag_type = #{tagType} and tr.option_id = #{optionId}
        </if>

    </select>

</mapper>
