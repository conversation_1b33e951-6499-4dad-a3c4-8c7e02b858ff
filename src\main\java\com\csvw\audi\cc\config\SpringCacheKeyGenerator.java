package com.csvw.audi.cc.config;

import com.csvw.audi.cc.common.utils.BeanUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Component("springCacheKeyGenerator")
@Slf4j
public class SpringCacheKeyGenerator implements KeyGenerator {

    @Autowired
    private ObjectMapper objectMapper;

    private final static int NO_PARAM_KEY = 0;

    private String keyPrefix = "saic_audi:appcache:audi_car_config";// key前缀，用于区分不同项目的缓存，建议每个项目单独设置

    @Override
    public Object generate(Object target, Method method, Object... params) {
        char sp = ':';
        StringBuilder strBuilder = new StringBuilder(30);
        strBuilder.append(keyPrefix);
        strBuilder.append(sp);
        // 类名
        strBuilder.append(target.getClass().getSimpleName());
        strBuilder.append(sp);
        // 方法名
        strBuilder.append(method.getName());
        strBuilder.append(sp);
        if (params.length > 0) {
            // 参数值
            for (Object object : params) {
                if (object != null && BeanUtils.isSimpleValueType(object.getClass())) {
                    strBuilder.append(object);
                } else {
                    try {
                        strBuilder.append(objectMapper.writeValueAsString(object));
                    } catch (JsonProcessingException e) {
                        e.printStackTrace();
                        log.error("序列化参数失败", e);
                    }
                }
            }
        } else {
            strBuilder.append(NO_PARAM_KEY);
        }
        return strBuilder.toString();
    }

    public String getKeyPrefix() {
        return keyPrefix;
    }

    public void setKeyPrefix(String keyPrefix) {
        this.keyPrefix = keyPrefix;
    }
}
