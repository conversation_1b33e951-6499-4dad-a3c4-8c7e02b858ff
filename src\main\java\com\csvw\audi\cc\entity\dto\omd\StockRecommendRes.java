package com.csvw.audi.cc.entity.dto.omd;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class StockRecommendRes {
    private String modelCode;
    private String modelYear;
    private String modelVersion;
    private String interiorCode;
    private String prList;
    private String colorCode;
    private String classCode;
    private String defaultFlag;
    private String dealerNetCode;
    private Long rowIndex;
    private String operateType;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operateTime;
    private Long stockRecommendModelId;
}
