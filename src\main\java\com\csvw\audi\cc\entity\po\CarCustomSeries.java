package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 自定义车系
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarCustomSeries对象", description="自定义车系")
public class CarCustomSeries extends Model<CarCustomSeries> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "自定义车系id")
    private String customSeriesId;

    @ApiModelProperty(value = "车系code")
    private String customSeriesCode;

    @ApiModelProperty(value = "车系code")
    private String customSeriesName;

    @ApiModelProperty(value = "车系id")
    private String seriesId;

    @ApiModelProperty(value = "车系code")
    private String seriesCode;

    @ApiModelProperty(value = "车系名称")
    private String seriesName;

    @ApiModelProperty(value = "车系图片")
    private String imageUrl;

    @ApiModelProperty(value = "车系分类，sedan: 轿车，suv：越野")
    private String classification;

    @ApiModelProperty(value = "默认配置")
    private String defaultConfig;

    @ApiModelProperty(value = "数据渠道")
    private String channel;

    @ApiModelProperty(value = "权重")
    private Integer weight;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
