package com.csvw.audi.cc.controller.svcd;

import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.dto.DealerDto;
import com.csvw.audi.cc.entity.vo.DealerVo;
import com.csvw.audi.cc.service.ISvcdChannelOrganizationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "代理商-运营侧")
@RestController
@RequestMapping("/admin/api/v1/dealer")
public class AdminDealerController extends BaseController {

    @Autowired
    private ISvcdChannelOrganizationService svcdChannelOrganizationService;

    @ApiOperation("运营侧-获取代理商列表")
    @GetMapping("/getDealerList")
    public AjaxMessage<List<DealerVo>> getAgentList(DealerDto dealerDto) {
        //默认返回总部
        if(dealerDto.getDefaultHeadquarters() == null) {
            dealerDto.setDefaultHeadquarters(1);
        }
        List<DealerVo> list =  svcdChannelOrganizationService.getAgentList(dealerDto);
        return new AjaxMessage<>("00", "成功", list);
    }

    @ApiOperation("运营侧-获取代理商/服务商列表")
    @GetMapping("/getOrgList")
    public AjaxMessage<List<DealerVo>> getOrgList(DealerDto dealerDto) {
        //默认查询全部
        if(dealerDto.getSearchType() == null) {
            dealerDto.setSearchType(0);
        }
        List<DealerVo> list =  svcdChannelOrganizationService.getDealerListByOfficialWebsite(dealerDto);
        return new AjaxMessage<>("00", "成功", list);
    }
}
