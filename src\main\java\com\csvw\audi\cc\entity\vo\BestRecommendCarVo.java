package com.csvw.audi.cc.entity.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BestRecommendCarVo implements Serializable {
    private String bestRecommendId;
    @JsonIgnore
    private Long recommendModelId;
    private ModelLineVo modelLine;
    private ModelLineSibInterieurVo modelLineSibInterieurVo;
    private List<ModelLineOptionVo> options;
    private ModelLineOptionVo sibOption;
    private ModelLineOptionVo vosOption;
    private ModelLineOptionVo standardRad;
    private Integer stockNum;
    private Object totalPrice;
}
