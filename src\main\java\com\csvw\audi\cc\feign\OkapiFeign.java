package com.csvw.audi.cc.feign;

import com.csvw.audi.cc.component.InfraInterceptorConfig;
import com.csvw.audi.cc.entity.dto.okapi.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "okapiFeign", url = "${audi-car-config.infra.smepUrl}", configuration = InfraInterceptorConfig.class)
public interface OkapiFeign {

    /**
     * 车系查询
     * @param brandCode
     * @return
     */
    @GetMapping(value = "/api/v1/sacar/series")
    OkapiRes<List<SeriesDto>> series(@RequestParam String brandCode);

    /**
     * 车型名称列表查询
     * @param brandCode
     * @return
     */
    @GetMapping(value = "/api/v1/sacar/models")
    OkapiRes<List<ModelsDto>> models(@RequestParam String brandCode, @RequestParam String seriesCode);

    /**
     * 车型价格列表查询
     * @param brandCode
     * @return
     */
    @GetMapping(value = "/api/v1/sacar/models/price")
    OkapiRes<List<ModelsPriceDto>> modelsPrice(@RequestParam String brandCode, @RequestParam String seriesCode, @RequestParam String modelUnicode);

    /**
     * 获取车型标签配置
     * @return
     */
    @PostMapping(value = "/api/v1/sacar/models/label/config")
    OkapiRes<List<LabelConfigResDto>> modelLabelConfig(@RequestBody LabelConfigParamDto paramDto);

    /**
     * 获取车型标签配置,选装包件
     * @return
     * @param paramDto
     */
    @GetMapping(value = "/api/v1/sacar/models/pfCode/label/config")
    OkapiRes<LabelConfigItemResDto> modelLabelConfigItem(@RequestParam String brandCode,@RequestParam String modelUnicode,@RequestParam String featureCode);

    /**
     * 车系车型素材
     * @return
     */
    @GetMapping(value = "/api/v1/sacar/seriesModel/material")
    OkapiRes<List<ModelMaterialDto>> seriesModelMaterial(@RequestParam String brandCode, @RequestParam String seriesCode, @RequestParam String subModelRuleId);

    /**
     * 获取车型内饰面料
     * @return
     */
    @GetMapping(value = "/api/v1/sacar/models/interiorFabric/label/config")
    OkapiRes<LabelInterieurResDto> modelSibInterieur(@RequestParam String brandCode,@RequestParam String modelUnicode,@RequestParam(required = false) String mstMgrpId,@RequestParam String interiorCode);


    /**
     * 销售车系
     * @return
     */
    @GetMapping(value = "/api/v1/sales/series")
    OkapiRes<List<SalesSeriesDto>> salesSeries(@RequestParam String brandCode);


    /**
     * 销售车型列表查询
     * @param brandCode
     * @return
     */
    @GetMapping(value = "/api/v1/sales/models")
    OkapiRes<List<SalesModelsDto>> salesModels(@RequestParam String brandCode, @RequestParam String seriesCode);

    /**
     * 销售车型明细
     * @param brandCode
     * @return
     */
    @GetMapping(value = "/api/v1/sales/models/detail")
    OkapiRes<List<SalesModelsDetail>> salesModelsDetail(@RequestParam String brandCode, @RequestParam String seriesCode, @RequestParam String modelUnicode);


    @GetMapping(value = "/api/v1/sacar/equipmentGroup/query")
    OkapiRes<EquipmentGroupResDto> equipmentGroup(@RequestParam String brandCode, @RequestParam String modelUnicode, @RequestParam(required = false) String labelCode, @RequestParam(required = false) String labelValue);

    @GetMapping(value = "/api/v1/sacar/models/highlight/label/config")
    OkapiRes<HighLightConfigResDto> highlight(@RequestParam String brandCode, @RequestParam String modelUnicode, @RequestParam(required = false) String mstMgrpId);
}
