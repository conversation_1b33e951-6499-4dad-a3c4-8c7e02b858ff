package com.csvw.audi.cc.controller;


import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.vo.TypeVo;
import com.csvw.audi.cc.service.IOptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


@RestController
@RequestMapping("/api/v1/type")
@Api(tags = "车型")
public class TypeController extends BaseController {

    @Autowired
    private IOptionService optionService;

    @GetMapping("/list")
    @ApiOperation(value = "车型")
    public AjaxMessage<List<TypeVo>> types(){
        List<TypeVo> types = new ArrayList<>();
        TypeVo dto = new TypeVo();
        dto.setHeadline("A7L edition one");
        dto.setPrice(BigDecimal.valueOf(508000.0));
        dto.setPreview("test/2021/06/02/a7l/type.png");
        dto.setThumbnail("test/2021/06/02/a7l/type.png");
        types.add(dto);
        return successMessage(types);
    }

}

