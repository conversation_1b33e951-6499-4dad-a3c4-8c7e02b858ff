package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.application.TestAppRun;
import com.csvw.audi.cc.entity.dto.ModelParamDto;
import com.csvw.audi.cc.entity.po.CarCustomSeries;
import com.csvw.audi.cc.entity.vo.ModelLineConfigVo;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.entity.vo.ModelLineVo;
import com.csvw.audi.cc.service.ICarCustomSeriesService;
import com.csvw.audi.cc.service.ICarModelLineService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.stream.Collectors;

import static com.csvw.audi.cc.common.Constant.MASTER_CHANNEL;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = TestAppRun.class)
public class CCServicesTest {

    private static final String CAR_SERIES_ID = "64fe9f8e-050d-4843-bffc-c47675a9956a";
    private static final String XIANXING_TYPE_CODE = "TYPE:498B2Y-GPAHPAH-GPDWPDW-GPF3PF3-GPS6PS6-GWA6WA6-MEIH5MD-MRAD53D-MBBO6FJ-MHSW8IZ-MLSE9VS";
    private static final String XIANXING_MODEL_LINE_ID = "3351ff6b-b818-418c-accc-28ba59dc175d";
    private static final String XIANJIAN_TYPE_CODE = "q";
    private static final String XIANJIAN_MODEL_LINE_ID = "fb1c9fa0-e64b-4b99-bd74-88fcc37c9369";
    private static final String JINGYUAN_TYPE_CODE = "TYPE:498B2Y-MRADF15-GWCVWCV";
    private static final String JINGYUAN_MODEL_LINE_ID = "8af31bcc-16a2-4ab2-916d-0facc5608db9";
    private static final String JINGYUAN_YAOHEI_TYPE_CODE = "TYPE:498B2Y-MRADF15-GPAHPAH-GWCVWCV";
    private static final String JINGYUAN_YAOHEI_MODEL_LINE_ID = "b5c9ab29-4a90-4928-9696-3b23e15c8ec0";
    private static final String ZHIYUAN_TYPE_CODE = "TYPE:498B2Y";
    private static final String ZHIYUAN_MODEL_LINE_ID = "203ef4c9-27e5-4f87-af5d-4c0c34ab2884";
    private static final String ZHIYUAN_YAOHEI_TYPE_CODE = "TYPE:498B2Y-GPAHPAH";
    private static final String ZHIYUAN_YAOHEI_MODEL_LINE_ID = "aa0b274c-3e52-4cab-86ce-221303575ba3";
    @Autowired
    private ICarCustomSeriesService carCustomSeriesService;

    @Autowired
    private ICarModelLineService carModelLineService;

    @Test
    public void listCustomSeriesTest() throws Exception {
        List<CarCustomSeries> data = carCustomSeriesService.listCustomSeries(MASTER_CHANNEL, null);
        Assert.assertEquals(1, data.size());
        Assert.assertEquals(data.get(0).getCustomSeriesCode(), "49");
        Assert.assertEquals(data.get(0).getCustomSeriesName(), "A7L");
        Assert.assertEquals(data.get(0).getCustomSeriesId(), CAR_SERIES_ID);
    }

    @Test
    public void listModelLineTest() throws Exception {
        ModelParamDto paramDto = new ModelParamDto();
        paramDto.setCustomSeriesId(CAR_SERIES_ID);
        paramDto.setDelFlag(0);
        paramDto.setChannel(MASTER_CHANNEL);
        List<ModelLineVo> data = carModelLineService.listModelLine(paramDto);
        Assert.assertEquals(data.size(), 6);
        List<ModelLineVo> vo = data.stream().filter(modelLineVo -> {
            return modelLineVo.getAccbTypeCode().equals(XIANXING_TYPE_CODE);
        }).collect(Collectors.toList());
        Assert.assertEquals(vo.size(), 1);
        Assert.assertEquals(vo.get(0).getModelLineId(), XIANXING_MODEL_LINE_ID);

        vo = data.stream().filter(modelLineVo -> {
            return modelLineVo.getAccbTypeCode().equals(XIANJIAN_TYPE_CODE);
        }).collect(Collectors.toList());
        Assert.assertEquals(vo.size(), 1);
        Assert.assertEquals(vo.get(0).getModelLineId(), XIANJIAN_MODEL_LINE_ID);

        vo = data.stream().filter(modelLineVo -> {
            return modelLineVo.getAccbTypeCode().equals(JINGYUAN_TYPE_CODE);
        }).collect(Collectors.toList());
        Assert.assertEquals(vo.size(), 1);
        Assert.assertEquals(vo.get(0).getModelLineId(), JINGYUAN_MODEL_LINE_ID);

        vo = data.stream().filter(modelLineVo -> {
            return modelLineVo.getAccbTypeCode().equals(JINGYUAN_YAOHEI_TYPE_CODE);
        }).collect(Collectors.toList());
        Assert.assertEquals(vo.size(), 1);
        Assert.assertEquals(vo.get(0).getModelLineId(), JINGYUAN_YAOHEI_MODEL_LINE_ID);

        vo = data.stream().filter(modelLineVo -> {
            return modelLineVo.getAccbTypeCode().equals(ZHIYUAN_TYPE_CODE);
        }).collect(Collectors.toList());
        Assert.assertEquals(vo.size(), 1);
        Assert.assertEquals(vo.get(0).getModelLineId(), ZHIYUAN_MODEL_LINE_ID);

        vo = data.stream().filter(modelLineVo -> {
            return modelLineVo.getAccbTypeCode().equals(ZHIYUAN_YAOHEI_TYPE_CODE);
        }).collect(Collectors.toList());
        Assert.assertEquals(vo.size(), 1);
        Assert.assertEquals(vo.get(0).getModelLineId(), ZHIYUAN_YAOHEI_MODEL_LINE_ID);
    }

    @Test
    public void testModelLineConfig() throws Exception {
        ModelLineConfigVo configVo = carModelLineService.modelLineConfig(MASTER_CHANNEL, XIANJIAN_MODEL_LINE_ID, null);
        List<ModelLineOptionVo> outColors = configVo.getModelLineOption().stream().filter(modelLineOptionVo -> {
            return modelLineOptionVo.getOptionType().equals("outcolor") && modelLineOptionVo.getStatus().equals(2);
        }).collect(Collectors.toList());
        Assert.assertEquals(outColors.size(), 2);
    }
}
