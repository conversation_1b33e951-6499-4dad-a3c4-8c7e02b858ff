package com.csvw.audi.cc.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.entity.dto.CarCustomDto;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

public class StringUtil {
    /**
     * UTF-8解码
     * @param str
     * @return
     */
    public static String getURLDecoderString(String str) {
        String result = "";
        if (null == str) {
            return "";
        }
        try {
            result = java.net.URLDecoder.decode(str, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 判断String是否是数字型
     * @param str
     * @return
     */
    public static boolean isInteger(String str) {
        if(str == null || "".equals(str)) {
            return false;
        }
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        return pattern.matcher(str).matches();
    }

    public static boolean isNotEmpty(String aStr) {

        return (aStr != null && aStr.length() > 0);
    }

    public static boolean isEmpty(String aStr) {

        return (aStr == null || aStr.trim().length() == 0);
    }

    public static void testFile(String directoryPath,String filePrefix) {

        String fileName = "";
        String fileSuffix = "";
        String fileParentPath = "";
        Map<String,String> map = new HashMap<>();
        map.put("2ZM","WA3");
        map.put("9ZE","PT1");
        map.put("4E7","PGC");
        map.put("QL5","PDW_WA6_2V3_GZ2");
        map.put("2F1","WAO");
        map.put("Q2J","PS8");
        map.put("QW4","WAB_PS8");
        map.put("4V1","PS1");
        map.put("8T8","PCY");
        File directory = new File(directoryPath);
        File[] files = directory.listFiles();
        if(files == null) {
            return;
        }
        for(File fileObj : files) {
            try {
                fileParentPath = fileObj.getParent();
                fileName = fileObj.getName();
                if(fileName.indexOf(" ") == -1 || fileName.indexOf(" ") <= 2) {
                    continue;
                }
                fileSuffix = fileName.substring(fileName.lastIndexOf("."));
                fileName = fileName.substring(0,fileName.indexOf(" "));
                if(map.containsKey(fileName)) {
                    System.out.println("拷贝文件 "+fileName+" -> "+map.get(fileName));
                    Files.copy(fileObj.toPath(),new File(fileParentPath + File.separatorChar + map.get(fileName) + filePrefix + fileSuffix).toPath());
                }
                if("QL5".equals(fileName)) {
                    System.out.println("拷贝文件 "+fileName+" -> VW0");
                    Files.copy(fileObj.toPath(),new File(fileParentPath + File.separatorChar + "VW0" + filePrefix + fileSuffix).toPath());
//                    System.out.println("拷贝文件 "+fileName+" -> VF1");
//                    Files.copy(fileObj.toPath(),new File(fileParentPath + File.separatorChar + "VF1" + filePrefix + fileSuffix).toPath());
//                    System.out.println("拷贝文件 "+fileName+" -> GS5");
//                    Files.copy(fileObj.toPath(),new File(fileParentPath + File.separatorChar + "GS5" + filePrefix + fileSuffix).toPath());
//                    System.out.println("拷贝文件 "+fileName+" -> 7HD");
//                    Files.copy(fileObj.toPath(),new File(fileParentPath + File.separatorChar + "7HD" + filePrefix + fileSuffix).toPath());
//                    System.out.println("拷贝文件 "+fileName+" -> 2V3");
//                    Files.copy(fileObj.toPath(),new File(fileParentPath + File.separatorChar + "2V3" + filePrefix + fileSuffix).toPath());
//                    System.out.println("拷贝文件 "+fileName+" -> GZ2");
//                    Files.copy(fileObj.toPath(),new File(fileParentPath + File.separatorChar + "GZ2" + filePrefix + fileSuffix).toPath());
//                    System.out.println("拷贝文件 "+fileName+" -> VW0");
//                    Files.copy(fileObj.toPath(),new File(fileParentPath + File.separatorChar + "VW0" + filePrefix + fileSuffix).toPath());
                }
//                if("QW4".equals(fileName)) {
//                    System.out.println("拷贝文件 "+fileName+" -> WAB");
//                    Files.copy(fileObj.toPath(),new File(fileParentPath + File.separatorChar + "WAB" + filePrefix + fileSuffix).toPath());
//                    System.out.println("拷贝文件 "+fileName+" -> PS8");
//                    Files.copy(fileObj.toPath(),new File(fileParentPath + File.separatorChar + "PS8" + filePrefix + fileSuffix).toPath());
//                }

                fileObj.renameTo(new File(fileParentPath + File.separatorChar + fileName + filePrefix + fileSuffix));
            } catch (Exception e){
                System.out.println(fileObj.getPath());
                e.printStackTrace();
            }
        }
        System.out.println("结束.......................");
    }

    //经纬度计算距离（有较大误差，暂停使用）
    public static double gitDistance_1(double longitude1,double latitude1,double longitude2,double latitude2) {
        //经度
        double lng1 = Math.toRadians(longitude1);
        double lng2 = Math.toRadians(longitude2);
        //纬度
        double lat1 = Math.toRadians(latitude1);
        double lat2 = Math.toRadians(latitude2);
        double a = lat1 - lat2;
        double b = lng1 - lng2;
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2))) + Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(b / 2), 2);
        s = s * 6378.137 * 1000;// 6378.137 地球半径，单位：千米
        return s;//单位：米
    }

    //转化为弧度(rad)
    private static double rad(double d){    return d * Math.PI / 180.0;}
    /**
     * 基于余弦定理求两经纬度距离
     * @param lon1 第一点的精度
     * @param lat1 第一点的纬度
     * @param lon2 第二点的精度
     * @param lat2 第二点的纬度
     * @return 返回的距离，单位m
     */
    public static double gitDistance(double lon1, double lat1,double lon2, double lat2) {
        double earthRadius = 6378137;//赤道半径(单位m)
        double radLat1 = rad(lat1);
        double radLat2 = rad(lat2);
        double radLon1 = rad(lon1);
        double radLon2 = rad(lon2);
        if (radLat1 < 0)
            radLat1 = Math.PI / 2 + Math.abs(radLat1);// south
        if (radLat1 > 0)
            radLat1 = Math.PI / 2 - Math.abs(radLat1);// north
        if (radLon1 < 0)
            radLon1 = Math.PI * 2 - Math.abs(radLon1);// west
        if (radLat2 < 0)
            radLat2 = Math.PI / 2 + Math.abs(radLat2);// south
        if (radLat2 > 0)
            radLat2 = Math.PI / 2 - Math.abs(radLat2);// north
        if (radLon2 < 0)
            radLon2 = Math.PI * 2 - Math.abs(radLon2);// west
        double x1 = earthRadius * Math.cos(radLon1) * Math.sin(radLat1);
        double y1 = earthRadius * Math.sin(radLon1) * Math.sin(radLat1);
        double z1 = earthRadius * Math.cos(radLat1);
        double x2 = earthRadius * Math.cos(radLon2) * Math.sin(radLat2);
        double y2 = earthRadius * Math.sin(radLon2) * Math.sin(radLat2);
        double z2 = earthRadius * Math.cos(radLat2);
        double d = Math.sqrt((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2)+ (z1 - z2) * (z1 - z2));//余弦定理求夹角
        double theta = Math.acos((earthRadius * earthRadius + earthRadius * earthRadius - d * d) / (2 * earthRadius * earthRadius));
        double dist = theta * earthRadius;
        return dist;
    }

    public static void main(String args[]) throws UnsupportedEncodingException {
        String result = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(new Long("1633875324000")));
        System.out.println("mesquite in your cellar".replaceFirst("e", "o"));

        System.out.println(java.net.URLDecoder.decode("%E5%B1%B1%E9%AB%98%E4%BA%BA%E4%B8%BA%E5%B3%B0", "UTF-8"));
        System.out.println(gitDistance(121.962947,31.513473,118.796623,32.059352));

//        File file = new File("C:\\Users\\<USER>\\Desktop\\CCPro9.26所有配置图片素材\\先见先行\\奥迪Pro版增强型驾驶辅助系统\\2ZM 四幅多功能真皮方向盘，带换挡拨片及加热功能 - 副本.png");
//        String fileName = file.getName();
//        String fileSuffix = fileName.substring(fileName.lastIndexOf("."));
//        String fileParentPath = file.getParent();
//        System.out.println(fileParentPath);
//        System.out.println(fileName);
//        fileName = fileName.substring(0,3)+fileSuffix;
//        System.out.println(fileName);
//        System.out.println(fileParentPath+File.separatorChar+fileName);
//        file.renameTo(new File(fileParentPath+File.separatorChar+fileName));


        // 1000 x 1000
//        String filePrefix_list = "_list";
//        String directoryPath = "C:\\Users\\<USER>\\Desktop\\CCPro9.26改名后\\3.0T车型\\座椅\\1000_1000";
//        testFile(directoryPath,filePrefix_list);
//
//        // 1500 x 880
//        String filePrefix_detail = "_detail";
//        directoryPath = "C:\\Users\\<USER>\\Desktop\\CCPro9.26改名后\\3.0T车型\\座椅\\1500_880";
//        testFile(directoryPath,filePrefix_detail);
//
//        // 1372 x 520
//        String filePrefix = "";
//        directoryPath = "C:\\Users\\<USER>\\Desktop\\CCPro9.26改名后\\3.0T车型\\座椅\\1372_520";
//        testFile(directoryPath,filePrefix);
    }
}
