package com.csvw.audi.cc.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MeasureConfigCode {
    @ApiModelProperty(value = "半订制车辆id")
    private String measureId;

    @ApiModelProperty(value = "外饰颜色code")
    private String colorCode;

    @ApiModelProperty(value = "内饰颜色code")
    private String interiorCode;

    @ApiModelProperty(value = "轮毂code")
    private String radCode;

    @ApiModelProperty(value = "面料code")
    private String sibCode;

    @ApiModelProperty(value = "座椅code")
    private String vosCode;

    @ApiModelProperty(value = "饰条code")
    private String eihCode;

    @ApiModelProperty(value = "内饰颜色面料id")
    private String sibInterieurId;

    @ApiModelProperty(value = "配置唯一码")
    private String ccUniqueCode;

    @ApiModelProperty(value = "私人定制列表")
    private List<String> prCodes;
}
