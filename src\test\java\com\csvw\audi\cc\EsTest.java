package com.csvw.audi.cc;

import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.elasticsearch.DetailIndex;
import com.csvw.audi.cc.elasticsearch.OmdDetailIndex;
import com.csvw.audi.cc.entity.dto.vwcc.CarCustomDetail;
import org.apache.http.HttpHost;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.admin.indices.create.CreateIndexResponse;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.admin.indices.get.GetIndexRequest;
import org.elasticsearch.action.admin.indices.mapping.put.PutMappingRequest;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.junit.Test;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

public class EsTest {

    RestHighLevelClient client = new RestHighLevelClient(RestClient.builder(new HttpHost("localhost", 9200, "http")));

    @Test
    public void testIndex() throws IOException {
        GetIndexRequest request = new GetIndexRequest();
        request.indices("test");
        boolean flag = client.indices().exists(request, RequestOptions.DEFAULT);
        System.out.println(flag);
    }

    @Test
    public void deleteIndex() throws IOException {
        DeleteIndexRequest request = new DeleteIndexRequest("test");
        AcknowledgedResponse response = client.indices().delete(request, RequestOptions.DEFAULT);
    }

    @Test
    public void createIndex() throws IOException {
        CreateIndexRequest request = new CreateIndexRequest("test");
        JSONObject source = new JSONObject();
        JSONObject settings = new JSONObject();
        settings.put("number_of_shards", 1);
        settings.put("number_of_replicas", 0);
        source.put("settings",settings);
        String mapping = "{\"doc\":{\"properties\":{\"ccid\":{\"type\":\"keyword\"},\"totalPrice\":{\"type\":\"keyword\"},\"bestRecommendId\":{\"type\":\"keyword\"},\"measureId\":{\"type\":\"keyword\"},\"depositType\":{\"type\":\"keyword\"},\"dataTime\":{\"type\":\"date\",\"format\":\"yyyy-MM-dd'T'HH:mm:ss.SSS'Z'||yyyy-MM-dd HH:mm:ss.SSS||yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis\"},\"configDetail\":{\"properties\":{\"carModel\":{\"properties\":{\"customModelCode\":{\"type\":\"keyword\"},\"modelCode\":{\"type\":\"keyword\"},\"modelNameCn\":{\"type\":\"text\"},\"modelYear\":{\"type\":\"keyword\"},\"omdModelVersion\":{\"type\":\"long\"}}},\"carSeries\":{\"properties\":{\"seriesCode\":{\"type\":\"keyword\"},\"seriesNameCn\":{\"type\":\"text\"}}},\"insideColor\":{\"properties\":{\"colorCode\":{\"type\":\"keyword\"},\"colorNameCn\":{\"type\":\"keyword\"}}},\"outsideColor\":{\"properties\":{\"colorCode\":{\"type\":\"keyword\"},\"colorNameCn\":{\"type\":\"keyword\"},\"price\":{\"type\":\"keyword\"}}},\"optionList\":{\"properties\":{\"optionNameCn\":{\"type\":\"text\"},\"optionCode\":{\"type\":\"keyword\"},\"optionPrice\":{\"type\":\"keyword\"},\"optionClassification\":{\"type\":\"keyword\"},\"optionClassification2nd\":{\"type\":\"keyword\"}}}}}}}}";
        source.put("mappings", JSONObject.parseObject(mapping));
        request.source(source.toJSONString(), XContentType.JSON);

        CreateIndexResponse response = client.indices().create(request, RequestOptions.DEFAULT);
        System.out.println(response.isAcknowledged());
    }

    @Test
    public void editIndex() throws IOException {
        PutMappingRequest request = new PutMappingRequest("test");
        String mapping = "{\"doc\":{\"properties\":{\"ccid\":{\"type\":\"keyword\"},\"totalPrice\":{\"type\":\"keyword\"},\"bestRecommendId\":{\"type\":\"keyword\"},\"measureId\":{\"type\":\"keyword\"},\"depositType\":{\"type\":\"keyword\"},\"dataTime\":{\"type\":\"date\",\"format\":\"yyyy-MM-dd'T'HH:mm:ss.SSS'Z'||yyyy-MM-dd HH:mm:ss.SSS||yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis\"},\"configDetail\":{\"properties\":{\"carModel\":{\"properties\":{\"customModelCode\":{\"type\":\"keyword\"},\"modelCode\":{\"type\":\"keyword\"},\"modelNameCn\":{\"type\":\"text\"},\"modelYear\":{\"type\":\"keyword\"},\"omdModelVersion\":{\"type\":\"long\"}}},\"carSeries\":{\"properties\":{\"seriesCode\":{\"type\":\"keyword\"},\"seriesNameCn\":{\"type\":\"text\"}}},\"insideColor\":{\"properties\":{\"colorCode\":{\"type\":\"keyword\"},\"colorNameCn\":{\"type\":\"keyword\"}}},\"outsideColor\":{\"properties\":{\"colorCode\":{\"type\":\"keyword\"},\"colorNameCn\":{\"type\":\"keyword\"},\"price\":{\"type\":\"keyword\"}}},\"optionList\":{\"properties\":{\"optionNameCn\":{\"type\":\"text\"},\"optionCode\":{\"type\":\"keyword\"},\"optionPrice\":{\"type\":\"keyword\"},\"optionClassification\":{\"type\":\"keyword\"},\"optionClassification2nd\":{\"type\":\"keyword\"}}}}}}}}";
        request.type("doc").source(mapping, XContentType.JSON);
        AcknowledgedResponse response = client.indices().putMapping(request, RequestOptions.DEFAULT);
        System.out.println(response.isAcknowledged());
    }

    @Test
    public void addIndex() throws IOException {
        IndexRequest request = new IndexRequest("test", "doc","2");
        String text = "{\"accountId\":null,\"agreementPrice\":null,\"carActiveStatus\":null,\"ccId\":\"1579734336909008897\",\"ccName\":null,\"configDetail\":{\"attachmentList\":null,\"carModel\":{\"customModelCode\":\"498B2Y-GPAHPAH\",\"customModelId\":null,\"defaultInteriorColor\":null,\"displacement\":null,\"emissionLevel\":null,\"energyType\":null,\"engineComment\":null,\"engineDesc\":null,\"engineId\":null,\"engineName\":null,\"modelCode\":\"498B2Y-GPAHPAH\",\"modelNameCn\":\"奥迪A7L 志远型 曜黑套装\",\"modelNameEn\":null,\"modelParamList\":null,\"modelPrice\":790000,\"modelYear\":\"2022\",\"modellineDesc\":null,\"modellineId\":null,\"modellineName\":null,\"omdModelVersion\":\"0\",\"promotionActivities\":null,\"seriesCode\":null,\"imageUrl\":null,\"headImageUrl\":null,\"deliveryTime\":null,\"modelLineCode\":null},\"carSeries\":{\"brandCode\":null,\"categoryId\":null,\"categoryName\":null,\"categoryWeight\":null,\"classCode\":null,\"configStepList\":null,\"customSeriesCode\":null,\"customSeriesId\":null,\"imageUrl\":null,\"isVirtualSeries\":null,\"modelYear\":null,\"overviewImageUrl\":null,\"seriesCode\":\"49\",\"seriesNameCn\":\"Audi A7L\",\"seriesNameEn\":null,\"seriesWeight\":null},\"groups\":null,\"insideColor\":{\"colorCode\":\"MP\",\"colorDesc\":null,\"colorFlag\":null,\"colorNameCn\":\"black / soul 静谧黑\",\"colorNameEn\":null,\"colorPainting\":null,\"colorWeight\":null,\"customColorCode\":null,\"customColorId\":null,\"customModelId\":null,\"imageUrl\":null,\"price\":null},\"modellineDetail\":null,\"optionList\":[{\"customOptionId\":null,\"optionType\":null,\"optionSubType\":null,\"optionWeight\":null,\"isNecessary\":null,\"optionNameCn\":\"智能激光大灯\",\"optionNameEn\":null,\"optionDesc\":null,\"optionCode\":\"8IZ\",\"optionPrice\":230,\"priceFlag\":null,\"priceLabel\":null,\"customOptionCode\":null,\"imageUrl\":null,\"optionGroup\":null,\"optionClassification\":\"HSW\",\"optionClassificationName\":null,\"optionClassification2nd\":\"HSW\",\"optionClassification2ndName\":null,\"disStatus\":null,\"reportType\":null,\"optionDetailVOs\":null},{\"customOptionId\":null,\"optionType\":null,\"optionSubType\":null,\"optionWeight\":null,\"isNecessary\":null,\"optionNameCn\":null,\"optionNameEn\":null,\"optionDesc\":null,\"optionCode\":\"PDW\",\"optionPrice\":5000,\"priceFlag\":null,\"priceLabel\":null,\"customOptionCode\":null,\"imageUrl\":null,\"optionGroup\":null,\"optionClassification\":null,\"optionClassificationName\":null,\"optionClassification2nd\":null,\"optionClassification2ndName\":null,\"disStatus\":null,\"reportType\":null,\"optionDetailVOs\":null},{\"customOptionId\":null,\"optionType\":null,\"optionSubType\":null,\"optionWeight\":null,\"isNecessary\":null,\"optionNameCn\":null,\"optionNameEn\":null,\"optionDesc\":null,\"optionCode\":\"2V3\",\"optionPrice\":230,\"priceFlag\":null,\"priceLabel\":null,\"customOptionCode\":null,\"imageUrl\":null,\"optionGroup\":null,\"optionClassification\":null,\"optionClassificationName\":null,\"optionClassification2nd\":null,\"optionClassification2ndName\":null,\"disStatus\":null,\"reportType\":null,\"optionDetailVOs\":null},{\"customOptionId\":null,\"optionType\":null,\"optionSubType\":null,\"optionWeight\":null,\"isNecessary\":null,\"optionNameCn\":null,\"optionNameEn\":null,\"optionDesc\":null,\"optionCode\":\"GZ2\",\"optionPrice\":230,\"priceFlag\":null,\"priceLabel\":null,\"customOptionCode\":null,\"imageUrl\":null,\"optionGroup\":null,\"optionClassification\":null,\"optionClassificationName\":null,\"optionClassification2nd\":null,\"optionClassification2ndName\":null,\"disStatus\":null,\"reportType\":null,\"optionDetailVOs\":null},{\"customOptionId\":null,\"optionType\":null,\"optionSubType\":null,\"optionWeight\":null,\"isNecessary\":null,\"optionNameCn\":null,\"optionNameEn\":null,\"optionDesc\":null,\"optionCode\":\"WA6\",\"optionPrice\":0,\"priceFlag\":null,\"priceLabel\":null,\"customOptionCode\":null,\"imageUrl\":null,\"optionGroup\":null,\"optionClassification\":null,\"optionClassificationName\":null,\"optionClassification2nd\":null,\"optionClassification2ndName\":null,\"disStatus\":null,\"reportType\":null,\"optionDetailVOs\":null},{\"customOptionId\":null,\"optionType\":null,\"optionSubType\":null,\"optionWeight\":null,\"isNecessary\":null,\"optionNameCn\":\"20吋10幅 星芒轮毂\",\"optionNameEn\":null,\"optionDesc\":null,\"optionCode\":\"F15\",\"optionPrice\":6000,\"priceFlag\":null,\"priceLabel\":null,\"customOptionCode\":null,\"imageUrl\":null,\"optionGroup\":null,\"optionClassification\":\"RAD\",\"optionClassificationName\":null,\"optionClassification2nd\":\"RAD\",\"optionClassification2ndName\":null,\"disStatus\":null,\"reportType\":null,\"optionDetailVOs\":null},{\"customOptionId\":null,\"optionType\":null,\"optionSubType\":null,\"optionWeight\":null,\"isNecessary\":null,\"optionNameCn\":\"智能电动尾门套装\",\"optionNameEn\":null,\"optionDesc\":null,\"optionCode\":\"PGC\",\"optionPrice\":0,\"priceFlag\":null,\"priceLabel\":null,\"customOptionCode\":null,\"imageUrl\":null,\"optionGroup\":null,\"optionClassification\":\"PACKET\",\"optionClassificationName\":null,\"optionClassification2nd\":\"PACKET\",\"optionClassification2ndName\":null,\"disStatus\":null,\"reportType\":null,\"optionDetailVOs\":null}],\"outsideColor\":{\"colorCode\":\"2T2T\",\"colorDesc\":null,\"colorFlag\":null,\"colorNameCn\":\"旷夜黑 曜黑套装\",\"colorNameEn\":null,\"colorPainting\":null,\"colorWeight\":null,\"customColorCode\":null,\"customColorId\":null,\"customModelId\":null,\"imageUrl\":null,\"price\":1000},\"totalPrice\":null},\"configSrcType\":null,\"configTime\":\"2022-10-11 12:00:00\",\"ownerInfoQfExpired\":null,\"ownerMobile\":null,\"ownerName\":null,\"bestRecommendId\":null,\"measureId\":\"1553543545143095297\",\"depositType\":null,\"valid\":1,\"invalidReason\":null,\"updateFlag\":0,\"updateContent\":null}";
        request.source(text, XContentType.JSON);
        IndexResponse response = client.index(request, RequestOptions.DEFAULT);
        System.out.println(response.toString());
    }

    @Test
    public void detailIndex() throws Exception {
        String text = "{\"accountId\":null,\"agreementPrice\":null,\"carActiveStatus\":null,\"ccId\":\"1579734336909008897\",\"ccName\":null,\"configDetail\":{\"attachmentList\":null,\"carModel\":{\"customModelCode\":\"498B2Y-GPAHPAH\",\"customModelId\":null,\"defaultInteriorColor\":null,\"displacement\":null,\"emissionLevel\":null,\"energyType\":null,\"engineComment\":null,\"engineDesc\":null,\"engineId\":null,\"engineName\":null,\"modelCode\":\"498B2Y-GPAHPAH\",\"modelNameCn\":\"奥迪A7L 志远型 曜黑套装\",\"modelNameEn\":null,\"modelParamList\":null,\"modelPrice\":790000,\"modelYear\":\"2022\",\"modellineDesc\":null,\"modellineId\":null,\"modellineName\":null,\"omdModelVersion\":\"0\",\"promotionActivities\":null,\"seriesCode\":null,\"imageUrl\":null,\"headImageUrl\":null,\"deliveryTime\":null,\"modelLineCode\":null},\"carSeries\":{\"brandCode\":null,\"categoryId\":null,\"categoryName\":null,\"categoryWeight\":null,\"classCode\":null,\"configStepList\":null,\"customSeriesCode\":null,\"customSeriesId\":null,\"imageUrl\":null,\"isVirtualSeries\":null,\"modelYear\":null,\"overviewImageUrl\":null,\"seriesCode\":\"49\",\"seriesNameCn\":\"Audi A7L\",\"seriesNameEn\":null,\"seriesWeight\":null},\"groups\":null,\"insideColor\":{\"colorCode\":\"MP\",\"colorDesc\":null,\"colorFlag\":null,\"colorNameCn\":\"black / soul 静谧黑\",\"colorNameEn\":null,\"colorPainting\":null,\"colorWeight\":null,\"customColorCode\":null,\"customColorId\":null,\"customModelId\":null,\"imageUrl\":null,\"price\":null},\"modellineDetail\":null,\"optionList\":[{\"customOptionId\":null,\"optionType\":null,\"optionSubType\":null,\"optionWeight\":null,\"isNecessary\":null,\"optionNameCn\":\"智能激光大灯\",\"optionNameEn\":null,\"optionDesc\":null,\"optionCode\":\"8IZ\",\"optionPrice\":230,\"priceFlag\":null,\"priceLabel\":null,\"customOptionCode\":null,\"imageUrl\":null,\"optionGroup\":null,\"optionClassification\":\"HSW\",\"optionClassificationName\":null,\"optionClassification2nd\":\"HSW\",\"optionClassification2ndName\":null,\"disStatus\":null,\"reportType\":null,\"optionDetailVOs\":null},{\"customOptionId\":null,\"optionType\":null,\"optionSubType\":null,\"optionWeight\":null,\"isNecessary\":null,\"optionNameCn\":null,\"optionNameEn\":null,\"optionDesc\":null,\"optionCode\":\"PDW\",\"optionPrice\":5000,\"priceFlag\":null,\"priceLabel\":null,\"customOptionCode\":null,\"imageUrl\":null,\"optionGroup\":null,\"optionClassification\":null,\"optionClassificationName\":null,\"optionClassification2nd\":null,\"optionClassification2ndName\":null,\"disStatus\":null,\"reportType\":null,\"optionDetailVOs\":null},{\"customOptionId\":null,\"optionType\":null,\"optionSubType\":null,\"optionWeight\":null,\"isNecessary\":null,\"optionNameCn\":null,\"optionNameEn\":null,\"optionDesc\":null,\"optionCode\":\"2V3\",\"optionPrice\":230,\"priceFlag\":null,\"priceLabel\":null,\"customOptionCode\":null,\"imageUrl\":null,\"optionGroup\":null,\"optionClassification\":null,\"optionClassificationName\":null,\"optionClassification2nd\":null,\"optionClassification2ndName\":null,\"disStatus\":null,\"reportType\":null,\"optionDetailVOs\":null},{\"customOptionId\":null,\"optionType\":null,\"optionSubType\":null,\"optionWeight\":null,\"isNecessary\":null,\"optionNameCn\":null,\"optionNameEn\":null,\"optionDesc\":null,\"optionCode\":\"GZ2\",\"optionPrice\":230,\"priceFlag\":null,\"priceLabel\":null,\"customOptionCode\":null,\"imageUrl\":null,\"optionGroup\":null,\"optionClassification\":null,\"optionClassificationName\":null,\"optionClassification2nd\":null,\"optionClassification2ndName\":null,\"disStatus\":null,\"reportType\":null,\"optionDetailVOs\":null},{\"customOptionId\":null,\"optionType\":null,\"optionSubType\":null,\"optionWeight\":null,\"isNecessary\":null,\"optionNameCn\":null,\"optionNameEn\":null,\"optionDesc\":null,\"optionCode\":\"WA6\",\"optionPrice\":0,\"priceFlag\":null,\"priceLabel\":null,\"customOptionCode\":null,\"imageUrl\":null,\"optionGroup\":null,\"optionClassification\":null,\"optionClassificationName\":null,\"optionClassification2nd\":null,\"optionClassification2ndName\":null,\"disStatus\":null,\"reportType\":null,\"optionDetailVOs\":null},{\"customOptionId\":null,\"optionType\":null,\"optionSubType\":null,\"optionWeight\":null,\"isNecessary\":null,\"optionNameCn\":\"20吋10幅 星芒轮毂\",\"optionNameEn\":null,\"optionDesc\":null,\"optionCode\":\"F15\",\"optionPrice\":6000,\"priceFlag\":null,\"priceLabel\":null,\"customOptionCode\":null,\"imageUrl\":null,\"optionGroup\":null,\"optionClassification\":\"RAD\",\"optionClassificationName\":null,\"optionClassification2nd\":\"RAD\",\"optionClassification2ndName\":null,\"disStatus\":null,\"reportType\":null,\"optionDetailVOs\":null},{\"customOptionId\":null,\"optionType\":null,\"optionSubType\":null,\"optionWeight\":null,\"isNecessary\":null,\"optionNameCn\":\"智能电动尾门套装\",\"optionNameEn\":null,\"optionDesc\":null,\"optionCode\":\"PGC\",\"optionPrice\":0,\"priceFlag\":null,\"priceLabel\":null,\"customOptionCode\":null,\"imageUrl\":null,\"optionGroup\":null,\"optionClassification\":\"PACKET\",\"optionClassificationName\":null,\"optionClassification2nd\":\"PACKET\",\"optionClassification2ndName\":null,\"disStatus\":null,\"reportType\":null,\"optionDetailVOs\":null}],\"outsideColor\":{\"colorCode\":\"2T2T\",\"colorDesc\":null,\"colorFlag\":null,\"colorNameCn\":\"旷夜黑 曜黑套装\",\"colorNameEn\":null,\"colorPainting\":null,\"colorWeight\":null,\"customColorCode\":null,\"customColorId\":null,\"customModelId\":null,\"imageUrl\":null,\"price\":1000},\"totalPrice\":null},\"configSrcType\":null,\"configTime\":\"2022-10-11 12:00:00\",\"ownerInfoQfExpired\":null,\"ownerMobile\":null,\"ownerName\":null,\"bestRecommendId\":null,\"measureId\":\"1553543545143095297\",\"depositType\":null,\"valid\":1,\"invalidReason\":null,\"updateFlag\":0,\"updateContent\":null}";
        DetailIndex detailIndex = new DetailIndex(client, Constant.MASTER_CHANNEL);
        List<CarCustomDetail> detailList = new ArrayList<>();
        CarCustomDetail detail = JSONObject.parseObject(text, CarCustomDetail.class);
        detailList.add(detail);
        detailIndex.batchAddIndex(detailList);
    }

    @Test
    public void testPath() throws Exception{
        InputStream inputStream = OmdDetailIndex.class.getResourceAsStream("/index/mapping/detailMapping.json");
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        byte[] buf = new byte[8192];
        int i = -1;
        while ((i = inputStream.read(buf)) >0){
            bos.write(buf, 0, i);
        }
        System.out.println(bos.toString("UTF-8"));
    }

}
