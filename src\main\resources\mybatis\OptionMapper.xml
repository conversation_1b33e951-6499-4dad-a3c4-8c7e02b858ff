<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.OptionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.Option">
        <id column="option_id" property="optionId" />
        <result column="accb_option_id" property="accbOptionId" />
        <result column="preview" property="preview" />
        <result column="thumbnail" property="thumbnail" />
        <result column="code" property="code" />
        <result column="price" property="price" />
        <result column="description" property="description" />
        <result column="category" property="category" />
        <result column="classification" property="classification" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        option_id, accb_option_id, preview, thumbnail, code, price, description, category, classification
    </sql>

</mapper>
