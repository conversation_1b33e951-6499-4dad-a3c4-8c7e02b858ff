package com.csvw.audi.cc.entity.dto.omd;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class PrPriceRes {
    private String accbTypeCode;
    private String priceVersion;
    private String modelCode;
    private String modelVersion;
    private BigDecimal price;
    private String priceType;
    private BigDecimal discountAmount;
    private String prCode;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDate expireDate;
    private String modelYear;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDate effectiveDate;
    private String brandCode;
}
