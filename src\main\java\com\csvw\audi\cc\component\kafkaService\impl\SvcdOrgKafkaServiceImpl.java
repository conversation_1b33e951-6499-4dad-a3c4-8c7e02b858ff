package com.csvw.audi.cc.component.kafkaService.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.csvw.audi.cc.component.kafkaService.SvcdKafkaService;
import com.csvw.audi.cc.entity.dto.DealerDto;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.mapper.*;
import com.csvw.audi.cc.service.IDealerImageAssistant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
@Slf4j
@Service("svcdOrgKafkaService")
public class SvcdOrgKafkaServiceImpl implements SvcdKafkaService {

    @Autowired
    private SvcdChannelOrganizationMapper svcdChannelOrganizationMapper;

    @Autowired
    private SvcdOwnershipStructureBosMapper svcdOwnershipStructureBosMapper;

    @Autowired
    private SvcdChannelOrganizationFileMapper svcdChannelOrganizationFileMapper;

    @Autowired
    private SvcdChannelOrganizationPrimaryMapper svcdChannelOrganizationPrimaryMapper;

    @Autowired
    private SvcdChannelOrganizationPolicyMapper svcdChannelOrganizationPolicyMapper;
    @Autowired
    private IDealerImageAssistant dealerImageAssistant;

    @Override
    public void insertData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdChannelOrganization svcdOrg = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdChannelOrganization.class);
        svcdOrg.setCreatedAt(nowDate);
        svcdOrg.setUpdatedAt(nowDate);
        svcdOrg.insert();
        //股权信息
        if(svcdOrg.getOwnershipStructureBOS() != null) {
            for(SvcdOwnershipStructureBos svcdOwnershipStructureBos : svcdOrg.getOwnershipStructureBOS()) {
                svcdOwnershipStructureBos.setChannelOrganizationId(svcdOrg.getChannelOrganizationId());
                svcdOwnershipStructureBos.setCreatedAt(nowDate);
                svcdOwnershipStructureBos.setUpdatedAt(nowDate);
                svcdOwnershipStructureBos.insert();
            }
        }
        //文件信息
        if(svcdOrg.getFileList() != null) {
            for(SvcdChannelOrganizationFile orgFile : svcdOrg.getFileList()) {
                orgFile.setChannelOrganizationId(svcdOrg.getChannelOrganizationId());
                orgFile.setCreatedAt(nowDate);
                orgFile.setUpdatedAt(nowDate);
                orgFile.insert();
            }
            try {
                dealerImageAssistant.asyncFindAndStorageDealerImage(svcdOrg.getFileList());
            }catch (Exception e){
                log.info("网发推送来的消息文件上传oss异常");
                e.printStackTrace();
            }
        }
        //区域商务经理
        if(svcdOrg.getOrgPrimary() != null) {
            SvcdChannelOrganizationPrimary orgPrimary = svcdOrg.getOrgPrimary();
            orgPrimary.setChannelOrganizationId(svcdOrg.getChannelOrganizationId());
            orgPrimary.setCreatedAt(nowDate);
            orgPrimary.setUpdatedAt(nowDate);
            orgPrimary.insert();
        }
        //政策信息
        if(svcdOrg.getPolicyList() != null) {
            for(SvcdChannelOrganizationPolicy policy : svcdOrg.getPolicyList()) {
                policy.setChannelOrganizationId(svcdOrg.getChannelOrganizationId());
                policy.setDealerCode(svcdOrg.getDealerCode());
                policy.setCreatedAt(nowDate);
                policy.setUpdatedAt(nowDate);
                policy.insert();
            }
        }
    }

    @Override
    public void updateData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdChannelOrganization org = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdChannelOrganization.class);
        String dealerCode = org.getDealerCode();

        QueryWrapper<SvcdChannelOrganization> orgQusery = new QueryWrapper<>();
        orgQusery.eq("dealer_code",dealerCode);
        List<SvcdChannelOrganization> list = org.selectList(orgQusery);
        if(list == null || list.size() == 0) {
            org.setCreatedAt(nowDate);
            org.setUpdatedAt(nowDate);
            org.insert();
        } else {
            org.setUpdatedAt(nowDate);

            UpdateWrapper<SvcdChannelOrganization> orgUpdateWrapper = new UpdateWrapper<>();
            orgUpdateWrapper.eq("dealer_code",dealerCode);
            org.setDealerCode(null);//主键置空，防止数据库更新报错
            svcdChannelOrganizationMapper.update(org,orgUpdateWrapper);

            //删除 股权信息
            QueryWrapper<SvcdOwnershipStructureBos> orgBosQusery = new QueryWrapper<>();
            orgBosQusery.eq("dealer_code",dealerCode);
            svcdOwnershipStructureBosMapper.delete(orgBosQusery);

            //删除 文件信息
            QueryWrapper<SvcdChannelOrganizationFile> orgFileQusery = new QueryWrapper<>();
            orgFileQusery.eq("dealer_code",dealerCode);
            svcdChannelOrganizationFileMapper.delete(orgFileQusery);

            //删除 区域商务经理
            QueryWrapper<SvcdChannelOrganizationPrimary> orgPrimaryQusery = new QueryWrapper<>();
            orgPrimaryQusery.eq("dealer_code",dealerCode);
            svcdChannelOrganizationPrimaryMapper.delete(orgPrimaryQusery);

            //删除 政策信息
            QueryWrapper<SvcdChannelOrganizationPolicy> orgPolicyQusery = new QueryWrapper<>();
            orgPolicyQusery.eq("dealer_code",dealerCode);
            svcdChannelOrganizationPolicyMapper.delete(orgPolicyQusery);

            org.setChannelOrganizationId(list.get(list.size()-1).getChannelOrganizationId());
        }

        //股权信息
        if(org.getOwnershipStructureBOS() != null) {
            for(SvcdOwnershipStructureBos svcdOwnershipStructureBos : org.getOwnershipStructureBOS()) {
                svcdOwnershipStructureBos.setChannelOrganizationId(org.getChannelOrganizationId());
                svcdOwnershipStructureBos.setCreatedAt(nowDate);
                svcdOwnershipStructureBos.setUpdatedAt(nowDate);
                svcdOwnershipStructureBos.insert();
            }
        }
        //文件信息
        if(org.getFileList() != null) {
            for(SvcdChannelOrganizationFile orgFile : org.getFileList()) {
                orgFile.setChannelOrganizationId(org.getChannelOrganizationId());
                orgFile.setCreatedAt(nowDate);
                orgFile.setUpdatedAt(nowDate);
                orgFile.insert();
            }
            try {
                dealerImageAssistant.asyncFindAndStorageDealerImage(org.getFileList());
            }catch (Exception e){
                log.info("网发推送来的消息文件上传oss异常");
                e.printStackTrace();
            }
        }
        //区域商务经理
        if(org.getOrgPrimary() != null) {
            SvcdChannelOrganizationPrimary orgPrimary = org.getOrgPrimary();
            orgPrimary.setChannelOrganizationId(org.getChannelOrganizationId());
            orgPrimary.setCreatedAt(nowDate);
            orgPrimary.setUpdatedAt(nowDate);
            orgPrimary.insert();
        }
        //政策信息
        if(org.getPolicyList() != null) {
            for(SvcdChannelOrganizationPolicy policy : org.getPolicyList()) {
                policy.setChannelOrganizationId(org.getChannelOrganizationId());
                policy.setDealerCode(dealerCode);
                policy.setCreatedAt(nowDate);
                policy.setUpdatedAt(nowDate);
                policy.insert();
            }
        }
    }
}
