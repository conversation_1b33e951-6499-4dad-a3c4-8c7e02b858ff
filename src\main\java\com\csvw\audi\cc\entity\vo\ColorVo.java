package com.csvw.audi.cc.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class ColorVo {
    @ApiModelProperty(value = "自定义颜色ID")
    private String customColorId;

    @ApiModelProperty(value = "车系4位码")
    private String seriesCode;

    @ApiModelProperty(value = "颜色代码，两位（内饰）或四位（外饰）")
    private String colorCode;

    @ApiModelProperty(value = "自定义颜色代码")
    private String customColorCode;

    @ApiModelProperty(value = "颜色英文名称")
    private String colorNameEn;

    @ApiModelProperty(value = "颜色中文名称")
    private String colorNameCn;

    @ApiModelProperty(value = "颜色描述")
    private String colorDesc;

    @ApiModelProperty(value = "喷漆")
    private String colorPainting;

    @ApiModelProperty(value = "内外标志0：内饰，1：外观")
    private String colorFlag;

    @ApiModelProperty(value = "前置颜色条件")
    private String cdColorCode;

    @ApiModelProperty(value = "前置选装包条件")
    private String cdOptionCode;

    @ApiModelProperty(value = "颜色状态")
    private Boolean colorStatus;

    @ApiModelProperty(value = "排序权重")
    private Integer colorWeight;

    @ApiModelProperty(value = "标识是否默认颜色 1：是 其它：否")
    private String isDefaultColor;

    @ApiModelProperty(value = "图片地址")
    private String imageUrl;

    @ApiModelProperty(value = "图片地址")
    private List<ColorDesc> descImageUrls;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ColorDesc{
        @ApiModelProperty(value = "图片地址")
        private String imageUrl;
        @ApiModelProperty(value = "描述")
        private String desc;
    }

}
