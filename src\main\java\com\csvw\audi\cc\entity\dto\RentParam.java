package com.csvw.audi.cc.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RentParam {
    private Long loanRuleId;
    @ApiModelProperty(value = "价格")
    private BigDecimal price;
    @ApiModelProperty(value = "先付金额")
    private BigDecimal payment;
    @ApiModelProperty(value = "先付比例")
    private BigDecimal paymentRatio;
    @ApiModelProperty(value = "融资租赁月期")
    private String month;
}
