package com.csvw.audi.cc.controller.ccnew;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.common.utils.ChannelDataUtils;
import com.csvw.audi.cc.config.AppConfig;
import com.csvw.audi.cc.entity.dto.*;
import com.csvw.audi.cc.entity.dto.accb.AudiConfigDto;
import com.csvw.audi.cc.entity.dto.accb.OptionBriefDto;
import com.csvw.audi.cc.entity.dto.omd.OmdModelDto;
import com.csvw.audi.cc.entity.dto.vwcc.CarCustomDetail;
import com.csvw.audi.cc.entity.enumeration.BestRecommendTypeEnum;
import com.csvw.audi.cc.entity.enumeration.OptionCategoryEnum;
import com.csvw.audi.cc.entity.enumeration.OptionTypeEnum;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.entity.vo.*;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.service.*;
import com.csvw.audi.cc.service.impl.CacheServiceImpl;
import com.csvw.audi.cc.service.impl.CarConfigSnapshotService;
import com.csvw.audi.cc.service.impl.CarRecommendCustomServiceImpl;
import com.csvw.audi.cc.service.impl.OmdServiceImpl;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/open/api/v1/cc")
@Api(tags = "车辆配置开放接口")
@Slf4j
public class CarConfigOpenApiController extends BaseController {

    @Autowired
    private ICarCustomOptionService optionService;

    @Autowired
    private ICarRenderService renderService;

    @Autowired
    private ICarCustomService customService;

    @Autowired
    private ICarCustomSeriesService customSeriesService;

    @Autowired
    private ICarModelLineService modelLineService;

    @Autowired
    private ICarModelLineOptionService modelLineOptionService;

    @Autowired
    private ICarOptionRelateService optionRelateService;

    @Autowired
    private ICarRecommendService recommendService;

    @Autowired
    private ICarModelLineSibInterieurService sibInterieurService;

    @Autowired
    private ICarOmdPriceTypeService priceTypeService;

    @Autowired
    private CarRecommendCustomServiceImpl recommendCustomService;

    @Autowired
    private ICarCustomService carCustomService;

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private ICarBestRecommendStockService bestRecommendStockService;

    @Autowired
    private ICarOmdVehicleTypeService omdVehicleTypeService;

    @Autowired
    private CacheServiceImpl cacheService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ICarOmdDataMapService omdDataMapService;

    @Autowired
    private OmdServiceImpl omdService;

    @Autowired
    private ICarCustomOptionService carCustomOptionService;

    @Autowired
    private ICarModelLineParameterService modelLineParameterService;

    @Autowired
    private ICarMeasureMadeConfigService measureMadeConfigService;

    @Autowired
    private ICarModelLineTypeService modelLineTypeService;

    @Autowired
    private CarConfigSnapshotService carConfigSnapshot;

    @Autowired
    private ICarShoppingCartService carShoppingCartService;

    private final String OMD_ABC_LOCK = "saic_audi:applock:audi_car_config:receive_omd_abc";

    @GetMapping("/modelLine/configs/color_exterieur")
    @ApiOperation("配置线外饰")
    public AjaxMessage<List<ModelLineOptionVo>> modelLineExterieur(@RequestHeader(value = "channel", defaultValue = Constant.MASTER_CHANNEL) String channel, String modelLineId, String seats) throws Exception {
        String category = "COLOR_EXTERIEUR";
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        ModelLineVo line = lines.get(0);
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, modelLineId, category);
        if (StringUtils.isNotBlank(line.getTypeFlag()) && !line.getTypeFlag().contains("C")) {
            TypePrListQueryDto queryDto = new TypePrListQueryDto(line.getAccbTypeCode(), line.getModelYear(), line.getVersion());
            optionVos = optionVos.stream().filter(o->{
                queryDto.setOptionVo(o);
                queryDto.setModelLineVo(line);
                queryDto.setSeats(seats);
                return modelLineTypeService.filterOption(queryDto);
            }).collect(Collectors.toList());
        }
        return successMessage(optionVos);
    }


    @GetMapping("/modelLine/configs/color_interieur")
    @ApiOperation("配置线内饰")
    public AjaxMessage<List<ModelLineOptionVo>> modelLineInterieur(@RequestHeader(value = "channel", defaultValue = Constant.MASTER_CHANNEL) String channel, String modelLineId) throws Exception {
        String category = "COLOR_INTERIEUR";
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, modelLineId, category);
        return successMessage(optionVos);
    }

    @GetMapping(value = {"/modelLine/configs/sib_color_interieur"})
    @ApiOperation("配置线内饰面料整合")
    public AjaxMessage<List<ModelLineSibInterieurVo>> modelLineSibInterieur(@RequestHeader(value = "channel", defaultValue = Constant.MASTER_CHANNEL) String channel,
                                                                            String modelLineId, String sibInterieurId, String seats) throws Exception {
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        ModelLineVo line = lines.get(0);
        List<ModelLineSibInterieurVo> sibInterieurVos = sibInterieurService.modelLineSibInterieur(channel, modelLineId, sibInterieurId);
        OptionRelateParam optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLineId);
        optionRelateParam.setRelateTypes(Arrays.asList("conflict", "depend"));
//        optionRelateParam.setNotInRelateCategory(Arrays.asList("COLOR_INTERIEUR", "COLOR_EXTERIEUR", "SIB"));
        List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        Map<String, List<OptionRelDto>> oMap = new HashMap<>();
        relDtos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel == null) {
                oRel = new ArrayList<>();
                oMap.put(i.getOptionId(), oRel);
            }
            oRel.add(i);
        });
        sibInterieurVos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getSibInterieurId());
            if (oRel != null){
                i.setSibInterieurRelates(oRel);
            }
        });
        if (StringUtils.isNotBlank(line.getTypeFlag()) && !line.getTypeFlag().contains("C")) {
            TypePrListQueryDto queryDto = new TypePrListQueryDto(line.getAccbTypeCode(), line.getModelYear(), line.getVersion());
            sibInterieurVos = sibInterieurVos.stream().filter(o->{
                queryDto.setSibInterieurVo(o);
                queryDto.setModelLineVo(line);
                queryDto.setSeats(seats);
                return modelLineTypeService.filterInterOption(queryDto);
            }).collect(Collectors.toList());
        }
        return successMessage(sibInterieurVos);
    }

    @GetMapping(value = {"/modelLine/configs/eih"})
    @ApiOperation("配置线饰条")
    public AjaxMessage<List<ModelLineOptionVo>> modelLineEid(@RequestHeader(value = "channel", defaultValue = Constant.MASTER_CHANNEL) String channel,
                                                             String modelLineId, String seats) throws Exception {
        String category = "EIH";
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        ModelLineVo line = lines.get(0);
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, modelLineId, category);
        if (StringUtils.isNotBlank(line.getTypeFlag()) && !line.getTypeFlag().contains("C")) {
            TypePrListQueryDto queryDto = new TypePrListQueryDto(line.getAccbTypeCode(), line.getModelYear(), line.getVersion());
            optionVos = optionVos.stream().filter(o->{
                queryDto.setOptionVo(o);
                queryDto.setModelLineVo(line);
                queryDto.setSeats(seats);
                return modelLineTypeService.filterOption(queryDto);
            }).collect(Collectors.toList());
        }
        return successMessage(optionVos);
    }

    @GetMapping(value = {"/modelLine/configs/vos"})
    @ApiOperation("配置线座椅")
    public AjaxMessage<List<ModelLineOptionVo>> modelLineVos(@RequestHeader(value = "channel", defaultValue = Constant.MASTER_CHANNEL) String channel,
                                                             @RequestParam String modelLineId) throws Exception {
        String category = "VOS";
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, modelLineId, category);
        return successMessage(optionVos);
    }

    @GetMapping("/modelLine/configs/optional_equipment")
    @ApiOperation("配置线可选装备")
    public AjaxMessage<List<ModelLineOptionVo>> modelLineEquipment(String modelLineId){
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOptionalEquipment(Constant.MASTER_CHANNEL, modelLineId);
        return successMessage(optionVos);
    }

    @GetMapping(value = {"/modelLine/configs/rad"})
    @ApiOperation("配置线轮毂")
    public AjaxMessage<List<ModelLineOptionVo>> modelLineRad(@RequestHeader(value = "channel", defaultValue = Constant.MASTER_CHANNEL) String channel, String modelLineId, String seats) throws Exception {
        String category = "RAD";
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        ModelLineVo line = lines.get(0);
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, modelLineId, category);
        if (StringUtils.isNotBlank(line.getTypeFlag()) && !line.getTypeFlag().contains("C")) {
            TypePrListQueryDto queryDto = new TypePrListQueryDto(line.getAccbTypeCode(), line.getModelYear(), line.getVersion());
            optionVos = optionVos.stream().filter(o->{
                queryDto.setOptionVo(o);
                queryDto.setModelLineVo(line);
                queryDto.setSeats(seats);
                return modelLineTypeService.filterOption(queryDto);
            }).collect(Collectors.toList());
        }
        optionVos = modelLineService.handleVirtualRad(line, seats, optionVos);
        return successMessage(optionVos);
    }

    @GetMapping("/customSeries")
    @ApiOperation("自定义车系(车系)")
    public AjaxMessage<List<CustomSeriesVo>> customSeries() throws Exception {
        SeriesParamDto paramDto = new SeriesParamDto();
        paramDto.setChannel("master");
        List<CustomSeriesVo> data = customSeriesService.listCustomSeriesVo(paramDto);
        return successMessage(data);
    }


    @GetMapping(value = {"/modelLine/parameter"})
    @ApiOperation("配置线参数")
    public AjaxMessage<List<ModelLineParameterVo>> parameter(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                             @RequestParam String modelLineId) throws Exception {
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        List<ModelLineParameterVo> data = modelLineParameterService.listModelLineParameter(modelLineId);
        return successMessage(data);
    }

    @GetMapping(value = {"/modelLine/groupList"})
    @ApiOperation("配置线(车型)分组列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "配置线类型，1：高定，2：半定，3：高定半定混合", required = true, paramType = "query")
    })
    public AjaxMessage<List<ModelLineGroupVo>> modelLineGroupList(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                                  @RequestParam String customSeriesId, @RequestParam(defaultValue = "1") int type) throws Exception {
        CustomSeriesParam seriesParam = new CustomSeriesParam();
        seriesParam.setCustomSeriesId(customSeriesId);
        List<CarCustomSeries> carCustomSeries = customSeriesService.listCustomSeries(channel, seriesParam);
        if (CollectionUtils.isEmpty(carCustomSeries)){
            throw new ServiceException("参数异常：customSeriesId", "400401", "");
        }
        ModelParamDto paramDto = new ModelParamDto();
        paramDto.setCustomSeriesId(customSeriesId);
        paramDto.setDelFlag(0);
        paramDto.setChannel(channel);
        List<ModelLineVo> data = modelLineService.listModelLine(paramDto);
        data = data.stream().filter(i-> {
            if((i.getMeasure() == null || i.getMeasure() != 1) && Constant.NEED_TYPE_FLAG.contains(channel)){
                if (StringUtils.isBlank(i.getTypeFlag())){
                    return false;
                }
            }
            List<ModelLineOptionVo> optionVos;
            try {
                optionVos = modelLineService.modelLinePersonalOptionDefault(i.getModelLineId());
                if(i.getPriceAddColor() != null && i.getPriceAddColor().intValue() == 1) {
                    String category = "COLOR_EXTERIEUR";
                    List<ModelLineOptionVo> exterieurs = modelLineService.modelLineOption(channel, i.getModelLineId(), category).stream().filter(e -> e.getDefaultConfig() == 1).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(exterieurs)) {
                        optionVos.add(exterieurs.get(0));
                    }
                }
            } catch (Exception e) {
                log.error("查询配置线默认私人定制异常", e);
                return false;
            }
            if (CollectionUtils.isNotEmpty(optionVos)){
                List<String> optionIds = optionVos.stream().map(ModelLineOptionVo::getOptionId).collect(Collectors.toList());
                PriceComputeParam computeParam = new PriceComputeParam();
                computeParam.setOptionIds(optionIds);
                computeParam.setModelLineId(i.getModelLineId());
                try {
                    i.setPrice(modelLineService.computePrice(computeParam));
                } catch (ServiceException e) {
                    log.error("查询配置线价格计算异常", e);
                    return false;
                }
            }
            if (i.getFrontStatus() == null || i.getFrontStatus().intValue() == 0) {
                return false;
            }
            switch (type){
                case 1:
                    return i.getPersonal() != null && i.getPersonal().intValue() == 1;
                case 2:
                    return i.getMeasure() != null && i.getMeasure().intValue() == 1 && modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId());
                case 3:
                    return (i.getMeasure() != null && i.getMeasure().intValue() == 1 && modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId())) ||
                            (i.getPersonal() != null && i.getPersonal().intValue()  == 1);
            }
            return false;
        }).collect(Collectors.toList());
        Map<String, ModelLineGroupVo> map = new HashMap<>();
        List<ModelLineGroupVo> res = new ArrayList<>();
        ModelLineGroupVo noEngineVo = new ModelLineGroupVo();
        res.add(noEngineVo);
        for (ModelLineVo v : data){
            if (StringUtils.isBlank(v.getEngine())){
                if (noEngineVo.getModelLines() == null){
                    noEngineVo.setModelLines(new ArrayList<>());
                }
                noEngineVo.getModelLines().add(v);
                continue;
            }
            ModelLineGroupVo vo = map.get(v.getEngine());
            if (vo == null){
                vo = new ModelLineGroupVo();
                map.put(v.getEngine(), vo);
                res.add(vo);
                vo.setModelLines(new ArrayList<>());
                vo.setEngine(v.getEngine());
                vo.getModelLines().add(v);
            }else {
                vo.getModelLines().add(v);
            }
        }
        if(res.get(0).getModelLines() == null){
            res.remove(0);
        }
        return successMessage(res);
    }

    /**
     * vsearch渠道车型，智能客服
     * @param channel
     * @param customSeriesId
     * @return
     * @throws Exception
     */
    @GetMapping("/modelLine")
    @ApiOperation("配置线(车型)")
    public AjaxMessage<List<ModelLineVo>> modelLine(@RequestHeader(value = "channel", defaultValue = Constant.MASTER_CHANNEL) String channel,
            @RequestParam String customSeriesId) throws Exception {
        ModelParamDto paramDto = new ModelParamDto();
        paramDto.setCustomSeriesId(customSeriesId);
        paramDto.setChannel(channel);
        paramDto.setDelFlag(0);
        List<ModelLineVo> data = modelLineService.listModelLine(paramDto).stream().filter(i ->{
            if(i.getFrontStatus() == null || i.getFrontStatus().intValue() == 0){
                return false;
            }
            if(i.getOmdModelStatus() == 0 && (i.getMeasure() == null || i.getMeasure() != 1) && Constant.NEED_TYPE_FLAG.contains(channel)){
                if (StringUtils.isBlank(i.getTypeFlag())){
                    return false;
                }
            }
            return (i.getMeasure() != null && i.getMeasure().intValue() == 1 && modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId())) ||
                            (i.getPersonal() != null && i.getPersonal().intValue()  == 1);
        }).collect(Collectors.toList());
        return successMessage(data);
    }

    @GetMapping("/toAstro/modelLine")
    @ApiOperation("配置线(车型)")
    public AjaxMessage<List<ModelLineBriefVo>> modelLine() throws Exception {
        ModelParamDto paramDto = new ModelParamDto();
        paramDto.setChannel(Constant.MASTER_CHANNEL);
        List<ModelLineBriefVo> data = modelLineService.listModelLineAstro(paramDto);
        return successMessage(data);
    }

    @GetMapping(value = {"/modelLine/parameterAndOptions"})
    @ApiOperation("配置线参数标配")
    public AjaxMessage<ModelLineConfigVo> modelLineConfigs(@RequestHeader(value = "channel", defaultValue = Constant.MASTER_CHANNEL) String channel,
                                                           @RequestParam String modelLineId) throws Exception {
        Integer status = 1;
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        ModelLineConfigVo configVo = modelLineService.modelLineConfigToDrm(channel, modelLineId, status);
        return successMessage(configVo);
    }

    @GetMapping("/recommendCar")
    @ApiOperation("推荐车")
    public AjaxMessage<List<DrmRecommendCarVo>> recommendCar(@RequestHeader(value = "channel", defaultValue = Constant.DRM_CHANNEL) String channel, String customSeriesId){
        List<RecommendCarVo> recommendCarVos = recommendService.recommendCar(channel, customSeriesId);
        return successMessage(recommendCarVos.stream().map(i->{
            DrmRecommendCarVo vo = new DrmRecommendCarVo();
            BeanUtils.copyProperties(i, vo);
            vo.setSpecialLine(i.getModelLine().getSpecialLine());
            return vo;
        }).collect(Collectors.toList()));
    }


    @PostMapping("/modelLine/price")
    @ApiOperation("配置线价格")
    public AjaxMessage<BigDecimal> price(@RequestBody @Validated TypePriceParam priceParam, BindingResult bindingResult) throws ServiceException {
        super.validParam(bindingResult);
        BigDecimal price = priceTypeService.accbTypePrice(priceParam);
        return successMessage(price);
    }

    @PostMapping("/modelLine/color/price")
    @ApiOperation("外饰价格")
    public AjaxMessage<BigDecimal> colorPrice(@RequestBody @Validated OptionPriceParam priceParam, BindingResult bindingResult) throws ServiceException {
        super.validParam(bindingResult);
        OptionPriceQuery priceQuery = new OptionPriceQuery();
        BeanUtils.copyProperties(priceParam, priceQuery);
        priceQuery.setCategory("COLOR_EXTERIEUR");
        BigDecimal price = priceTypeService.accbOptionPrice(priceQuery);
        return successMessage(price);
    }

    @PostMapping("/modelLine/pr/price")
    @ApiOperation("选装件价格")
    public AjaxMessage<BigDecimal> prPrice(@RequestBody @Validated OptionPriceParam priceParam, BindingResult bindingResult) throws ServiceException {
        super.validParam(bindingResult);
        OptionPriceQuery priceQuery = new OptionPriceQuery();
        BeanUtils.copyProperties(priceParam, priceQuery);
        priceQuery.setCategory("");
        BigDecimal price = priceTypeService.accbOptionPrice(priceQuery);
        return successMessage(price);
    }

    @PostMapping("/modelLine/prPkg/price")
    @ApiOperation("选装包价格")
    public AjaxMessage<BigDecimal> prPkgPrice(@RequestBody @Validated OptionPriceParam priceParam, BindingResult bindingResult) throws ServiceException {
        super.validParam(bindingResult);
        OptionPriceQuery priceQuery = new OptionPriceQuery();
        BeanUtils.copyProperties(priceParam, priceQuery);
        priceQuery.setCategory("PACKET");
        BigDecimal price = priceTypeService.accbOptionPrice(priceQuery);
        return successMessage(price);
    }

    @GetMapping(value = {"/bestRecommendCarAgent", "/public/bestRecommendCarAgent"})
    @ApiOperation("OMD经销商推荐车（经销商库存车）")
    public AjaxMessage<List<BestRecommendCarVo>> bestRecommendCarAgent(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, String dealerCode, String customSeriesId, Boolean sortedByStockNum){
        List<BestRecommendCarVo> recommendCarVos = recommendService.omdBestRecommendCar(channel, dealerCode, customSeriesId);
        recommendCarVos = recommendCarVos.stream().sorted((a,b)->{
            BigDecimal aPrice=null, bPrice=null;
            if (a.getTotalPrice() != null && a.getTotalPrice() instanceof BigDecimal){
                aPrice = (BigDecimal) a.getTotalPrice();
            }
            if (b.getTotalPrice() != null && b.getTotalPrice() instanceof BigDecimal){
                bPrice = (BigDecimal) b.getTotalPrice();
            }
            if (aPrice==null && bPrice== null){
                return 0;
            }
            if (aPrice == null){
                return 1;
            }
            if (bPrice == null){
                return -1;
            }
            return bPrice.compareTo(aPrice);
        }).filter(i->{
            // 过滤先行版
            if(appConfig.getBestRecommend() != null
                    && appConfig.getBestRecommend().getFilterEnable()
                    && CollectionUtils.isNotEmpty(appConfig.getBestRecommend().getModelLineIds())
                    && appConfig.getBestRecommend().getModelLineIds().contains(i.getModelLine().getModelLineId())){
                return false;
            }
            // 过滤Q5E
            if(appConfig.getBestRecommend() != null
                    && appConfig.getBestRecommend().getFilterEnable()
                    && CollectionUtils.isNotEmpty(appConfig.getBestRecommend().getCustomSeriesIds())
                    && appConfig.getBestRecommend().getCustomSeriesIds().contains(i.getModelLine().getCustomSeriesId())){
                return false;
            }

            // 特定经销商过滤特定渠道特定配置，N5V-FF。处理错误车辆特选车销售
            if(!channel.equals(Constant.ONEAPP_CHANNEL)){
                if (Arrays.asList("76646059", "76623019").contains(dealerCode)
                        && i.getModelLine().getAccbTypeCode().equals("TYPE:498B2Y-GPAHPAH")
                        && i.getModelLineSibInterieurVo().getSibInterieurCode().equals("N5V-FF")){
                    return false;
                }
            }

            // 库存过滤
            return bestRecommendStockService.validRecommendStock(i, dealerCode);
        }).collect(Collectors.toList());
        if (sortedByStockNum != null && sortedByStockNum){
            recommendCarVos.sort(Comparator.comparing(BestRecommendCarVo::getStockNum).reversed());
        }
        return successMessage(recommendCarVos);
    }

    @PostMapping(value = {"/modelLine/configs/ccPriceCompute"})
    @ApiOperation("价格计算")
    public AjaxMessage<Object> ccPriceCompute(@RequestBody @Validated PriceComputeParam priceComputeParam, BindingResult bindingResult) throws ServiceException {
        super.validParam(bindingResult);
        Object price = modelLineService.computePrice(priceComputeParam);
        return successMessage(price);
    }

    @PostMapping(value = {"/modelLine/configs/measureQuery"})
    @ApiOperation("半订制化，查询选配")
    @ApiResponses(value = {@ApiResponse(code = 200001, message = "配置线没有半订制化车辆"), @ApiResponse(code = 400401, message = "操作繁忙，稍后重试")})
    public AjaxMessage<MeasureVo> measureQuery(@RequestHeader(value ="channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, @RequestBody @Validated MeasureQueryParam measureQueryParam, BindingResult bindingResult, HttpServletResponse response) throws Exception {
        super.validParam(bindingResult);
        MeasureVo measureVo = measureMadeConfigService.measureQuery(measureQueryParam, channel);
        if (measureVo == null){
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            return new AjaxMessage<>("400401", "操作繁忙，稍后重试", null);
        }
        return successMessage(measureVo);
    }

    @PostMapping(value = {"/measure/carConfig"})
    @ApiOperation("配置器半定配车（ccid）")
    public AjaxMessage<CarCustomDetail> addcarconfig(@RequestHeader(value = "X-User-Id", required = false) String userId,
                                                     @RequestHeader(value = "X-User-Mobile", required = false) String userMobile,
                                                     @RequestHeader(value = "channel", required = false) String channel,
                                                     Long measureId, Long sourceId, String entryPoint, String packetEquityId) throws Exception {
        CarCustom carCustom = measureMadeConfigService.addCarCustomConfig(userId,userMobile,measureId, sourceId, channel, entryPoint, packetEquityId);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        return new AjaxMessage<>("00", "成功", detail);
    }

    @PostMapping(value = {"/modelLine/configs/ccEstimate"})
    @ApiOperation("CC预计交付时间")
    public AjaxMessage<CcEstimateVo> ccEstimate(@RequestBody @Validated CcEstimateDeliveryParam estimateDeliveryParam, BindingResult bindingResult) throws Exception {
        super.validParam(bindingResult);
        CcEstimateVo res = modelLineService.ccEstimate(estimateDeliveryParam);
        return successMessage(res);
    }

    @PostMapping("/modelLine/configs/priceCompute")
    @ApiOperation("CCPRO-CODE价格计算")
    public AjaxMessage<Object> priceCompute(@RequestHeader(value = "channel", defaultValue = Constant.DRM_CHANNEL) String channel,
                                                @RequestBody @Validated CcProCodePriceComputeParam priceComputeParam, BindingResult bindingResult) throws Exception {
        super.validParam(bindingResult);
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(priceComputeParam.getModelLineId());
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        if (!lines.get(0).getAccbTypeCode().equals(priceComputeParam.getAccbTypeCode()) ){
            throw new ServiceException("参数异常：配置线参数不一致", "400401", "");
        }
        ModelLineVo line = lines.get(0);
        List<String> optionIds= new ArrayList<>();
        if (CollectionUtils.isNotEmpty(priceComputeParam.getCcProCodes())) {
            OptionParamDto measureOptionParam = new OptionParamDto();
            measureOptionParam.setModelLineId(priceComputeParam.getModelLineId());
            measureOptionParam.setNotOptionTypes(Arrays.asList(OptionTypeEnum.PACKETITEM.getValue()));
            measureOptionParam.setDelFlag(0);
            measureOptionParam.setChannel(channel);
            measureOptionParam.setCustomSeriesId(line.getCustomSeriesId());
            measureOptionParam.setOptionCodes(priceComputeParam.getCcProCodes().stream().map(i->{
                String[] codeSplit = i.split(":");
                if (codeSplit.length == 2){
                    return codeSplit[1];
                }
                return codeSplit[0];
            }).collect(Collectors.toList()));
            Map<String, String > optionMap = new HashMap<>();
            modelLineService.modelLineOptionQueryWithOutPriceFilter(measureOptionParam).forEach(i->{
                if (optionMap.get(i.getOptionCode()) == null){
                    optionMap.put(i.getOptionCode(), i.getOptionId());
                }
            });
            optionIds = new ArrayList<>(optionMap.values());
        }
        PriceComputeParam ccPriceComputeParam = new PriceComputeParam();
        ccPriceComputeParam.setModelLineId(line.getModelLineId());
        ccPriceComputeParam.setCustomSeriesId(line.getCustomSeriesId());
        ccPriceComputeParam.setOptionIds(optionIds);
        Object price = modelLineService.computePrice(ccPriceComputeParam);
        return successMessage(price);
    }

    @PostMapping("/modelLine/configs/accbPriceCompute")
    @ApiOperation("ACCB价格计算")
    public AjaxMessage<BigDecimal> accbPriceCompute(@RequestHeader(value = "channel", defaultValue = Constant.DRM_CHANNEL) String channel,
                                                @RequestBody @Validated AccbPriceComputeParam priceComputeParam, BindingResult bindingResult) throws Exception {
        super.validParam(bindingResult);
        OptionPriceQuery priceQuery = new OptionPriceQuery();
        BeanUtils.copyProperties(priceComputeParam, priceQuery);
        BigDecimal price = priceTypeService.accbTypePrice(priceQuery);
        if (price == null){
            return successMessage(null);
        }
        Map<String, String> packageItemMap = new HashMap<>();
        List<String> duplicateCodes = new ArrayList<>();
        for (String code : priceComputeParam.getAccbCodes()){
            String[] codeItems = code.split(":");
            if (codeItems.length != 2){
                return successMessage(null);
            }
            priceQuery.setCode(codeItems[1]);
            priceQuery.setCategory(codeItems[0]);
            if (priceQuery.getCategory().equals("COLOR_INTERIEUR")) {
                continue;
            }
            // 排除标装价格计算
            if (StringUtils.isNotBlank(priceComputeParam.getModelLineId())) {
                ModelParamDto modelParamDto = new ModelParamDto();
                modelParamDto.setDelFlag(0);
                modelParamDto.setModelLineId(priceComputeParam.getModelLineId());
                modelParamDto.setChannel(channel);
                List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
                if (CollectionUtils.isEmpty(lines)){
                    throw new ServiceException("参数异常：modelLineId", "400401", "");
                }
                if (!lines.get(0).getAccbTypeCode().equals(priceComputeParam.getAccbTypeCode()) ){
                    throw new ServiceException("参数异常：配置线参数不一致", "400401", "");
                }
                OptionParamDto optionParamDto = new OptionParamDto();
                optionParamDto.setModelLineId(priceComputeParam.getModelLineId());
                optionParamDto.setStatus(1);
                optionParamDto.setOptionCodes(Arrays.asList(priceQuery.getCode()));
                if (CollectionUtils.isNotEmpty(modelLineOptionService.modelLineOptionQuery(optionParamDto))){
                    continue;
                }
            }

            if (priceQuery.getCategory().equals("PACKET")) {
                List<ModelLineOptionVo> packageItems = modelLineOptionService.listOptionPacketItemByDrm(priceQuery);
                if (!CollectionUtils.isEmpty(packageItems)){
                    packageItems.forEach(i->{
                        if (packageItemMap.get(priceQuery.getCode()) != null){
                            duplicateCodes.add(priceQuery.getCode());
                        }else {
                            packageItemMap.put(priceQuery.getCode(), priceQuery.getCategory());
                        }
                    });
                }
            }
            BigDecimal oPrice = priceTypeService.accbOptionPrice(priceQuery);
            if (oPrice != null) {
                price = price.add(oPrice);
            }else {
                throw new ServiceException("价格参数异常："+code, "500501", null);
            }
        }
        for (String i : duplicateCodes){
            priceQuery.setCode(i);
            priceQuery.setCategory("");
            BigDecimal optionPrice = priceTypeService.accbOptionPrice(priceQuery);
            if (optionPrice != null) {
                price = price.subtract(optionPrice);
            }
        }
        return successMessage(price);
    }

    @GetMapping
    public AjaxMessage<AudiConfigVo> carconfig(@RequestParam String ccid) throws Exception {
        AudiConfigVo vo = customService.getAudiConfigVo(ccid);
        return new AjaxMessage<>("00", "成功", vo);
    }

    /**
     * omd ccdetail
     * to OMD
     * @param ccid
     * @return
     */
    @GetMapping("/omdCcDetail")
    public AjaxMessage<CarCustomDetail> carConfig(@RequestParam String ccid) throws Exception {

        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        if (carCustom == null){
            return failureMessage(null);
        }
        if (StringUtils.isBlank(carCustom.getModelLineId())){
            return super.failureMessage(null);
        }
        CarCustomDetail customDetail;
        if (carCustom.getBestRecommendId() != null){
            customDetail = recommendCustomService.getCarConfigDetailOmd(carCustom);
        }else {
            customDetail = customService.getCarConfigDetailOmd(carCustom);
        }
        if (ccid != null && customDetail != null) {
            log.info("customDetailOmd, ccid: " + ccid + "customDetail: " + JSONObject.toJSONString(customDetail));
        }
        return new AjaxMessage<>("00", "成功", customDetail);
    }

    /**
     *
     * @return
     * @throws Exception
     */
    @GetMapping("/receiveOmdAbc")
    public AjaxMessage<Object> receiveOmdAbc() throws Exception {
        RLock lock = redissonClient.getLock(OMD_ABC_LOCK);
        if(lock.isLocked()){
            return new AjaxMessage<>("50001", "存在正在同步的任务", null);
        }
        new Thread(()->{
            try {
                omdVehicleTypeService.receiveOmdAbc();
            } catch (Exception e) {
                log.error("接收ABC类数据异常", e);
            }
            cacheService.cacheEvictCc();
        }).start();
        return successMessage("");
    }

    @GetMapping("render")
    @ApiOperation(value = "车辆渲染图")
    @ApiImplicitParams(value = {@ApiImplicitParam(name="typeCode", value="配置线编码", required = true),
            @ApiImplicitParam(name="exteriorCode", value="外饰编码", required = true)})
    public AjaxMessage<List<CarRenderVo>> render(@RequestParam String typeCode, @RequestParam String exteriorCode){
        LambdaQueryWrapper<CarRender> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CarRender::getDelFlag, 0).eq(CarRender::getTypeCode, typeCode).eq(CarRender::getExteriorCode, exteriorCode);
        List<CarRender> carRenders = renderService.list(queryWrapper);
        List<CarRenderVo> vos = carRenders.stream().map(i->{
            CarRenderVo vo = new CarRenderVo();
            BeanUtils.copyProperties(i, vo);
            return vo;
        }).collect(Collectors.toList());
        return new AjaxMessage<>("00", "成功", vos);
    }

    @PostMapping("/source")
    @ApiOperation("ccid来源")
    @Transactional
    public AjaxMessage<CustomSourceVo> source(@Validated @RequestBody CustomSourceDto sourceDto, BindingResult bindingResult) throws Exception {
        validParam(bindingResult);
        LambdaQueryWrapper<CarCustomSource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CarCustomSource::getName, sourceDto.getName())
                .eq(CarCustomSource::getApp, sourceDto.getApp())
                .eq(CarCustomSource::getDealerCode, sourceDto.getDealerCode())
                .eq(CarCustomSource::getDealerName, sourceDto.getDealerName());
        CarCustomSource source = new CarCustomSource();
        CustomSourceVo vo = new CustomSourceVo();
        List<CarCustomSource> exists = source.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(exists)){
            CarCustomSource carCustomSource = exists.get(0);
            BeanUtils.copyProperties(carCustomSource, vo);
            vo.setSourceId(String.valueOf(carCustomSource.getSourceId()));
        }else {
            BeanUtils.copyProperties(sourceDto, source);
            source.setCreateTime(LocalDateTime.now());
            source.setType("2");
            source.insert();
            BeanUtils.copyProperties(source, vo);
            vo.setSourceId(String.valueOf(source.getSourceId()));
        }
        return successMessage(vo);
    }

    @ApiOperation("配置单同步(DRM -> CCPro)")
    @PostMapping("/addCarconfigFromDRM")
    public AjaxMessage<CarCustomDetail> addCarconfigFromDRM(@RequestHeader(value = "X-User-Id") String userId,
                                                            @RequestHeader(value = "X-User-Mobile") String userMobile,
                                                            @RequestHeader(value="X-Member-Id", required = false) String memberId,
                                                            @RequestHeader(value = "channel") String channel,
                                                            @RequestBody SyncToCCProVo syncToCCProVo) throws Exception {
        if (syncToCCProVo.getAudiCode() == null || "".equals(syncToCCProVo.getAudiCode())){
            return new AjaxMessage<>("40004", "参数异常：audiCode", null);
        }

        if (syncToCCProVo.getSourceId() == null || "".equals(syncToCCProVo.getSourceId())){
            return new AjaxMessage<>("40004", "参数异常：sourceId", null);
        }

        log.info("DRM audiCode : "+syncToCCProVo.getAudiCode());

        CarCustomDetail detail = customService.addCarconfigFromDRM(memberId, userId,userMobile, channel, syncToCCProVo);

        return new AjaxMessage<>("00", "成功", detail);
    }


    /**
     * cop ccdetail
     * to cop to OMD
     * @param ccid
     * @return
     */
    @GetMapping("/contract")
    public AjaxMessage<CarCustomDetail> carConfigCustom(@RequestParam String ccid) throws Exception {

        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        if (carCustom == null){
            return failureMessage(null);
        }
        CarCustomDetail customDetail;
        if (carCustom.getBestRecommendId() != null){
            customDetail = recommendCustomService.getCarConfigDetailContract(carCustom);
        }else {
            customDetail = carCustomService.getCarConfigDetailContract(carCustom);
        }
        if (ccid != null && customDetail != null) {
            log.info("customDetailOmd, ccid: " + ccid + "customDetail: " + JSONObject.toJSONString(customDetail));
        }
        if (customDetail.getConfigDetail().getCarModel().getModelPrice() instanceof String){
            customDetail.getConfigDetail().getCarModel().setModelPrice("0");
        }
        if (customDetail.getConfigDetail().getTotalPrice() instanceof String){
            customDetail.getConfigDetail().setTotalPrice(BigDecimal.ZERO);
        }
        return new AjaxMessage<>("00", "成功", customDetail);
    }

    @GetMapping(value = {"/modelLine/configs"})
    @ApiOperation("配置线全配置")
    public AjaxMessage<ModelLineConfigVo> modelLineConfigs(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                           @RequestParam String modelLineId,
                                                           @RequestParam(value = "filter", defaultValue = "STANDARD_EQUIPMENT", required = false) String filter) throws Exception {
        Integer status = null;
        if (filter != null){
            switch (filter) {
                case "STANDARD_EQUIPMENT":
                    status = 1;
                    break;
                case "OPTIONAL_EQUIPMENT":
                    status = 2;
                    break;
                default:
                    status = null;
            }
        }
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        ModelLineConfigVo configVo = modelLineService.modelLineConfig(channel, modelLineId, status);
        return successMessage(configVo);
    }

    @GetMapping(value = {"/bestRecommendCar", "/public/bestRecommendCar"})
    @ApiOperation("OMD虎头车-DRM")
    public AjaxMessage<List<DrmBestRecommendCarVo>> bestRecommendCar(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, String customerSeriesId){
        List<BestRecommendCarVo> recommendCarVos = recommendService.omdBestRecommendCar(channel, appConfig.getHqDealerCode(), customerSeriesId);
        List<DrmBestRecommendCarVo> drmBestRecommendCarVos = recommendCarVos.stream().sorted((a,b)->{
            BigDecimal aPrice=null, bPrice=null;
            if (a.getTotalPrice() != null && a.getTotalPrice() instanceof BigDecimal){
                aPrice = (BigDecimal) a.getTotalPrice();
            }
            if (b.getTotalPrice() != null && b.getTotalPrice() instanceof BigDecimal){
                bPrice = (BigDecimal) b.getTotalPrice();
            }
            if (aPrice==null && bPrice== null){
                return 0;
            }
            if (aPrice == null){
                return 1;
            }
            if (bPrice == null){
                return -1;
            }
            return bPrice.compareTo(aPrice);
        }).map(i->{
            DrmBestRecommendCarVo drmBestRecommendCarVo = new DrmBestRecommendCarVo();
            BeanUtils.copyProperties(i, drmBestRecommendCarVo);
            String audiCode = generateAudiCodeByBestRecommendCar(i);
            drmBestRecommendCarVo.setAudiCode(audiCode);
            return drmBestRecommendCarVo;
        }).filter(i->{
            // 过滤先行版
            if(appConfig.getBestRecommend() != null
                    && appConfig.getBestRecommend().getFilterEnable()
                    && CollectionUtils.isNotEmpty(appConfig.getBestRecommend().getModelLineIds())
                    && appConfig.getBestRecommend().getModelLineIds().contains(i.getModelLine().getModelLineId())){
                return false;
            }
            // 过滤Q5E
            if(appConfig.getBestRecommend() != null
                    && appConfig.getBestRecommend().getFilterEnable()
                    && CollectionUtils.isNotEmpty(appConfig.getBestRecommend().getCustomSeriesIds())
                    && appConfig.getBestRecommend().getCustomSeriesIds().contains(i.getModelLine().getCustomSeriesId())){
                return false;
            }
            // 库存过滤
            return  recommendService.validHqType(i, BestRecommendTypeEnum.TIGER.getValue()) && bestRecommendStockService.validRecommendStock(i, appConfig.getHqDealerCode());
        }).collect(Collectors.toList());
        return successMessage(drmBestRecommendCarVos);
    }

    @GetMapping(value = {"/modelLine/configs/personalOption"})
    @ApiOperation("私人定制")
    public AjaxMessage<List<ModelLineOptionVo>> modelLinePersonalOption(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                                        @RequestParam String modelLineId) throws Exception {
        List<String> notInCategory = Arrays.asList("RAD", "COLOR_EXTERIEUR", "COLOR_INTERIEUR", "SIB", "EIH");
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        List<ModelLineOptionVo> optionVos = modelLineService.modelLinePersonalOption(channel, modelLineId, notInCategory);
        OptionRelateParam optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLineId);
        optionRelateParam.setRelateTypes(Arrays.asList("conflict", "depend"));
//        optionRelateParam.setNotInRelateCategory(Arrays.asList("COLOR_INTERIEUR", "COLOR_EXTERIEUR", "SIB"));
        List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        Map<String, List<OptionRelDto>> oMap = new HashMap<>();
        relDtos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel == null) {
                oRel = new ArrayList<>();
                oMap.put(i.getOptionId(), oRel);
            }
            oRel.add(i);
        });
        optionVos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel != null){
                i.setOptionRelates(oRel);
            }
        });
        return successMessage(optionVos);
    }

    @GetMapping(value = {"/modelLine/configs/personalOptionPlus"})
    @ApiOperation("私人定制+组合列表")
    public AjaxMessage<PersonalOptionVo> modelLinePersonalOptionPlus(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel,
                                                                     @RequestParam String modelLineId, String seats) throws Exception {
        List<String> notInCategory = Arrays.asList("RAD", "COLOR_EXTERIEUR", "COLOR_INTERIEUR", "SIB", "EIH");
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        ModelLineVo line = lines.get(0);
        List<ModelLineOptionVo> optionVos = modelLineService.modelLinePersonalOption(channel, modelLineId, notInCategory);
        OptionRelateParam optionRelateParam = new OptionRelateParam();
        optionRelateParam.setModelLineId(modelLineId);
        optionRelateParam.setRelateTypes(Arrays.asList("conflict", "depend"));
//        optionRelateParam.setNotInRelateCategory(Arrays.asList("COLOR_INTERIEUR", "COLOR_EXTERIEUR", "SIB"));
        List<OptionRelDto> relDtos =  optionRelateService.listOptionRelDto(optionRelateParam);
        Map<String, List<OptionRelDto>> oMap = new HashMap<>();
        relDtos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel == null) {
                oRel = new ArrayList<>();
                oMap.put(i.getOptionId(), oRel);
            }
            oRel.add(i);
        });
        optionVos.forEach(i->{
            List<OptionRelDto> oRel = oMap.get(i.getOptionId());
            if (oRel != null){
                i.setOptionRelates(oRel);
            }
        });
        PersonalOptionVo vo = new PersonalOptionVo();
        if (CollectionUtils.isNotEmpty(optionVos)) {
            if ("G4".equals(line.getCustomSeriesCode()) && "7".equals(seats)) {
                List<String> q5e6Seat = Arrays.asList("WE8", "8I6+WE8", "4D3+WE8");
                optionVos = optionVos.stream().filter(o -> !q5e6Seat.contains(o.getOptionCode())).collect(Collectors.toList());
            } else if ("G6".equals(line.getCustomSeriesCode()) && "7".equals(seats)) {
                List<String> q6Seat = Arrays.asList("PS1");
                optionVos = optionVos.stream().filter(o -> !q6Seat.contains(o.getOptionCode())).collect(Collectors.toList());
            }
        }
        vo.setPersonalOptions(optionVos);
        TypePrListQueryDto queryDto = new TypePrListQueryDto(line.getAccbTypeCode(), line.getModelYear(), line.getVersion(), optionVos);
        if (StringUtils.isNotBlank(seats)) {
            queryDto.setSeats(seats);
        }
        vo.setPersonalOptionComposes(modelLineTypeService.queryPersonalComposes(queryDto));
        return successMessage(vo);
    }

    @GetMapping({"/modelLine/configs/packetEquity", "/public/modelLine/configs/packetEquity"})
    @ApiOperation("配置器权益列表")
    public AjaxMessage<List<ModelLineOptionVo>> packetEquity(@RequestHeader(value = "channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, @RequestParam String modelLineId) throws Exception {
        ModelParamDto modelParamDto = new ModelParamDto();
        modelParamDto.setDelFlag(0);
        modelParamDto.setModelLineId(modelLineId);
        modelParamDto.setChannel(channel);
        List<ModelLineVo> lines = modelLineService.listModelLine(modelParamDto);
        if (CollectionUtils.isEmpty(lines)){
            throw new ServiceException("参数异常：modelLineId", "400401", "");
        }
        String category = OptionCategoryEnum.PACKET.getValue();
        List<ModelLineOptionVo> optionVos = modelLineService.modelLineOption(channel, modelLineId, category).stream().filter(i-> OptionTypeEnum.PACKET_EQUITY.getValue().equals(i.getOptionType())).collect(Collectors.toList());
        return successMessage(optionVos);
    }

    @PostMapping(value = {"/updateCarConfig/{api}"})
    @ApiOperation("通过编码修改配置单")
    public AjaxMessage<Long> convertOmdModelOriginData(OmdModelDto omdModelDto, @RequestParam Long ccid, @RequestParam(required = false) String mstMgrpId, @PathVariable String api) throws Exception {
        OmdModelDto omdModelMappedDto = omdDataMapService.getOmdDataFromMap(omdModelDto, api);
        omdModelDto = omdModelMappedDto != null ? omdModelMappedDto : omdModelDto;
        CarModelDto carModelDto = omdService.convertOriginData(omdModelDto);
        if(carModelDto == null){
            return failureMessage(null);
        }
        log.info("open api modify cc, ccid: {}, info: {}", ccid, omdModelDto);
        if(carModelDto.getModelLineVo().getFromOmd() != null && carModelDto.getModelLineVo().getFromOmd() == 1){
            if (StringUtils.isBlank(mstMgrpId)){
                return failureMessage("400400", "mstMgrpId is not exist", null);
            }
        }
        CarCustomDto carCustomDto = new CarCustomDto();
        carCustomDto.setModelLineId(carModelDto.getModelLineVo().getModelLineId());
        carCustomDto.setSibInterieurId(carModelDto.getSibInterieur().getSibInterieurId());
        carCustomDto.setOptionIds(new HashSet<>());
        carCustomDto.getOptionIds().addAll(carModelDto.getOptions().stream().map(ModelLineOptionVo::getOptionId).collect(Collectors.toSet()));
        carCustomDto.getOptionIds().add(carModelDto.getSibInterieur().getInterieurOptionId());
        carCustomDto.setMstMgrpId(mstMgrpId);
        log.debug("========= carcustomdto: {}", carCustomDto);
        carCustomService.updateCarCustomConfig(api, api, api, carCustomDto, String.valueOf(ccid));
        return successMessage(ccid);
    }

    /**
     * ams 获取配置单
     * @param ccid
     * @return
     * @throws Exception
     */
    @GetMapping(value = {"amsCarConfig"})
    public AjaxMessage<AudiAMSConfigVo> carconfigAMS(@RequestParam String ccid) throws Exception {
        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));

        if (carCustom == null){
            return failureMessage(null);
        }
        CarCustomDetail customDetail;
        if (carCustom.getBestRecommendId() != null){
            customDetail = recommendCustomService.getCarConfigDetailContract(carCustom);
        }else {
            customDetail = carCustomService.getCarConfigDetailContract(carCustom);
        }
        if (ccid != null && customDetail != null) {
            log.info("customDetailOmd, ccid: " + ccid + "customDetail: " + JSONObject.toJSONString(customDetail));
        }
        if (customDetail.getConfigDetail().getCarModel().getModelPrice() instanceof String){
            customDetail.getConfigDetail().getCarModel().setModelPrice("0");
        }
        if (customDetail.getConfigDetail().getTotalPrice() instanceof String){
            customDetail.getConfigDetail().setTotalPrice(BigDecimal.ZERO);
        }

        AudiAMSConfigVo amsConfigVo = new AudiAMSConfigVo();
        amsConfigVo.setCustomSeriesName(customDetail.getConfigDetail().getCarSeries().getSeriesNameCn());
        amsConfigVo.setModelLineName(customDetail.getConfigDetail().getCarModel().getModelNameCn());
        amsConfigVo.setAccbTypeCode(customDetail.getConfigDetail().getCarModel().getModelCode());
        amsConfigVo.setModelYear(customDetail.getConfigDetail().getCarModel().getModelYear());
        amsConfigVo.setModelVersion(customDetail.getConfigDetail().getCarModel().getOmdModelVersion());

        amsConfigVo.setOptions(new ArrayList<>());
        amsConfigVo.getOptions().add(new AudiAMSConfigVo.OMDOptionBriefDto(customDetail.getConfigDetail().getOutsideColor().getColorCode(),
                OptionCategoryEnum.OUTCOLOR.getValue(),
                customDetail.getConfigDetail().getOutsideColor().getColorNameCn()));
        amsConfigVo.getOptions().add(new AudiAMSConfigVo.OMDOptionBriefDto(customDetail.getConfigDetail().getInsideColor().getColorCode(),
                OptionCategoryEnum.INCOLOR.getValue(),
                customDetail.getConfigDetail().getInsideColor().getColorNameCn()));
        String channel = Constant.ONEAPP_CHANNEL;
        List<ModelLineOptionVo> packetAllItems = modelLineOptionService.listModelLinePacketItem(channel, carCustom.getModelLineId(), 0);
        List<ModelLineOptionVo> packetItems = ChannelDataUtils.channelData(packetAllItems, ModelLineOptionVo.class, channel, "optionId", false);
        Map<String, String> options = carCustomOptionService.list(Wrappers.<CarCustomOption>lambdaQuery().eq(CarCustomOption::getCcid, ccid))
                .stream().collect(Collectors.toMap(CarCustomOption::getCode, CarCustomOption::getOptionId, (a1, a2) -> a2));
        if(customDetail.getConfigDetail().getOptionList() != null){
            customDetail.getConfigDetail().getOptionList().forEach(o->{
                AudiAMSConfigVo.OMDOptionBriefDto  optionBriefDto = new AudiAMSConfigVo.OMDOptionBriefDto(o.getOptionCode(),
                        o.getOptionClassification(),
                        o.getOptionNameCn());
                optionBriefDto.setItems(new ArrayList<>());
                amsConfigVo.getOptions().add(optionBriefDto);
                if (OptionCategoryEnum.PACKET.getValue().equals(o.getOptionClassification())
                        && options.get(o.getOptionCode()) != null){
                    String optionId = options.get(o.getOptionCode());
                    packetItems.stream().filter(i->i.getPackageId().equals(optionId)
                                    && i.getStatus() != null && i.getStatus().intValue() != 0)
                            .forEach(item-> optionBriefDto.getItems().add(new AudiAMSConfigVo.OMDOptionBriefDto(item.getOptionCode(),
                                item.getCategory(),
                                item.getOptionName()))
                            );
                }

            });
        }
        return new AjaxMessage<>("00", "成功", amsConfigVo);
    }

    @GetMapping("/appConfig")
    @ApiOperation("获取APP配置单")
    public AjaxMessage<CarCustomDetail> appConfig(@RequestParam String ccid) throws Exception {
        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        if (carCustom == null){
            return failureMessage(null);
        }
        String channel = Constant.ONEAPP_CHANNEL;
        CarCustomDetail customDetail = carConfigSnapshot.carConfigDetail(channel, carCustom);
        return new AjaxMessage<>("00", "成功", customDetail);
    }

    /**
     * cop ccdetail
     * to cop to OMD
     * @param ccid
     * @return
     */
    @GetMapping("/omdConfig")
    @ApiOperation("获取OMD配置单")
    public AjaxMessage<CarCustomDetail> carOmdConfig(@RequestParam String ccid) throws Exception {

        CarCustom carCustom = new CarCustom();
        carCustom = carCustom.selectById(Long.valueOf(ccid));
        if (carCustom == null){
            return failureMessage(null);
        }
        CarCustomDetail customDetail;
        customDetail = carConfigSnapshot.carConfigOmd(carCustom);
        if (customDetail.getConfigDetail().getCarModel().getModelPrice() instanceof String){
            customDetail.getConfigDetail().getCarModel().setModelPrice("0");
        }
        if (customDetail.getConfigDetail().getTotalPrice() instanceof String){
            customDetail.getConfigDetail().setTotalPrice(BigDecimal.ZERO);
        }
        return new AjaxMessage<>("00", "成功", customDetail);
    }

    @ApiOperation("获取购物车列表")
    @GetMapping("/getCarShoppingCartList")
    public AjaxMessage<List<CarShoppingCartVo>> getCarShoppingCartList(@RequestHeader(value = "X-User-Id") String userId) {
        List<CarShoppingCartVo> list = carShoppingCartService.getCarShoppingCartList(userId);
        return new AjaxMessage<>("00", "成功", list);
    }

    @PostMapping(value = {"/convertOmdModelOriginData"})
    @ApiOperation("Omd车辆数据转换")
    public AjaxMessage<CarModelDto> convertOmdModelOriginData(OmdModelDto omdModelDto) throws Exception {
        CarModelDto carModelDto = omdService.convertOriginData(omdModelDto);
        return successMessage(carModelDto);
    }

    @Autowired
    private IACCBService iaccbService;
    private String generateAudiCodeByBestRecommendCar(BestRecommendCarVo recommendCarVo) {
        ModelLineVo modelLine = recommendCarVo.getModelLine();
        CustomSeriesDto seriesDto = customSeriesService.getCustomSeriesDto(Constant.MASTER_CHANNEL, modelLine.getCustomSeriesId());
        AudiConfigDto audiConfigDto = new AudiConfigDto();
        audiConfigDto.setModelId(seriesDto.getAccbModelId());
        audiConfigDto.setModelCode(seriesDto.getAccbModelCode());
        audiConfigDto.setModelDesc(seriesDto.getSeriesName());
        audiConfigDto.setTypeId(modelLine.getAccbTypeId());
        audiConfigDto.setTypeCode(modelLine.getAccbTypeCode());
        audiConfigDto.setTypeDesc(modelLine.getModelLineName());
        audiConfigDto.setModelYear(modelLine.getModelYear());
        audiConfigDto.setModelVersion(modelLine.getVersion());
        audiConfigDto.setHeadline(modelLine.getModelLineName());
        List<ModelLineOptionVo> options = recommendCarVo.getOptions();
        List<OptionBriefDto> optionBriefDtoList = new ArrayList<>();
        options.forEach(i->
                optionBriefDtoList.add(new OptionBriefDto(i.getCategory()+":"+i.getOptionCode(), i.getCategory(),i.getOptionName())));
        audiConfigDto.setOptions(optionBriefDtoList);
        String audiCode = iaccbService.genAudiCode(audiConfigDto);
        return audiCode;
    }

}

