package com.csvw.audi.cc.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

@Data
@ApiModel(value = "信息发送-请求参数")
public class SmsDto {
    @ApiModelProperty(value = "idpid")
    private String idpid;

    @ApiModelProperty(value = "userId")
    private String userId;

    @ApiModelProperty(value = "模板编码")
    @NotNull(message = "需要指明模板编码")
    private String templateCode;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "业务方id")
    private String outId;

    @ApiModelProperty(value = "03:推送给sx(默认),01:推送给sc")
    private String source;

    @ApiModelProperty(value = "app路由")
    private String appRoute;

    @ApiModelProperty(value = "替换参数")
    private Map<String, String> replaceParams;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "通知类型（1：服务通知，2：优惠促销，3：预约通知，4：盲盒券通知，5：问卷通知，6：运营侧通知，7：加精，8：动态评论驳回）")
    private Integer type;
}
