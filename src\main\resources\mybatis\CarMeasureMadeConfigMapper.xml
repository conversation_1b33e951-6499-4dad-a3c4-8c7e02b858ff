<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarMeasureMadeConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarMeasureMadeConfig">
        <id column="measure_id" property="measureId" />
        <result column="model_line_id" property="modelLineId" />
        <result column="color_id" property="colorId" />
        <result column="color_code" property="colorCode" />
        <result column="interior_id" property="interiorId" />
        <result column="interior_code" property="interiorCode" />
        <result column="rad_id" property="radId" />
        <result column="rad_code" property="radCode" />
        <result column="sib_id" property="sibId" />
        <result column="sib_code" property="sibCode" />
        <result column="vos_id" property="vosId" />
        <result column="vos_code" property="vosCode" />
        <result column="eih_id" property="eihId" />
        <result column="eih_code" property="eihCode" />
        <result column="sib_interieur_id" property="sibInterieurId" />
        <result column="pr_flag" property="prFlag" />
        <result column="measure_origin_id" property="measureOriginId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        measure_id, model_line_id, color_id, color_code, interior_id, interior_code, rad_id, rad_code, sib_id, sib_code, vos_id, vos_code, eih_id, eih_code, sib_interieur_id, pr_flag, measure_origin_id, create_time, update_time, del_flag
    </sql>

    <select id="listOriginConfigToConvert" resultType="com.csvw.audi.cc.entity.po.CarMeasureMadeOriginConfig">
        SELECT oc.* FROM car_measure_made_origin_config oc
        LEFT JOIN  car_measure_made_config c on oc.measure_origin_id = c.measure_origin_id
        where measure_id IS NULL order by oc.create_time desc
    </select>

    <select id="measureQuery" parameterType="com.csvw.audi.cc.entity.dto.MeasureQueryDto"
            resultType="com.csvw.audi.cc.entity.dto.CarMeasureMadeConfigDto">
        SELECT distinct c.* FROM car_measure_made_config c left join car_measure_made_origin_config oc on oc.measure_origin_id = c.measure_origin_id left join car_measure_made_config_option co on c.measure_id = co.measure_id
        where model_line_id = #{modelLineId} and oc.`stock_num` > 0
        <if test="colorCode != null and colorCode != ''">
            and c.color_code = #{colorCode}
        </if>
        <if test="radCode != null and radCode != ''">
            and c.rad_code = #{radCode}
        </if>

        <if test="eihCode != null and eihCode != ''">
            and c.eih_code = #{eihCode}
        </if>
        <!--and ( (pr_flag=0
                <if test="interiorCode != null">
                    and interior_code = #{interiorCode}
                </if>
                <if test="sibCode != null">
                    and sib_code = #{sibCode}
                </if>
                <if test="vosCode != null">
                    and vos_code = #{vosCode}
                </if>
                )
                or pr_flag=4)-->
        <if test="interiorCode != null">
            and c.interior_code = #{interiorCode}
        </if>
        <if test="sibCode != null">
            and c.sib_code = #{sibCode}
        </if>
        <if test="vosCode != null">
            and c.vos_code = #{vosCode}
        </if>
        <if test="containOptionCodes != null and containOptionCodes.size() > 0">
            and
            <foreach collection="containOptionCodes" item="item" open="(" close=")" separator="or">
                co.option_code = #{item}
            </foreach>
        </if>
        <if test="optionCodes != null and optionCodes.size() > 0 and optionCodeNum != null">
            and co.option_code in
            <foreach collection="optionCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            group by c.measure_id having count(c.measure_id) = #{optionCodeNum}
        </if>
    </select>

    <select id="listDependSibInterieur" resultType="String">
        SELECT distinct c.sib_interieur_id FROM car_measure_made_config c left join car_measure_made_config_option co on c.measure_id = co.measure_id
        where c.measure_id in
        <foreach collection="measureIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and co.option_code = #{optionCode}
    </select>

    <select id="measureCount" resultType="int" parameterType="com.csvw.audi.cc.entity.po.CarMeasureMadeOriginConfig">
        select count(*) num from `car_measure_made_config` c
        LEFT JOIN  `car_measure_made_origin_config` oc on c.`measure_origin_id` = oc.`measure_origin_id`
        WHERE oc.`accb_type_code` = #{accbTypeCode}
        and oc.`model_year` = #{modelYear}
        and oc.`model_version` = #{modelVersion}
        and oc.`stock_num` > 0
        <if test=" colorCode != null and colorCode != '' ">
            and oc.color_code = #{colorCode}
        </if>
    </select>

</mapper>
