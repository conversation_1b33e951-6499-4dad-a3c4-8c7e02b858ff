package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.entity.po.CarPackageItem;
import com.csvw.audi.cc.mapper.CarPackageItemMapper;
import com.csvw.audi.cc.service.ICarPackageItemService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 选装包配置项关联 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Service
public class CarPackageItemServiceImpl extends ServiceImpl<CarPackageItemMapper, CarPackageItem> implements ICarPackageItemService {

}
