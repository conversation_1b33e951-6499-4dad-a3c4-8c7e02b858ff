spring:
  messages:
    basename: i18n/messages
    encoding: UTF-8
  security:
    user:
      name: ms
      password: ms_1234

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    logfile:
      external_file: logs/info.log
    health:
      show-details: ALWAYS
  health:
    probes:
      enabled: true

info:
  instance: ${eureka.instance.instance-id}
  app:
    spring-cloud-version: "@spring-cloud.version@"
    description: 奥迪车配服务cc
    developer: <EMAIL> # 应用负责人邮箱
