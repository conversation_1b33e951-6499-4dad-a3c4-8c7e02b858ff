package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 车辆动力系统
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="CarEnergySystem对象", description="车辆动力系统")
public class CarEnergySystem extends Model<CarEnergySystem> {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "动力系统")
    private String energySystem;

    @ApiModelProperty(value = "展示图片")
    private String imageUrl;

    @ApiModelProperty(value = "车系id")
    private String customSeriesId;

    @ApiModelProperty(value = "渠道值")
    private String channel;

    @ApiModelProperty(value = "配置线引擎数值")
    private String engine;

    @ApiModelProperty(value = "权重")
    private Integer weight;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
