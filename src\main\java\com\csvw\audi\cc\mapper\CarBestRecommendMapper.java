package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.dto.BestRecommendQueryParam;
import com.csvw.audi.cc.entity.dto.CarBestRecommendDto;
import com.csvw.audi.cc.entity.po.CarBestRecommend;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 畅销推荐车 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-08
 */
public interface CarBestRecommendMapper extends BaseMapper<CarBestRecommend> {

    List<CarBestRecommendDto> listBestRecommend(BestRecommendQueryParam queryParam);

    List<CarBestRecommendDto> listBestRecommendFix(BestRecommendQueryParam queryParam);
}
