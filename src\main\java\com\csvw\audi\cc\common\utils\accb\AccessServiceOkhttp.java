package com.csvw.audi.cc.common.utils.accb;

import java.util.Map;

public abstract class AccessServiceOkhttp {
    protected String ak = null;

    protected String sk = null;

    public AccessServiceOkhttp(String ak, String sk) {
        this.ak = ak;
        this.sk = sk;
    }

    public abstract  okhttp3.Request access(String paramString1, Map<String, String> paramMap, String paramString2, HttpMethodName paramHttpMethodName) throws Exception;

    public  okhttp3.Request access(String url, Map<String, String> header, HttpMethodName httpMethod) throws Exception {
        return access(url, header, null, httpMethod);
    }

    public  okhttp3.Request access(String url, String entity, HttpMethodName httpMethod) throws Exception {
        return access(url, (Map<String, String>)null, entity, httpMethod);
    }

    public  okhttp3.Request access(String url, HttpMethodName httpMethod) throws Exception {
        return access(url, (Map<String, String>)null, null, httpMethod);
    }

    public String getAk() {
        return this.ak;
    }

    public void setAk(String ak) {
        this.ak = ak;
    }

    public String getSk() {
        return this.sk;
    }

    public void setSk(String sk) {
        this.sk = sk;
    }
}
