package com.csvw.audi.cc.entity.vo;

import com.csvw.audi.cc.entity.po.SvcdChannelOrganizationPolicy;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class SvcdAfterSalesVo {

    @ApiModelProperty(value = "门店编号")
    private String dealerCode;

    @ApiModelProperty(value = "门店名称-简称")
    private String dealerName;

    @ApiModelProperty(value = "门店名称-全称")
    private String dealerFullName;

    @ApiModelProperty(value = "门店地址")
    private String dealerAdrress;

    @ApiModelProperty(value = "门店图片地址(封面图)")
    private String imageUrl;

    @ApiModelProperty(value = "门店图片地址(略缩图)")
    private String thumbnailUrl;

    @ApiModelProperty(value = "门店联系人")
    private String dealerContacts;

    @ApiModelProperty(value = "门店联系电话")
    private String dealerPhone;

    @ApiModelProperty(value = "经度")
    private Double longitude;

    @ApiModelProperty(value = "纬度")
    private Double latitude;

    @ApiModelProperty(value = "距离（单位：米）")
    private Double distance;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "省份代码")
    private String provinceCode;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "城市代码")
    private String cityCode;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "区域代码")
    private String areaCode;

    @ApiModelProperty(value = "售后救援服务联系电话")
    private String rescuePhone;

    @ApiModelProperty(value = "758编码")
    private String code758;

    @ApiModelProperty(value = "经度(临时值,不做为有效返回结果)")
    private Double afterSalesLongitude;

    @ApiModelProperty(value = "纬度(临时值,不做为有效返回结果)")
    private Double afterSalesLatitude;

    @ApiModelProperty(value = "是否提供上汽奥迪取送车服务(0：否，1：是)")
    private String provideSaicAudiPickUpService;

    @ApiModelProperty(value = "业务状态（1:意向 2:筹备 3:开业 4:出网 5:PopUP 6:试运营 7:意向终止 8:预营业）")
    private String businessStatus;

    @ApiModelProperty(value = "上汽奥迪维修级别/是否新能源车售后(B：否，A：是)")
    private String saicAudiMaintenanceLevel;

    @ApiModelProperty(value = "绑定的代理商编号")
    private String bindAgentCode;

    @ApiModelProperty(value = "绑定的代理商名称")
    private String bindAgentName;
    @ApiModelProperty(value = "服务商使用系统(EP/AMS)")
    private String useSystem;
    @ApiModelProperty(value = "售后代码")
    private String serviceCode;

    private Integer dealerType;
    private Integer dealerAfterSaleType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    private List<SvcdChannelOrganizationPolicy> policyList;
}
