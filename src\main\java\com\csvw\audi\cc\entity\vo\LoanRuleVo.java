package com.csvw.audi.cc.entity.vo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

@Data
public class LoanRuleVo {
    private String loanRuleId;

    @ApiModelProperty(value = "金融机构")
    private String loanAgency;

    @ApiModelProperty(value = "最低首付比例")
    private BigDecimal minPaymentRatio;

    /**
     *   "minPaymentRatio":20,  最低先付比例，百分比
     *   "maxPaymentRatio":35,  最高先付比例，百分比
     *   "balanceRatio":65,     尾款比例，百分比
     *   "investRate":11.66,    活动利率
     *   "discountAmount":16692 贴息金额
     */
    @ApiModelProperty(value = "利率")
    private JSONObject interestRates;

}
