package com.csvw.audi.cc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.csvw.audi.cc.common.utils.CcUtils;
import com.csvw.audi.cc.entity.dto.omd.DealerVehicleBody;
import com.csvw.audi.cc.entity.dto.omd.HqVehicleBody;
import com.csvw.audi.cc.entity.po.CarMeasureMadeOriginConfig;
import com.csvw.audi.cc.entity.po.CarOmdBestRecommend;
import com.csvw.audi.cc.entity.po.CarOmdStockMap;
import com.csvw.audi.cc.entity.po.CarOmdStockRecommend;
import com.csvw.audi.cc.mapper.CarOmdStockMapMapper;
import com.csvw.audi.cc.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * OMD库存车映射 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
@Service
public class CarOmdStockMapServiceImpl extends ServiceImpl<CarOmdStockMapMapper, CarOmdStockMap> implements ICarOmdStockMapService {

    @Autowired
    private ICarMeasureMadeOriginConfigService measureMadeOriginConfigService;

    @Autowired
    private CarOmdStockMapMapper mapper;

    @Autowired
    private ICarRecommendService carRecommendService;

    @Autowired
    private OmdServiceImpl omdService;

    @Autowired
    private ICarOmdStockRecommendService stockRecommendService;

    @Autowired
    private ICarOmdBestRecommendService bestRecommendService;

    @Override
    public void handleStockMapCcUniqueCode() {
        LambdaQueryWrapper<CarOmdStockMap> noCcUniqueQ = new LambdaQueryWrapper<>();
        noCcUniqueQ.isNull(CarOmdStockMap::getCcUniqueCode);
        List<CarOmdStockMap> carOmdStockMaps = this.list(noCcUniqueQ);
        carOmdStockMaps.forEach(i->{
            StringBuilder originCcUniqueCode = new StringBuilder();
            originCcUniqueCode.append(i.getOriginModelCode()).append("/").append(i.getOriginModelYear()).append("/")
                    .append(i.getOriginModelVersion()).append("/").append(i.getOriginColorCode()).append("/")
                    .append(i.getOriginInteriorCode()).append("/");
            if (i.getOriginPrList() != null){
                String[] prs = i.getOriginPrList().split(",");
                ArrayList<String> prList = new ArrayList<>(Arrays.asList(prs));
                prList.sort(String::compareTo);
                originCcUniqueCode.append(Strings.join(prList, ','));
            }
            StringBuilder ccUniqueCode = new StringBuilder();
            ccUniqueCode.append(i.getModelCode()).append("/").append(i.getModelYear()).append("/")
                    .append(i.getModelVersion()).append("/").append(i.getColorCode()).append("/")
                    .append(i.getInteriorCode()).append("/");
            if (i.getPrList() != null){
                String[] prs = i.getPrList().split(",");
                ArrayList<String> prList = new ArrayList<>(Arrays.asList(prs));
                prList.sort(String::compareTo);
                ccUniqueCode.append(Strings.join(prList, ','));
            }
            LambdaUpdateWrapper<CarOmdStockMap> ccUniqueU = new LambdaUpdateWrapper<>();
            ccUniqueU.set(CarOmdStockMap::getUpdateTime, LocalDateTime.now())
                    .set(CarOmdStockMap::getOriginCcUniqueCode, originCcUniqueCode.toString())
                    .set(CarOmdStockMap::getCcUniqueCode, ccUniqueCode.toString())
                    .eq(CarOmdStockMap::getId, i.getId());
            this.update(ccUniqueU);
        });
    }

    /**
     * 查询库存映射中匹配上且转换失败的半订制；
     * 存在相同的映射，加上映射前的库存，更新
     * 不存在相同的映射，新增半订制变种
     * 最后调用半订制转换接口
     */
    @Override
    public void handleMeasureStockMap() throws Exception {
        this.handleStockMapCcUniqueCode();
        List<CarOmdStockMap> measureMap = mapper.measureMaps();
        if (CollectionUtils.isEmpty(measureMap)){
            return ;
        }
        measureMap.forEach(i->{
            LambdaQueryWrapper<CarMeasureMadeOriginConfig> originQ = new LambdaQueryWrapper<>();
            originQ.eq(CarMeasureMadeOriginConfig::getCcUniqueCode, i.getOriginCcUniqueCode())
                    .eq(CarMeasureMadeOriginConfig::getAccbTypeCode, i.getOriginAccbTypeCode())
                    .eq(CarMeasureMadeOriginConfig::getDelFlag,0);
            CarMeasureMadeOriginConfig originConfig = measureMadeOriginConfigService.getOne(originQ);
            if (originConfig == null){
                return;
            }
            LambdaQueryWrapper<CarMeasureMadeOriginConfig> configQ = new LambdaQueryWrapper<>();
            configQ.eq(CarMeasureMadeOriginConfig::getCcUniqueCode, i.getCcUniqueCode())
                    .eq(CarMeasureMadeOriginConfig::getAccbTypeCode, i.getAccbTypeCode())
                    .eq(CarMeasureMadeOriginConfig::getDelFlag, 0);
            CarMeasureMadeOriginConfig config = measureMadeOriginConfigService.getOne(configQ);
            if (config != null){
                LambdaUpdateWrapper<CarMeasureMadeOriginConfig> configU = new LambdaUpdateWrapper();
                configU.set(CarMeasureMadeOriginConfig::getStockNum, originConfig.getStockNum() + config.getStockNum())
                        .eq(CarMeasureMadeOriginConfig::getMeasureOriginId, config.getMeasureOriginId());
                measureMadeOriginConfigService.update(configU);
                return;
            }
            config = new CarMeasureMadeOriginConfig();
            config.setModelCode(i.getModelCode());
            config.setModelVersion(i.getModelVersion());
            config.setModelYear(i.getModelYear());
            config.setClassCode(i.getClassCode());
            config.setAccbTypeCode(i.getAccbTypeCode());
            config.setColorCode(i.getColorCode());
            config.setInteriorCode(i.getInteriorCode());
            config.setPrList(i.getPrList());
            String prCodes = i.getPrList() == null ? "":i.getPrList();
            config.setUniqueCode(i.getModelCode()+i.getColorCode()+i.getInteriorCode()+prCodes);
            config.setCcUniqueCode(i.getCcUniqueCode());
            config.setStockNum(originConfig.getStockNum());
            config.setCreateTime(LocalDateTime.now());
            measureMadeOriginConfigService.save(config);
        });
        measureMadeOriginConfigService.convertMeasureMade();
    }

    /**
     *
     */
    @Override
    public void handleRecommendStockMap() {
        this.handleStockMapCcUniqueCode();
        this.handleRecommendUniqueCode();

        List<CarOmdStockMap> hqMap = mapper.hqMaps();
        if (CollectionUtils.isEmpty(hqMap)){
            return;
        }
        carRecommendService.syncOmdBestRecommendCarStockMap(hqMap);
    }

    @Override
    public void handleStockRecommendStockMap() {
        this.handleStockMapCcUniqueCode();
        this.handleDealerRecommendUniqueCode();
        carRecommendService.convertStockRecommendCarStockMap();
    }

    private void handleRecommendUniqueCode() {
        LambdaQueryWrapper<CarOmdBestRecommend> bestRecommendsQ = new LambdaQueryWrapper<>();
        bestRecommendsQ.isNull(CarOmdBestRecommend::getCcUniqueCode);
        List<CarOmdBestRecommend> bestRecommends = bestRecommendService.list(bestRecommendsQ);
        bestRecommends.forEach(i->{
            String ccUniqueCode = CcUtils.getCcUniqueCode(i.getModelCode().split("-")[0], i.getModelYear(), i.getModelVersion(),
                    i.getColorCode(), i.getInteriorCode(), i.getPrList());
            LambdaUpdateWrapper<CarOmdBestRecommend> ccUniqueU = new LambdaUpdateWrapper<>();
            ccUniqueU.set(CarOmdBestRecommend::getCcUniqueCode, ccUniqueCode)
                    .eq(CarOmdBestRecommend::getBestSellRecommendModelId, i.getBestSellRecommendModelId());
            bestRecommendService.update(ccUniqueU);
        });
    }

    private void handleDealerRecommendUniqueCode() {
        LambdaQueryWrapper<CarOmdStockRecommend> stockRecommendsQ = new LambdaQueryWrapper<>();
        stockRecommendsQ.isNull(CarOmdStockRecommend::getCcUniqueCode);
        List<CarOmdStockRecommend> stockRecommends = stockRecommendService.list(stockRecommendsQ);
        stockRecommends.forEach(i->{
            StringBuilder ccUniqueCode = new StringBuilder();
            ccUniqueCode.append(i.getModelCode().split("-")[0]).append("/").append(i.getModelYear()).append("/")
                    .append(i.getModelVersion()).append("/").append(i.getColorCode()).append("/")
                    .append(i.getInteriorCode()).append("/");
            if (i.getPrList() != null){
                String[] prs = i.getPrList().split(",");
                ArrayList<String> prList = new ArrayList<>(Arrays.asList(prs));
                prList.sort(String::compareTo);
                ccUniqueCode.append(Strings.join(prList, ','));
            }
            LambdaUpdateWrapper<CarOmdStockRecommend> ccUniqueU = new LambdaUpdateWrapper<>();
            ccUniqueU.set(CarOmdStockRecommend::getCcUniqueCode, ccUniqueCode.toString())
                    .eq(CarOmdStockRecommend::getStockRecommendModelId, i.getStockRecommendModelId());
            stockRecommendService.update(ccUniqueU);
        });
    }

    @Override
    public Long getStockNumHqOrigin(CarOmdBestRecommend omdBestRecommend) {
        List<CarOmdStockMap> hqMap = mapper.hqMaps();
        if (CollectionUtils.isEmpty(hqMap)){
            return null;
        }
        for (CarOmdStockMap stockMap : hqMap){
            if (stockMap.getCcUniqueCode().equals(omdBestRecommend.getCcUniqueCode()) &&
                    stockMap.getAccbTypeCode().equals(omdBestRecommend.getModelCode())){
                HqVehicleBody vehicleBody = new HqVehicleBody();
                vehicleBody.setBrandCode("A");
                vehicleBody.setModelCode(stockMap.getOriginModelCode());
                vehicleBody.setModelYear(stockMap.getOriginModelYear());
                vehicleBody.setModelVersion(stockMap.getOriginModelVersion());
                vehicleBody.setInteriorCode(stockMap.getOriginInteriorCode());
                vehicleBody.setColorCode(stockMap.getOriginColorCode());
                vehicleBody.setPrList(stockMap.getOriginPrList());
                vehicleBody.setAccbTypeCode(stockMap.getOriginAccbTypeCode());
                Long appendNum = omdService.queryAudiHqVehicle(vehicleBody);
                return appendNum;
            }
        }
        return null;
    }

    @Override
    public Long getStockNumDealerOrigin(CarOmdStockRecommend omdStockRecommend) {
        LambdaQueryWrapper<CarOmdStockMap> stockMapQ = new LambdaQueryWrapper<>();
        stockMapQ.eq(CarOmdStockMap::getCcUniqueCode, omdStockRecommend.getCcUniqueCode())
                .eq(CarOmdStockMap::getAccbTypeCode, omdStockRecommend.getModelCode())
                .eq(CarOmdStockMap::getDelFlag, 0);
        List<CarOmdStockMap> stockMaps = this.list(stockMapQ);
        if (CollectionUtils.isEmpty(stockMaps)){
            return null;
        }
        Long appendNum = 0l;
        for(CarOmdStockMap stockMap: stockMaps) {
            if (stockMap.getCcUniqueCode().equals(omdStockRecommend.getCcUniqueCode()) &&
                    stockMap.getAccbTypeCode().equals(omdStockRecommend.getModelCode())) {
                DealerVehicleBody vehicleBody = new DealerVehicleBody();
                vehicleBody.setBrandCode("A");
                vehicleBody.setModelCode(stockMap.getOriginModelCode());
                vehicleBody.setModelYear(stockMap.getOriginModelYear());
                vehicleBody.setModelVersion(stockMap.getOriginModelVersion());
                vehicleBody.setInteriorCode(stockMap.getOriginInteriorCode());
                vehicleBody.setColorCode(stockMap.getOriginColorCode());
                vehicleBody.setPrList(stockMap.getOriginPrList());
                vehicleBody.setAccbTypeCode(stockMap.getOriginModelCode());
                vehicleBody.setDealerNetCode(omdStockRecommend.getDealerNetCode());
                appendNum = omdService.queryAudiDealerVehicle(vehicleBody) + appendNum;

            }
        }

        return appendNum;
    }

}
