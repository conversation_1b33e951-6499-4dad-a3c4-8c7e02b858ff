#!/usr/bin/env bash

N=kmsp-template-demo
V=1.0.0

#TAG=dcp/${N}:${V}
TAG=kmsp/${N}:${V}
#R_TAG=*************:8083/${TAG}
R_TAG=*************:1189/${TAG}

mvn clean
mvn package -DskipTests

docker rmi ${TAG}
docker rm `docker ps -a -q`
docker rmi $(docker images | grep "none" | awk '{print $3}')

docker build -t ${TAG} .

docker images | grep ${APP}

docker tag ${TAG} ${R_TAG}

docker save -o /tmp/${N}-${V}.tar ${R_TAG}

#docker push ${R_TAG}