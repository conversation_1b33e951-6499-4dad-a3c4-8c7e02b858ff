package com.csvw.audi.cc.common.utils;

import org.apache.logging.log4j.util.Strings;

import java.util.ArrayList;
import java.util.Arrays;

public class CcUtils {

    public final static String getCcUniqueCode(String modelCode, String modelYear, String modelVersion,
                                               String colorCode, String interiorCode, String prs){
        StringBuilder ccUniqueCode = new StringBuilder();
        ccUniqueCode.append(modelCode).append("/").append(modelYear).append("/")
                .append(modelVersion).append("/").append(colorCode).append("/")
                .append(interiorCode).append("/");
        if (prs != null){
            String[] prsArray = prs.split(",");
            ArrayList<String> prList = new ArrayList<>(Arrays.asList(prsArray));
            prList.sort(String::compareTo);
            ccUniqueCode.append(Strings.join(prList, ','));
        }
        return ccUniqueCode.toString();
    }

}
