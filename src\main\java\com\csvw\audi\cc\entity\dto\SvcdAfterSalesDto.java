package com.csvw.audi.cc.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SvcdAfterSalesDto {

    @ApiModelProperty(value = "门店编号(服务商code)")
    private String dealerCode;

    @ApiModelProperty(value = "门店地址")
    private String dealerAddress;

    @ApiModelProperty(value = "门店联系电话")
    private String dealerPhone;

    @ApiModelProperty(value = "经度")
    private Double longitude;

    @ApiModelProperty(value = "纬度")
    private Double latitude;

    @ApiModelProperty(value = "省份代码")
    private String provinceCode;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "城市代码")
    private String cityCode;

    @ApiModelProperty(value = "区域代码")
    private String areaCode;

    @ApiModelProperty(value = "758编码")
    private String code758;

    @ApiModelProperty(value = "业务状态（0:全部 1:意向 2:筹备 3:开业 4:出网 5:PopUP 6:试运营 7:意向终止 8:预营业）")
    private String businessStatus;

    @ApiModelProperty(value = "代理商code(检索代理商指定的服务商)")
    private String agentCode;
    @ApiModelProperty(value = "售后代码")
    private String serviceCode;

    private Integer sortType;//排序：1智能推荐，2距离优先

    @ApiModelProperty(value = "用户最近选择的门店编号(服务商code)")
    private String dealerCodeNewly;
}
