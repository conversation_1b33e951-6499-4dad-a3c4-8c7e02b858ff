package com.csvw.audi.cc.component;

import com.csvw.audi.cc.feign.AudiTimerFeign;
import com.csvw.audi.cc.service.impl.CacheServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class CarConfigInit implements ApplicationRunner {

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private AudiTimerFeign timerFeign;

    @Autowired
    private CacheServiceImpl cacheService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("car config init runner");
        if (active != null && active.equals("local")){
            log.info("local not execute some init step");
        }else {
            this.notLocalInitStep();
        }
        cacheService.cacheEvictCc();
    }

    private void notLocalInitStep(){
        try{
            Map<String, String> map = new HashMap<>();
            map.put("jobId", "carConfigSyncOmdPriceJob");
            map.put("date", "0 0 1 * * ?");
            timerFeign.removeSyncOmdUpdatePrice(map);
            timerFeign.addSyncOmdUpdatePrice(map);

            Map<String, String> recommendMap = new HashMap<>();
            recommendMap.put("jobId", "carConfigSyncOmdRecommendCarJob");
            recommendMap.put("date", "0 0/30 * * * ?");
            timerFeign.removeSyncOmdUpdateRecommendCar(recommendMap);
            timerFeign.addSyncOmdUpdateRecommendCar(recommendMap);

            Map<String, String> recommendStockMap = new HashMap<>();
            recommendStockMap.put("jobId", "carConfigSyncOmdRecommendCarStockJob");
            recommendStockMap.put("date", "0 0/30 * * * ?");
//            timerFeign.removeSyncOmdUpdateRecommendCarStock(recommendStockMap);
//            timerFeign.addSyncOmdUpdateRecommendCarStock(recommendStockMap);

            Map<String, String> measureMadeStockMap = new HashMap<>();
            measureMadeStockMap.put("jobId", "carConfigSyncOmdMeasureMadeStockJob");
            measureMadeStockMap.put("date", "0 23 4 * * ?");
            timerFeign.removeSyncOmdMeasureMadeStock(measureMadeStockMap);
            timerFeign.addSyncOmdMeasureMadeStock(measureMadeStockMap);

            Map<String, String> historySnapshotMap = new HashMap<>();
            historySnapshotMap.put("jobId", "carConfigHistorySnapshotJob");
            historySnapshotMap.put("date", "0 0/6 * * * ?");
//            timerFeign.removeHistorySnapshot(historySnapshotMap);
//            timerFeign.addHistorySnapshot(historySnapshotMap);
        }catch (Exception e){
            log.error("启动初始化异常，任务处理失败", e);
        }

    }
}
