package com.csvw.audi.cc.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.dto.accb.OptionDto;
import com.csvw.audi.cc.entity.po.Option;
import com.csvw.audi.cc.entity.vo.OptionDetailVo;
import com.csvw.audi.cc.service.IOptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/api/v1/option")
@Api(tags = "配置项")
public class OptionController extends BaseController {

    @Autowired
    private IOptionService optionService;

    @GetMapping("/{salesFamilyCode}")
    @ApiOperation(value = "装备项", notes = "RAD:轮毂")
    public AjaxMessage<List<OptionDetailVo>> option(@PathVariable("salesFamilyCode") String salesFamilyCode){
        LambdaQueryWrapper<Option> queryWrapper = new LambdaQueryWrapper<>();
        // 轮毂选装
        if (salesFamilyCode.equals("RAD")){
            queryWrapper.eq(Option::getCategory, "RAD");
        }else {
            queryWrapper.ne(Option::getCategory, "RAD");
        }
        List<Option> optionList = optionService.list(queryWrapper);
        List<OptionDetailVo> optionDetailVos = optionList.stream().map(i->{
            OptionDetailVo vo = new OptionDetailVo();
            BeanUtils.copyProperties(i, vo);
            vo.setDetailThumbnail("test/2021/06/02/a7l/RAD-detail-thumbnail.png");
            vo.setDetailPreview("test/2021/06/02/a7l/RAD-detail-preview.png");
            return vo;
        }).collect(Collectors.toList());
        return successMessage(optionDetailVos);
    }

    @GetMapping("/equipment")
    @ApiOperation("装备")
    public AjaxMessage<List<Option>> equipment(){
        LambdaQueryWrapper<Option> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.notIn(Option::getCategory, "RAD", "inter");
        return successMessage(optionService.list(queryWrapper));
    }

}

