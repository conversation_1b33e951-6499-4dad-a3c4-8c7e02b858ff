package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.config.OssConfig;
import com.csvw.audi.common.config.OssServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/21 14:34
 * @description
 */
@Slf4j
@Service
public abstract class AbsSvcdImageToOssService {

    @Value("${svcd.dealer-image-url}")
    private String svcdDealerImageUrl;
    @Autowired
    private OssConfig ossConfig;
    @Autowired
    private OssServer ossServer;
    @Autowired
    private RestTemplate restTemplate;

    /**
     * @description 获取上传具体目录位置
     */
    abstract String findUploadRelDirPathPosition(String unique);

    public String publicUpload(String imageUrl, String unique) {
        String svcdImageUrl = svcdDealerImageUrl + imageUrl;
        String ossImageUrl = null;
        try {
            InputStream inputStream = findResultInputStream(svcdImageUrl);
            String filePath = findUploadRelDirPathPosition(unique);
            ossImageUrl = ossServer.publicUpload(inputStream, imageUrl, ossConfig.getBucketName(),
                ossConfig.getSegmentationUrl(), filePath);
            log.info("上传oss地址:{}", ossImageUrl);
        } catch (Exception e) {
            log.error("获取图片上传oss异常-{}", imageUrl, e);
        }
        return ossImageUrl;
    }

    protected InputStream findResultInputStream(String dealerImageUrl) throws IOException {
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.add("referer", "https://audi-embedded-wap.saic-audi.mobi/");
        HttpEntity requestEntity = new HttpEntity(requestHeaders);
        ResponseEntity<Resource> resultEntity =
            restTemplate.exchange(dealerImageUrl, HttpMethod.GET, requestEntity, Resource.class);
        InputStream inputStream = Objects.requireNonNull(resultEntity.getBody()).getInputStream();
        return inputStream;
    }
}
