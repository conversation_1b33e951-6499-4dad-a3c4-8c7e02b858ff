package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.dto.PriceCondition;
import com.csvw.audi.cc.entity.po.CarOmdPriceType;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.math.BigDecimal;

/**
 * <p>
 * 配置线价格 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-18
 */
public interface CarOmdPriceTypeMapper extends BaseMapper<CarOmdPriceType> {

    BigDecimal getPrice(PriceCondition condition);
}
