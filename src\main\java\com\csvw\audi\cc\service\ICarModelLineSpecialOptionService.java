package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.po.CarModelLineOption;
import com.csvw.audi.cc.entity.po.CarModelLineSpecialOption;
import com.baomidou.mybatisplus.extension.service.IService;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.exception.ServiceException;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 特定配置线配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
public interface ICarModelLineSpecialOptionService extends IService<CarModelLineSpecialOption> {

    BigDecimal totalOptionPrice(String modelLineId) throws ServiceException;

    List<CarModelLineSpecialOption> listModelLineSpecialOption(String modelLineId);

    List<ModelLineOptionVo> listModelLineSpecialOptionVO(String modelLineId);
}
