package com.csvw.audi.cc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.config.AppConfig;
import com.csvw.audi.cc.entity.dto.CarModelDto;
import com.csvw.audi.cc.entity.dto.OptionParamDto;
import com.csvw.audi.cc.entity.dto.omd.*;
import com.csvw.audi.cc.entity.enumeration.OptionCategoryEnum;
import com.csvw.audi.cc.entity.po.*;
import com.csvw.audi.cc.entity.vo.BestRecommendCarVo;
import com.csvw.audi.cc.entity.vo.ModelLineOptionVo;
import com.csvw.audi.cc.feign.OmdFeign;
import com.csvw.audi.cc.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.channels.Channel;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OmdServiceImpl {

    @Autowired
    AppConfig appConfig;

    @Autowired
    OmdFeign omdFeign;

    @Autowired
    SignService signService;

    @Autowired
    ICarRecommendService carRecommendService;

    @Autowired
    ICarModelLineService modelLineService;

    @Autowired
    private ICarOmdPriceTypeService priceTypeService;
    @Autowired
    private ICarOmdPriceColorService priceColorService;
    @Autowired
    private ICarOmdPricePrpkgService pricePrpkgService;
    @Autowired
    private ICarOmdPricePrService pricePrService;
    @Autowired
    private ICarOmdStockRecommendLogService stockRecommendLogService;

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Transactional
    public void syncTypePrice(LocalDateTime date) {
        /*if (priceTypeService.count() > 0 && date == null){
            return;
        }*/
        String timestamp = signService.getTimestamp();
        String sequenceNo = signService.getSequence(timestamp);
        PriceRequestBody requestBody = new PriceRequestBody();
        if (date == null)
            requestBody.setDate("all");
        else
            requestBody.setDate(date.format(formatter));
        Integer page = 1;
        requestBody.setBrandCode("A");
        requestBody.setPriceType("130");
        requestBody.setPageSize("100");
        OmdParam<PriceRequestBody> omdParam = new OmdParam<>(requestBody);
        OmdRes<TypePriceRes> response;
        do {
            requestBody.setPage(String.valueOf(page));
            String json = JSONObject.toJSONString(omdParam);
            String sign = signService.signPostData(sequenceNo, timestamp, json);
            response = omdFeign.getCurrentTypePrice(appConfig.getInfra().getAppId(), sequenceNo, timestamp, sign, json);
            log.info("page:" + page + response.toString());
            if (response == null) {
                log.error("选装包价格数据同步失败: 未收到结果");
            }
            if (!("20000".equals(response.getCode())
                    && "1".equals(response.getResult().getRspStatus()))) {
                log.error("选装包价格数据同步失败：" + JSONObject.toJSONString(response));
            }
            List<TypePriceRes> data = response.getResult().getResultData();
            if (!CollectionUtils.isEmpty(data)) {
                data.forEach(i -> {
                    // 查询更新
                    LambdaQueryWrapper<CarOmdPriceType> queryWrapper = new LambdaQueryWrapper();
                    queryWrapper.eq(CarOmdPriceType::getAccbTypeCode, i.getAccbTypeCode())
                            .eq(CarOmdPriceType::getModelCode, i.getModelCode())
                            .eq(CarOmdPriceType::getModelYear, i.getModelYear())
                            .eq(CarOmdPriceType::getModelVersion, i.getModelVersion())
                            .eq(CarOmdPriceType::getPriceType, requestBody.getPriceType())
                            .eq(CarOmdPriceType::getBrandCode, requestBody.getBrandCode());
                    List<CarOmdPriceType> types = priceTypeService.list(queryWrapper);
                    if (types != null && types.size() > 0) {
                        types.forEach(t -> t.deleteById());
                    }
                    CarOmdPriceType price = new CarOmdPriceType();
                    BeanUtils.copyProperties(i, price);
                    price.setCreateTime(LocalDateTime.now());
                    price.insert();
                });
            }
            page = page + 1;
        } while (response != null && "20000".equals(response.getCode())
                && "1".equals(response.getResult().getRspStatus())
                && !CollectionUtils.isEmpty(response.getResult().getResultData()));
    }

    @Transactional
    public void syncCurrentTypeColorPrice(LocalDateTime date) {
        /*if (priceColorService.count() > 0 && date == null){
            return;
        }*/
        String timestamp = signService.getTimestamp();
        String sequenceNo = signService.getSequence(timestamp);
        PriceRequestBody requestBody = new PriceRequestBody();
        if (date == null)
            requestBody.setDate("all");
        else
            requestBody.setDate(date.format(formatter));
        Integer page = 1;
        requestBody.setBrandCode("A");
        requestBody.setPriceType("130");
        requestBody.setPageSize("100");
        OmdParam<PriceRequestBody> omdParam = new OmdParam<>(requestBody);
        OmdRes<ColorPriceRes> response;
        do {
            requestBody.setPage(String.valueOf(page));
            String json = JSONObject.toJSONString(omdParam);
            String sign = signService.signPostData(sequenceNo, timestamp, json);
            response = omdFeign.getCurrentTypeColorPrice(appConfig.getInfra().getAppId(), sequenceNo, timestamp, sign, json);
            if (response == null) {
                log.error("选装包价格数据同步失败: 未收到结果");
            }
            if (!("20000".equals(response.getCode())
                    && "1".equals(response.getResult().getRspStatus()))) {
                log.error("选装包价格数据同步失败：" + JSONObject.toJSONString(response));
            }
            List<ColorPriceRes> data = response.getResult().getResultData();
            if (!CollectionUtils.isEmpty(data)) {
                data.forEach(i -> {
                    // 查询更新
                    LambdaQueryWrapper<CarOmdPriceColor> queryWrapper = new LambdaQueryWrapper();
                    queryWrapper.eq(CarOmdPriceColor::getColorCode, i.getColorCode())
                            .eq(CarOmdPriceColor::getAccbTypeCode, i.getAccbTypeCode())
                            .eq(CarOmdPriceColor::getModelCode, i.getModelCode())
                            .eq(CarOmdPriceColor::getModelYear, i.getModelYear())
                            .eq(CarOmdPriceColor::getModelVersion, i.getModelVersion())
                            .eq(CarOmdPriceColor::getPriceType, requestBody.getPriceType())
                            .eq(CarOmdPriceColor::getBrandCode, requestBody.getBrandCode());
                    List<CarOmdPriceColor> prices = priceColorService.list(queryWrapper);
                    if (prices != null && prices.size() > 0) {
                        prices.forEach(t -> t.deleteById());
                    }
                    CarOmdPriceColor price = new CarOmdPriceColor();
                    BeanUtils.copyProperties(i, price);
                    price.setCreateTime(LocalDateTime.now());
                    price.insert();
                });
            }
            page = page + 1;
        } while (response != null && "20000".equals(response.getCode())
                && "1".equals(response.getResult().getRspStatus())
                && !CollectionUtils.isEmpty(response.getResult().getResultData()));
    }

    @Transactional
    public void syncCurrentTypePrPrice(LocalDateTime date) {
        /*if (pricePrService.count() > 0 && date == null){
            return;
        }*/
        String timestamp = signService.getTimestamp();
        String sequenceNo = signService.getSequence(timestamp);
        PriceRequestBody requestBody = new PriceRequestBody();
        if (date == null)
            requestBody.setDate("all");
        else
            requestBody.setDate(date.format(formatter));
        Integer page = 1;
        requestBody.setBrandCode("A");
        requestBody.setPriceType("130");
        requestBody.setPageSize("100");
        OmdParam<PriceRequestBody> omdParam = new OmdParam<>(requestBody);
        OmdRes<PrPriceRes> response;
        do {
            requestBody.setPage(String.valueOf(page));
            String json = JSONObject.toJSONString(omdParam);
            String sign = signService.signPostData(sequenceNo, timestamp, json);
            response = omdFeign.getCurrentTypePrPrice(appConfig.getInfra().getAppId(), sequenceNo, timestamp, sign, json);
            if (response == null) {
                log.error("选装包价格数据同步失败: 未收到结果");
            }
            if (!("20000".equals(response.getCode())
                    && "1".equals(response.getResult().getRspStatus()))) {
                log.error("选装包价格数据同步失败：" + JSONObject.toJSONString(response));
            }
            List<PrPriceRes> data = response.getResult().getResultData();
            if (!CollectionUtils.isEmpty(data)) {
                data.forEach(i -> {
                    // 查询更新
                    LambdaQueryWrapper<CarOmdPricePr> queryWrapper = new LambdaQueryWrapper();
                    queryWrapper.eq(CarOmdPricePr::getPrCode, i.getPrCode())
                            .eq(CarOmdPricePr::getAccbTypeCode, i.getAccbTypeCode())
                            .eq(CarOmdPricePr::getModelCode, i.getModelCode())
                            .eq(CarOmdPricePr::getModelYear, i.getModelYear())
                            .eq(CarOmdPricePr::getModelVersion, i.getModelVersion())
                            .eq(CarOmdPricePr::getPriceType, requestBody.getPriceType())
                            .eq(CarOmdPricePr::getBrandCode, requestBody.getBrandCode());
                    List<CarOmdPricePr> prices = pricePrService.list(queryWrapper);
                    if (prices != null && prices.size() > 0) {
                        prices.forEach(t -> t.deleteById());
                    }
                    CarOmdPricePr price = new CarOmdPricePr();
                    BeanUtils.copyProperties(i, price);
                    price.setCreateTime(LocalDateTime.now());
                    price.insert();
                });
            }
            page = page + 1;
        } while (response != null && "20000".equals(response.getCode())
                && "1".equals(response.getResult().getRspStatus())
                && !CollectionUtils.isEmpty(response.getResult().getResultData()));
    }

    @Transactional
    public void syncCurrentTypePrpkgPrice(LocalDateTime date) {
        /*if (pricePrpkgService.count() > 0 && date == null){
            return;
        }*/
        String timestamp = signService.getTimestamp();
        String sequenceNo = signService.getSequence(timestamp);
        PriceRequestBody requestBody = new PriceRequestBody();
        if (date == null)
            requestBody.setDate("all");
        else
            requestBody.setDate(date.format(formatter));
        Integer page = 1;
        requestBody.setBrandCode("A");
        requestBody.setPriceType("130");
        requestBody.setPageSize("100");
        OmdParam<PriceRequestBody> omdParam = new OmdParam<>(requestBody);
        OmdRes<PrPkgPriceRes> response;
        do {
            requestBody.setPage(String.valueOf(page));
            String json = JSONObject.toJSONString(omdParam);
            String sign = signService.signPostData(sequenceNo, timestamp, json);
            response = omdFeign.getCurrentTypePrpkgPrice(appConfig.getInfra().getAppId(), sequenceNo, timestamp, sign, json);
            if (response == null) {
                log.error("选装包价格数据同步失败: 未收到结果");
            }
            if (!("20000".equals(response.getCode())
                    && "1".equals(response.getResult().getRspStatus()))) {
                log.error("选装包价格数据同步失败：" + JSONObject.toJSONString(response));
            }
            List<PrPkgPriceRes> data = response.getResult().getResultData();
            if (!CollectionUtils.isEmpty(data)) {
                data.forEach(i -> {
                    // 查询更新
                    LambdaQueryWrapper<CarOmdPricePrpkg> queryWrapper = new LambdaQueryWrapper();
                    queryWrapper.eq(CarOmdPricePrpkg::getPrpkgCode, i.getPrpkgCode())
                            .eq(CarOmdPricePrpkg::getAccbTypeCode, i.getAccbTypeCode())
                            .eq(CarOmdPricePrpkg::getModelCode, i.getModelCode())
                            .eq(CarOmdPricePrpkg::getModelYear, i.getModelYear())
                            .eq(CarOmdPricePrpkg::getModelVersion, i.getModelVersion())
                            .eq(CarOmdPricePrpkg::getPriceType, requestBody.getPriceType())
                            .eq(CarOmdPricePrpkg::getBrandCode, requestBody.getBrandCode());
                    List<CarOmdPricePrpkg> prices = pricePrpkgService.list(queryWrapper);
                    if (prices != null && prices.size() > 0) {
                        prices.forEach(t -> t.deleteById());
                    }
                    CarOmdPricePrpkg price = new CarOmdPricePrpkg();
                    BeanUtils.copyProperties(i, price);
                    price.setCreateTime(LocalDateTime.now());
                    price.insert();
                });
            }
            page = page + 1;
        } while (response != null && "20000".equals(response.getCode())
                && "1".equals(response.getResult().getRspStatus())
                && !CollectionUtils.isEmpty(response.getResult().getResultData()));

    }

    public List<BestRecommendRes> bestRecommendRes() {
        List<BestRecommendRes> bestRecommendRes = new ArrayList<>();
        int page = 1;
        BaseRequestBody requestBody = new BaseRequestBody();
        requestBody.setBrandCode("A");
        requestBody.setPageSize("100");
        OmdParam<BaseRequestBody> omdParam = new OmdParam<>(requestBody);
        OmdRes<BestRecommendRes> response;
        do {
            requestBody.setPage(String.valueOf(page));
            response = omdFeign.bestRecommendModelQuery(omdParam);
            if (response == null) {
                log.error("选装包价格数据同步失败: 未收到结果");
            }
            if (!("20000".equals(response.getCode())
                    && "1".equals(response.getResult().getRspStatus()))) {
                log.error("畅销推荐车同步失败：" + JSONObject.toJSONString(response));
            }
            List<BestRecommendRes> data = response.getResult().getResultData();
            if (!CollectionUtils.isEmpty(data)) {
                bestRecommendRes.addAll(data);
            }
            page = page + 1;
        } while (response != null && "20000".equals(response.getCode())
                && "1".equals(response.getResult().getRspStatus())
                && !CollectionUtils.isEmpty(response.getResult().getResultData()));
        return bestRecommendRes;
    }

    public Long queryAudiHqVehicle(HqVehicleBody vehicleBody) {
        OmdParam<HqVehicleBody> omdParam = new OmdParam<>(vehicleBody);
        log.info("omd畅销车库存查询参数：{}", omdParam);
        OmdNormalRes<HqQueryResult> response = omdFeign.queryAudiHqVehicle(omdParam);
        log.info("omd畅销车库存查询参数：{} , 结果： {}", omdParam, response);
        if (response != null && "20000".equals(response.getCode())
                && "1".equals(response.getResult().getRspStatus())
                && response.getResult().getStockNum() != null) {
            return response.getResult().getStockNum();
        } else {
            log.error("获取畅销车库存异常，参数：{} , 返回结果： {}", vehicleBody, response);
        }
        return null;
    }

    public OmdObjectRes<EstimateDeliveryRes> estimateDeliveryQuery(EstimateDeliveryBody estimateDeliveryBody) {
        OmdParam<EstimateDeliveryBody> omdParam = new OmdParam<>(estimateDeliveryBody);
        log.info("omd交付时间相似车查询参数：{}", omdParam);
        OmdObjectRes<EstimateDeliveryRes> response = omdFeign.estimateDeliveryQuery(omdParam);
        log.info("omd交付时间相似车查询参数：{} , 结果： {}", omdParam, response);
        if (response != null && "20000".equals(response.getCode())
                && "1".equals(response.getResult().getRspStatus())
                && response.getResult().getResultData() != null) {
            return response;
        } else {
            log.error("omd交付时间相似车查询参数，参数：{} , 返回结果： {}", estimateDeliveryBody, response);
        }
        return null;
    }

    public Long queryAudiDealerVehicle(DealerVehicleBody vehicleBody) {
        OmdParam<DealerVehicleBody> omdParam = new OmdParam<>(vehicleBody);
        log.info("omd经销商库存查询参数：{}", omdParam);
        OmdNormalRes<HqQueryResult> response = omdFeign.queryAudiDealerVehicle(omdParam);
        log.info("omd经销商库存查询参数：{} , 结果： {}", omdParam, response);
        if (response != null && "20000".equals(response.getCode())
                && "1".equals(response.getResult().getRspStatus())
                && response.getResult().getStockNum() != null) {
            return response.getResult().getStockNum();
        } else {
            log.error("获取omd经销商库存查询参数异常，参数：{} , 返回结果： {}", vehicleBody, response);
        }
        return null;
    }

    @Transactional
    public void syncStockRecommendModel() {
        LambdaQueryWrapper<CarOmdStockRecommendLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(CarOmdStockRecommendLog::getRowIndex).last("limit 1");
        List<CarOmdStockRecommendLog> logs = stockRecommendLogService.list(queryWrapper);
        Long maxStartIndex = 0l;
        if (CollectionUtils.isNotEmpty(logs)) {
            maxStartIndex = logs.get(0).getRowIndex();
        }
        StockRecommendBody requestBody = new StockRecommendBody();
        requestBody.setBrandCode("A");
        requestBody.setPageSize(100);
        OmdParam<StockRecommendBody> omdParam = new OmdParam<>(requestBody);
        OmdRes<StockRecommendRes> response;
        do {
            Long startIndex = maxStartIndex + 1;
            requestBody.setStartIndex(startIndex);
            response = omdFeign.stockRecommendModelQuery(omdParam);
            if (response == null) {
                log.error("经销商库存车数据同步失败: 未收到结果");
            }
            if (!("20000".equals(response.getCode())
                    && "1".equals(response.getResult().getRspStatus()))) {
                log.error("经销商库存车数据同步失败：" + JSONObject.toJSONString(response));
            }
            List<StockRecommendRes> data = response.getResult().getResultData();
            if (!CollectionUtils.isEmpty(data)) {
                data.forEach(i -> {
                    CarOmdStockRecommendLog log = new CarOmdStockRecommendLog();
                    BeanUtils.copyProperties(i, log);
                    log.setCreateTime(LocalDateTime.now());
                    stockRecommendLogService.save(log);
                });
                maxStartIndex = data.get(data.size() - 1).getRowIndex();
            }
        } while (response != null && "20000".equals(response.getCode())
                && "1".equals(response.getResult().getRspStatus())
                && !CollectionUtils.isEmpty(response.getResult().getResultData()));

    }

    public CarModelDto convertOriginData(OmdModelDto omdModelDto) throws Exception {
        CarModelDto carModelDto = new CarModelDto();
        List<ModelLineOptionVo> options = new ArrayList<>();
        carModelDto.setOptions(options);
        String channel = Constant.MASTER_CHANNEL;
        BestRecommendCarVo recommendCarVo = carRecommendService.bestRecommendToVo(omdModelDto, Constant.MASTER_CHANNEL);
        carModelDto.setModelLineVo(recommendCarVo.getModelLine());
        carModelDto.setSibInterieur(recommendCarVo.getModelLineSibInterieurVo());

        ModelLineOptionVo color = null, rad = null, vos = null, sib = null, eih = null;
        List<ModelLineOptionVo> prOptions = new ArrayList<>();
        prOptions.addAll(recommendCarVo.getOptions());
        Iterator<ModelLineOptionVo> prIter = prOptions.iterator();
        List<String> prFilters = Arrays.asList(OptionCategoryEnum.OUTCOLOR.getValue(), OptionCategoryEnum.INCOLOR.getValue(),
                OptionCategoryEnum.WHEEL.getValue(), OptionCategoryEnum.SEET.getValue(), OptionCategoryEnum.SIB.getValue(),
                OptionCategoryEnum.EIH.getValue());
        while (prIter.hasNext()) {
            ModelLineOptionVo optionVo = prIter.next();
            if (OptionCategoryEnum.WHEEL.getValue().equals(optionVo.getCategory())) {
                rad = optionVo;
            } else if (OptionCategoryEnum.SEET.getValue().equals(optionVo.getCategory())) {
                vos = optionVo;
            } else if (OptionCategoryEnum.SIB.getValue().equals(optionVo.getCategory())) {
                sib = optionVo;
            } else if (OptionCategoryEnum.EIH.getValue().equals(optionVo.getCategory())) {
                eih = optionVo;
            } else if (OptionCategoryEnum.OUTCOLOR.getValue().equals(optionVo.getCategory())) {
                color = optionVo;
            }
            if (prFilters.contains(optionVo.getCategory())) {
                prIter.remove();
            }
        }

        List<ModelLineOptionVo> optionVos = null;
        if (rad == null) {
            String category = OptionCategoryEnum.WHEEL.getValue();
            optionVos = modelLineService.modelLineOption(channel, recommendCarVo.getModelLine().getModelLineId(), category);
            for (ModelLineOptionVo o : optionVos) {
                if (o.getStatus() != null && o.getStatus().intValue() == 1) {
                    rad = o;
                    break;
                }
            }
        }
        vos = vos == null ? recommendCarVo.getVosOption() : vos;

        sib = sib == null ? recommendCarVo.getSibOption() : sib;

        if (eih == null) {
            String category = OptionCategoryEnum.EIH.getValue();
            optionVos = modelLineService.modelLineOption(channel, recommendCarVo.getModelLine().getModelLineId(), category);
            for (ModelLineOptionVo o : optionVos) {
                if (o.getStatus() != null && o.getStatus().intValue() == 1) {
                    eih = o;
                    break;
                }
            }
        }



        if (color != null) {
            carModelDto.getOptions().add(color);
        }
        if (vos != null) {
            carModelDto.getOptions().add(vos);
        }
        if (sib != null) {

        }
        if (eih != null) {
            carModelDto.getOptions().add(eih);
        }
        if (rad != null) {
            carModelDto.getOptions().add(rad);
        }
        int prFlag = 0;
        if (CollectionUtils.isNotEmpty(prOptions)) {
            OptionParamDto optionParamDto = new OptionParamDto();
            optionParamDto.setModelLineId(recommendCarVo.getModelLine().getModelLineId());
            optionParamDto.setCategories(Arrays.asList(OptionCategoryEnum.SEET.getValue(), OptionCategoryEnum.SIB.getValue()));
            optionParamDto.setOptionIds(prOptions.stream().map(o -> o.getOptionId()).collect(Collectors.toList()));
            List<ModelLineOptionVo> items = modelLineService.modelLinePacketItem(optionParamDto);
            boolean hasVos = false, hasSib = false;

            if (CollectionUtils.isNotEmpty(items)) {
                Set<String> categories = items.stream().map(o -> o.getCategory()).collect(Collectors.toSet());
                if (categories.contains(OptionCategoryEnum.SEET.getValue())) {
                    hasVos = true;
                }
                if (categories.contains(OptionCategoryEnum.SIB.getValue())) {
                    hasSib = true;
                }
            }
            if (hasSib && hasVos) {
                prFlag = 4;
            } else if (hasSib) {
                prFlag = 3;
            } else if (hasVos) {
                prFlag = 2;
            }
        }
        carModelDto.getOptions().addAll(prOptions);
        return carModelDto;
    }


    public void semiCustomizationModel(Consumer<? super CarMeasureMadeOriginConfig> action) {
        int page = 1;
        BaseRequestBody requestBody = new BaseRequestBody();
        requestBody.setBrandCode("A");
        requestBody.setPageSize("100");
        OmdParam<BaseRequestBody> omdParam = new OmdParam<>(requestBody);
        OmdRes<CarMeasureMadeOriginConfig> response;
        do {
            requestBody.setPage(String.valueOf(page));
            response = omdFeign.semiCustomizationModel(omdParam);
            if (response == null) {
                log.error("未收到结果");
            }
            if (!("20000".equals(response.getCode())
                    && "1".equals(response.getResult().getRspStatus()))) {
                log.error("半订制同步失败：" + JSONObject.toJSONString(response));
            }
            List<CarMeasureMadeOriginConfig> data = response.getResult().getResultData();
            if (!CollectionUtils.isEmpty(data)) {
                data.forEach(i->action.accept(i));
            }
            page = page + 1;
        } while (response != null && "20000".equals(response.getCode())
                && "1".equals(response.getResult().getRspStatus())
                && !CollectionUtils.isEmpty(response.getResult().getResultData()));
    }

    public List<ModelQueryRes> omdModelQuery(ModelQueryBody body){
        log.info("omd model query, param: {}", body);
        OmdParam<ModelQueryBody> omdParam = new OmdParam<>(body);
        OmdRes<ModelQueryRes> res = omdFeign.modelQuery(omdParam);
        log.info("omd model query, res:{}", res);
        return res.getResult().getResultData();
    }

    public void receiveOmdAbcData(Consumer<? super CarOmdVehicleType> action) {
        int page = 1;
        BaseRequestBody requestBody = new BaseRequestBody();
        requestBody.setBrandCode("A");
        requestBody.setPageSize("100");
        OmdParam<BaseRequestBody> omdParam = new OmdParam<>(requestBody);
        OmdRes<CarOmdVehicleType> response;
        do {
            requestBody.setPage(String.valueOf(page));
            response = omdFeign.abcClassisfModelDetailQuery(omdParam);
            if (response == null) {
                log.error("未收到结果");
            }
            if (!("20000".equals(response.getCode())
                    && "1".equals(response.getResult().getRspStatus()))) {
                log.error("半订制同步失败：" + JSONObject.toJSONString(response));
            }
            List<CarOmdVehicleType> data = response.getResult().getResultData();
            if (!CollectionUtils.isEmpty(data)) {
                data.forEach(i->action.accept(i));
            }
            page = page + 1;
        } while (response != null && "20000".equals(response.getCode())
                && "1".equals(response.getResult().getRspStatus())
                && !CollectionUtils.isEmpty(response.getResult().getResultData()));
    }
}
