package com.csvw.audi.cc.controller.ccnew;


import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.entity.dto.MeasureQueryParam;
import com.csvw.audi.cc.entity.dto.vwcc.CarCustomDetail;
import com.csvw.audi.cc.entity.po.CarCustom;
import com.csvw.audi.cc.entity.vo.MeasureVo;
import com.csvw.audi.cc.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 半订制化车辆配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@RestController
@RequestMapping("/api/v1/cc/measure/made")
@Slf4j
@Api(tags = "半订制车辆配置")
public class CarMeasureMadeConfigController extends BaseController {

    @Autowired
    private ICarMeasureMadeConfigService measureMadeConfigService;

    @Autowired
    private ICarMeasureMadeOriginConfigService measureMadeOriginConfigService;

    @Autowired
    private ICarCustomSeriesService customSeriesService;

    @Autowired
    private ICarModelLineService modelLineService;

    @Autowired
    private ICarCustomService carCustomService;

    @PostMapping(value = {"/modelLine/configs/measureQuery", "/public/modelLine/configs/measureQuery"})
    @ApiOperation("半订制化，查询选配")
    @ApiResponses(value = {@ApiResponse(code = 200001, message = "配置线没有半订制化车辆"), @ApiResponse(code = 400401, message = "操作繁忙，稍后重试")})
    public AjaxMessage<MeasureVo> measureQuery(@RequestHeader(value ="channel", defaultValue = Constant.ONEAPP_CHANNEL) String channel, @RequestBody @Validated MeasureQueryParam measureQueryParam, BindingResult bindingResult, HttpServletResponse response) throws Exception {
        super.validParam(bindingResult);
        MeasureVo measureVo = measureMadeConfigService.measureQuery(measureQueryParam, channel);
        if (measureVo == null){
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            return new AjaxMessage<>("400401", "操作繁忙，稍后重试", null);
        }
        return successMessage(measureVo);
    }

    @PostMapping(value = {"/carConfig", "/public/carConfig"})
    @ApiOperation("配置器配车（ccid）")
    public AjaxMessage<CarCustomDetail> addcarconfig(@RequestHeader(value = "X-User-Id", required = false) String userId,
                                                     @RequestHeader(value = "X-User-Mobile", required = false) String userMobile,
                                                     @RequestHeader(value = "channel", required = false) String channel,
                                                     Long measureId, Long sourceId, String entryPoint, String packetEquityId) throws Exception {
        CarCustom carCustom = measureMadeConfigService.addCarCustomConfig(userId,userMobile,measureId, sourceId, channel, entryPoint, packetEquityId);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        return new AjaxMessage<>("00", "成功", detail);
    }

    @PutMapping(value = {"/carConfig"})
    @ApiOperation("半订制配置修改（ccid）")
    public AjaxMessage<CarCustomDetail> updateCarConfig(@RequestHeader(value = "X-User-Id", required = false) String userId,
                                                     @RequestHeader(value = "X-User-Mobile", required = false) String userMobile,
                                                     @RequestHeader(value = "channel", required = false) String channel,
                                                     @RequestParam Long ccid, @RequestParam Long measureId, String packetEquityId) throws Exception {
        CarCustom cc = carCustomService.getById(ccid);
        if (cc == null || (cc.getUserId() != null && !cc.getUserId().equals(userId)) || cc.getMeasureId() == null){
            return new AjaxMessage<>("40004", "配置单不可修改", null);
        }

        CarCustom carCustom = measureMadeConfigService.updateCarCustomConfig(ccid, measureId, userId, userMobile, packetEquityId);
        CarCustomDetail detail = new CarCustomDetail();
        detail.setCcId(String.valueOf(carCustom.getCcid()));
        return new AjaxMessage<>("00", "成功", detail);
    }
}

