package com.csvw.audi.cc.feign;

import com.alibaba.fastjson.JSONObject;
import com.csvw.audi.cc.entity.dto.SmsDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Map;

@FeignClient(name = "audi-info")
//@FeignClient(value = "audi-info", url = "http://dev-audi-copapi.svwsx.cn/audi-info")
public interface AudiInfoFeign {

    @RequestMapping(value = "/private/api/v1/sendSMS", method = RequestMethod.POST)
    JSONObject sendSMS(@RequestBody SmsDto smsDto);

}
