package com.csvw.audi.cc.controller.svcd;

import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.entity.dto.SvcdProvinceDto;
import com.csvw.audi.cc.entity.vo.SvcdProvinceVo;
import com.csvw.audi.cc.service.ISvcdProvinceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "代理商-省份")
@RestController
@RequestMapping("/api/v1/svcdProvince")
public class SvcdProvinceController extends BaseController {

    @Autowired
    private ISvcdProvinceService svcdProvinceService;

    @ApiOperation("获取省份列表,默认查询存在代理商的")
    @GetMapping("/getProvinceList")
    public AjaxMessage<List<SvcdProvinceVo>> getProvinceList(SvcdProvinceDto provinceDto) {
        List<SvcdProvinceVo> list =  svcdProvinceService.getProvinceList(provinceDto);
        return new AjaxMessage<>("00", "成功", list);
    }

    @ApiOperation("获取省份列表-官网,默认查询存在经销商的")
    @GetMapping("/getProvinceListByOfficialWebsite")
    public AjaxMessage<List<SvcdProvinceVo>> getProvinceListByOfficialWebsite(SvcdProvinceDto provinceDto) {
        List<SvcdProvinceVo> list =  svcdProvinceService.getProvinceListByOfficialWebsite(provinceDto);
        return new AjaxMessage<>("00", "成功", list);
    }
}
