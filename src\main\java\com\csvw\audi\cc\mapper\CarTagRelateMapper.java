package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.dto.TagQueryDto;
import com.csvw.audi.cc.entity.po.CarTagRelate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.csvw.audi.cc.entity.vo.CarTagVo;

import java.util.List;

/**
 * <p>
 * 车配标签 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface CarTagRelateMapper extends BaseMapper<CarTagRelate> {

    List<CarTagVo> tagQuery(TagQueryDto queryDto);
}
