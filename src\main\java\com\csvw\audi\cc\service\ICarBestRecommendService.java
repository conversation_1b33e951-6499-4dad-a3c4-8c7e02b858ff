package com.csvw.audi.cc.service;

import com.csvw.audi.cc.entity.dto.CarBestRecommendDto;
import com.csvw.audi.cc.entity.po.CarBestRecommend;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 畅销推荐车 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-08
 */
public interface ICarBestRecommendService extends IService<CarBestRecommend> {

    List<CarBestRecommendDto> listBestRecommendByDealerCode(String dealerCode, String customSeriesId);

    List<CarBestRecommendDto> listBestRecommendByDealerCodeFix(String dealerCode, String customSeriesId);
}
