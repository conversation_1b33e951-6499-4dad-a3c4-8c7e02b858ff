package com.csvw.audi.cc.component.kafkaService.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.csvw.audi.cc.component.kafkaService.SvcdKafkaService;
import com.csvw.audi.cc.entity.po.SvcdArea;
import com.csvw.audi.cc.mapper.SvcdAreaMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service("svcdAreaKafkaService")
public class SvcdAreaKafkaServiceImpl implements SvcdKafkaService {

    @Autowired
    private SvcdAreaMapper svcdAreaMapper;

    @Override
    public void insertData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdArea area = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdArea.class);
        area.setCreatedAt(nowDate);
        area.setUpdatedAt(nowDate);
        area.insert();
    }

    @Override
    public void updateData(JSONObject bodyJsonObject, LocalDateTime nowDate) throws Exception {
        SvcdArea area = JSONObject.parseObject(bodyJsonObject.toJSONString(), SvcdArea.class);
        Long areaCode = area.getAreaCode();

        QueryWrapper<SvcdArea> areaQusery = new QueryWrapper<>();
        areaQusery.eq("area_code",areaCode);
        List<SvcdArea> list = area.selectList(areaQusery);
        if(list == null || list.size() == 0) {
            area.setCreatedAt(nowDate);
            area.setUpdatedAt(nowDate);
            area.insert();
        } else {
            area.setUpdatedAt(nowDate);

            UpdateWrapper<SvcdArea> areaUpdateWrapper = new UpdateWrapper<>();
            areaUpdateWrapper.eq("area_code",areaCode);
            area.setAreaCode(null);//主键置空，防止数据库更新报错
            svcdAreaMapper.update(area,areaUpdateWrapper);
        }
    }
}
