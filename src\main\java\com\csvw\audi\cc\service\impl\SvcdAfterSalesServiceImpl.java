package com.csvw.audi.cc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.csvw.audi.cc.common.utils.StringUtil;
import com.csvw.audi.cc.entity.dto.SvcdAfterSalesDto;
import com.csvw.audi.cc.entity.po.SvcdAfterSales;
import com.csvw.audi.cc.entity.po.SvcdChannelOrganizationFile;
import com.csvw.audi.cc.entity.po.SvcdChannelOrganizationPolicy;
import com.csvw.audi.cc.entity.vo.SvcdAfterSalesVo;
import com.csvw.audi.cc.feign.AudiEshopFeign;
import com.csvw.audi.cc.mapper.SvcdAfterSalesMapper;
import com.csvw.audi.cc.mapper.SvcdChannelOrganizationFileMapper;
import com.csvw.audi.cc.service.ISvcdAfterSalesService;
import com.csvw.audi.cc.service.ISvcdChannelOrganizationPolicyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 售后服务商 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Slf4j
@Service
public class SvcdAfterSalesServiceImpl extends ServiceImpl<SvcdAfterSalesMapper, SvcdAfterSales> implements ISvcdAfterSalesService {

    @Value("${svcd.dealer-image-url}")
    private String svcdAfterSalesImageUrl;

    //服务商默认图片 封面图
    static String afterSalesDefaultImageUrl = "test/2021/06/02/a7l/afterSales-default.png";
    //服务商默认图片 略缩图
    static String afterSalesDefaultThumbnailUrl = "test/2021/06/02/a7l/afterSales-default-thumbnai.png";

    private String getAfterSalesDefaultImageUrl() {
        return afterSalesDefaultImageUrl;
    }

    private String getAfterSalesDefaultThumbnailImageUrl() {
        return afterSalesDefaultThumbnailUrl;
    }

    @Autowired
    private ISvcdChannelOrganizationPolicyService svcdChannelOrganizationPolicyService;

    @Resource
    private SvcdAfterSalesMapper svcdAfterSalesMapper;

    @Resource
    private SvcdChannelOrganizationFileMapper svcdChannelOrganizationFileMapper;

    @Override
    public List<SvcdAfterSalesVo> getAfterSalesList(SvcdAfterSalesDto afterSalesDto) {
        List<SvcdAfterSalesVo> list = svcdAfterSalesMapper.getAfterSalesList(afterSalesDto);
        if(list == null) {
            list = new ArrayList<>();
        }

        //设置服务商图片
        if(list.size() > 0) {
            setAfterSalesImage(list);
        }

        //处理经纬度值
        for(SvcdAfterSalesVo vo : list) {
            if(vo.getAfterSalesLatitude() != null) {
                vo.setLatitude(vo.getAfterSalesLatitude());
                vo.setAfterSalesLatitude(null);
            }
            if(vo.getAfterSalesLongitude() != null) {
                vo.setLongitude(vo.getAfterSalesLongitude());
                vo.setAfterSalesLongitude(null);
            }
        }

        return list;
    }

    @Override
    public SvcdAfterSalesVo getAfterSalesByAgent(String agentCode) {
        SvcdAfterSalesVo svcdAfterSalesVo = null;
        SvcdAfterSalesDto afterSalesDto = new SvcdAfterSalesDto();
        afterSalesDto.setBusinessStatus("0");
        afterSalesDto.setAgentCode(agentCode);
        List<SvcdAfterSalesVo> list = svcdAfterSalesMapper.getAfterSalesList(afterSalesDto);
        if(list != null && list.size() > 0) {
            //设置服务商图片
            setAfterSalesImage(list);
            svcdAfterSalesVo = list.get(0);
            //处理经纬度值
            if(svcdAfterSalesVo.getAfterSalesLatitude() != null) {
                svcdAfterSalesVo.setLatitude(svcdAfterSalesVo.getAfterSalesLatitude());
                svcdAfterSalesVo.setAfterSalesLatitude(null);
            }
            if(svcdAfterSalesVo.getAfterSalesLongitude() != null) {
                svcdAfterSalesVo.setLongitude(svcdAfterSalesVo.getAfterSalesLongitude());
                svcdAfterSalesVo.setAfterSalesLongitude(null);
            }

        }
        return svcdAfterSalesVo;
    }

    @Override
    public List<SvcdAfterSalesVo> getAfterSalesListForReservation(String userId,SvcdAfterSalesDto afterSalesDto) {
        log.info("服务商列表查询条件:{} , {}",userId,afterSalesDto);
        String cityCode = afterSalesDto.getCityCode();
        Double longitude = null;
        if(afterSalesDto.getLongitude()!=null){
            longitude = afterSalesDto.getLongitude();
        }
        Double latitude = null;
        if(afterSalesDto.getLatitude()!=null){
            latitude = afterSalesDto.getLatitude();
        }
        String dealerCodeNewly=afterSalesDto.getDealerCodeNewly();//用户上次选择的代理商
        List<SvcdAfterSalesVo> list = new ArrayList<>();//返回的列表
        List<SvcdAfterSalesVo> afterSalesList;//所有服务商列表
        List<SvcdAfterSalesVo> agentBindingAfterSalesList = svcdAfterSalesMapper.getAgentBindingAfterSalesList();//有代理商指定的服务商
        boolean boolFlag = false;
        List<JSONObject> orderJsonList = null;//用户下单时门店指定的服务商

        //查询当前用户下单的代理商
//        try {
//            orderJsonList = audiEshopFeign.getOrderMessage(userId);
//            if(orderJsonList != null && orderJsonList.size() > 0) {
//                boolFlag = true;
//            } else {
//                log.info("查询用户下单的代理商 无数据");
//            }
//        } catch (Exception e) {
//            log.info("查询用户下单的代理商 失败",e);
//        }
        /*------------------------------------------- 数据准备 START -------------------------------------------*/
        //所有 已开业/试运营 的服务商列表
        SvcdAfterSalesDto afterSalesAll = new SvcdAfterSalesDto();
        afterSalesAll.setDealerAddress(afterSalesDto.getDealerAddress());
        afterSalesAll.setCityCode(cityCode);
        afterSalesList = svcdAfterSalesMapper.getAfterSalesList(afterSalesAll);
        log.info("所有已开业/试运营的服务商列表:{} , {}",userId,afterSalesList);
        //处理经纬度值
        for(SvcdAfterSalesVo vo : afterSalesList) {
            if(vo.getAfterSalesLatitude() != null) {
                vo.setLatitude(vo.getAfterSalesLatitude());
                vo.setAfterSalesLatitude(null);
            }
            if(vo.getAfterSalesLongitude() != null) {
                vo.setLongitude(vo.getAfterSalesLongitude());
                vo.setAfterSalesLongitude(null);
            }
        }
        //处理指定的代理商名称值
        for(SvcdAfterSalesVo allVo : afterSalesList) {
            for(SvcdAfterSalesVo agentBindingVo : agentBindingAfterSalesList) {
                if(agentBindingVo.getDealerCode().equals(allVo.getDealerCode())) {
                    allVo.setBindAgentCode(agentBindingVo.getBindAgentCode());
                    allVo.setBindAgentName(agentBindingVo.getBindAgentName());
                    break;
                }
            }
        }
        /*------------------------------------------- 数据准备 END -------------------------------------------*/

        /*------------------------------------------- 排序 START -------------------------------------------*/
//        if(boolFlag) {
//            //用户下单代理商指定的服务商
//            for(JSONObject jSONObject : orderJsonList) {
//                for(SvcdAfterSalesVo allVo : afterSalesList) {
//                    if(allVo.getBindAgentCode() != null && allVo.getBindAgentCode().equals(jSONObject.getString("dealerCode"))) {
//                        //添加到返回结果
//                        list.add(allVo);
//                        afterSalesList.remove(allVo);
//                        break;
//                    }
//                }
//            }
//        }

        //按城市筛选
//        if(StringUtil.isNotEmpty(cityCode)) {
//            for(int i = 0 ; i < afterSalesList.size() ; i++) {
//                if(!cityCode.equals(afterSalesList.get(i).getCityCode())) {
//                    afterSalesList.remove(i);
//                    i--;
//                }
//            }
//        }

//        //有代理商指定的服务商
//        tempList = new ArrayList<>(agentBindingAfterSalesList.size());
//        for(SvcdAfterSalesVo agentBindingVo : agentBindingAfterSalesList) {
//            for(SvcdAfterSalesVo allVo : afterSalesList) {
//                if(agentBindingVo.getDealerCode().equals(allVo.getDealerCode())) {
//                    tempList.add(allVo);
//                    afterSalesList.remove(allVo);
//                    break;
//                }
//            }
//        }
//        agentBindingAfterSalesList = tempList;
//        //按距离排序
//        locationSorting(agentBindingAfterSalesList,longitude,latitude);
//        //添加到返回结果
//        list.addAll(agentBindingAfterSalesList);

        if(afterSalesDto.getSortType() !=null && afterSalesDto.getSortType()==2
                && afterSalesDto.getLongitude()!=null && afterSalesDto.getLatitude()!=null){
            //距离优先,并且开启定位
            locationSorting(afterSalesList,longitude,latitude);//按距离排序
            list.addAll(afterSalesList);//添加到返回结果
            log.info("距离优先,并且开启定位:{} , {}",userId,list);
        }else{
            //上次选择的门店.
            log.info("上次选择的门店: {},{}",userId,dealerCodeNewly);
            if(StringUtils.isNotEmpty(dealerCodeNewly)){
                for(SvcdAfterSalesVo allVo : afterSalesList) {
                    if(allVo.getDealerCode() != null && allVo.getDealerCode().equals(dealerCodeNewly)) {
                        //添加到返回结果
                        list.add(allVo);
                        if(afterSalesDto.getLongitude()!=null && afterSalesDto.getLatitude()!=null){
                            locationSorting(list,longitude,latitude);//按距离排序
                        }
                        afterSalesList.remove(allVo);
                        break;
                    }
                }
            }
            if(afterSalesDto.getLongitude()==null && afterSalesDto.getLatitude()==null){//未开启定位，按照入库顺序
                list=this.sortSvcdAfterSales(afterSalesList,2,longitude,latitude,userId);
                log.info("未开启定位，按照入库顺序:{} , {}",userId,list);
            }else{//智能推荐，定位开启
                List<SvcdAfterSalesVo> lists = new ArrayList<>();
                lists=this.sortSvcdAfterSales(afterSalesList,1,longitude,latitude,userId);
                list.addAll(lists);
            }
        }
        /*------------------------------------------- 排序 END -------------------------------------------*/

        //设置服务商图片
        if(list.size() > 0) {
            setAfterSalesImage(list);
        }
        List<SvcdAfterSalesVo> collect = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)){
            collect = list.stream().map(a -> {
                SvcdAfterSalesVo vo2 = new SvcdAfterSalesVo();
                BeanUtils.copyProperties(a, vo2);
                vo2.setPolicyList(svcdChannelOrganizationPolicyService.list(Wrappers.<SvcdChannelOrganizationPolicy>lambdaQuery()
                        .eq(SvcdChannelOrganizationPolicy::getDealerCode, a.getDealerCode())));
                return vo2;
            }).collect(Collectors.toList());
        }
        return collect;
    }

    /**
     * 服务商结果排序
     * @param afterSalesList 服务商列表
     * @param ifLongitude 是否开启定位:1开启定位，2未开启定位
     * @return
     */
    private List<SvcdAfterSalesVo> sortSvcdAfterSales(List<SvcdAfterSalesVo> afterSalesList,
                                                      Integer ifLongitude,Double longitude, Double latitude,String userId){
        List<SvcdAfterSalesVo> list = new ArrayList<>();//返回的列表
        List<SvcdAfterSalesVo> tempList;//其他服务商

        //用户中心
        List<SvcdAfterSalesVo> userCenterList=new ArrayList<>();
        for(SvcdAfterSalesVo allVo : afterSalesList) {
            if(allVo.getDealerType()!=null){
                if(allVo.getDealerType()==0 || allVo.getDealerType()==4) {
                    userCenterList.add(allVo);
                }
            }
        }
        if(ifLongitude.equals(1)){
            locationSorting(userCenterList,longitude,latitude);//按距离排序
        }else if(ifLongitude.equals(2)){
            userCenterList.stream()
                    .sorted(Comparator.comparing(SvcdAfterSalesVo::getCreatedAt));
        }
        list.addAll(userCenterList);//添加到返回结果
        afterSalesList.removeAll(userCenterList);
        log.info("用户中心服务商:{} , {}",userId,userCenterList);

        //代理商及销售对应服务商
        List<SvcdAfterSalesVo> agentList=new ArrayList<>();
        for(SvcdAfterSalesVo allVo : afterSalesList) {
            if(allVo.getDealerType() !=null && allVo.getDealerAfterSaleType()!=null){
                if(allVo.getDealerType()==2 || (allVo.getDealerType()==1 && allVo.getDealerAfterSaleType()==1)) {
                    agentList.add(allVo);
                }
            }
        }
        if(ifLongitude.equals(1)){
            locationSorting(agentList,longitude,latitude);//按距离排序
        }else if(ifLongitude.equals(2)){
            agentList.stream()
                    .sorted(Comparator.comparing(SvcdAfterSalesVo::getCreatedAt));
        }
        list.addAll(agentList); //添加到返回结果
        afterSalesList.removeAll(agentList);
        log.info("代理商及销售对应服务商:{} , {}",userId,agentList);

        //独立服务商
        List<SvcdAfterSalesVo> duliList=new ArrayList<>();
        for(SvcdAfterSalesVo allVo : afterSalesList) {
            if(allVo.getDealerType() !=null && allVo.getDealerAfterSaleType()!=null){
                if(allVo.getDealerType()==1 && allVo.getDealerAfterSaleType()==2) {
                    duliList.add(allVo);
                }
            }
        }
        if(ifLongitude.equals(1)){
            locationSorting(duliList,longitude,latitude);//按距离排序
        }else if(ifLongitude.equals(2)){
            duliList.stream()
                    .sorted(Comparator.comparing(SvcdAfterSalesVo::getCreatedAt));
        }
        list.addAll(duliList); //添加到返回结果
        afterSalesList.removeAll(duliList);
        log.info("独立服务商:{} , {}",userId,duliList);

        //其他服务商
        tempList = afterSalesList;
        if(ifLongitude.equals(1)){
            locationSorting(tempList,longitude,latitude);//按距离排序
        }else if(ifLongitude.equals(2)){
            tempList.stream()
                    .sorted(Comparator.comparing(SvcdAfterSalesVo::getCreatedAt));
        }
        //添加到返回结果
        list.addAll(tempList);
        log.info("其他服务商:{} , {}",userId,tempList);
        return list;
    }

    //设置门店图片
    private void setAfterSalesImage(List<SvcdAfterSalesVo> list) {
        Set<String> dealerCodes = list.stream().map(i->i.getDealerCode()).collect(Collectors.toSet());
        QueryWrapper<SvcdChannelOrganizationFile> fileQuery = new QueryWrapper();
        fileQuery.eq("file_type","7").in("dealer_code", new ArrayList<>(dealerCodes));
        List<SvcdChannelOrganizationFile> fileList = svcdChannelOrganizationFileMapper.selectList(fileQuery);
        for(SvcdAfterSalesVo vo : list) {
//            for(SvcdChannelOrganizationFile fileObj : fileList) {
//                if(vo.getDealerCode().equals(fileObj.getDealerCode())) {
//                    if(fileObj.getName() != null && fileObj.getName().indexOf("封面图片") == 0) {
//                        vo.setImageUrl(svcdAfterSalesImageUrl+fileObj.getUrl());
//                    }
//                    else if(fileObj.getName() != null && fileObj.getName().indexOf("细节图片") == 0) {
//                        vo.setThumbnailUrl(svcdAfterSalesImageUrl+fileObj.getUrl());
//                    }
//                }
//            }
//            //设置默认封面图
//            if(vo.getImageUrl() == null || "".equals(vo.getImageUrl())) {
//                vo.setImageUrl(getAfterSalesDefaultImageUrl());
//            }
//            //设置默认略缩图
//            if(vo.getThumbnailUrl() == null || "".equals(vo.getThumbnailUrl())) {
//                vo.setThumbnailUrl(getAfterSalesDefaultThumbnailImageUrl());
//            }
            vo.setImageUrl(getAfterSalesDefaultImageUrl());
            vo.setThumbnailUrl(getAfterSalesDefaultThumbnailImageUrl());
        }
    }

    //经纬度值有效性判断
    private Boolean isValid(Double a){
        return a != null && Math.abs(a - 0) > 0.001;
    }

    //根据用户的经纬度，由近至远排序
    private void locationSorting(List<SvcdAfterSalesVo> list,double longitude,double latitude) {
        if(!isValid(longitude) || !isValid(latitude)) {
            return;
        }

        //计算经纬度距离
        for(SvcdAfterSalesVo dealer : list) {
            if(dealer.getLongitude() != null && dealer.getLatitude() != null) {
                dealer.setDistance(BigDecimal.valueOf(StringUtil.gitDistance(dealer.getLongitude(),dealer.getLatitude(),longitude,latitude)).setScale(2, RoundingMode.HALF_UP).doubleValue());
            }
        }
        //按距离由近到远排序
        SvcdAfterSalesVo dealerTemp;
        for(int i = 0 ; i < list.size() ; i ++) {
            for(int j = 0 ; j < list.size()-i-1 ; j ++) {
                if(list.get(j).getDistance() == null && list.get(j+1).getDistance() != null) {
                    dealerTemp = list.get(j);
                    list.set(j,list.get(j+1));
                    list.set(j+1,dealerTemp);
                }
                else if(list.get(j+1).getDistance() != null && list.get(j).getDistance() > list.get(j + 1).getDistance()) {
                    dealerTemp = list.get(j);
                    list.set(j,list.get(j+1));
                    list.set(j+1,dealerTemp);
                }
            }
        }
    }
}
