package com.csvw.audi.cc.entity.dto.accb;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class AudiConfigDto {
    @ApiModelProperty(value = "车辆配置id", hidden = true)
    private String id;
    @ApiModelProperty("accb车系id")
    private String modelId;
    @ApiModelProperty("accb车系code")
    private String modelCode;
    @ApiModelProperty("车系描述")
    private String modelDesc;
    @ApiModelProperty("车型id")
    private String typeId;
    @ApiModelProperty("车型code")
    private String typeCode;
    @ApiModelProperty("车型描述")
    private String typeDesc;
    @ApiModelProperty("车系年款")
    private String modelYear;
    @ApiModelProperty("车系版本")
    private String modelVersion;
    @ApiModelProperty("omd车系code")
    private String seriesCode;
    private String headline;
    private List<OptionBriefDto> options;

}
