package com.csvw.audi.cc.entity.dto.vwcc;

import com.alibaba.fastjson.annotation.JSONField;
import com.csvw.audi.cc.entity.dto.vwcc.ConfigDetail;
import com.csvw.audi.cc.entity.enumeration.CcInvalidReasonEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CarCustomDetail {
    private String accountId;
    private String agreementPrice;
    private String carActiveStatus;
    @ApiModelProperty("配置单id")
    private String ccId;
    @ApiModelProperty("券码")
    private String couponNo;
    private String mstMgrpId;
    private String ccName;
    private ConfigDetail configDetail;
    private String configSrcType;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime configTime;
    private String ownerInfoQfExpired;
    private String ownerMobile;
    private String ownerName;
    @ApiModelProperty("特选车id")
    private String bestRecommendId;
    @ApiModelProperty("半订制id")
    private String measureId;
    @ApiModelProperty(value = "定金类型，1：排产，2：库存")
    private String depositType;
    @ApiModelProperty(value = "配置单是否有效，0:失效，1:有效")
    private Integer valid;
    @ApiModelProperty(value = "配置单失效原因")
    private String invalidReason;
    @ApiModelProperty(value = "配置单更新标识，0:无更新，1:有更新")
    private Integer updateFlag;

    @ApiModelProperty(value = "配置单更新内容")
    private String updateContent;

    @ApiModelProperty(value = "车辆清单类型")
    private String classify;

    @ApiModelProperty(value = "预计交付周期")
    private String estimateDelivery;

    private String entryPoint;
}
