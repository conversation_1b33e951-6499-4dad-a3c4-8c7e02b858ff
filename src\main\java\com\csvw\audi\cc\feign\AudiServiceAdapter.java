package com.csvw.audi.cc.feign;

import com.csvw.audi.cc.entity.dto.omd.AmsModelQueryRecordParam;
import com.csvw.audi.common.api.AudiServiceAdapterService;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "audi-service-adapter")
public interface AudiServiceAdapter extends AudiServiceAdapterService {

    /**
     * AMS车辆查询记录埋点
     * @param recordParam
     * @return
     */
    @PostMapping(value = "/private/adapter/ams/ccQueryRecordSave")
    Response amsModelQueryRecord(@RequestBody AmsModelQueryRecordParam recordParam);

}
