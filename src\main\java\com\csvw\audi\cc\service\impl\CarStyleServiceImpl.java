package com.csvw.audi.cc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csvw.audi.cc.common.Constant;
import com.csvw.audi.cc.common.utils.ChannelDataUtils;
import com.csvw.audi.cc.entity.dto.ModelParamDto;
import com.csvw.audi.cc.entity.dto.PriceComputeParam;
import com.csvw.audi.cc.entity.po.CarEnergySystem;
import com.csvw.audi.cc.entity.po.CarModelLineStyle;
import com.csvw.audi.cc.entity.po.CarStyle;
import com.csvw.audi.cc.entity.vo.*;
import com.csvw.audi.cc.exception.ServiceException;
import com.csvw.audi.cc.mapper.CarStyleMapper;
import com.csvw.audi.cc.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.analysis.bn.BengaliAnalyzer;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.swing.text.Style;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 款式 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
@Service
@Slf4j
public class CarStyleServiceImpl extends ServiceImpl<CarStyleMapper, CarStyle> implements ICarStyleService {

    @Autowired
    private CarStyleMapper styleMapper;

    @Autowired
    private ICarModelLineService modelLineService;

    @Autowired
    private ICarRecommendService recommendService;

    @Autowired
    private ICarModelLineStyleService modelLineStyleService;

    @Autowired
    private ICarEnergySystemService carEnergySystemService;

    @Autowired
    private ICarModelLineTypeService modelLineTypeService;

    @Override
    @Cacheable(cacheNames = {"cc_cache"}, keyGenerator = "springCacheKeyGenerator")
    public List<StyleVo> listStyle(String channel, String customSeriesId, Integer type) throws Exception {
        List<CarStyle> styles;
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            styles = this.lambdaQuery().eq(CarStyle::getDelFlag, 0)
                    .eq(CarStyle::getCustomSeriesId, customSeriesId)
                    .in(CarStyle::getChannel, Constant.MASTER_CHANNEL, channel).list();
            styles = ChannelDataUtils.channelData(styles, CarStyle.class, channel, "styleId", false)
                    .stream().filter(a -> a.getFrontStatus().compareTo(1) == 0).collect(Collectors.toList());
        }else {
            styles = this.lambdaQuery().eq(CarStyle::getDelFlag, 0)
                    .eq(CarStyle::getCustomSeriesId, customSeriesId).eq(CarStyle::getFrontStatus, 1)
                    .eq(CarStyle::getChannel, channel).list();
        }
        ModelParamDto paramDto = new ModelParamDto();
        paramDto.setCustomSeriesId(customSeriesId);
        paramDto.setDelFlag(0);
        paramDto.setChannel(channel);
        List<ModelLineVo> viewedModelLines = modelLineService.listModelLine(paramDto);
        Map<String, ModelLineVo> modelLineMap = new HashMap<>();
        viewedModelLines.stream()
                .filter(i-> {
                    if(i.getOmdModelStatus() == 0 && (i.getMeasure() == null || i.getMeasure() != 1) && Constant.NEED_TYPE_FLAG.contains(channel)){
                        if (StringUtils.isBlank(i.getTypeFlag())){
                            return false;
                        }
                    }
                    List<ModelLineOptionVo> optionVos;
                    try {
                        optionVos = modelLineService.modelLinePersonalOptionDefault(i.getModelLineId());
                        String category = "COLOR_EXTERIEUR";
                        List<ModelLineOptionVo> exterieurs = modelLineService.modelLineOption(channel, i.getModelLineId(), category).stream().filter(e->e.getDefaultConfig() == 1).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(exterieurs)){
                            optionVos.add(exterieurs.get(0));
                        }
                    } catch (Exception e) {
                        log.error("查询配置线默认私人定制异常", e);
                        return false;
                    }
                    if (CollectionUtils.isNotEmpty(optionVos)){
                        List<String> optionIds = optionVos.stream().map(ModelLineOptionVo::getOptionId).collect(Collectors.toList());
                        PriceComputeParam computeParam = new PriceComputeParam();
                        computeParam.setOptionIds(optionIds);
                        computeParam.setModelLineId(i.getModelLineId());
                        try {
                            i.setPrice(modelLineService.computePrice(computeParam));
                        } catch (ServiceException e) {
                            log.error("查询配置线价格计算异常", e);
                            return false;
                        }
                    }
                    if (i.getFrontStatus() == null || i.getFrontStatus().intValue() == 0) {
                        return false;
                    }
                    switch (type){
                        case 1:
                            return i.getPersonal() != null && i.getPersonal().intValue() == 1;
                        case 2:
                            return i.getMeasure() != null && i.getMeasure().intValue() == 1 && modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId());
                        case 3:
                            return (i.getMeasure() != null && i.getMeasure().intValue() == 1 && modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId())) ||
                                    (i.getPersonal() != null && i.getPersonal().intValue()  == 1);
                    }
                    return false;
                }).forEach(modelLineVo -> modelLineMap.put(modelLineVo.getModelLineId(), modelLineVo));
        List<StyleVo> vos = styles.stream().sorted(Comparator.comparing(CarStyle::getWeight)).map(i->{
            StyleVo vo = new StyleVo();
            BeanUtils.copyProperties(i, vo);
            List<String> modelLineIds = styleMapper.styleModelLines(i.getStyleId());
            List<ModelLineVo> modelLineVos = new ArrayList<>();
            modelLineIds.forEach(modelLineId->{
                if (modelLineMap.get(modelLineId) != null) {
                    modelLineVos.add(modelLineMap.get(modelLineId));
                }
            });
            if (CollectionUtils.isEmpty(modelLineVos)){
                return null;
            }
            modelLineVos.sort((a,b)->{
                if (a.getWeight() == null && b.getWeight() == null){
                    return 0;
                }
                if (a.getWeight() == null){
                    return -1;
                }else if (b.getWeight() == null){
                    return 1;
                }else {
                    return a.getWeight().compareTo(b.getWeight());
                }
            });
            vo.setModelLineList(modelLineVos);
            vo.setMeasure(modelLineVos.stream().anyMatch(a->a.getMeasure() != null && a.getMeasure() == 1)?1:0);
            vo.setPersonal(modelLineVos.stream().anyMatch(a->a.getPersonal() != null && a.getPersonal() == 1)?1:0);
            vo.setSellBlips(styleMapper.listBlips(vo.getStyleId()));
            vo.setPrice(modelLineVos.stream().map(ModelLineVo::getPrice).min(
                    (a, b)->{
                        if (a instanceof BigDecimal && b instanceof BigDecimal){
                            return ((BigDecimal) a).compareTo((BigDecimal) b);
                        }else {
                            return 0;
                        }
                    }
            ).get());
            if(CollectionUtils.isEmpty(modelLineVos)){
                return null;
            }
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return vos;
    }

    @Override
    public List<StyleModelLineVo> listModelLine(String channel, CarStyle style) throws Exception {
        List<CarModelLineStyle> modelLineStyles = modelLineStyleService.lambdaQuery()
                .eq(CarModelLineStyle::getStyleId, style.getStyleId()).list();
        List<StyleModelLineVo> data = null;
        ModelParamDto paramDto = new ModelParamDto();
        paramDto.setCustomSeriesId(style.getCustomSeriesId());
        paramDto.setDelFlag(0);
        paramDto.setChannel(channel);
        data = modelLineService.listModelLine(paramDto).stream().map(i->{
                           StyleModelLineVo vo = new StyleModelLineVo();
                           BeanUtils.copyProperties(i, vo);
                           return vo;
                        }).filter(i->{
                            for (CarModelLineStyle s : modelLineStyles){
                                if (i.getModelLineId().equals(s.getModelLineId())){
                                    i.setDefaultConfig(s.getDefaultConfig());
                                    return true;
                                }
                            }
                            return false;
                        }).collect(Collectors.toList());
        return data;
    }

    @Override
    public List<StyleVo> recommendStyleList(String channel, String customSeriesId) throws NoSuchFieldException, IllegalAccessException {
        List<CarStyle> styles;
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            styles = this.lambdaQuery().eq(CarStyle::getDelFlag, 0)
                    .eq(CarStyle::getCustomSeriesId, customSeriesId).eq(CarStyle::getRecommendStatus, 1)
                    .in(CarStyle::getChannel, Constant.MASTER_CHANNEL, channel).list();
            styles = ChannelDataUtils.channelData(styles, CarStyle.class, channel, "styleId", false);
        }else {
            styles = this.lambdaQuery().eq(CarStyle::getDelFlag, 0)
                    .eq(CarStyle::getCustomSeriesId, customSeriesId).eq(CarStyle::getRecommendStatus, 1)
                    .eq(CarStyle::getChannel, channel).list();
        }

        List<RecommendCarSphereVo> recommendCarVos = recommendService.recommendCarSphere(channel, customSeriesId);
        List<StyleVo> vos = styles.stream().sorted(Comparator.comparing(CarStyle::getWeight)).map(i->{
            StyleVo vo = new StyleVo();
            BeanUtils.copyProperties(i, vo);
            List<String> modelLineIds = styleMapper.styleModelLines(i.getStyleId());
            List<RecommendCarSphereVo> styleRecommends = recommendCarVos.stream()
                    .filter(r ->modelLineIds.contains(r.getModelLineId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(styleRecommends)){
                return null;
            }
            vo.setPrice(styleRecommends.stream().map(RecommendCarSphereVo::getPrice).min(
                    (a, b)->{
                        if (a instanceof BigDecimal && b instanceof BigDecimal){
                            return ((BigDecimal) a).compareTo((BigDecimal) b);
                        }else {
                            return 0;
                        }
                    }
            ));
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        return vos;
    }

    @Override
    public List<RecommendCarSphereVo> recommendCarByStyle(String channel, CarStyle style) {
        List<RecommendCarSphereVo> recommendCarVos = recommendService.recommendCarSphere(channel, style.getCustomSeriesId());
        List<String> modelLineIds = styleMapper.styleModelLines(style.getStyleId());
        List<RecommendCarSphereVo> styleRecommends = recommendCarVos.stream().filter(r -> modelLineIds.contains(r.getModelLineId())).collect(Collectors.toList());
        return styleRecommends;
    }

    @Override
    public List<RecommendFixVo> recommendStyleFix(String channel, String customSeriesId) throws NoSuchFieldException, IllegalAccessException {
        List<CarStyle> styles;
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            styles = this.lambdaQuery().eq(CarStyle::getDelFlag, 0)
                    .eq(CarStyle::getCustomSeriesId, customSeriesId)
                    .in(CarStyle::getChannel, Constant.MASTER_CHANNEL, channel).list();
            styles = ChannelDataUtils.channelData(styles, CarStyle.class, channel, "styleId", false).stream()
                    .filter(a->a.getRecommendStatus().compareTo(1)==0).collect(Collectors.toList());
        }else {
            styles = this.lambdaQuery().eq(CarStyle::getDelFlag, 0)
                    .eq(CarStyle::getCustomSeriesId, customSeriesId).eq(CarStyle::getRecommendStatus, 1)
                    .eq(CarStyle::getChannel, channel).list();
        }

        List<RecommendCarSphereVo> recommendCarVos = recommendService.recommendCarSphere(channel, customSeriesId);
        List<StyleExtVo> vos = styles.stream().sorted(Comparator.comparing(CarStyle::getWeight)).map(i->{
            StyleExtVo vo = new StyleExtVo();
            BeanUtils.copyProperties(i, vo);
            List<String> modelLineIds = styleMapper.styleModelLines(i.getStyleId());
            List<RecommendCarSphereVo> styleRecommends = recommendCarVos.stream().filter(r ->modelLineIds.contains(r.getModelLineId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(styleRecommends)){
                return null;
            }
            vo.setRecommendCarSphereVos(styleRecommends);
            vo.setPrice(styleRecommends.stream().map(RecommendCarSphereVo::getPrice).min(
                    (a, b)->{
                        if (a instanceof BigDecimal && b instanceof BigDecimal){
                            return ((BigDecimal) a).compareTo((BigDecimal) b);
                        }else {
                            return 0;
                        }
                    }
            ));
            vo.setSellBlips(styleMapper.listBlips(vo.getStyleId()));
            vo.setMeasure(styleRecommends.stream().anyMatch(a->a.getMeasure() != null && a.getMeasure() == 1)? 1:0);
            vo.setPersonal(styleRecommends.stream().anyMatch(a->a.getPersonal() != null && a.getPersonal() == 1)? 1:0);
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Set<String> handleStyle = new HashSet<>();
        List<RecommendFixVo> recommendFixVos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(vos)){
            for (RecommendCarSphereVo sphereVo : recommendCarVos){
                boolean handleSphereVo = false;
                for (StyleExtVo styleExtVo: vos) {
                    if (styleExtVo.getRecommendCarSphereVos().stream()
                            .map(RecommendCarSphereVo::getModelLineId).collect(Collectors.toList())
                            .contains(sphereVo.getModelLineId())){
                        handleSphereVo = true;
                        if (handleStyle.contains(styleExtVo.getStyleId())){
                            break;
                        }
                        RecommendFixVo fixVo = new RecommendFixVo();
                        fixVo.setStyleVo(styleExtVo);
                        recommendFixVos.add(fixVo);
                        handleStyle.add(styleExtVo.getStyleId());
                    }
                }
                if (! handleSphereVo) {
                    RecommendFixVo fixVo = new RecommendFixVo();
                    fixVo.setRecommendCarSphereVo(sphereVo);
                    recommendFixVos.add(fixVo);
                }
            }
        }else {
            if (CollectionUtils.isNotEmpty(recommendCarVos)){
                for (RecommendCarSphereVo sphereVo : recommendCarVos){
                    RecommendFixVo fixVo = new RecommendFixVo();
                    fixVo.setRecommendCarSphereVo(sphereVo);
                    recommendFixVos.add(fixVo);
                }
            }
        }

        return recommendFixVos;
    }

    @Override
    @Cacheable(cacheNames = {"cc_cache"}, keyGenerator = "springCacheKeyGenerator")
    public List<EnergyStyleVo> listEnergyStyle(String channel, String customSeriesId, String modelYear, Integer type) throws Exception {
        List<CarStyle> styles;
        if (!Constant.MASTER_CHANNEL.equals(channel)) {
            styles = this.lambdaQuery().eq(CarStyle::getDelFlag, 0)
                    .eq(CarStyle::getCustomSeriesId, customSeriesId)
                    .in(CarStyle::getChannel, Constant.MASTER_CHANNEL, channel).list();
            styles = ChannelDataUtils.channelData(styles, CarStyle.class, channel, "styleId", false)
                    .stream().filter(a -> a.getFrontStatus().compareTo(1) == 0).collect(Collectors.toList());
        }else {
            styles = this.lambdaQuery().eq(CarStyle::getDelFlag, 0)
                    .eq(CarStyle::getCustomSeriesId, customSeriesId).eq(CarStyle::getFrontStatus, 1)
                    .eq(CarStyle::getChannel, channel).list();
        }
        ModelParamDto paramDto = new ModelParamDto();
        paramDto.setCustomSeriesId(customSeriesId);
        paramDto.setDelFlag(0);
        paramDto.setChannel(channel);
        List<ModelLineVo> viewedModelLines = modelLineService.listModelLine(paramDto);
        Map<String, ModelLineVo> modelLineMap = new HashMap<>();
        viewedModelLines.stream()
                .filter(i-> {
                    if(i.getOmdModelStatus() == 0 && (i.getMeasure() == null || i.getMeasure() != 1) && Constant.NEED_TYPE_FLAG.contains(channel)){
                        if (StringUtils.isBlank(i.getTypeFlag())){
                            return false;
                        }
                    }
                    List<ModelLineOptionVo> optionVos;
                    try {
                        optionVos = modelLineService.modelLinePersonalOptionDefault(i.getModelLineId());
                        if(i.getPriceAddColor() != null && i.getPriceAddColor().intValue() == 1) {
                            String category = "COLOR_EXTERIEUR";
                            List<ModelLineOptionVo> exterieurs = modelLineService.modelLineOption(channel, i.getModelLineId(), category).stream().filter(e -> e.getDefaultConfig() == 1).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(exterieurs)) {
                                optionVos.add(exterieurs.get(0));
                            }
                        }
                    } catch (Exception e) {
                        log.error("查询配置线默认私人定制异常", e);
                        return false;
                    }
                    if (CollectionUtils.isNotEmpty(optionVos)){
                        List<String> optionIds = optionVos.stream().map(ModelLineOptionVo::getOptionId).collect(Collectors.toList());
                        PriceComputeParam computeParam = new PriceComputeParam();
                        computeParam.setOptionIds(optionIds);
                        computeParam.setModelLineId(i.getModelLineId());
                        try {
                            i.setPrice(modelLineService.computePrice(computeParam));
                        } catch (ServiceException e) {
                            log.error("查询配置线价格计算异常", e);
                            return false;
                        }
                    }
                    if (i.getFrontStatus() == null || i.getFrontStatus().intValue() == 0) {
                        return false;
                    }
                    if (StringUtils.isNotBlank(modelYear)){
                        return i.getModelYear().equalsIgnoreCase(modelYear);
                    }
                    switch (type){
                        case 1:
                            return i.getPersonal() != null && i.getPersonal().intValue() == 1;
                        case 2:
                            return i.getMeasure() != null && i.getMeasure().intValue() == 1 && modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId());
                        case 3:
                            return (i.getMeasure() != null && i.getMeasure().intValue() == 1 && modelLineService.measureFilter(channel, i.getAccbTypeCode(), i.getModelLineId())) ||
                                    (i.getPersonal() != null && i.getPersonal().intValue()  == 1);
                    }
                    return false;
                }).forEach(modelLineVo -> {
                    modelLineVo.setTypeIdsBySeats(modelLineTypeService.listTypeIdsBySeats(modelLineVo));
                    modelLineMap.put(modelLineVo.getModelLineId(), modelLineVo);
                });
        List<StyleVo> styleVos = styles.stream().sorted(Comparator.comparing(CarStyle::getWeight)).map(i->{
            StyleVo vo = new StyleVo();
            BeanUtils.copyProperties(i, vo);
            List<String> modelLineIds = styleMapper.styleModelLines(i.getStyleId());
            List<ModelLineVo> modelLineVos = new ArrayList<>();
            modelLineIds.forEach(modelLineId->{
                if (modelLineMap.get(modelLineId) != null) {
                    modelLineVos.add(modelLineMap.get(modelLineId));
                }
            });
            if (CollectionUtils.isEmpty(modelLineVos)){
                return null;
            }
            modelLineVos.sort((a,b)->{
                if (a == null || a.getPrice() == null){
                    return -1;
                }else if (b == null || b.getPrice() == null){
                    return 1;
                }else if (a.getPrice() instanceof BigDecimal && b.getPrice() instanceof BigDecimal){
                    return ((BigDecimal) a.getPrice()).compareTo((BigDecimal) b.getPrice());
                }else {
                    return 0;
                }
            });
            modelLineVos.sort((a,b)->{
                if (a == null || a.getModelYear() == null){
                    return -1;
                }else if (b == null || b.getModelYear() == null){
                    return 1;
                }else if(a.getModelYear().compareTo(b.getModelYear()) == 0) {
                    if (a == null || a.getVersion() == null){
                        return -1;
                    }else if (b == null || b.getVersion() == null){
                        return 1;
                    }else {
                        return a.getVersion().compareTo(b.getVersion());
                    }
                }else{
                    return a.getModelYear().compareTo(b.getModelYear());
                }
            });

            vo.setVersion(Integer.valueOf(modelLineVos.get(0).getVersion()));
            vo.setModelLineList(modelLineVos);
            vo.setEngines(modelLineVos.stream().map(ModelLineVo::getEngine).collect(Collectors.toSet()));
            vo.setMeasure(modelLineVos.stream().anyMatch(a->a.getMeasure() != null && a.getMeasure() == 1)?1:0);
            vo.setPersonal(modelLineVos.stream().anyMatch(a->a.getPersonal() != null && a.getPersonal() == 1)?1:0);
            vo.setSellBlips(styleMapper.listBlips(vo.getStyleId()));
            vo.setModelYear(modelLineVos.get(0).getModelYear());
            vo.setPrice(modelLineVos.stream().map(ModelLineVo::getPrice).min(
                    (a, b)->{
                        if (a instanceof BigDecimal && b instanceof BigDecimal){
                            return ((BigDecimal) a).compareTo((BigDecimal) b);
                        }else {
                            return 0;
                        }
                    }
            ).get());
            if(CollectionUtils.isEmpty(modelLineVos)){
                return null;
            }
            return vo;
        }).filter(Objects::nonNull).sorted((a,b)->{
            if (a == null || a.getPrice() == null){
                return -1;
            }else if (b == null || b.getPrice() == null){
                return 1;
            }else if (a.getPrice() instanceof BigDecimal && b.getPrice() instanceof BigDecimal){
                return ((BigDecimal) a.getPrice()).compareTo((BigDecimal) b.getPrice());
            }else {
                return 0;
            }
        }).collect(Collectors.toList());

        LambdaQueryWrapper<CarEnergySystem> enQ = new LambdaQueryWrapper<>();
        enQ.eq(CarEnergySystem::getDelFlag, 0).eq(CarEnergySystem::getCustomSeriesId, customSeriesId)
                .eq(CarEnergySystem::getChannel, Constant.MASTER_CHANNEL)
                .orderByAsc(CarEnergySystem::getWeight);
        List<CarEnergySystem> energySystems = carEnergySystemService.list(enQ);
        // 匹配动力
        List<EnergyStyleVo> energyStyleVos = energySystems.stream().map(en->{
            EnergyStyleVo vo = new EnergyStyleVo();
            BeanUtils.copyProperties(en, vo);
            for (StyleVo styleVo: styleVos){
                if (styleVo.getEngines().contains(en.getEngine())){
                    if (vo.getStyleVos() == null){
                        List<StyleVo> enStyleVos = new ArrayList<>();
                        enStyleVos.add(styleVo);
                        vo.setStyleVos(enStyleVos);
                    }else {
                        vo.getStyleVos().add(styleVo);
                    }
                }
            }
            List<StyleVo> enStyleVos = null;
            if(CollectionUtils.isNotEmpty(vo.getStyleVos())) {
                 enStyleVos = vo.getStyleVos().stream().sorted((a, b) -> {
                    if (a == null || a.getModelYear() == null) {
                        return -1;
                    } else if (b == null || b.getModelYear() == null) {
                        return 1;
                    } else if (a.getModelYear().compareTo(b.getModelYear()) == 0) {
                        if (a.getVersion() == null) {
                            return -1;
                        } else if (b.getVersion() == null) {
                            return 1;
                        } else if (a.getModelYear().compareTo(b.getModelYear()) == 0) {
                            if (a.getPrice() == null) {
                                return -1;
                            } else if (b.getPrice() == null) {
                                return 1;
                            } else if (a.getPrice() instanceof BigDecimal && b.getPrice() instanceof BigDecimal) {
                                return ((BigDecimal) a.getPrice()).compareTo((BigDecimal) b.getPrice());
                            } else {
                                return 0;
                            }
                        } else {
                            return a.getVersion().compareTo(b.getVersion());
                        }
                    } else {
                        return a.getModelYear().compareTo(b.getModelYear());
                    }
                }).collect(Collectors.toList());
            }
            vo.setStyleVos(enStyleVos);
            return vo;
        }).filter(i->CollectionUtils.isNotEmpty(i.getStyleVos())).collect(Collectors.toList());
        energyStyleVos.forEach(vo->{
            List<StyleVo> styleVoList = vo.getStyleVos();
            if (CollectionUtils.isNotEmpty(styleVoList)) {
                vo.setPrice(styleVoList.get(0).getPrice());
                vo.setPreferential(styleVoList.get(0).getPreferential());
            }
        });
        return energyStyleVos;
    }
}
