package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.po.CarOmdStockMap;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * OMD库存车映射 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-28
 */
public interface CarOmdStockMapMapper extends BaseMapper<CarOmdStockMap> {

    List<CarOmdStockMap> measureMaps();

    List<CarOmdStockMap> hqMaps();

    List<String> stockMapDealers();

    List<CarOmdStockMap> stockMaps(@Param("dealerCode") String dealerCode);
}
