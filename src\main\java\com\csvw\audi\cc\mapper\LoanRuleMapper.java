package com.csvw.audi.cc.mapper;

import com.csvw.audi.cc.entity.po.LoanRule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-18
 */
public interface LoanRuleMapper extends BaseMapper<LoanRule> {

    List<LoanRule> queryModelLineLoanRule(@Param("modelLineId") String modelLineId, @Param("loanAgencyCodes") List<String> loanAgencyCodes, @Param("type") Integer type);

}
