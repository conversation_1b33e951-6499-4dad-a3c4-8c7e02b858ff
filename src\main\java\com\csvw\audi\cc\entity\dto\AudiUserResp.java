package com.csvw.audi.cc.entity.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AudiUserResp {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty("用户车主标示 1小订车主 2大定车主 3车主")
    private Integer isCarOwner;

    @ApiModelProperty("用户车主标示 1小订车主 2大定车主 3车主")
    private Integer isCarOwner2; // 从isCarOwner复制过来的

    private Integer followsNo;
    private Integer fansNo;
    private String identityCode;
    private String identityName;
    private String comment;
    private String userIdIdp;
}
