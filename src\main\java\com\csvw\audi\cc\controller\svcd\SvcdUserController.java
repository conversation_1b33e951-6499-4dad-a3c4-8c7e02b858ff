package com.csvw.audi.cc.controller.svcd;


import cn.hutool.core.util.StrUtil;
import com.csvw.audi.cc.common.AjaxMessage;
import com.csvw.audi.cc.common.BaseController;
import com.csvw.audi.cc.constants.RedisConstant;
import com.csvw.audi.cc.entity.vo.svcd.SvcdUserListRO;
import com.csvw.audi.cc.entity.vo.svcd.SvcdUserListVO;
import com.csvw.audi.cc.service.ISvcdUserService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 渠道商人员信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-06
 */
@RestController
@RequestMapping("/api/svcdUser")
@Slf4j
public class SvcdUserController extends BaseController {
    @Autowired
    ISvcdUserService iSvcdUserService;
    @Autowired
    RedissonClient redissonClient;

    @ApiOperation(value = "获取渠道商奥迪管家",notes = "针对爱车页-门店-渠道商奥迪管家人员信息")
    @GetMapping("/audiSteward/list")
    public AjaxMessage<List<SvcdUserListRO>> audiStewardList(SvcdUserListVO vo) {
        vo.setPositionCode("GW003");
        //vo.setStatus(1);
        //vo.setWorkStatus(1);
        vo.setCheckStatus(1);
        vo.setWorkStatusList(Arrays.asList(0,1));
        Assert.isTrue(StrUtil.isNotBlank(vo.getDealerCode()),"请选择渠道商!");
        return successMessage(iSvcdUserService.listNoPage(vo));
    }

    @ApiOperation(value = "获取渠道商奥迪管家企微链接",notes = "针对爱车页-门店-渠道商奥迪管家人员信息企微链接")
    @GetMapping("/audiSteward/link/{userId}")
    public AjaxMessage<String> audiStewardLink(@PathVariable("userId") Long userId,
                                               @RequestHeader(value = "X-User-Mobile", required = false) String userMobile) {
        return successMessage(iSvcdUserService.audiStewardLink(userId,userMobile));
    }

    @ApiOperation(value = "人员数据kafka消费开关",notes = "网发推送的人员数据不一定为最新的,所以一次推送完,关闭不要再次消费，防止update的数据丢失")
    @PutMapping("/initUserFlag")
    public AjaxMessage initUserFlag(@RequestParam(value = "operType", required = false) Boolean operType) {
        RBucket<Object> bucket = redissonClient.getBucket(RedisConstant.KAFKA_USER_INIT_FLAG);
        bucket.set(operType,24, TimeUnit.HOURS);
        log.info("svcdUserInitUserFlag:{}",bucket.get());
        return successMessage("成功");
    }


}

