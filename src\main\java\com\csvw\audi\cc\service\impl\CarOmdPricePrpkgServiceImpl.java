package com.csvw.audi.cc.service.impl;

import com.csvw.audi.cc.entity.dto.PriceCondition;
import com.csvw.audi.cc.entity.po.CarOmdPricePrpkg;
import com.csvw.audi.cc.mapper.CarOmdPricePrpkgMapper;
import com.csvw.audi.cc.service.ICarOmdPricePrpkgService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <p>
 * 外饰价格 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-18
 */
@Service
public class CarOmdPricePrpkgServiceImpl extends ServiceImpl<CarOmdPricePrpkgMapper, CarOmdPricePrpkg> implements ICarOmdPricePrpkgService {

    @Autowired
    private CarOmdPricePrpkgMapper pricePrpkgMapper;

    @Override
    public BigDecimal getPrice(PriceCondition condition) {
        return pricePrpkgMapper.getPrice(condition);
    }
}
