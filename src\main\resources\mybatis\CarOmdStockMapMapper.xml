<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csvw.audi.cc.mapper.CarOmdStockMapMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.csvw.audi.cc.entity.po.CarOmdStockMap">
        <id column="id" property="id" />
        <result column="origin_model_code" property="originModelCode" />
        <result column="origin_model_version" property="originModelVersion" />
        <result column="origin_model_year" property="originModelYear" />
        <result column="origin_interior_code" property="originInteriorCode" />
        <result column="origin_color_code" property="originColorCode" />
        <result column="origin_pr_list" property="originPrList" />
        <result column="origin_class_code" property="originClassCode" />
        <result column="origin_cc_unique_code" property="originCcUniqueCode" />
        <result column="model_code" property="modelCode" />
        <result column="model_version" property="modelVersion" />
        <result column="model_year" property="modelYear" />
        <result column="interior_code" property="interiorCode" />
        <result column="color_code" property="colorCode" />
        <result column="pr_list" property="prList" />
        <result column="class_code" property="classCode" />
        <result column="cc_unique_code" property="ccUniqueCode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, origin_model_code, origin_model_version, origin_model_year, origin_interior_code, origin_color_code, origin_pr_list, origin_class_code, origin_cc_unique_code, model_code, model_version, model_year, interior_code, color_code, pr_list, class_code, cc_unique_code, create_time, update_time, del_flag
    </sql>

    <select id="measureMaps" resultType="com.csvw.audi.cc.entity.po.CarOmdStockMap">
        select m.* from
        `car_measure_made_origin_config` oc
        LEFT JOIN  `car_measure_made_config` c on oc.`measure_origin_id`  = c.`measure_origin_id`
        RIGHT JOIN  `car_omd_stock_map` m on oc.`cc_unique_code` = m.`origin_cc_unique_code`
        where c.`measure_id` is null and m.del_flag = 0
    </select>

    <select id="hqMaps" resultType="com.csvw.audi.cc.entity.po.CarOmdStockMap">
        select m.* from
        `car_omd_best_recommend` obr
        LEFT JOIN `car_best_recommend_stock` s on obr.`best_sell_recommend_model_id` = s.`recommend_model_id`
        LEFT JOIN `car_omd_stock_map` m on obr.`cc_unique_code` = m.`origin_cc_unique_code`
        where s.`recommend_model_id`  is null and m.del_flag = 0
    </select>

    <select id="stockMapDealers" resultType="String">
        select distinct(osr.`dealer_net_code`) from
        `car_omd_stock_recommend` osr
        LEFT JOIN `car_best_recommend_stock` s on osr.`stock_recommend_model_id` = s.`recommend_model_id`
        LEFT JOIN `car_omd_stock_map` m on osr.`cc_unique_code` = m.`origin_cc_unique_code`
        where s.`recommend_model_id`  is null and osr.`dealer_net_code` is not null and osr.del_flag = 0 and m.del_flag = 0
    </select>

    <select id="stockMaps" resultType="com.csvw.audi.cc.entity.po.CarOmdStockMap">
        select m.* from
        `car_omd_stock_recommend` osr
        LEFT JOIN `car_best_recommend_stock` s on osr.`stock_recommend_model_id` = s.`recommend_model_id`
        RIGHT JOIN `car_omd_stock_map` m on osr.`cc_unique_code` = m.`origin_cc_unique_code`
        where s.`recommend_model_id`  is null and osr.`dealer_net_code` = #{dealerCode} and osr.del_flag = 0 and m.del_flag = 0
    </select>

</mapper>
