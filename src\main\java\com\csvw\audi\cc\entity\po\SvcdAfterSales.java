package com.csvw.audi.cc.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 售后服务商
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SvcdAfterSales对象", description="售后服务商")
public class SvcdAfterSales extends Model<SvcdAfterSales> {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "数据id")
      @TableId(value = "after_sales_id", type = IdType.AUTO)
    private Long afterSalesId;

    @ApiModelProperty(value = "渠道商代码")
    private String dealerCode;

    @ApiModelProperty(value = "服务商类型(1：销售+售后绑定，2：单独售后)")
    private String dealerAfterSaleType;

    @ApiModelProperty(value = "一汽奥迪经销商建店级别")
    private String fawAudiBuildLevel;

    @ApiModelProperty(value = "一汽奥迪经销商开业时间")
    private String fawAudiStartTime;

    @ApiModelProperty(value = "一汽奥迪经销商授权种类")
    private String fawAudiAuthorizationType;

    @ApiModelProperty(value = "一汽奥迪授权截止日期")
    private String fawAudiAuthorizationDeadline;

    @ApiModelProperty(value = "一汽奥迪经销商新能源进口车资质")
    private String fawAudiNewEnergyImportVehicleQualification;

    @ApiModelProperty(value = "一汽奥迪经销商新能源国产车资质")
    private String fawAudiNewEnergyDomesticVehicleQualification;

    @ApiModelProperty(value = "一汽奥迪经销商授权高压电池维修中心")
    private String fawAudiBatteryMaintenanceCenter;

    @ApiModelProperty(value = "一汽奥迪经销商救援服务车")
    private Long fawAudiRescueServiceVehicleNum;

    @ApiModelProperty(value = "一汽奥迪经销商替换车数量")
    private Long fawAudiCarReplaceNum;
//    private Long fawAudiCarReplaceServiceNum;

    @ApiModelProperty(value = "工具验收日期")
    private String hardAcceptDate;

    @ApiModelProperty(value = "系统验收日期")
    private String itSysAcceptDate;

    @ApiModelProperty(value = "首批配附件验收日期")
    private String firstAttachmentsAcceptDate;

    @ApiModelProperty(value = "上汽奥迪授权截止日期")
    private String saicAudiAuthorizationDeadline;

    @ApiModelProperty(value = "上汽奥迪维修级别/是否新能源车售后(B：否，A：是)")
    private String saicAudiMaintenanceLevel;

    @ApiModelProperty(value = "上汽奥迪救援级别")
    private String saicAudiRescueLevel;

    @ApiModelProperty(value = "上汽奥迪授权高压电池维修中心")
    private String saicAudiBatteryMaintenanceCenter;

    @ApiModelProperty(value = "是否提供上汽奥迪替换车服务(0：否，1：是)")
    private String provideSaicAudiCarReplaceService;

    @ApiModelProperty(value = "是否提供上汽奥迪取送车服务(0：否，1：是)")
    private String provideSaicAudiPickUpService;

    @ApiModelProperty(value = "是否接收上汽奥迪 ASR 服务线索(0：否，1：是)")
    @TableField(value = "receive_saic_audi_asr_service_clues")
    private String receiveSaicAudiASRServiceClues;

    @ApiModelProperty(value = "是否接收上汽奥迪非 ASR 服务线索信息(0：否，1：是)")
    @TableField(value = "receive_saic_audi_non_asr_service_clues")
    private String receiveSaicAudiNonASRServiceClues;

    @ApiModelProperty(value = "售后服务电话")
    private String servicePhone;

    @ApiModelProperty(value = "售后救援服务联系人")
    private String rescue;

    @ApiModelProperty(value = "售后救援服务联系电话")
    private String rescuePhone;

    @ApiModelProperty(value = "服务接待区工作台")
    private Long serviceReceiveWorkbench;

    @ApiModelProperty(value = "机修工位")
    private Long machineRepairStation;

    @ApiModelProperty(value = "钣喷工位")
    private Long bpStation;

    @ApiModelProperty(value = "烤房数量")
    private Long bakeriesNum;

    @ApiModelProperty(value = "已有的交流桩功率")
    private Long interflowPower;

    @ApiModelProperty(value = "已有的交流桩数量")
    private Long interflowNum;

    @ApiModelProperty(value = "已有的直流桩功率")
    private Long directCurrentPower;

    @ApiModelProperty(value = "已有的直流桩数量")
    private Long directCurrentNum;

    @ApiModelProperty(value = "机修工位")
    private String checkApplyReport;

    @ApiModelProperty(value = "带条件验收承诺完成时间")
    private String checkCompleteTime;

    @ApiModelProperty(value = "服务验收日期")
    private String serviceCheckDate;

    @ApiModelProperty(value = "救援协议合同编号")
    private String rescueContractCode;

    @ApiModelProperty(value = "救援协议生效日期")
    private String rescueContractTakeEffectDate;

    @ApiModelProperty(value = "救援协议失效日期")
    private String rescueContractInvalidDate;

    @ApiModelProperty(value = "取送车协议合同编号")
    private String takeSendCarContractCode;

    @ApiModelProperty(value = "取送车协议生效日期")
    private String takeSendCarTakeEffectDate;

    @ApiModelProperty(value = "取送车协议失效日期")
    private String takeSendCarInvalidDate;

    @ApiModelProperty(value = "信息验收日期")
    private String infoCheckDate;

    @ApiModelProperty(value = "上汽奥迪服务牌现场照片")
    private String serviceImg;

    @ApiModelProperty(value = "机修工位")
    private String checkApprovalDate;

    @ApiModelProperty(value = "售后服务联系人")
    private String afterSaleServiceContacts;

    @ApiModelProperty(value = "售后服务联系人电话")
    private String afterSaleServiceContactsPhone;

    @ApiModelProperty(value = "是否大用户售后对口维修点")
    private String isVipAfterSale;

    @ApiModelProperty(value = "东经")
    private String eastLongitude;

    @ApiModelProperty(value = "北纬")
    private String northLatitude;

    @ApiModelProperty(value = "授权合同开始日期")
    private String contractStartDate;

    @ApiModelProperty(value = "授权合同结束日期")
    private String contractEndDate;

    @ApiModelProperty(value = "售后服务联系人座机")
    private String afterSaleServiceContactsTel;

    @ApiModelProperty(value = "758编码")
    private String code758;

    @ApiModelProperty(value = "预留字段")
    private String spare1;

    @ApiModelProperty(value = "预留字段")
    private String spare2;

    @ApiModelProperty(value = "预留字段")
    private String spare3;

    @ApiModelProperty(value = "是否删除(0：未删除，1：已删除)")
    private Long deleted;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;
    @ApiModelProperty(value = "渠道形态")
    private String exhibitionHallForm;
    @ApiModelProperty(value = "售后代码")
    private String serviceCode;
    @ApiModelProperty(value = "服务商使用系统(EP/AMS)")
    private String useSystem;
    @Override
    protected Serializable pkVal() {
        return this.afterSalesId;
    }

}
