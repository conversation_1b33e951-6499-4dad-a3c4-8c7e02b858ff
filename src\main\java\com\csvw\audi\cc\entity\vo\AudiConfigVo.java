package com.csvw.audi.cc.entity.vo;

import com.csvw.audi.cc.entity.dto.accb.OptionBriefDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AudiConfigVo {
    @ApiModelProperty(value = "配置单id")
    private String ccid;
    @ApiModelProperty(value = "自定义车系id")
    private String customSeriesId;
    @ApiModelProperty(value = "自定义车系code")
    private String customSeriesCode;
    @ApiModelProperty(value = "自定义车系code")
    private String customSeriesName;
    @ApiModelProperty(value = "车系id")
    private String seriesId;
    @ApiModelProperty(value = "车系code")
    private String seriesCode;
    @ApiModelProperty(value = "车系名称")
    private String seriesName;
    @ApiModelProperty(value = "配置线id")
    private String modelLineId;
    @ApiModelProperty(value = "配置线名称")
    private String modelLineName;
    @ApiModelProperty(value = "配置线编码")
    private String modelLineCode;
    @ApiModelProperty(value = "accb配置线code")
    private String accbTypeCode;
    @ApiModelProperty(value = "accb配置线ID")
    private String accbTypeId;
    @ApiModelProperty(value = "accb车系code")
    private String accbModelCode;
    @ApiModelProperty(value = "accb车系ID")
    private String accbModelId;
    @ApiModelProperty(value = "车型id")
    private String modelId;
    @ApiModelProperty(value = "omd车型编码")
    private String modelCode;
    @ApiModelProperty(value = "车型年款")
    private String modelYear;
    @ApiModelProperty(value = "车型版本")
    private String modelVersion;
    private List<OptionBriefDto> options;
}
